# Generated by Django 5.2 on 2025-07-18 17:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0026_alter_eighthourpersonnel_options_and_more'),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='unitpersonnel',
            unique_together={('unit', 'registration_number')},
        ),
        migrations.AddField(
            model_name='unitpersonnel',
            name='birth_date',
            field=models.DateField(blank=True, null=True, verbose_name='تاريخ الميلاد'),
        ),
        migrations.AddField(
            model_name='unitpersonnel',
            name='first_name',
            field=models.CharField(default='غير محدد', max_length=50, verbose_name='الاسم'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='unitpersonnel',
            name='joining_date',
            field=models.DateField(blank=True, null=True, verbose_name='تاريخ الالتحاق'),
        ),
        migrations.AddField(
            model_name='unitpersonnel',
            name='last_name',
            field=models.CharField(default='غير محدد', max_length=50, verbose_name='اللقب'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='unitpersonnel',
            name='gender',
            field=models.CharField(choices=[('male', 'ذكر'), ('female', 'أنثى')], default='male', max_length=10, verbose_name='الجنس'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='unitpersonnel',
            name='position',
            field=models.CharField(max_length=50, verbose_name='المنصب'),
        ),
        migrations.AlterField(
            model_name='unitpersonnel',
            name='rank',
            field=models.CharField(default=2, max_length=50, verbose_name='الرتبة'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='unitpersonnel',
            name='registration_number',
            field=models.CharField(default='غير محدد', max_length=20, unique=True, verbose_name='رقم التسجيل'),
            preserve_default=False,
        ),
        migrations.RemoveField(
            model_name='unitpersonnel',
            name='age',
        ),
        migrations.RemoveField(
            model_name='unitpersonnel',
            name='personnel_registration_number',
        ),
    ]
