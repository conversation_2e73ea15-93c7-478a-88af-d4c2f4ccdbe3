# Generated by Django 5.2 on 2025-07-19 17:48

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0030_add_souk_ahras_sample_data'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name='dailypersonnelstatus',
            name='status',
            field=models.CharField(choices=[('present', 'حاضر'), ('absent', 'غائب'), ('on_mission', 'في مهمة'), ('standby', 'احتياطي')], default='present', max_length=20, verbose_name='الحالة'),
        ),
        migrations.CreateModel(
            name='DailyIntervention',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('intervention_number', models.CharField(max_length=20, unique=True, verbose_name='رقم التدخل')),
                ('intervention_type', models.CharField(choices=[('medical', 'إجلاء صحي'), ('accident', 'حادث مرور'), ('fire', 'حريق'), ('agricultural-fire', 'حريق محاصيل زراعية'), ('building-fire', 'حرائق البنايات والمؤسسات'), ('other', 'عمليات مختلفة')], max_length=30, verbose_name='نوع التدخل')),
                ('status', models.CharField(choices=[('initial_report', 'بلاغ أولي'), ('reconnaissance', 'قيد التعرف'), ('intervention', 'عملية تدخل'), ('completed', 'منتهية'), ('escalated', 'مصعدة لكارثة كبرى')], default='initial_report', max_length=20, verbose_name='الحالة')),
                ('departure_time', models.TimeField(verbose_name='ساعة الخروج')),
                ('location', models.CharField(max_length=200, verbose_name='مكان الحادث')),
                ('contact_source', models.CharField(choices=[('citizen', 'مواطن'), ('police', 'الشرطة'), ('gendarmerie', 'الدرك الوطني'), ('army', 'الجيش الوطني الشعبي'), ('forest', 'مصالح الغابات'), ('customs', 'الجمارك'), ('local-authorities', 'السلطات المحلية'), ('other', 'أخرى')], max_length=30, verbose_name='الجهة المتصلة')),
                ('contact_type', models.CharField(choices=[('phone', 'هاتفي'), ('radio', 'راديو'), ('direct', 'مباشر'), ('unit-support', 'وحدة تطلب الدعم')], max_length=20, verbose_name='نوع الاتصال')),
                ('phone_number', models.CharField(blank=True, max_length=15, null=True, verbose_name='رقم الهاتف')),
                ('caller_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='اسم المتصل')),
                ('initial_notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات أولية')),
                ('arrival_time', models.TimeField(blank=True, null=True, verbose_name='ساعة الوصول')),
                ('location_type', models.CharField(blank=True, max_length=20, null=True, verbose_name='نوع الموقع')),
                ('injured_count', models.IntegerField(default=0, verbose_name='عدد المسعفين')),
                ('deaths_count', models.IntegerField(default=0, verbose_name='عدد الوفيات')),
                ('material_damage', models.TextField(blank=True, null=True, verbose_name='الخسائر المادية')),
                ('end_time', models.TimeField(blank=True, null=True, verbose_name='ساعة الانتهاء')),
                ('total_duration', models.CharField(blank=True, max_length=20, null=True, verbose_name='مدة التدخل')),
                ('final_injured_count', models.IntegerField(default=0, verbose_name='العدد النهائي للمسعفين')),
                ('final_deaths_count', models.IntegerField(default=0, verbose_name='العدد النهائي للوفيات')),
                ('date', models.DateField(auto_now_add=True, verbose_name='التاريخ')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='home.interventionunit', verbose_name='الوحدة')),
            ],
            options={
                'verbose_name': 'تدخل يومي',
                'verbose_name_plural': 'التدخلات اليومية',
                'ordering': ['-date', '-departure_time'],
            },
        ),
        migrations.CreateModel(
            name='WorkingHours',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('work_system', models.CharField(choices=[('24_hours', 'نظام 24 ساعة'), ('8_hours', 'نظام 8 ساعات')], max_length=20, verbose_name='نظام العمل')),
                ('start_time', models.TimeField(verbose_name='وقت البداية')),
                ('end_time', models.TimeField(verbose_name='وقت النهاية')),
                ('work_days', models.CharField(choices=[('all_week', 'جميع أيام الأسبوع'), ('sunday_to_thursday', 'الأحد إلى الخميس'), ('custom', 'مخصص')], default='all_week', max_length=20, verbose_name='أيام العمل')),
                ('is_overnight', models.BooleanField(default=False, verbose_name='عمل ليلي')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='home.interventionunit', verbose_name='الوحدة')),
            ],
            options={
                'verbose_name': 'ساعات العمل',
                'verbose_name_plural': 'ساعات العمل',
            },
        ),
        migrations.CreateModel(
            name='InterventionVehicle',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_primary', models.BooleanField(default=True, verbose_name='وسيلة أساسية')),
                ('assigned_at', models.DateTimeField(auto_now_add=True, verbose_name='وقت التعيين')),
                ('intervention', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='vehicles', to='home.dailyintervention', verbose_name='التدخل')),
                ('vehicle', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='home.unitequipment', verbose_name='الوسيلة')),
            ],
            options={
                'verbose_name': 'وسيلة التدخل',
                'verbose_name_plural': 'وسائل التدخل',
                'unique_together': {('intervention', 'vehicle')},
            },
        ),
        migrations.CreateModel(
            name='MonthlySchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.IntegerField(verbose_name='السنة')),
                ('month', models.IntegerField(verbose_name='الشهر')),
                ('schedule_data', models.JSONField(verbose_name='بيانات الجدولة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشأ بواسطة')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='home.interventionunit', verbose_name='الوحدة')),
            ],
            options={
                'verbose_name': 'جدولة شهرية',
                'verbose_name_plural': 'الجدولة الشهرية',
                'unique_together': {('unit', 'year', 'month')},
            },
        ),
        migrations.CreateModel(
            name='VehicleInterventionStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('available', 'متاحة'), ('in_intervention', 'في تدخل'), ('returning', 'في طريق العودة'), ('maintenance', 'صيانة'), ('out_of_service', 'خارج الخدمة')], default='available', max_length=20, verbose_name='الحالة')),
                ('date', models.DateField(auto_now_add=True, verbose_name='التاريخ')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='آخر تحديث')),
                ('current_intervention', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='home.dailyintervention', verbose_name='التدخل الحالي')),
                ('vehicle', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='intervention_status', to='home.unitequipment', verbose_name='الوسيلة')),
            ],
            options={
                'verbose_name': 'حالة الوسيلة في التدخل',
                'verbose_name_plural': 'حالات الوسائل في التدخل',
                'unique_together': {('vehicle', 'date')},
            },
        ),
    ]
