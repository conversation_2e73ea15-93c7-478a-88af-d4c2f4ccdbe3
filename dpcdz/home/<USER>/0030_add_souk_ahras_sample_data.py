# Generated by Django 4.2.7 on 2025-07-19 18:45

from django.db import migrations
from django.contrib.auth.models import User
from datetime import date, datetime
import random

def add_souk_ahras_sample_data(apps, schema_editor):
    """إضافة بيانات افتراضية شاملة لولاية سوق أهراس"""

    # استيراد النماذج
    InterventionUnit = apps.get_model('home', 'InterventionUnit')
    UnitPersonnel = apps.get_model('home', 'UnitPersonnel')
    UnitEquipment = apps.get_model('home', 'UnitEquipment')
    User = apps.get_model('auth', 'User')

    # الحصول على مستخدم افتراضي
    try:
        admin_user = User.objects.get(username='admin')
    except User.DoesNotExist:
        admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            is_staff=True,
            is_superuser=True
        )
    
    # أسماء جزائرية أصيلة من منطقة سوق أهراس
    first_names = [
        'عبد الرزاق', 'محمد', 'أحمد', 'عبد الله', 'عمر', 'علي', 'حسن', 'حسين',
        'يوسف', 'إبراهيم', 'عبد الرحمن', 'خالد', 'سعيد', 'عبد الكريم', 'مصطفى',
        'عبد العزيز', 'نور الدين', 'صلاح الدين', 'عبد الحميد', 'عبد المجيد',
        'بلال', 'طارق', 'عماد', 'رضا', 'فريد', 'كمال', 'جمال', 'رشيد',
        'عبد الوهاب', 'عبد الباسط', 'عبد الناصر', 'عبد الحليم', 'زكريا',
        'إسماعيل', 'عثمان', 'عبد الصمد', 'عبد الغني', 'عبد الحق', 'رمزي',
        'فتحي', 'مراد', 'هشام', 'وليد', 'سمير', 'عادل', 'نبيل', 'فاروق',
        'عبد السلام', 'عبد الجليل', 'عبد الفتاح', 'عبد الستار', 'بشير',
        'منير', 'أمين', 'ياسين', 'حكيم', 'كريم', 'رحيم', 'سليم', 'حليم'
    ]

    # أسماء عائلات من منطقة سوق أهراس والمناطق المجاورة
    last_names = [
        'مختاري', 'بوعزة', 'بن علي', 'حمدي', 'العربي', 'زروقي', 'قاسمي',
        'بلعباس', 'شريف', 'نوري', 'حجازي', 'سليماني', 'بوضياف', 'مرزوقي',
        'عثماني', 'جزائري', 'تبسي', 'سوقي', 'أهراسي', 'سدراتي', 'مداوروشي',
        'مشروحي', 'بوحوشي', 'عضايمي', 'مراهني', 'حدادي', 'إدريسي', 'زاني',
        'برالي', 'طريقي', 'صالحي', 'متقدمي', 'رئيسي', 'ثانوي', 'وحدوي',
        'حمايدي', 'عمراني', 'ريفي', 'جبلي', 'سهلي', 'واديي', 'تلي', 'صحراوي',
        'شرقي', 'غربي', 'شمالي', 'جنوبي', 'وسطي', 'داخلي', 'خارجي', 'حدودي'
    ]
    
    # الرتب العسكرية مقسمة حسب الفئات
    officer_ranks = ['عقيد', 'مقدم', 'رائد', 'نقيب', 'ملازم أول', 'ملازم']
    nco_ranks = ['رقيب أول', 'رقيب', 'عريف أول', 'عريف']
    soldier_ranks = ['جندي أول', 'جندي']

    # المناصب مقسمة حسب الرتب
    officer_positions = [
        'قائد الوحدة', 'نائب قائد الوحدة', 'رئيس المصلحة', 'مسؤول العمليات',
        'مسؤول الأمن', 'مسؤول التدريب', 'مسؤول الصيانة', 'مسؤول الإمداد'
    ]

    nco_positions = [
        'رئيس عدد', 'مسؤول فرقة', 'مدرب', 'تقني صيانة', 'مشغل راديو',
        'مسؤول مخزن', 'مراقب', 'منسق'
    ]

    soldier_positions = [
        'سائق', 'عون إطفاء', 'عون إسعاف', 'عون إنقاذ', 'عون أمن',
        'كاتب', 'حارس', 'طباخ', 'عامل صيانة', 'مساعد'
    ]
    
    # أنواع الوسائل المستخدمة في الحماية المدنية
    vehicle_types = [
        'سيارة إسعاف Mercedes Sprinter', 'سيارة إسعاف Iveco Daily',
        'سيارة إسعاف Renault Master', 'سيارة إسعاف Ford Transit',
        'شاحنة إطفاء MAN 6000L', 'شاحنة إطفاء Iveco 4000L',
        'شاحنة إطفاء Mercedes 3000L', 'شاحنة إطفاء Renault 2500L',
        'فورجون إنقاذ Iveco', 'فورجون تدخل سريع Mercedes',
        'مركبة قيادة Toyota Land Cruiser', 'مركبة استطلاع Nissan Patrol',
        'دراجة نارية Honda', 'دراجة نارية Yamaha', 'مولد كهربائي 50KW',
        'مضخة مياه متنقلة', 'رافعة إنقاذ 25 طن', 'سيارة ورشة متنقلة'
    ]
    
    # الحصول على جميع وحدات سوق أهراس
    souk_ahras_units = InterventionUnit.objects.filter(wilaya='41')
    
    print(f"إضافة بيانات افتراضية لـ {souk_ahras_units.count()} وحدة في سوق أهراس...")
    
    for unit in souk_ahras_units:
        print(f"معالجة الوحدة: {unit.name}")
        
        # تحديد عدد الأعوان والوسائل حسب نوع الوحدة
        if 'الرئيسية' in unit.name:
            personnel_count = 45  # الوحدة الرئيسية - أكبر عدد
            vehicle_count = 5
            officer_ratio = 0.3  # 30% ضباط
        elif 'المتقدم' in unit.name:
            personnel_count = 35  # المراكز المتقدمة - متوسط
            vehicle_count = 4
            officer_ratio = 0.25  # 25% ضباط
        else:
            personnel_count = random.randint(40, 44)  # الوحدات الثانوية
            vehicle_count = random.randint(4, 5)
            officer_ratio = 0.2  # 20% ضباط
        
        # إضافة الأعوان
        for i in range(personnel_count):
            first_name = random.choice(first_names)
            last_name = random.choice(last_names)
            full_name = f"{first_name} {last_name}"
            
            # تجنب التكرار
            while UnitPersonnel.objects.filter(unit=unit, full_name=full_name).exists():
                first_name = random.choice(first_names)
                last_name = random.choice(last_names)
                full_name = f"{first_name} {last_name}"
            
            # تحديد الرتبة والمنصب بناءً على التوزيع
            if i < int(personnel_count * officer_ratio):
                # ضباط
                rank = random.choice(officer_ranks)
                position = random.choice(officer_positions)
                work_system = '8_hours'  # الضباط غالباً 8 ساعات
                assigned_shift = None
            elif i < int(personnel_count * (officer_ratio + 0.4)):
                # ضباط صف
                rank = random.choice(nco_ranks)
                position = random.choice(nco_positions)
                work_system = random.choice(['24_hours', '8_hours'])
                assigned_shift = random.choice(['shift_1', 'shift_2', 'shift_3']) if work_system == '24_hours' else None
            else:
                # جنود
                rank = random.choice(soldier_ranks)
                position = random.choice(soldier_positions)
                work_system = '24_hours'  # الجنود غالباً 24 ساعة
                assigned_shift = random.choice(['shift_1', 'shift_2', 'shift_3'])

            # إنشاء رقم تسجيل فريد
            registration_number = f"SA{unit.id:02d}{i+1:03d}"

            # تواريخ عشوائية واقعية حسب الرتبة
            if rank in officer_ranks:
                birth_year = random.randint(1970, 1990)  # ضباط أكبر سناً
                joining_year = random.randint(max(1995, birth_year + 22), 2020)
            elif rank in nco_ranks:
                birth_year = random.randint(1975, 1995)  # ضباط صف متوسطي العمر
                joining_year = random.randint(max(1998, birth_year + 20), 2022)
            else:
                birth_year = random.randint(1980, 2000)  # جنود أصغر سناً
                joining_year = random.randint(max(2000, birth_year + 18), 2024)

            birth_date = date(birth_year, random.randint(1, 12), random.randint(1, 28))
            joining_date = date(joining_year, random.randint(1, 12), random.randint(1, 28))
            
            personnel = UnitPersonnel.objects.create(
                unit=unit,
                registration_number=registration_number,
                first_name=first_name,
                last_name=last_name,
                full_name=full_name,
                rank=rank,
                position=position,
                birth_date=birth_date,
                joining_date=joining_date,
                gender=random.choice(['male', 'female']) if random.random() < 0.15 else 'male',  # 15% إناث
                phone_number=f"0{random.randint(5,7)}{random.randint(10000000, 99999999)}",
                work_system=work_system,
                assigned_shift=assigned_shift,
                is_active=True,
                created_by=admin_user
            )
            
            print(f"  ✓ أضيف العون: {full_name} ({registration_number})")
        
        # إضافة الوسائل
        for i in range(vehicle_count):
            vehicle_type = random.choice(vehicle_types)
            serial_number = f"SA{unit.id:02d}-{vehicle_type[:3].upper()}-{i+1:02d}"
            radio_number = f"R{unit.id:02d}{i+1:02d}"
            
            equipment = UnitEquipment.objects.create(
                unit=unit,
                serial_number=serial_number,
                equipment_type=vehicle_type,
                radio_number=radio_number,
                is_active=True,
                created_by=admin_user
            )
            
            print(f"  ✓ أضيفت الوسيلة: {vehicle_type} ({serial_number})")
    
    print("✅ تم إنجاز إضافة البيانات الافتراضية لولاية سوق أهراس بنجاح!")

def remove_souk_ahras_sample_data(apps, schema_editor):
    """إزالة البيانات الافتراضية"""
    UnitPersonnel = apps.get_model('home', 'UnitPersonnel')
    UnitEquipment = apps.get_model('home', 'UnitEquipment')
    InterventionUnit = apps.get_model('home', 'InterventionUnit')
    
    # إزالة الأعوان والوسائل من وحدات سوق أهراس
    souk_ahras_units = InterventionUnit.objects.filter(wilaya='41')
    
    for unit in souk_ahras_units:
        UnitPersonnel.objects.filter(unit=unit).delete()
        UnitEquipment.objects.filter(unit=unit).delete()
    
    print("تم حذف البيانات الافتراضية لولاية سوق أهراس")

class Migration(migrations.Migration):
    dependencies = [
        ('home', '0028_add_shift_schedule'),
    ]

    operations = [
        migrations.RunPython(add_souk_ahras_sample_data, remove_souk_ahras_sample_data),
    ]
