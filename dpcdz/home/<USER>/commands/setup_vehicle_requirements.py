from django.core.management.base import BaseCommand
from home.models import VehicleRequirements


class Command(BaseCommand):
    help = 'إنشاء متطلبات الوسائل الأساسية'

    def handle(self, *args, **options):
        # متطلبات الوسائل المختلفة
        vehicle_requirements = [
            {
                'vehicle_type': 'شاحنة إطفاء FPT',
                'required_driver_count': 1,
                'required_crew_chief_count': 1,
                'required_agent_count': 3,
                'minimum_agent_count': 2
            },
            {
                'vehicle_type': 'سيارة إسعاف',
                'required_driver_count': 1,
                'required_crew_chief_count': 1,
                'required_agent_count': 2,
                'minimum_agent_count': 1
            },
            {
                'vehicle_type': 'سيارة خفيفة',
                'required_driver_count': 1,
                'required_crew_chief_count': 1,
                'required_agent_count': 2,
                'minimum_agent_count': 1
            },
            {
                'vehicle_type': 'شاحنة مياه',
                'required_driver_count': 1,
                'required_crew_chief_count': 1,
                'required_agent_count': 2,
                'minimum_agent_count': 1
            },
            {
                'vehicle_type': 'مولد كهربائي',
                'required_driver_count': 1,
                'required_crew_chief_count': 0,
                'required_agent_count': 1,
                'minimum_agent_count': 1
            },
            {
                'vehicle_type': 'سلم آلي',
                'required_driver_count': 1,
                'required_crew_chief_count': 1,
                'required_agent_count': 2,
                'minimum_agent_count': 1
            },
            {
                'vehicle_type': 'شاحنة إنقاذ',
                'required_driver_count': 1,
                'required_crew_chief_count': 1,
                'required_agent_count': 4,
                'minimum_agent_count': 2
            },
            {
                'vehicle_type': 'دراجة نارية',
                'required_driver_count': 1,
                'required_crew_chief_count': 0,
                'required_agent_count': 0,
                'minimum_agent_count': 0
            }
        ]

        created_count = 0
        updated_count = 0

        for req_data in vehicle_requirements:
            requirement, created = VehicleRequirements.objects.get_or_create(
                vehicle_type=req_data['vehicle_type'],
                defaults={
                    'required_driver_count': req_data['required_driver_count'],
                    'required_crew_chief_count': req_data['required_crew_chief_count'],
                    'required_agent_count': req_data['required_agent_count'],
                    'minimum_agent_count': req_data['minimum_agent_count']
                }
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'تم إنشاء متطلبات: {req_data["vehicle_type"]}')
                )
            else:
                # تحديث البيانات الموجودة
                requirement.required_driver_count = req_data['required_driver_count']
                requirement.required_crew_chief_count = req_data['required_crew_chief_count']
                requirement.required_agent_count = req_data['required_agent_count']
                requirement.minimum_agent_count = req_data['minimum_agent_count']
                requirement.save()
                updated_count += 1
                self.stdout.write(
                    self.style.WARNING(f'تم تحديث متطلبات: {req_data["vehicle_type"]}')
                )

        self.stdout.write(
            self.style.SUCCESS(
                f'تم الانتهاء! تم إنشاء {created_count} متطلبات جديدة وتحديث {updated_count} متطلبات موجودة.'
            )
        )
