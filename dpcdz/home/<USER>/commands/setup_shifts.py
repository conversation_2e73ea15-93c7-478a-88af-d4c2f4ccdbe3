from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from home.models import InterventionUnit, WorkShift, ShiftPersonnel, UnitPersonnel
from datetime import date, timedelta


class Command(BaseCommand):
    help = 'إعداد نظام الفرق (24/48 ساعة) للوحدات'

    def add_arguments(self, parser):
        parser.add_argument(
            '--unit-id',
            type=int,
            help='معرف الوحدة المحددة (اختياري - إذا لم يتم تحديده سيتم إعداد جميع الوحدات)'
        )
        parser.add_argument(
            '--reset',
            action='store_true',
            help='إعادة تعيين الفرق الموجودة'
        )

    def handle(self, *args, **options):
        unit_id = options.get('unit_id')
        reset = options.get('reset')

        # تحديد الوحدات المراد إعدادها
        if unit_id:
            try:
                units = [InterventionUnit.objects.get(id=unit_id)]
                self.stdout.write(f'إعداد الفرق للوحدة: {units[0].name}')
            except InterventionUnit.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'الوحدة بالمعرف {unit_id} غير موجودة')
                )
                return
        else:
            units = InterventionUnit.objects.all()
            self.stdout.write(f'إعداد الفرق لجميع الوحدات ({units.count()} وحدة)')

        # الحصول على مستخدم النظام
        try:
            system_user = User.objects.filter(is_superuser=True).first()
            if not system_user:
                system_user = User.objects.create_user(
                    username='system',
                    email='<EMAIL>',
                    password='system123',
                    is_staff=True,
                    is_superuser=True
                )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'خطأ في إنشاء مستخدم النظام: {str(e)}')
            )
            return

        total_created = 0
        total_updated = 0

        for unit in units:
            self.stdout.write(f'\n--- إعداد الفرق للوحدة: {unit.name} ---')
            
            # إعادة تعيين الفرق إذا طُلب ذلك
            if reset:
                existing_shifts = WorkShift.objects.filter(unit=unit)
                deleted_count = existing_shifts.count()
                existing_shifts.delete()
                self.stdout.write(f'تم حذف {deleted_count} فرقة موجودة')

            # إنشاء الفرق الثلاث (A, B, C) لنظام 24/48 ساعة
            shift_names = ['A', 'B', 'C']
            
            for shift_name in shift_names:
                shift, created = WorkShift.objects.get_or_create(
                    unit=unit,
                    name=shift_name,
                    defaults={
                        'shift_type': '24_48',
                        'is_active': True,
                        'created_by': system_user
                    }
                )
                
                if created:
                    total_created += 1
                    self.stdout.write(
                        self.style.SUCCESS(f'✓ تم إنشاء فصيلة {shift_name}')
                    )
                else:
                    total_updated += 1
                    self.stdout.write(f'○ فصيلة {shift_name} موجودة مسبقاً')

            # إنشاء فرق نظام 8 ساعات للأعوان الإداريين
            eight_hour_shifts = ['morning', 'evening', 'night']
            
            for shift_name in eight_hour_shifts:
                shift, created = WorkShift.objects.get_or_create(
                    unit=unit,
                    name=shift_name,
                    defaults={
                        'shift_type': '8_hours',
                        'is_active': True,
                        'created_by': system_user
                    }
                )
                
                if created:
                    total_created += 1
                    self.stdout.write(
                        self.style.SUCCESS(f'✓ تم إنشاء فترة {shift.get_name_display()}')
                    )
                else:
                    total_updated += 1
                    self.stdout.write(f'○ فترة {shift.get_name_display()} موجودة مسبقاً')

            # توزيع الأعوان على الفرق تلقائياً (إذا لم يكونوا موزعين)
            self.auto_assign_personnel_to_shifts(unit, system_user)

        self.stdout.write(
            self.style.SUCCESS(
                f'\n✅ تم الانتهاء من إعداد الفرق'
                f'\n📊 الإحصائيات:'
                f'\n   - فرق جديدة: {total_created}'
                f'\n   - فرق موجودة: {total_updated}'
            )
        )

    def auto_assign_personnel_to_shifts(self, unit, system_user):
        """توزيع الأعوان على الفرق تلقائياً"""
        self.stdout.write(f'\n🔄 توزيع الأعوان على الفرق...')
        
        # الحصول على أعوان الوحدة النشطين
        personnel = UnitPersonnel.objects.filter(unit=unit, is_active=True)
        
        if not personnel.exists():
            self.stdout.write('⚠️ لا توجد أعوان نشطين في هذه الوحدة')
            return

        # الحصول على فرق نظام 24/48 ساعة
        shifts_24_48 = WorkShift.objects.filter(
            unit=unit, 
            shift_type='24_48', 
            is_active=True
        ).order_by('name')

        if not shifts_24_48.exists():
            self.stdout.write('⚠️ لا توجد فرق نظام 24/48 ساعة')
            return

        # توزيع الأعوان بالتساوي على الفرق
        personnel_list = list(personnel)
        shift_count = shifts_24_48.count()
        
        assigned_count = 0
        
        for i, person in enumerate(personnel_list):
            # تحديد الفرقة بناءً على الفهرس
            shift_index = i % shift_count
            target_shift = shifts_24_48[shift_index]
            
            # التحقق من عدم وجود تعيين مسبق
            existing_assignment = ShiftPersonnel.objects.filter(
                personnel=person,
                shift__unit=unit,
                shift__shift_type='24_48',
                is_active=True
            ).first()
            
            if not existing_assignment:
                ShiftPersonnel.objects.create(
                    shift=target_shift,
                    personnel=person,
                    is_active=True,
                    assigned_by=system_user
                )
                assigned_count += 1
                self.stdout.write(
                    f'✓ تم تعيين {person.full_name} إلى فصيلة {target_shift.get_name_display()}'
                )
            else:
                self.stdout.write(
                    f'○ {person.full_name} معين مسبقاً إلى فصيلة {existing_assignment.shift.get_name_display()}'
                )

        self.stdout.write(
            self.style.SUCCESS(f'✅ تم تعيين {assigned_count} عون جديد')
        )

    def get_next_active_shift(self, unit, current_date):
        """تحديد الفرقة العاملة التالية بناءً على نظام التناوب"""
        from home.models import DailyShiftSchedule
        
        # البحث عن آخر جدولة
        last_schedule = DailyShiftSchedule.objects.filter(
            unit=unit
        ).order_by('-date').first()
        
        # الحصول على فرق نظام 24/48 ساعة
        shifts = WorkShift.objects.filter(
            unit=unit,
            shift_type='24_48',
            is_active=True
        ).order_by('name')
        
        if not shifts.exists():
            return None
            
        if not last_schedule:
            # إذا لم توجد جدولة سابقة، ابدأ بالفصيلة A
            return shifts.filter(name='A').first()
        
        # تحديد الفرقة التالية في التناوب
        current_shift = last_schedule.active_shift
        shift_names = ['A', 'B', 'C']
        
        try:
            current_index = shift_names.index(current_shift.name)
            next_index = (current_index + 1) % len(shift_names)
            next_shift_name = shift_names[next_index]
            
            return shifts.filter(name=next_shift_name).first()
        except (ValueError, AttributeError):
            # في حالة حدوث خطأ، ارجع للفصيلة A
            return shifts.filter(name='A').first()
