from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from home.models import UserProfile

class Command(BaseCommand):
    help = 'Sets up the admin user profile with proper role and wilaya'

    def handle(self, *args, **kwargs):
        try:
            admin = User.objects.get(username='admin')
            profile, created = UserProfile.objects.get_or_create(
                user=admin,
                defaults={
                    'role': 'admin',
                    'wilaya': '41'  # <PERSON>uk <PERSON>
                }
            )
            if not created:
                profile.role = 'admin'
                profile.wilaya = '41'
                profile.save()
            
            self.stdout.write(self.style.SUCCESS('Successfully set up admin profile'))
            
        except User.DoesNotExist:
            self.stdout.write(self.style.ERROR('Admin user does not exist'))
