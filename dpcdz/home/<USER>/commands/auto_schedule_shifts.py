from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from home.models import InterventionUnit, WorkShift, DailyShiftSchedule
from datetime import date, timedelta


class Command(BaseCommand):
    help = 'جدولة تلقائية للفرق العاملة (نظام 24/48 ساعة)'

    def add_arguments(self, parser):
        parser.add_argument(
            '--unit-id',
            type=int,
            help='معرف الوحدة المحددة (اختياري)'
        )
        parser.add_argument(
            '--date',
            type=str,
            help='التاريخ المحدد (YYYY-MM-DD) - افتراضياً اليوم'
        )
        parser.add_argument(
            '--days',
            type=int,
            default=1,
            help='عدد الأيام للجدولة (افتراضياً يوم واحد)'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='إعادة كتابة الجدولة الموجودة'
        )

    def handle(self, *args, **options):
        unit_id = options.get('unit_id')
        target_date = options.get('date')
        days_count = options.get('days', 1)
        force = options.get('force', False)

        # تحديد التاريخ المستهدف
        if target_date:
            try:
                start_date = date.fromisoformat(target_date)
            except ValueError:
                self.stdout.write(
                    self.style.ERROR('تنسيق التاريخ غير صحيح. استخدم YYYY-MM-DD')
                )
                return
        else:
            start_date = date.today()

        # تحديد الوحدات
        if unit_id:
            try:
                units = [InterventionUnit.objects.get(id=unit_id)]
            except InterventionUnit.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'الوحدة بالمعرف {unit_id} غير موجودة')
                )
                return
        else:
            units = InterventionUnit.objects.all()

        # الحصول على مستخدم النظام
        system_user = User.objects.filter(is_superuser=True).first()
        if not system_user:
            self.stdout.write(
                self.style.ERROR('لا يوجد مستخدم نظام متاح')
            )
            return

        self.stdout.write(
            f'🗓️ جدولة الفرق من {start_date} لمدة {days_count} أيام'
        )
        self.stdout.write(f'🏢 الوحدات: {units.count()}')

        total_scheduled = 0
        total_skipped = 0
        total_errors = 0

        for unit in units:
            self.stdout.write(f'\n--- جدولة الفرق للوحدة: {unit.name} ---')
            
            # التحقق من وجود فرق نظام 24/48 ساعة
            available_shifts = WorkShift.objects.filter(
                unit=unit,
                shift_type='24_48',
                is_active=True
            ).order_by('name')

            if not available_shifts.exists():
                self.stdout.write(
                    self.style.WARNING(f'⚠️ لا توجد فرق نظام 24/48 ساعة للوحدة {unit.name}')
                )
                total_errors += 1
                continue

            # جدولة الأيام المطلوبة
            for day_offset in range(days_count):
                current_date = start_date + timedelta(days=day_offset)
                
                # التحقق من وجود جدولة مسبقة
                existing_schedule = DailyShiftSchedule.objects.filter(
                    unit=unit,
                    date=current_date
                ).first()

                if existing_schedule and not force:
                    self.stdout.write(
                        f'○ {current_date}: فرقة {existing_schedule.active_shift.get_name_display()} (موجودة مسبقاً)'
                    )
                    total_skipped += 1
                    continue

                # تحديد الفرقة العاملة
                active_shift = self.determine_active_shift(unit, current_date, available_shifts)
                
                if not active_shift:
                    self.stdout.write(
                        self.style.ERROR(f'❌ {current_date}: فشل في تحديد الفرقة العاملة')
                    )
                    total_errors += 1
                    continue

                # إنشاء أو تحديث الجدولة
                schedule, created = DailyShiftSchedule.objects.update_or_create(
                    unit=unit,
                    date=current_date,
                    defaults={
                        'active_shift': active_shift,
                        'created_by': system_user
                    }
                )

                action = 'تم إنشاء' if created else 'تم تحديث'
                self.stdout.write(
                    self.style.SUCCESS(
                        f'✓ {current_date}: فرقة {active_shift.get_name_display()} ({action})'
                    )
                )
                total_scheduled += 1

        # ملخص النتائج
        self.stdout.write(
            self.style.SUCCESS(
                f'\n✅ انتهت عملية الجدولة'
                f'\n📊 الإحصائيات:'
                f'\n   - تم الجدولة: {total_scheduled}'
                f'\n   - تم التخطي: {total_skipped}'
                f'\n   - أخطاء: {total_errors}'
            )
        )

    def determine_active_shift(self, unit, target_date, available_shifts):
        """تحديد الفرقة العاملة بناءً على نظام التناوب"""
        
        # البحث عن آخر جدولة قبل التاريخ المستهدف
        last_schedule = DailyShiftSchedule.objects.filter(
            unit=unit,
            date__lt=target_date
        ).order_by('-date').first()

        # إذا لم توجد جدولة سابقة، ابدأ بالفصيلة A
        if not last_schedule:
            return available_shifts.filter(name='A').first()

        # تحديد الفرقة التالية في التناوب
        current_shift = last_schedule.active_shift
        
        # حساب عدد الأيام منذ آخر جدولة
        days_diff = (target_date - last_schedule.date).days
        
        # نظام 24/48 ساعة: كل فرقة تعمل يوم واحد ثم تستريح يومين
        shift_names = ['A', 'B', 'C']
        
        try:
            current_index = shift_names.index(current_shift.name)
            
            # حساب الفرقة التالية بناءً على عدد الأيام
            next_index = (current_index + days_diff) % len(shift_names)
            next_shift_name = shift_names[next_index]
            
            return available_shifts.filter(name=next_shift_name).first()
            
        except (ValueError, AttributeError):
            # في حالة حدوث خطأ، ارجع للفصيلة A
            return available_shifts.filter(name='A').first()

    def get_shift_rotation_pattern(self):
        """الحصول على نمط تناوب الفرق"""
        # نظام 24/48 ساعة:
        # اليوم 1: فصيلة A
        # اليوم 2: فصيلة B  
        # اليوم 3: فصيلة C
        # اليوم 4: فصيلة A (تكرار)
        return ['A', 'B', 'C']

    def validate_shift_schedule(self, unit, start_date, end_date):
        """التحقق من صحة جدولة الفرق"""
        schedules = DailyShiftSchedule.objects.filter(
            unit=unit,
            date__range=[start_date, end_date]
        ).order_by('date')

        issues = []
        
        for i, schedule in enumerate(schedules):
            # التحقق من عدم وجود فجوات في الجدولة
            if i > 0:
                prev_schedule = schedules[i-1]
                expected_date = prev_schedule.date + timedelta(days=1)
                
                if schedule.date != expected_date:
                    issues.append(f'فجوة في الجدولة بين {prev_schedule.date} و {schedule.date}')

            # التحقق من صحة نمط التناوب
            expected_shift = self.get_expected_shift_for_date(unit, schedule.date)
            if expected_shift and schedule.active_shift.name != expected_shift:
                issues.append(
                    f'{schedule.date}: متوقع فصيلة {expected_shift} لكن مجدولة فصيلة {schedule.active_shift.name}'
                )

        return issues

    def get_expected_shift_for_date(self, unit, target_date):
        """الحصول على الفرقة المتوقعة لتاريخ معين"""
        # البحث عن نقطة مرجعية (أول جدولة)
        first_schedule = DailyShiftSchedule.objects.filter(
            unit=unit
        ).order_by('date').first()

        if not first_schedule:
            return None

        # حساب عدد الأيام منذ النقطة المرجعية
        days_diff = (target_date - first_schedule.date).days
        
        # تحديد الفرقة بناءً على نمط التناوب
        shift_pattern = self.get_shift_rotation_pattern()
        first_shift_index = shift_pattern.index(first_schedule.active_shift.name)
        
        expected_index = (first_shift_index + days_diff) % len(shift_pattern)
        return shift_pattern[expected_index]
