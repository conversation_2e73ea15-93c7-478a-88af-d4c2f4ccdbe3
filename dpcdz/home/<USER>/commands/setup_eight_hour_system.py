from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from home.models import InterventionUnit, UnitPersonnel, EightHourPersonnel
from datetime import date, time, timedelta


class Command(BaseCommand):
    help = 'إعداد نظام 8 ساعات للأعوان الإداريين والدعم'

    def add_arguments(self, parser):
        parser.add_argument(
            '--unit-id',
            type=int,
            help='معرف الوحدة المحددة (اختياري)'
        )
        parser.add_argument(
            '--date',
            type=str,
            help='التاريخ المحدد (YYYY-MM-DD) - افتراضياً اليوم'
        )
        parser.add_argument(
            '--days',
            type=int,
            default=7,
            help='عدد الأيام للإعداد (افتراضياً أسبوع)'
        )
        parser.add_argument(
            '--reset',
            action='store_true',
            help='إعادة تعيين البيانات الموجودة'
        )

    def handle(self, *args, **options):
        unit_id = options.get('unit_id')
        target_date = options.get('date')
        days_count = options.get('days', 7)
        reset = options.get('reset', False)

        # تحديد التاريخ المستهدف
        if target_date:
            try:
                start_date = date.fromisoformat(target_date)
            except ValueError:
                self.stdout.write(
                    self.style.ERROR('تنسيق التاريخ غير صحيح. استخدم YYYY-MM-DD')
                )
                return
        else:
            start_date = date.today()

        # تحديد الوحدات
        if unit_id:
            try:
                units = [InterventionUnit.objects.get(id=unit_id)]
            except InterventionUnit.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'الوحدة بالمعرف {unit_id} غير موجودة')
                )
                return
        else:
            units = InterventionUnit.objects.all()

        # الحصول على مستخدم النظام
        system_user = User.objects.filter(is_superuser=True).first()
        if not system_user:
            self.stdout.write(
                self.style.ERROR('لا يوجد مستخدم نظام متاح')
            )
            return

        self.stdout.write(
            f'⏰ إعداد نظام 8 ساعات من {start_date} لمدة {days_count} أيام'
        )
        self.stdout.write(f'🏢 الوحدات: {len(units)}')

        total_created = 0
        total_skipped = 0
        total_errors = 0

        for unit in units:
            self.stdout.write(f'\n--- إعداد نظام 8 ساعات للوحدة: {unit.name} ---')
            
            # الحصول على الأعوان المناسبين لنظام 8 ساعات
            administrative_personnel = self.get_administrative_personnel(unit)
            
            if not administrative_personnel:
                self.stdout.write(
                    self.style.WARNING(f'⚠️ لا توجد أعوان إداريين للوحدة {unit.name}')
                )
                continue

            # إعادة تعيين البيانات إذا طُلب ذلك
            if reset:
                existing_records = EightHourPersonnel.objects.filter(
                    unit=unit,
                    date__range=[start_date, start_date + timedelta(days=days_count-1)]
                )
                deleted_count = existing_records.count()
                existing_records.delete()
                self.stdout.write(f'تم حذف {deleted_count} سجل موجود')

            # إعداد الجدولة للأيام المطلوبة
            for day_offset in range(days_count):
                current_date = start_date + timedelta(days=day_offset)
                
                day_created, day_skipped, day_errors = self.setup_day_schedule(
                    unit, current_date, administrative_personnel, system_user
                )
                
                total_created += day_created
                total_skipped += day_skipped
                total_errors += day_errors

        # ملخص النتائج
        self.stdout.write(
            self.style.SUCCESS(
                f'\n✅ انتهت عملية إعداد نظام 8 ساعات'
                f'\n📊 الإحصائيات:'
                f'\n   - تم الإنشاء: {total_created}'
                f'\n   - تم التخطي: {total_skipped}'
                f'\n   - أخطاء: {total_errors}'
            )
        )

    def get_administrative_personnel(self, unit):
        """الحصول على الأعوان المناسبين لنظام 8 ساعات"""
        # يمكن تخصيص هذه الدالة بناءً على معايير محددة
        # مثل المنصب أو نوع العمل
        
        administrative_positions = [
            'إداري',
            'سكرتير',
            'محاسب',
            'مخزني',
            'حارس',
            'تقني',
            'سائق إداري',
            'عون صيانة',
            'عون اتصالات'
        ]
        
        personnel = UnitPersonnel.objects.filter(
            unit=unit,
            is_active=True
        )
        
        # فلترة بناءً على المنصب إذا كان متاحاً
        administrative_personnel = []
        for person in personnel:
            if person.position and any(pos in person.position for pos in administrative_positions):
                administrative_personnel.append(person)
        
        # إذا لم نجد أعوان إداريين محددين، نأخذ عينة من الأعوان العاديين
        if not administrative_personnel and personnel.exists():
            # نأخذ 20% من الأعوان كأعوان إداريين
            admin_count = max(1, personnel.count() // 5)
            administrative_personnel = list(personnel[:admin_count])
        
        return administrative_personnel

    def setup_day_schedule(self, unit, target_date, personnel, system_user):
        """إعداد جدولة يوم واحد"""
        created = 0
        skipped = 0
        errors = 0
        
        # تحديد الفترات والمهام
        work_periods = [
            {
                'period': 'morning',
                'start_time': time(8, 0),
                'end_time': time(16, 0),
                'tasks': ['administrative', 'communications', 'storage']
            },
            {
                'period': 'evening', 
                'start_time': time(16, 0),
                'end_time': time(0, 0),
                'tasks': ['security', 'maintenance', 'support']
            },
            {
                'period': 'night',
                'start_time': time(0, 0),
                'end_time': time(8, 0),
                'tasks': ['security', 'communications', 'technical']
            }
        ]
        
        # توزيع الأعوان على الفترات
        personnel_per_period = max(1, len(personnel) // 3)
        
        for i, period_info in enumerate(work_periods):
            # تحديد الأعوان لهذه الفترة
            start_idx = i * personnel_per_period
            end_idx = start_idx + personnel_per_period
            
            # للفترة الأخيرة، نأخذ باقي الأعوان
            if i == len(work_periods) - 1:
                period_personnel = personnel[start_idx:]
            else:
                period_personnel = personnel[start_idx:end_idx]
            
            # إنشاء سجلات للأعوان في هذه الفترة
            for j, person in enumerate(period_personnel):
                # اختيار نوع المهمة
                task_type = period_info['tasks'][j % len(period_info['tasks'])]
                
                # التحقق من وجود سجل مسبق
                existing_record = EightHourPersonnel.objects.filter(
                    unit=unit,
                    personnel=person,
                    date=target_date,
                    work_period=period_info['period']
                ).first()
                
                if existing_record:
                    skipped += 1
                    continue
                
                try:
                    # إنشاء السجل
                    EightHourPersonnel.objects.create(
                        unit=unit,
                        personnel=person,
                        date=target_date,
                        work_period=period_info['period'],
                        task_type=task_type,
                        task_description=self.get_task_description(task_type),
                        start_time=period_info['start_time'],
                        end_time=period_info['end_time'],
                        is_present=True,
                        created_by=system_user
                    )
                    created += 1
                    
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f'خطأ في إنشاء سجل {person.full_name}: {str(e)}')
                    )
                    errors += 1
        
        if created > 0:
            self.stdout.write(
                self.style.SUCCESS(f'✓ {target_date}: تم إنشاء {created} سجل')
            )
        elif skipped > 0:
            self.stdout.write(f'○ {target_date}: تم تخطي {skipped} سجل موجود')
        
        return created, skipped, errors

    def get_task_description(self, task_type):
        """الحصول على وصف المهمة"""
        descriptions = {
            'administrative': 'أعمال إدارية ومكتبية',
            'maintenance': 'صيانة المعدات والوسائل',
            'communications': 'إدارة الاتصالات والراديو',
            'storage': 'إدارة المخزن والمواد',
            'security': 'حراسة وأمن المركز',
            'support': 'دعم العمليات والفرق',
            'technical': 'أعمال تقنية ومتخصصة'
        }
        return descriptions.get(task_type, 'مهام عامة')

    def generate_weekly_schedule(self, unit, start_date, personnel, system_user):
        """إنشاء جدولة أسبوعية متوازنة"""
        week_schedule = []
        
        for day_offset in range(7):
            current_date = start_date + timedelta(days=day_offset)
            day_name = current_date.strftime('%A')
            
            # تحديد نمط العمل بناءً على اليوم
            if day_name in ['Friday']:  # يوم الجمعة
                # فترة واحدة فقط (صباحية)
                periods = ['morning']
            elif day_name in ['Saturday']:  # يوم السبت
                # فترتان (صباحية ومسائية)
                periods = ['morning', 'evening']
            else:
                # أيام العمل العادية - ثلاث فترات
                periods = ['morning', 'evening', 'night']
            
            week_schedule.append({
                'date': current_date,
                'periods': periods,
                'day_name': day_name
            })
        
        return week_schedule
