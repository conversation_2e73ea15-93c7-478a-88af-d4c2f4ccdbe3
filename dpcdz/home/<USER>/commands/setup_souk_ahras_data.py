"""
أمر Django لإعداد البيانات الافتراضية لولاية سوق أهراس
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from home.models import InterventionUnit, UnitPersonnel, UnitEquipment
from datetime import date
import random

class Command(BaseCommand):
    help = 'إعداد البيانات الافتراضية لولاية سوق أهراس'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='حذف البيانات الموجودة قبل إضافة الجديدة',
        )
        parser.add_argument(
            '--unit-id',
            type=int,
            help='معرف وحدة محددة لإضافة البيانات لها فقط',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🚀 بدء إعداد البيانات الافتراضية لولاية سوق أهراس...')
        )

        # حذف البيانات الموجودة إذا طُلب ذلك
        if options['reset']:
            self.stdout.write('🗑️ حذف البيانات الموجودة...')
            if options['unit_id']:
                unit = InterventionUnit.objects.get(id=options['unit_id'])
                UnitPersonnel.objects.filter(unit=unit).delete()
                UnitEquipment.objects.filter(unit=unit).delete()
                self.stdout.write(f'✅ تم حذف بيانات الوحدة: {unit.name}')
            else:
                souk_ahras_units = InterventionUnit.objects.filter(wilaya='41')
                for unit in souk_ahras_units:
                    UnitPersonnel.objects.filter(unit=unit).delete()
                    UnitEquipment.objects.filter(unit=unit).delete()
                self.stdout.write('✅ تم حذف جميع البيانات لولاية سوق أهراس')

        # الحصول على مستخدم افتراضي
        try:
            admin_user = User.objects.get(username='admin')
        except User.DoesNotExist:
            admin_user = User.objects.create_user(
                username='admin',
                email='<EMAIL>',
                password='admin123',
                is_staff=True,
                is_superuser=True
            )
            self.stdout.write('👤 تم إنشاء مستخدم admin افتراضي')

        # الحصول على الوحدات
        if options['unit_id']:
            units = [InterventionUnit.objects.get(id=options['unit_id'])]
        else:
            units = InterventionUnit.objects.filter(wilaya='41')

        if not units:
            self.stdout.write(
                self.style.ERROR('❌ لم يتم العثور على وحدات في ولاية سوق أهراس')
            )
            return

        # إحصائيات
        total_personnel = 0
        total_vehicles = 0

        for unit in units:
            self.stdout.write(f'📍 معالجة الوحدة: {unit.name}')
            
            # تحديد عدد الأعوان والوسائل
            if 'الرئيسية' in unit.name:
                personnel_count = 45
                vehicle_count = 5
            elif 'المتقدم' in unit.name:
                personnel_count = 35
                vehicle_count = 4
            else:
                personnel_count = random.randint(40, 44)
                vehicle_count = random.randint(4, 5)

            # إضافة الأعوان
            personnel_added = self.add_personnel(unit, personnel_count, admin_user)
            total_personnel += personnel_added

            # إضافة الوسائل
            vehicles_added = self.add_vehicles(unit, vehicle_count, admin_user)
            total_vehicles += vehicles_added

            self.stdout.write(
                f'  ✅ أضيف {personnel_added} عون و {vehicles_added} وسيلة'
            )

        # النتيجة النهائية
        self.stdout.write(
            self.style.SUCCESS(
                f'🎉 تم الانتهاء! أضيف إجمالي {total_personnel} عون و {total_vehicles} وسيلة'
            )
        )

    def add_personnel(self, unit, count, admin_user):
        """إضافة الأعوان للوحدة"""
        # أسماء جزائرية
        first_names = [
            'عبد الرزاق', 'محمد', 'أحمد', 'عبد الله', 'عمر', 'علي', 'حسن',
            'يوسف', 'إبراهيم', 'خالد', 'سعيد', 'مصطفى', 'نور الدين', 'صلاح الدين',
            'بلال', 'طارق', 'عماد', 'رضا', 'فريد', 'كمال', 'جمال', 'رشيد'
        ]
        
        last_names = [
            'مختاري', 'بوعزة', 'بن علي', 'حمدي', 'العربي', 'زروقي', 'قاسمي',
            'بلعباس', 'شريف', 'نوري', 'حجازي', 'سليماني', 'بوضياف', 'مرزوقي'
        ]

        # الرتب والمناصب
        officer_ranks = ['عقيد', 'مقدم', 'رائد', 'نقيب', 'ملازم أول', 'ملازم']
        nco_ranks = ['رقيب أول', 'رقيب', 'عريف أول', 'عريف']
        soldier_ranks = ['جندي أول', 'جندي']

        officer_positions = ['قائد الوحدة', 'نائب قائد الوحدة', 'رئيس المصلحة', 'مسؤول العمليات']
        nco_positions = ['رئيس عدد', 'مسؤول فرقة', 'مدرب', 'تقني صيانة']
        soldier_positions = ['سائق', 'عون إطفاء', 'عون إسعاف', 'عون إنقاذ']

        added_count = 0
        for i in range(count):
            # تجنب التكرار في الأسماء
            attempts = 0
            while attempts < 10:
                first_name = random.choice(first_names)
                last_name = random.choice(last_names)
                full_name = f"{first_name} {last_name}"
                
                if not UnitPersonnel.objects.filter(unit=unit, full_name=full_name).exists():
                    break
                attempts += 1
            
            if attempts >= 10:
                continue  # تخطي إذا لم نجد اسم فريد

            # تحديد الرتبة والمنصب
            if i < count * 0.2:  # 20% ضباط
                rank = random.choice(officer_ranks)
                position = random.choice(officer_positions)
                work_system = '8_hours'
                assigned_shift = None
            elif i < count * 0.6:  # 40% ضباط صف
                rank = random.choice(nco_ranks)
                position = random.choice(nco_positions)
                work_system = random.choice(['24_hours', '8_hours'])
                assigned_shift = random.choice(['shift_1', 'shift_2', 'shift_3']) if work_system == '24_hours' else None
            else:  # 40% جنود
                rank = random.choice(soldier_ranks)
                position = random.choice(soldier_positions)
                work_system = '24_hours'
                assigned_shift = random.choice(['shift_1', 'shift_2', 'shift_3'])

            # إنشاء العون
            registration_number = f"SA{unit.id:02d}{i+1:03d}"
            birth_date = date(random.randint(1970, 2000), random.randint(1, 12), random.randint(1, 28))
            joining_date = date(random.randint(2000, 2024), random.randint(1, 12), random.randint(1, 28))

            try:
                UnitPersonnel.objects.create(
                    unit=unit,
                    registration_number=registration_number,
                    first_name=first_name,
                    last_name=last_name,
                    full_name=full_name,
                    rank=rank,
                    position=position,
                    birth_date=birth_date,
                    joining_date=joining_date,
                    gender='male' if random.random() > 0.15 else 'female',
                    phone_number=f"0{random.randint(5,7)}{random.randint(10000000, 99999999)}",
                    work_system=work_system,
                    assigned_shift=assigned_shift,
                    is_active=True,
                    created_by=admin_user
                )
                added_count += 1
            except Exception as e:
                self.stdout.write(f'⚠️ خطأ في إضافة العون {full_name}: {e}')

        return added_count

    def add_vehicles(self, unit, count, admin_user):
        """إضافة الوسائل للوحدة"""
        vehicle_types = [
            'سيارة إسعاف Mercedes Sprinter', 'سيارة إسعاف Iveco Daily',
            'شاحنة إطفاء MAN 6000L', 'شاحنة إطفاء Iveco 4000L',
            'فورجون إنقاذ Iveco', 'مركبة قيادة Toyota Land Cruiser'
        ]

        added_count = 0
        for i in range(count):
            vehicle_type = random.choice(vehicle_types)
            serial_number = f"SA{unit.id:02d}-{vehicle_type[:3].upper()}-{i+1:02d}"
            radio_number = f"R{unit.id:02d}{i+1:02d}"

            try:
                UnitEquipment.objects.create(
                    unit=unit,
                    serial_number=serial_number,
                    equipment_type=vehicle_type,
                    radio_number=radio_number,
                    is_active=True,
                    created_by=admin_user
                )
                added_count += 1
            except Exception as e:
                self.stdout.write(f'⚠️ خطأ في إضافة الوسيلة {vehicle_type}: {e}')

        return added_count
