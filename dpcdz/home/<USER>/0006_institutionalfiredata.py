# Generated by Django 5.2 on 2025-05-08 00:57

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0005_miscoperationsdata'),
    ]

    operations = [
        migrations.CreateModel(
            name='InstitutionalFireData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('intervening_unit', models.Char<PERSON>ield(max_length=100)),
                ('incident_type', models.Char<PERSON>ield(max_length=100)),
                ('operations_count', models.PositiveIntegerField()),
                ('activity_nature', models.CharField(max_length=200)),
                ('institution_name', models.CharField(max_length=200)),
                ('activity', models.CharField(max_length=200)),
                ('class_type', models.CharField(max_length=100)),
                ('year_pii', models.BooleanField(default=False)),
                ('year_edd', models.BooleanField(default=False)),
                ('zone_zr', models.BooleanField(default=False)),
                ('zone_zu', models.BooleanField(default=False)),
                ('zone_za', models.BooleanField(default=False)),
                ('zone_zi', models.BooleanField(default=False)),
                ('injured_count', models.PositiveIntegerField()),
                ('deaths_count', models.PositiveIntegerField()),
                ('ambulances', models.PositiveIntegerField()),
                ('fire_trucks', models.PositiveIntegerField()),
                ('mechanical_ladders', models.PositiveIntegerField()),
                ('other_resources', models.PositiveIntegerField()),
                ('intervention_duration', models.CharField(max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
