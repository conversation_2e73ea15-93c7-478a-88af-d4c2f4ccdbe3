# Generated by Django 5.2 on 2025-07-25 11:08

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0034_dailyintervention_intervention_subtype'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='interventioncasualty',
            options={'verbose_name': 'ضحية التدخل', 'verbose_name_plural': 'ضحايا التدخل'},
        ),
        migrations.AlterModelOptions(
            name='interventionreport',
            options={'verbose_name': 'تقرير التدخل', 'verbose_name_plural': 'تقارير التدخل'},
        ),
        migrations.RemoveField(
            model_name='interventioncasualty',
            name='generated_by',
        ),
        migrations.RemoveField(
            model_name='interventioncasualty',
            name='report_generated_at',
        ),
        migrations.AddField(
            model_name='interventionreport',
            name='report_generated_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now, verbose_name='تاريخ إنشاء التقرير'),
            preserve_default=False,
        ),
    ]
