# Generated by Django 5.2 on 2025-06-18 13:22

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0015_coordinationcenterforestfire_deaths_count_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='CoordinationCenterCropFire',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('telegram_number', models.PositiveIntegerField(verbose_name='رقم البرقية')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('intervention_time', models.CharField(max_length=20, verbose_name='ساعة التدخل')),
                ('intervening_unit', models.CharField(max_length=100, verbose_name='الوحدة المتدخلة')),
                ('municipality', models.CharField(max_length=100, verbose_name='البلدية')),
                ('location_name', models.CharField(max_length=200, verbose_name='المكان المسمى')),
                ('operation_duration', models.CharField(max_length=20, verbose_name='مدة العملية')),
                ('intervention_means', models.TextField(verbose_name='الوسائل المتدخلة')),
                ('loss_nature', models.TextField(verbose_name='طبيعة الخسائر')),
                ('losses_hectare', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الخسائر بالهكتار')),
                ('losses_are', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الخسائر بالآر')),
                ('losses_square_meter', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الخسائر بالمتر مربع')),
                ('other_loss_nature', models.TextField(blank=True, null=True, verbose_name='طبيعة الخسائر الأخرى')),
                ('other_loss_count', models.PositiveIntegerField(default=0, verbose_name='عدد الخسائر')),
                ('fire_control_status', models.CharField(choices=[('أخمد نهائيا', 'أخمد نهائيا'), ('مسيطر عليه', 'مسيطر عليه'), ('خارج عن السيطرة', 'خارج عن السيطرة')], default='أخمد نهائيا', max_length=50, verbose_name='وضعية التحكم في الحريق')),
                ('injured_count', models.PositiveIntegerField(default=0, verbose_name='عدد الجرحى')),
                ('deaths_count', models.PositiveIntegerField(default=0, verbose_name='عدد الوفيات')),
                ('evacuated_families_count', models.PositiveIntegerField(default=0, verbose_name='عدد العائلات الذين تم إجلاءهم')),
                ('evacuated_people_count', models.PositiveIntegerField(default=0, verbose_name='عدد الأشخاص الذين تم إجلاءهم')),
                ('evacuation_locations', models.TextField(blank=True, null=True, verbose_name='أماكن إجلاء العائلات بالتفصيل')),
                ('family_care_measures', models.TextField(blank=True, null=True, verbose_name='الإجراءات المتخذة للتكفل بالعائلات المتضررة')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'حريق محاصيل زراعية - مركز التنسيق',
                'verbose_name_plural': 'حرائق المحاصيل الزراعية - مركز التنسيق',
                'ordering': ['-date', '-telegram_number'],
            },
        ),
        migrations.AlterModelOptions(
            name='coordinationcenterforestfire',
            options={'verbose_name': 'حريق غابات - مركز التنسيق', 'verbose_name_plural': 'حرائق الغابات - مركز التنسيق'},
        ),
    ]
