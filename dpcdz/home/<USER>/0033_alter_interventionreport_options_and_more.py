# Generated by Django 5.2 on 2025-07-21 19:55

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0032_interventionvehicle_crew_count_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='interventionreport',
            options={},
        ),
        migrations.RemoveField(
            model_name='interventionreport',
            name='generated_by',
        ),
        migrations.RemoveField(
            model_name='interventionreport',
            name='report_generated_at',
        ),
        migrations.CreateModel(
            name='AgriculturalFireDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('fire_type', models.CharField(blank=True, choices=[('standing_wheat', 'قمح واقف'), ('harvest', 'حصيدة'), ('barley', 'شعير'), ('straw_bales', 'حزم تبن'), ('forest_bushes', 'غابة / أحراش'), ('grain_bags', 'أكياس شعير / قمح'), ('fruit_trees', 'أشجار مثمرة'), ('beehives', 'خلايا نحل')], max_length=30, null=True, verbose_name='نوع الحريق')),
                ('fire_sources_count', models.IntegerField(default=0, verbose_name='عدد البؤر (الموقد)')),
                ('wind_direction', models.CharField(blank=True, max_length=50, null=True, verbose_name='اتجاه الرياح')),
                ('wind_speed', models.FloatField(blank=True, null=True, verbose_name='سرعة الرياح (كم/سا)')),
                ('population_threat', models.BooleanField(default=False, verbose_name='تهديد للسكان')),
                ('evacuation_location', models.CharField(blank=True, max_length=200, null=True, verbose_name='مكان إجلاء السكان')),
                ('intervening_agents_count', models.IntegerField(default=0, verbose_name='عدد الأعوان المتدخلين')),
                ('affected_families_count', models.IntegerField(default=0, verbose_name='عدد العائلات المتأثرة')),
                ('present_authorities', models.JSONField(blank=True, default=list, verbose_name='الجهات الحاضرة')),
                ('support_request', models.CharField(blank=True, choices=[('under_control', 'شكراً، الوضع تحت السيطرة'), ('additional_vehicle', 'نعم وسيلة إضافية'), ('neighboring_unit', 'نعم وحدة مجاورة'), ('specialized_teams', 'نعم فرق متخصصة')], max_length=30, null=True, verbose_name='طلب دعم')),
                ('specialized_team_type', models.CharField(blank=True, choices=[('divers', 'فرقة الغطس'), ('rough_terrain', 'فرقة الأماكن الوعرة'), ('cynotechnical', 'فرقة السينوتقنية'), ('other', 'أخرى')], max_length=30, null=True, verbose_name='نوع الفريق المتخصص')),
                ('standing_wheat_area', models.FloatField(default=0, verbose_name='قمح واقف (هكتار)')),
                ('harvest_area', models.FloatField(default=0, verbose_name='حصيدة (هكتار)')),
                ('forest_area', models.FloatField(default=0, verbose_name='غابة/أحراش (هكتار/آر/متر مربع)')),
                ('barley_area', models.FloatField(default=0, verbose_name='شعير (هكتار)')),
                ('straw_bales_count', models.IntegerField(default=0, verbose_name='حزم تبن (عدد)')),
                ('grain_bags_count', models.IntegerField(default=0, verbose_name='أكياس قمح/شعير (عدد)')),
                ('fruit_trees_count', models.IntegerField(default=0, verbose_name='أشجار مثمرة (عدد)')),
                ('beehives_count', models.IntegerField(default=0, verbose_name='خلايا نحل (عدد)')),
                ('saved_area', models.FloatField(default=0, verbose_name='مساحة منقذة (هكتار)')),
                ('saved_straw_bales', models.IntegerField(default=0, verbose_name='عدد حزم التبن المنقذة')),
                ('saved_equipment', models.TextField(blank=True, null=True, verbose_name='ممتلكات أو آلات تم إنقاذها')),
                ('victims_details', models.JSONField(blank=True, default=list, verbose_name='تفاصيل الضحايا')),
                ('fatalities_details', models.JSONField(blank=True, default=list, verbose_name='تفاصيل الوفيات')),
                ('final_notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات ختامية')),
                ('intervention', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='agricultural_fire_detail', to='home.dailyintervention', verbose_name='التدخل')),
            ],
            options={
                'verbose_name': 'تفاصيل حريق المحاصيل الزراعية',
                'verbose_name_plural': 'تفاصيل حرائق المحاصيل الزراعية',
            },
        ),
        migrations.CreateModel(
            name='BuildingFireDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('fire_nature', models.CharField(blank=True, choices=[('residential_building', 'حريق بناية مخصصة للسكن'), ('classified_institution', 'حريق مؤسسة مصنفة'), ('public_place', 'حريق مكان مستقبل للجمهور'), ('vehicle_fire', 'حريق مركبة'), ('shop_market', 'حريق محل أو سوق')], max_length=30, null=True, verbose_name='طبيعة الحريق')),
                ('residential_type', models.CharField(blank=True, choices=[('apartment', 'شقة'), ('individual_house', 'منزل فردي'), ('building', 'عمارة'), ('residential_complex', 'مجمع سكني')], max_length=30, null=True, verbose_name='نوع البناية السكنية')),
                ('institution_type', models.CharField(blank=True, choices=[('factory', 'مصنع'), ('workshop', 'ورشة'), ('poultry_farm', 'مدجنة'), ('warehouse', 'مخزن')], max_length=30, null=True, verbose_name='نوع المؤسسة')),
                ('public_place_type', models.CharField(blank=True, choices=[('school', 'مدرسة'), ('hospital', 'مستشفى'), ('mosque', 'مسجد'), ('event_hall', 'قاعة حفلات'), ('shopping_center', 'مركز تجاري')], max_length=30, null=True, verbose_name='نوع المكان العام')),
                ('vehicle_type', models.CharField(blank=True, choices=[('car', 'سيارة'), ('truck', 'شاحنة'), ('bus', 'حافلة'), ('motorcycle', 'دراجة نارية'), ('other_vehicle', 'مركبة أخرى')], max_length=30, null=True, verbose_name='نوع المركبة')),
                ('fire_location', models.CharField(blank=True, choices=[('inside_building', 'داخل البناية'), ('outside_building', 'خارج المبنى'), ('specific_floor', 'طابق معين'), ('specific_room', 'غرفة محددة'), ('threatened_area', 'مكان مهدد بالانتشار')], max_length=30, null=True, verbose_name='موقع الحريق')),
                ('specific_floor', models.CharField(blank=True, max_length=50, null=True, verbose_name='الطابق المحدد')),
                ('specific_room', models.CharField(blank=True, max_length=100, null=True, verbose_name='الغرفة المحددة')),
                ('fire_points_count', models.IntegerField(default=0, verbose_name='عدد نقاط الاشتعال')),
                ('wind_direction', models.CharField(blank=True, max_length=50, null=True, verbose_name='جهة الرياح')),
                ('wind_speed', models.FloatField(blank=True, null=True, verbose_name='سرعة الرياح (كم/سا)')),
                ('population_threat', models.BooleanField(default=False, verbose_name='تهديد السكان')),
                ('population_evacuated', models.BooleanField(default=False, verbose_name='تم إجلاء السكان')),
                ('assistance_provided', models.TextField(blank=True, null=True, verbose_name='المساعدات المقدمة للسكان')),
                ('support_request', models.CharField(blank=True, max_length=50, null=True, verbose_name='طلب الدعم')),
                ('present_personnel', models.JSONField(blank=True, default=list, verbose_name='الأعوان الحاضرون')),
                ('field_situation_notes', models.TextField(blank=True, null=True, verbose_name='ملاحظة عن الوضع والخسائر الميدانية')),
                ('intervening_agents_count', models.IntegerField(default=0, verbose_name='عدد الأعوان المتدخلين')),
                ('affected_families_count', models.IntegerField(default=0, verbose_name='عدد العائلات المتضررة')),
                ('injured_details', models.JSONField(blank=True, default=list, verbose_name='تفاصيل المسعفين')),
                ('fatalities_details', models.JSONField(blank=True, default=list, verbose_name='تفاصيل الوفيات')),
                ('damages_description', models.TextField(blank=True, null=True, verbose_name='وصف الخسائر')),
                ('saved_properties', models.TextField(blank=True, null=True, verbose_name='الأملاك المنقذة')),
                ('final_notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات ختامية')),
                ('intervention', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='building_fire_detail', to='home.dailyintervention', verbose_name='التدخل')),
            ],
            options={
                'verbose_name': 'تفاصيل حريق البنايات والمؤسسات',
                'verbose_name_plural': 'تفاصيل حرائق البنايات والمؤسسات',
            },
        ),
        migrations.CreateModel(
            name='InterventionCasualty',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('casualty_type', models.CharField(choices=[('injured', 'مسعف'), ('fatality', 'وفاة')], max_length=20, verbose_name='نوع الضحية')),
                ('full_name', models.CharField(max_length=100, verbose_name='الاسم الكامل')),
                ('age', models.IntegerField(verbose_name='السن')),
                ('gender', models.CharField(choices=[('male', 'ذكر'), ('female', 'أنثى')], max_length=10, verbose_name='الجنس')),
                ('status', models.CharField(blank=True, choices=[('driver', 'سائق'), ('passenger', 'راكب'), ('pedestrian', 'مشاة'), ('resident', 'ساكن'), ('employee', 'موظف'), ('visitor', 'زائر'), ('other', 'أخرى')], max_length=20, null=True, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإضافة')),
                ('report_generated_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ إنشاء التقرير')),
                ('generated_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('intervention', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='casualties', to='home.dailyintervention', verbose_name='التدخل')),
            ],
            options={
                'verbose_name': 'تقرير التدخل',
                'verbose_name_plural': 'تقارير التدخل',
            },
        ),
        migrations.CreateModel(
            name='MedicalEvacuationDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('location_type', models.CharField(blank=True, choices=[('inside_home', 'داخل المنزل'), ('outside_home', 'خارج المنزل')], max_length=20, null=True, verbose_name='الموقع')),
                ('intervention_nature', models.CharField(blank=True, max_length=50, null=True, verbose_name='طبيعة التدخل')),
                ('suffocation_type', models.CharField(blank=True, choices=[('natural_gas', 'بالغاز الطبيعي أو البوتان'), ('co_gas', 'غاز CO'), ('airway_obstruction', 'انسداد المجاري التنفسية'), ('closed_spaces', 'الأماكن المغلقة'), ('other', 'أخرى')], max_length=30, null=True, verbose_name='نوع الاختناق')),
                ('poisoning_type', models.CharField(blank=True, choices=[('food', 'مواد غذائية'), ('medication', 'أدوية'), ('cleaning', 'منظفات'), ('bites_stings', 'لسعات أو عضّات'), ('other', 'أخرى')], max_length=30, null=True, verbose_name='نوع التسمم')),
                ('burn_type', models.CharField(blank=True, choices=[('flames', 'ألسنة اللهب'), ('hot_liquids', 'سوائل ساخنة'), ('chemical_radioactive', 'مواد كيميائية/مشعة'), ('electrical', 'صعقات كهربائية')], max_length=30, null=True, verbose_name='نوع الحرق')),
                ('explosion_type', models.CharField(blank=True, choices=[('butane_natural_gas', 'غاز البوتان / الغاز الطبيعي'), ('electrical_heating', 'الأجهزة الكهرومنزلية / أجهزة التدفئة')], max_length=30, null=True, verbose_name='نوع الانفجار')),
                ('drowning_type', models.CharField(blank=True, choices=[('water_bodies', 'مسطحات مائية'), ('dams', 'سدود'), ('valleys', 'أودية'), ('beaches', 'شواطئ'), ('other_places', 'أماكن أخرى')], max_length=30, null=True, verbose_name='نوع الغرق')),
                ('support_request', models.CharField(blank=True, choices=[('under_control', 'شكراً، الوضع تحت السيطرة'), ('additional_vehicle', 'نعم وسيلة إضافية'), ('neighboring_unit', 'نعم وحدة مجاورة'), ('specialized_team', 'نعم فريق متخصص')], max_length=30, null=True, verbose_name='طلب الدعم')),
                ('specialized_team_type', models.CharField(blank=True, choices=[('divers', 'فرقة الغطاسين'), ('rough_terrain', 'التدخل في الأماكن الوعرة'), ('cynotechnical', 'فرقة السينوتقنية'), ('other', 'أخرى')], max_length=30, null=True, verbose_name='نوع الفريق المتخصص')),
                ('material_damage_notes', models.TextField(blank=True, null=True, verbose_name='ملاحظة عن الخسائر المادية')),
                ('injured_details', models.JSONField(blank=True, default=list, verbose_name='تفاصيل المسعفين')),
                ('fatalities_details', models.JSONField(blank=True, default=list, verbose_name='تفاصيل الوفيات')),
                ('total_interventions', models.IntegerField(default=0, verbose_name='عدد التدخلات')),
                ('intervention', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='medical_detail', to='home.dailyintervention', verbose_name='التدخل')),
            ],
            options={
                'verbose_name': 'تفاصيل الإجلاء الصحي',
                'verbose_name_plural': 'تفاصيل الإجلاء الصحي',
            },
        ),
        migrations.CreateModel(
            name='TrafficAccidentDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('accident_type', models.CharField(blank=True, choices=[('vehicle_collision', 'ضحايا مصدومة بالمركبات'), ('vehicle_crash', 'ضحايا تصادم المركبات'), ('vehicle_rollover', 'ضحايا إنقلاب'), ('train_collision', 'ضحايا مصدومة بالقطار'), ('other_accidents', 'حوادث أخرى')], max_length=30, null=True, verbose_name='نوع الحادث')),
                ('involved_vehicles', models.JSONField(blank=True, default=list, verbose_name='المركبات المتورطة')),
                ('material_damage_notes', models.TextField(blank=True, null=True, verbose_name='ملاحظة حول الخسائر المادية')),
                ('road_type', models.CharField(blank=True, choices=[('highway', 'الطريق السيار'), ('national', 'الطريق الوطني'), ('wilaya', 'الطريق الولائي'), ('municipal', 'الطريق البلدي'), ('other', 'طرق أخرى')], max_length=20, null=True, verbose_name='نوع الطريق')),
                ('victims_details', models.JSONField(blank=True, default=list, verbose_name='تفاصيل الضحايا')),
                ('fatalities_details', models.JSONField(blank=True, default=list, verbose_name='تفاصيل الوفيات')),
                ('intervention', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='traffic_detail', to='home.dailyintervention', verbose_name='التدخل')),
            ],
            options={
                'verbose_name': 'تفاصيل حادث المرور',
                'verbose_name_plural': 'تفاصيل حوادث المرور',
            },
        ),
    ]
