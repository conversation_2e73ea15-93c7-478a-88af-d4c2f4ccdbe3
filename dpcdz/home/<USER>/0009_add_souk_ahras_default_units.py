from django.db import migrations

def add_souk_ahras_units(apps, schema_editor):
    InterventionUnit = apps.get_model('home', 'InterventionUnit')
    
    # Default units for Souk Ahras (41)
    default_units = [
        {'name': 'الوحدة الرئيسية سوق أهراس', 'code': 'SA-MAIN'},
        {'name': 'المركز المتقدم طريق رقم 16', 'code': 'SA-ADV16'},
        {'name': 'المركز المتقدم برال صالح', 'code': 'SA-ADVBS'},
        {'name': 'الوحدة الثانوية سدراتة', 'code': 'SA-SEDR'},
        {'name': 'الوحدة الثانوية مداوروش', 'code': 'SA-MEDA'},
        {'name': 'الوحدة الثانوية المشروحة', 'code': 'SA-MASH'},
        {'name': 'الوحدة الثانوية بئربوحوش', 'code': 'SA-BIRB'},
        {'name': 'الوحدة الثانوية أم العضائم', 'code': 'SA-OUMA'},
        {'name': 'الوحدة الثانوية المراهنة', 'code': 'SA-MARA'},
        {'name': 'الوحدة الثانوية الحدادة', 'code': 'SA-HADA'},
        {'name': 'الوحدة الثانوية أولاد إدريس', 'code': 'SA-OUID'},
        {'name': 'الوحدة الثانوية عين الزانة', 'code': 'SA-AINZ'}
    ]
    
    for unit in default_units:
        InterventionUnit.objects.get_or_create(
            wilaya='41',  # Souk Ahras
            code=unit['code'],
            defaults={
                'name': unit['name'],
                'is_default': True
            }
        )

def remove_souk_ahras_units(apps, schema_editor):
    InterventionUnit = apps.get_model('home', 'InterventionUnit')
    InterventionUnit.objects.filter(wilaya='41', is_default=True).delete()

class Migration(migrations.Migration):
    dependencies = [
        ('home', '0008_alter_interventionunit_options_and_more'),
    ]

    operations = [
        migrations.RunPython(add_souk_ahras_units, remove_souk_ahras_units),
    ]
