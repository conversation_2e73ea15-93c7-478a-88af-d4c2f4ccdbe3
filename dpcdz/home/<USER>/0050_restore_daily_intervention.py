# Generated manually on 2025-08-01 16:00

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('home', '0049_remove_buildingfiredetail_intervention_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='DailyIntervention',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('intervention_number', models.CharField(max_length=20, unique=True, verbose_name='رقم التدخل')),
                ('intervention_type', models.CharField(choices=[('medical', 'إجلاء صحي'), ('accident', 'حادث مرور'), ('agricultural-fire', 'حريق محاصيل زراعية'), ('building-fire', 'حرائق البنايات والمؤسسات'), ('other', 'عمليات مختلفة')], max_length=30, verbose_name='نوع التدخل')),
                ('status', models.CharField(choices=[('initial_report', 'بلاغ أولي'), ('reconnaissance', 'قيد التعرف'), ('intervention', 'عملية تدخل'), ('completed', 'منتهية'), ('escalated', 'مصعدة لكارثة كبرى')], default='initial_report', max_length=20, verbose_name='الحالة')),
                ('exit_time', models.TimeField(verbose_name='ساعة الخروج')),
                ('intervention_location', models.CharField(max_length=200, verbose_name='مكان التدخل')),
                ('contact_source', models.CharField(choices=[('citizen', 'مواطن'), ('police', 'الشرطة'), ('gendarmerie', 'الدرك الوطني'), ('army', 'الجيش الوطني الشعبي'), ('forest', 'مصالح الغابات'), ('customs', 'الجمارك'), ('local-authorities', 'السلطات المحلية'), ('other', 'أخرى')], max_length=30, verbose_name='الجهة المتصلة')),
                ('contact_type', models.CharField(choices=[('phone', 'هاتف'), ('radio', 'راديو'), ('unit-request', 'وحدة تطلب الدعم'), ('direct', 'مباشر')], max_length=20, verbose_name='نوع الاتصال')),
                ('phone_number', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف')),
                ('additional_notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات إضافية')),
                ('arrival_time', models.TimeField(blank=True, null=True, verbose_name='ساعة الوصول')),
                ('reconnaissance_notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات التعرف')),
                ('support_requested', models.BooleanField(default=False, verbose_name='تم طلب الدعم')),
                ('end_time', models.TimeField(blank=True, null=True, verbose_name='ساعة الانتهاء')),
                ('total_duration', models.CharField(blank=True, max_length=20, null=True, verbose_name='مدة التدخل الإجمالية')),
                ('final_injured_count', models.IntegerField(default=0, verbose_name='عدد المسعفين النهائي')),
                ('final_deaths_count', models.IntegerField(default=0, verbose_name='عدد الوفيات النهائي')),
                ('final_notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات ختامية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='home.interventionunit', verbose_name='الوحدة')),
            ],
            options={
                'verbose_name': 'تدخل يومي',
                'verbose_name_plural': 'التدخلات اليومية',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='InterventionVehicle',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_primary', models.BooleanField(default=True, verbose_name='وسيلة أساسية')),
                ('assigned_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التعيين')),
                ('intervention', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='vehicles', to='home.dailyintervention', verbose_name='التدخل')),
                ('vehicle', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='home.unitequipment', verbose_name='الوسيلة')),
            ],
            options={
                'verbose_name': 'وسيلة التدخل',
                'verbose_name_plural': 'وسائل التدخل',
                'unique_together': {('intervention', 'vehicle')},
            },
        ),
    ]
