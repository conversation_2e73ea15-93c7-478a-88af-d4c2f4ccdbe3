# Generated by Django 5.2 on 2025-05-07 12:27

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ResidentialFireData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('intervening_unit', models.CharField(max_length=100)),
                ('family_first', models.BooleanField(default=False)),
                ('family_second', models.BooleanField(default=False)),
                ('family_third', models.BooleanField(default=False)),
                ('family_fourth', models.BooleanField(default=False)),
                ('number_of_fires', models.PositiveIntegerField()),
                ('number_of_interventions', models.PositiveIntegerField()),
                ('number_of_injured', models.PositiveIntegerField()),
                ('number_of_deaths', models.PositiveIntegerField()),
                ('ambulances', models.PositiveIntegerField()),
                ('fire_trucks', models.PositiveIntegerField()),
                ('mechanical_ladders', models.PositiveIntegerField()),
                ('other_resources', models.PositiveIntegerField()),
                ('intervention_duration', models.CharField(max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
