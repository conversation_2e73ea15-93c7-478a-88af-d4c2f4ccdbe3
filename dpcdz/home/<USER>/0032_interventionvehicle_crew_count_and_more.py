# Generated by Django 5.2 on 2025-07-20 04:34

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0031_alter_dailypersonnelstatus_status_dailyintervention_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='interventionvehicle',
            name='crew_count',
            field=models.IntegerField(default=0, verbose_name='عدد الأعوان'),
        ),
        migrations.AddField(
            model_name='interventionvehicle',
            name='crew_details',
            field=models.TextField(blank=True, null=True, verbose_name='تفاصيل الطاقم'),
        ),
        migrations.AddField(
            model_name='interventionvehicle',
            name='vehicle_role',
            field=models.CharField(choices=[('primary', 'وسيلة أساسية'), ('support', 'وسيلة دعم'), ('backup', 'وسيلة احتياطية')], default='primary', max_length=20, verbose_name='دور الوسيلة'),
        ),
        migrations.CreateModel(
            name='InterventionReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_vehicles', models.IntegerField(default=0, verbose_name='إجمالي الوسائل')),
                ('primary_vehicles', models.IntegerField(default=0, verbose_name='الوسائل الأساسية')),
                ('support_vehicles', models.IntegerField(default=0, verbose_name='وسائل الدعم')),
                ('total_personnel', models.IntegerField(default=0, verbose_name='إجمالي الأعوان')),
                ('vehicles_summary', models.TextField(blank=True, null=True, verbose_name='ملخص الوسائل')),
                ('personnel_summary', models.TextField(blank=True, null=True, verbose_name='ملخص الأعوان')),
                ('report_generated_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ إنشاء التقرير')),
                ('generated_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('intervention', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='report', to='home.dailyintervention', verbose_name='التدخل')),
            ],
            options={
                'verbose_name': 'تقرير التدخل',
                'verbose_name_plural': 'تقارير التدخل',
            },
        ),
    ]
