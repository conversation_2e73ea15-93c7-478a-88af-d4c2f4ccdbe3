# Generated by Django 5.2 on 2025-07-19 09:44

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0027_alter_unitpersonnel_unique_together_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name='dailypersonnelstatus',
            name='status',
            field=models.CharField(choices=[('present', 'حاضر'), ('absent', 'غائب'), ('on_mission', 'في مهمة'), ('reserve', 'احتياطي')], default='present', max_length=20, verbose_name='الحالة'),
        ),
        migrations.CreateModel(
            name='ShiftSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('working_shift', models.CharField(choices=[('shift_1', 'الفرقة الأولى (A)'), ('shift_2', 'الفرقة الثانية (B)'), ('shift_3', 'الفرقة الثالثة (C)')], max_length=20, verbose_name='الفرقة العاملة')),
                ('start_datetime', models.DateTimeField(verbose_name='بداية العمل')),
                ('end_datetime', models.DateTimeField(verbose_name='نهاية العمل')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='home.interventionunit', verbose_name='الوحدة')),
            ],
            options={
                'verbose_name': 'جدولة الفرقة',
                'verbose_name_plural': 'جدولة الفرق',
                'ordering': ['-start_datetime'],
            },
        ),
    ]
