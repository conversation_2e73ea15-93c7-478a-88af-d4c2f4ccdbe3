# Generated by Django 5.2.4 on 2025-07-16 20:18

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0024_update_vehicle_crew_assignment_models'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name='vehiclecrewassignment',
            name='role',
            field=models.CharField(choices=[('driver', 'سائق'), ('crew_chief', 'رئيس عدد'), ('agent', 'عون')], max_length=50, verbose_name='الدور'),
        ),
        migrations.CreateModel(
            name='ReadinessAlert',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('alert_type', models.CharField(choices=[('personnel_shortage', 'نقص في الأعوان'), ('vehicle_shortage', 'نقص في الوسائل'), ('shift_incomplete', 'فرقة ناقصة'), ('vehicle_not_ready', 'وسيلة غير جاهزة'), ('manual_confirmation_needed', 'تحتاج تأكيد يدوي')], max_length=30, verbose_name='نوع التنبيه')),
                ('priority', models.CharField(choices=[('low', 'منخفض'), ('medium', 'متوسط'), ('high', 'عالي'), ('critical', 'حرج')], default='medium', max_length=10, verbose_name='الأولوية')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('acknowledged', 'تم الاطلاع'), ('resolved', 'تم الحل'), ('dismissed', 'تم التجاهل')], default='active', max_length=15, verbose_name='الحالة')),
                ('title', models.CharField(max_length=200, verbose_name='العنوان')),
                ('message', models.TextField(verbose_name='الرسالة')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('acknowledged_at', models.DateTimeField(blank=True, null=True, verbose_name='وقت الاطلاع')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='وقت الحل')),
                ('resolution_notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات الحل')),
                ('acknowledged_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم الاطلاع بواسطة')),
                ('resolved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resolved_alerts', to=settings.AUTH_USER_MODEL, verbose_name='تم الحل بواسطة')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='home.interventionunit', verbose_name='الوحدة')),
            ],
            options={
                'verbose_name': 'تنبيه جاهزية',
                'verbose_name_plural': 'تنبيهات الجاهزية',
                'ordering': ['-created_at', '-priority'],
            },
        ),
        migrations.CreateModel(
            name='WorkShift',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('A', 'فصيلة A'), ('B', 'فصيلة B'), ('C', 'فصيلة C'), ('morning', 'فترة صباحية'), ('evening', 'فترة مسائية'), ('night', 'فترة ليلية')], max_length=20, verbose_name='اسم الفرقة')),
                ('shift_type', models.CharField(choices=[('24_48', 'نظام 24/48 ساعة'), ('8_hours', 'نظام 8 ساعات')], max_length=20, verbose_name='نوع النظام')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='home.interventionunit', verbose_name='الوحدة')),
            ],
            options={
                'verbose_name': 'فرقة عمل',
                'verbose_name_plural': 'فرق العمل',
                'ordering': ['unit', 'shift_type', 'name'],
                'unique_together': {('unit', 'name')},
            },
        ),
        migrations.CreateModel(
            name='EightHourPersonnel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('work_period', models.CharField(choices=[('morning', 'فترة صباحية (08:00 - 16:00)'), ('evening', 'فترة مسائية (16:00 - 00:00)'), ('night', 'فترة ليلية (00:00 - 08:00)')], max_length=20, verbose_name='فترة العمل')),
                ('task_type', models.CharField(choices=[('administrative', 'عمل إداري'), ('maintenance', 'صيانة'), ('communications', 'اتصالات'), ('storage', 'مخزن'), ('security', 'أمن'), ('support', 'دعم'), ('technical', 'تقني')], max_length=20, verbose_name='نوع المهمة')),
                ('task_description', models.TextField(blank=True, null=True, verbose_name='وصف المهمة')),
                ('start_time', models.TimeField(verbose_name='وقت البداية')),
                ('end_time', models.TimeField(verbose_name='وقت النهاية')),
                ('is_present', models.BooleanField(default=True, verbose_name='حاضر')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('personnel', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='home.unitpersonnel', verbose_name='العون')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='home.interventionunit', verbose_name='الوحدة')),
            ],
            options={
                'verbose_name': 'عون نظام 8 ساعات',
                'verbose_name_plural': 'أعوان نظام 8 ساعات',
                'ordering': ['-date', 'work_period', 'personnel__full_name'],
                'unique_together': {('personnel', 'date', 'work_period')},
            },
        ),
        migrations.CreateModel(
            name='ShiftPersonnel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('assigned_date', models.DateField(auto_now_add=True, verbose_name='تاريخ التعيين')),
                ('assigned_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='عين بواسطة')),
                ('personnel', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='shifts', to='home.unitpersonnel', verbose_name='العون')),
                ('shift', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='personnel', to='home.workshift', verbose_name='الفرقة')),
            ],
            options={
                'verbose_name': 'عون في فرقة',
                'verbose_name_plural': 'الأعوان في الفرق',
                'ordering': ['shift', 'personnel__full_name'],
                'unique_together': {('shift', 'personnel')},
            },
        ),
        migrations.CreateModel(
            name='MorningCheckSummary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('total_personnel', models.IntegerField(default=0, verbose_name='إجمالي الأعوان')),
                ('present_personnel', models.IntegerField(default=0, verbose_name='الأعوان الحاضرين')),
                ('absent_personnel', models.IntegerField(default=0, verbose_name='الأعوان الغائبين')),
                ('on_mission_personnel', models.IntegerField(default=0, verbose_name='الأعوان في مهمة')),
                ('total_vehicles', models.IntegerField(default=0, verbose_name='إجمالي الوسائل')),
                ('ready_vehicles', models.IntegerField(default=0, verbose_name='الوسائل الجاهزة')),
                ('not_ready_vehicles', models.IntegerField(default=0, verbose_name='الوسائل غير الجاهزة')),
                ('under_maintenance_vehicles', models.IntegerField(default=0, verbose_name='الوسائل تحت الصيانة')),
                ('assigned_personnel', models.IntegerField(default=0, verbose_name='الأعوان المعينين')),
                ('unassigned_personnel', models.IntegerField(default=0, verbose_name='الأعوان غير المعينين')),
                ('overall_readiness_score', models.IntegerField(default=0, verbose_name='نسبة الجاهزية العامة')),
                ('is_fully_ready', models.BooleanField(default=False, verbose_name='جاهز بالكامل')),
                ('check_completed_at', models.DateTimeField(blank=True, null=True, verbose_name='وقت إتمام التحقق')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='home.interventionunit', verbose_name='الوحدة')),
                ('active_shift', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='home.workshift', verbose_name='الفرقة العاملة')),
            ],
            options={
                'verbose_name': 'ملخص التحقق الصباحي',
                'verbose_name_plural': 'ملخصات التحقق الصباحي',
                'ordering': ['-date', 'unit'],
                'unique_together': {('unit', 'date')},
            },
        ),
        migrations.CreateModel(
            name='DailyShiftSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='home.interventionunit', verbose_name='الوحدة')),
                ('active_shift', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='home.workshift', verbose_name='الفرقة العاملة')),
            ],
            options={
                'verbose_name': 'جدولة الفرق اليومية',
                'verbose_name_plural': 'جدولة الفرق اليومية',
                'ordering': ['-date', 'unit'],
                'unique_together': {('unit', 'date')},
            },
        ),
    ]
