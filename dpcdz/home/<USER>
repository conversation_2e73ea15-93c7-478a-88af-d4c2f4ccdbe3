import json
import traceback
import os
import pandas as pd
import numpy as np
import errno
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from .models import WILAYA_CHOICES
from datetime import datetime

# قاموس لتحويل أرقام الأشهر إلى أسماء الأشهر العربية الجزائرية
ALGERIAN_ARABIC_MONTHS = {
    '01': 'جانفي',
    '02': 'فيفري',
    '03': 'مارس',
    '04': 'أفريل',
    '05': 'ماي',
    '06': 'جوان',
    '07': 'جويلية',
    '08': 'أوت',
    '09': 'سبتمبر',
    '10': 'أكتوبر',
    '11': 'نوفمبر',
    '12': 'ديسمبر'
}

def format_date_algerian_arabic(date_obj):
    """
    تحويل كائن التاريخ إلى تنسيق عربي جزائري (مثال: 15 جانفي 2024)

    Args:
        date_obj: كائن تاريخ من نوع datetime.date أو datetime.datetime أو نص بتنسيق YYYY-MM-DD

    Returns:
        str: التاريخ بالتنسيق العربي الجزائري
    """
    try:
        # إذا كان التاريخ نصًا، نحوله إلى كائن تاريخ
        if isinstance(date_obj, str):
            date_obj = datetime.strptime(date_obj, '%Y-%m-%d').date()

        # استخراج اليوم والشهر والسنة
        day = date_obj.day
        month = date_obj.month
        year = date_obj.year

        # تحويل رقم الشهر إلى اسم الشهر بالعربية الجزائرية
        month_str = ALGERIAN_ARABIC_MONTHS.get(f"{month:02d}", "")

        # تنسيق التاريخ بالعربية
        return f"{day} {month_str} {year}"
    except Exception as e:
        print(f"خطأ في تنسيق التاريخ: {e}")
        return str(date_obj)  # إرجاع التاريخ الأصلي في حالة حدوث خطأ

def handle_post_request(request, process_data_func):
    """
    وظيفة مساعدة لمعالجة طلبات POST بشكل موحد

    Args:
        request: طلب HTTP
        process_data_func: دالة لمعالجة البيانات المستخرجة

    Returns:
        JsonResponse: استجابة JSON
    """
    if request.method != 'POST':
        return None

    try:
        # طباعة معلومات التصحيح
        print("Content-Type:", request.content_type)
        print("Request Body:", request.body[:200])  # طباعة أول 200 حرف فقط

        # استخراج البيانات من الطلب
        if request.content_type and 'application/json' in request.content_type:
            try:
                data = json.loads(request.body)
            except json.JSONDecodeError as e:
                print("JSON Decode Error:", str(e))
                print("Request Body:", request.body)
                return JsonResponse({
                    'status': 'error',
                    'message': f'خطأ في تحليل البيانات: {str(e)}'
                })
        else:
            # إذا لم يكن نوع المحتوى JSON، استخدم بيانات النموذج العادية
            data = {}
            for key, value in request.POST.items():
                data[key] = value

        # معالجة البيانات باستخدام الدالة المقدمة
        return process_data_func(data)

    except Exception as e:
        # طباعة تفاصيل الخطأ للتصحيح
        print("Exception in handle_post_request:", str(e))
        traceback.print_exc()
        return JsonResponse({
            'status': 'error',
            'message': f'حدث خطأ أثناء معالجة الطلب: {str(e)}'
        })

def save_to_excel(excel_path, new_data, user=None, form_type=None):
    """
    Save data to Excel file with proper error handling, organized by wilaya folders

    Args:
        excel_path (str): Original path to the Excel file
        new_data (dict): Dictionary of data to save
        user (User, optional): The user saving the data. If provided, will save in wilaya-specific folder
        form_type (str, optional): Type of form being saved. Used for naming the file

    Returns:
        tuple: (success, message)
    """
    try:
        # Create DataFrame with the new data
        new_df = pd.DataFrame([new_data])

        # If user is provided, organize files by wilaya
        print("User:", user)
        if user:
            print("User is authenticated:", user.is_authenticated)
            print("User has userprofile:", hasattr(user, 'userprofile'))

        if user and hasattr(user, 'userprofile'):
            # Get wilaya code and name
            wilaya_code = user.userprofile.wilaya
            wilaya_name = dict(WILAYA_CHOICES).get(wilaya_code, 'unknown')
            print(f"Wilaya code: {wilaya_code}, Wilaya name: {wilaya_name}")

            # Create wilaya directory if it doesn't exist
            wilaya_dir = os.path.join('media', f'wilaya_{wilaya_code}_{wilaya_name}')
            os.makedirs(wilaya_dir, exist_ok=True)

            # Determine the filename based on form_type or original path
            if form_type:
                filename = f"{form_type}_{wilaya_name}.xlsx"
            else:
                # Extract the original filename from the path
                original_filename = os.path.basename(excel_path)
                filename = f"{original_filename.split('.')[0]}_{wilaya_name}.xlsx"

            # Update the excel_path to use the wilaya directory
            excel_path = os.path.join(wilaya_dir, filename)
        else:
            # User doesn't have a userprofile, use a default directory
            print("User doesn't have a userprofile or is not authenticated, using default directory")

            # Create a default directory for users without profiles
            default_dir = os.path.join('media', 'default')
            os.makedirs(default_dir, exist_ok=True)

            # Determine the filename based on form_type or original path
            if form_type:
                filename = f"{form_type}_default.xlsx"
            else:
                # Extract the original filename from the path
                original_filename = os.path.basename(excel_path)
                filename = f"{original_filename.split('.')[0]}_default.xlsx"

            # Update the excel_path to use the default directory
            excel_path = os.path.join(default_dir, filename)

            # Ensure media directory exists for the original path
            os.makedirs(os.path.dirname(excel_path), exist_ok=True)

        # Check if file exists and is accessible
        if os.path.exists(excel_path):
            try:
                # Read existing file and append new data
                existing_df = pd.read_excel(excel_path)

                # Remove any existing sum rows (rows with 'المجموع' in the first column)
                if len(existing_df) > 0 and existing_df.columns.size > 0:
                    first_col = existing_df.columns[0]
                    existing_df = existing_df[existing_df[first_col] != 'المجموع']

                # Concatenate with new data
                updated_df = pd.concat([existing_df, new_df], ignore_index=True)

                # Add a single sum row for numeric columns at the end of the workbook
                numeric_cols = updated_df.select_dtypes(include=['number']).columns
                if len(numeric_cols) > 0:
                    sum_row = updated_df[numeric_cols].sum().to_dict()
                    sum_row_df = pd.DataFrame([sum_row])

                    # Add a label for the sum row in the first column if it exists
                    if len(updated_df.columns) > 0:
                        first_col = updated_df.columns[0]
                        sum_row_df[first_col] = 'المجموع'

                    # Concatenate the sum row to the DataFrame
                    updated_df = pd.concat([updated_df, sum_row_df], ignore_index=True)

                # Save with xlsxwriter to support RTL
                with pd.ExcelWriter(excel_path, engine='xlsxwriter', engine_kwargs={'options': {'nan_inf_to_errors': True}}) as writer:
                    updated_df.to_excel(writer, index=False, sheet_name='البيانات')

                    # Get the xlsxwriter workbook and worksheet objects
                    workbook = writer.book
                    worksheet = writer.sheets['البيانات']

                    # Set RTL direction for the worksheet
                    worksheet.right_to_left()

                    # Freeze the header row so it stays visible when scrolling
                    worksheet.freeze_panes(1, 0)

                    # Format headers with black bold fonts and proper background
                    header_format = workbook.add_format({
                        'bold': True,
                        'font_color': 'black',
                        'text_wrap': True,
                        'valign': 'top',
                        'align': 'center',
                        'border': 1,
                        'bg_color': '#E6E6FA'  # Light background for headers
                    })

                    # Apply header format
                    for col_num, value in enumerate(updated_df.columns.values):
                        worksheet.write(0, col_num, value, header_format)

                    # Format the sum row with bold, background color, and borders if it exists
                    if len(numeric_cols) > 0:
                        # Create a format for the sum row with amber background and black bold fonts
                        sum_row_format = workbook.add_format({
                            'bold': True,
                            'font_color': 'black',
                            'bg_color': '#FFC000',  # Amber background color
                            'border': 1,
                            'border_color': '#000000',
                            'align': 'center',
                            'num_format': '0'  # Format numbers without decimal places
                        })

                        # Special format for the "المجموع" (Total) cell
                        total_cell_format = workbook.add_format({
                            'bold': True,
                            'font_color': 'black',
                            'bg_color': '#FFC000',  # Amber background color
                            'border': 1,
                            'border_color': '#000000',
                            'align': 'center',
                            'font_size': 12
                        })

                        sum_row_index = len(updated_df)
                        for col_num, col in enumerate(updated_df.columns):
                            try:
                                value = updated_df.iloc[sum_row_index-1, col_num]

                                # Check if this is the first column (with "المجموع" text)
                                if col_num == 0:
                                    worksheet.write(sum_row_index, col_num, value, total_cell_format)
                                # For numeric columns, ensure proper formatting
                                elif col in numeric_cols:
                                    if pd.isna(value) or (hasattr(value, 'dtype') and pd.api.types.is_float_dtype(value.dtype) and (np.isinf(value) or np.isnan(value))):
                                        value = 0
                                    worksheet.write(sum_row_index, col_num, value, sum_row_format)
                                # For non-numeric columns in the sum row
                                else:
                                    worksheet.write(sum_row_index, col_num, '', sum_row_format)
                            except Exception as e:
                                # If there's an error, write 0 for numeric columns or empty for others
                                if col in numeric_cols:
                                    worksheet.write(sum_row_index, col_num, 0, sum_row_format)
                                else:
                                    worksheet.write(sum_row_index, col_num, '', sum_row_format)

                    # Auto-adjust column widths
                    for i, col in enumerate(updated_df.columns):
                        try:
                            col_data = updated_df[col].fillna('').astype(str)
                            max_data_len = col_data.str.len().max() if not col_data.empty else 0
                            col_len = len(str(col))
                            max_len = max(max_data_len, col_len) + 2
                            max_len = min(max_len, 50)  # Set a reasonable maximum width
                            worksheet.set_column(i, i, max_len)
                        except Exception as e:
                            worksheet.set_column(i, i, 15)  # Default width

            except (PermissionError, OSError) as e:
                if isinstance(e, PermissionError) or e.errno == errno.EACCES:
                    # Handle permission error
                    print(f"Permission denied when accessing {excel_path}: {e}")
                    # Try to save to a temporary file in the same directory
                    temp_filename = f"{os.path.splitext(os.path.basename(excel_path))[0]}_temp_{os.getpid()}.xlsx"
                    temp_path = os.path.join(os.path.dirname(excel_path), temp_filename)

                    # Add a single sum row for numeric columns at the end of the workbook
                    numeric_cols = new_df.select_dtypes(include=['number']).columns
                    if len(numeric_cols) > 0:
                        sum_row = new_df[numeric_cols].sum().to_dict()
                        sum_row_df = pd.DataFrame([sum_row])

                        # Add a label for the sum row in the first column if it exists
                        if len(new_df.columns) > 0:
                            first_col = new_df.columns[0]
                            sum_row_df[first_col] = 'المجموع'

                        # Concatenate the sum row to the DataFrame
                        new_df = pd.concat([new_df, sum_row_df], ignore_index=True)

                    # Save with xlsxwriter to support RTL
                    with pd.ExcelWriter(temp_path, engine='xlsxwriter', engine_kwargs={'options': {'nan_inf_to_errors': True}}) as writer:
                        new_df.to_excel(writer, index=False, sheet_name='البيانات')

                        # Get the xlsxwriter workbook and worksheet objects
                        workbook = writer.book
                        worksheet = writer.sheets['البيانات']

                        # Set RTL direction for the worksheet
                        worksheet.right_to_left()

                        # Freeze the header row so it stays visible when scrolling
                        worksheet.freeze_panes(1, 0)

                        # Format headers with black bold fonts and proper background
                        header_format = workbook.add_format({
                            'bold': True,
                            'font_color': 'black',
                            'text_wrap': True,
                            'valign': 'top',
                            'align': 'center',
                            'border': 1,
                            'bg_color': '#E6E6FA'  # Light background for headers
                        })

                        # Apply header format
                        for col_num, value in enumerate(new_df.columns.values):
                            worksheet.write(0, col_num, value, header_format)

                        # Format the sum row with bold, background color, and borders if it exists
                        if len(numeric_cols) > 0:
                            # Create a format for the sum row with amber background and black bold fonts
                            sum_row_format = workbook.add_format({
                                'bold': True,
                                'font_color': 'black',
                                'bg_color': '#FFC000',  # Amber background color
                                'border': 1,
                                'border_color': '#000000',
                                'align': 'center',
                                'num_format': '0'  # Format numbers without decimal places
                            })

                            # Special format for the "المجموع" (Total) cell
                            total_cell_format = workbook.add_format({
                                'bold': True,
                                'font_color': 'black',
                                'bg_color': '#FFC000',  # Amber background color
                                'border': 1,
                                'border_color': '#000000',
                                'align': 'center',
                                'font_size': 12
                            })

                            sum_row_index = len(new_df)
                            for col_num, col in enumerate(new_df.columns):
                                try:
                                    value = new_df.iloc[sum_row_index-1, col_num]

                                    # Check if this is the first column (with "المجموع" text)
                                    if col_num == 0:
                                        worksheet.write(sum_row_index, col_num, value, total_cell_format)
                                    # For numeric columns, ensure proper formatting
                                    elif col in numeric_cols:
                                        if pd.isna(value) or (hasattr(value, 'dtype') and pd.api.types.is_float_dtype(value.dtype) and (np.isinf(value) or np.isnan(value))):
                                            value = 0
                                        worksheet.write(sum_row_index, col_num, value, sum_row_format)
                                    # For non-numeric columns in the sum row
                                    else:
                                        worksheet.write(sum_row_index, col_num, '', sum_row_format)
                                except Exception as e:
                                    # If there's an error, write 0 for numeric columns or empty for others
                                    if col in numeric_cols:
                                        worksheet.write(sum_row_index, col_num, 0, sum_row_format)
                                    else:
                                        worksheet.write(sum_row_index, col_num, '', sum_row_format)

                    return (False, f"تم حفظ البيانات في ملف مؤقت {temp_path} بسبب مشكلة في الوصول إلى الملف الأصلي")
                else:
                    # Handle other OS errors
                    print(f"OS error when accessing {excel_path}: {e}")
                    return (False, f"حدث خطأ في نظام التشغيل عند محاولة الوصول إلى الملف: {str(e)}")
            except Exception as e:
                # Handle other errors
                print(f"Error reading/writing Excel file {excel_path}: {e}")
                # Add a single sum row for numeric columns at the end of the workbook
                numeric_cols = new_df.select_dtypes(include=['number']).columns
                if len(numeric_cols) > 0:
                    sum_row = new_df[numeric_cols].sum().to_dict()
                    sum_row_df = pd.DataFrame([sum_row])

                    # Add a label for the sum row in the first column if it exists
                    if len(new_df.columns) > 0:
                        first_col = new_df.columns[0]
                        sum_row_df[first_col] = 'المجموع'

                    # Concatenate the sum row to the DataFrame
                    new_df = pd.concat([new_df, sum_row_df], ignore_index=True)

                # Try to create a new file with RTL support
                with pd.ExcelWriter(excel_path, engine='xlsxwriter', engine_kwargs={'options': {'nan_inf_to_errors': True}}) as writer:
                    new_df.to_excel(writer, index=False, sheet_name='البيانات')

                    # Get the xlsxwriter workbook and worksheet objects
                    workbook = writer.book
                    worksheet = writer.sheets['البيانات']

                    # Set RTL direction for the worksheet
                    worksheet.right_to_left()

                    # Freeze the header row so it stays visible when scrolling
                    worksheet.freeze_panes(1, 0)

                    # Format headers with black bold fonts and proper background
                    header_format = workbook.add_format({
                        'bold': True,
                        'font_color': 'black',
                        'text_wrap': True,
                        'valign': 'top',
                        'align': 'center',
                        'border': 1,
                        'bg_color': '#E6E6FA'  # Light background for headers
                    })

                    # Apply header format
                    for col_num, value in enumerate(new_df.columns.values):
                        worksheet.write(0, col_num, value, header_format)

                    # Format the sum row with bold, background color, and borders if it exists
                    if len(numeric_cols) > 0:
                        # Create a format for the sum row with amber background and black bold fonts
                        sum_row_format = workbook.add_format({
                            'bold': True,
                            'font_color': 'black',
                            'bg_color': '#FFC000',  # Amber background color
                            'border': 1,
                            'border_color': '#000000',
                            'align': 'center',
                            'num_format': '0'  # Format numbers without decimal places
                        })

                        # Special format for the "المجموع" (Total) cell
                        total_cell_format = workbook.add_format({
                            'bold': True,
                            'font_color': 'black',
                            'bg_color': '#FFC000',  # Amber background color
                            'border': 1,
                            'border_color': '#000000',
                            'align': 'center',
                            'font_size': 12
                        })

                        sum_row_index = len(new_df)
                        for col_num, col in enumerate(new_df.columns):
                            try:
                                value = new_df.iloc[sum_row_index-1, col_num]

                                # Check if this is the first column (with "المجموع" text)
                                if col_num == 0:
                                    worksheet.write(sum_row_index, col_num, value, total_cell_format)
                                # For numeric columns, ensure proper formatting
                                elif col in numeric_cols:
                                    if pd.isna(value) or (hasattr(value, 'dtype') and pd.api.types.is_float_dtype(value.dtype) and (np.isinf(value) or np.isnan(value))):
                                        value = 0
                                    worksheet.write(sum_row_index, col_num, value, sum_row_format)
                                # For non-numeric columns in the sum row
                                else:
                                    worksheet.write(sum_row_index, col_num, '', sum_row_format)
                            except Exception as e:
                                # If there's an error, write 0 for numeric columns or empty for others
                                if col in numeric_cols:
                                    worksheet.write(sum_row_index, col_num, 0, sum_row_format)
                                else:
                                    worksheet.write(sum_row_index, col_num, '', sum_row_format)
        else:
            # Create new file with RTL support

            # Add a single sum row for numeric columns at the end of the workbook
            numeric_cols = new_df.select_dtypes(include=['number']).columns
            if len(numeric_cols) > 0:
                sum_row = new_df[numeric_cols].sum().to_dict()
                sum_row_df = pd.DataFrame([sum_row])

                # Add a label for the sum row in the first column if it exists
                if len(new_df.columns) > 0:
                    first_col = new_df.columns[0]
                    sum_row_df[first_col] = 'المجموع'

                # Concatenate the sum row to the DataFrame
                new_df = pd.concat([new_df, sum_row_df], ignore_index=True)

            with pd.ExcelWriter(excel_path, engine='xlsxwriter', engine_kwargs={'options': {'nan_inf_to_errors': True}}) as writer:
                new_df.to_excel(writer, index=False, sheet_name='البيانات')

                # Get the xlsxwriter workbook and worksheet objects
                workbook = writer.book
                worksheet = writer.sheets['البيانات']

                # Set RTL direction for the worksheet
                worksheet.right_to_left()

                # Freeze the header row so it stays visible when scrolling
                worksheet.freeze_panes(1, 0)

                # Format headers with black bold fonts and proper background
                header_format = workbook.add_format({
                    'bold': True,
                    'font_color': 'black',
                    'text_wrap': True,
                    'valign': 'top',
                    'align': 'center',
                    'border': 1,
                    'bg_color': '#E6E6FA'  # Light background for headers
                })

                # Apply header format
                for col_num, value in enumerate(new_df.columns.values):
                    worksheet.write(0, col_num, value, header_format)

                # Format the sum row with bold, background color, and borders if it exists
                if len(numeric_cols) > 0:
                    # Create a format for the sum row with amber background and black bold fonts
                    sum_row_format = workbook.add_format({
                        'bold': True,
                        'font_color': 'black',
                        'bg_color': '#FFC000',  # Amber background color
                        'border': 1,
                        'border_color': '#000000',
                        'align': 'center',
                        'num_format': '0'  # Format numbers without decimal places
                    })

                    # Special format for the "المجموع" (Total) cell
                    total_cell_format = workbook.add_format({
                        'bold': True,
                        'font_color': 'black',
                        'bg_color': '#FFC000',  # Amber background color
                        'border': 1,
                        'border_color': '#000000',
                        'align': 'center',
                        'font_size': 12
                    })

                    sum_row_index = len(new_df)
                    for col_num, col in enumerate(new_df.columns):
                        try:
                            value = new_df.iloc[sum_row_index-1, col_num]

                            # Check if this is the first column (with "المجموع" text)
                            if col_num == 0:
                                worksheet.write(sum_row_index, col_num, value, total_cell_format)
                            # For numeric columns, ensure proper formatting
                            elif col in numeric_cols:
                                if pd.isna(value) or (hasattr(value, 'dtype') and pd.api.types.is_float_dtype(value.dtype) and (np.isinf(value) or np.isnan(value))):
                                    value = 0
                                worksheet.write(sum_row_index, col_num, value, sum_row_format)
                            # For non-numeric columns in the sum row
                            else:
                                worksheet.write(sum_row_index, col_num, '', sum_row_format)
                        except Exception as e:
                            # If there's an error, write 0 for numeric columns or empty for others
                            if col in numeric_cols:
                                worksheet.write(sum_row_index, col_num, 0, sum_row_format)
                            else:
                                worksheet.write(sum_row_index, col_num, '', sum_row_format)

                # Auto-adjust column widths
                for i, col in enumerate(new_df.columns):
                    try:
                        col_data = new_df[col].fillna('').astype(str)
                        max_data_len = col_data.str.len().max() if not col_data.empty else 0
                        col_len = len(str(col))
                        max_len = max(max_data_len, col_len) + 2
                        max_len = min(max_len, 50)  # Set a reasonable maximum width
                        worksheet.set_column(i, i, max_len)
                    except Exception as e:
                        worksheet.set_column(i, i, 15)  # Default width

        return (True, "تم حفظ البيانات بنجاح")

    except Exception as e:
        print(f"Error saving to Excel {excel_path}: {e}")
        import traceback
        traceback.print_exc()

        # Try to save to a fallback location as a last resort
        try:
            # Create a fallback directory
            fallback_dir = os.path.join('media', 'fallback')
            os.makedirs(fallback_dir, exist_ok=True)

            # Create a unique filename with timestamp
            import time
            timestamp = int(time.time())
            if form_type:
                fallback_filename = f"{form_type}_fallback_{timestamp}.xlsx"
            else:
                fallback_filename = f"data_fallback_{timestamp}.xlsx"

            fallback_path = os.path.join(fallback_dir, fallback_filename)

            # Create DataFrame with the new data
            new_df = pd.DataFrame([new_data])

            # Save to fallback location
            new_df.to_excel(fallback_path, index=False)

            return (False, f"حدث خطأ أثناء حفظ البيانات في المسار الأصلي، تم حفظ البيانات في: {fallback_path}")
        except Exception as fallback_error:
            print(f"Error saving to fallback location: {fallback_error}")
            traceback.print_exc()
            return (False, f"حدث خطأ أثناء حفظ البيانات: {str(e)} - وفشل الحفظ الاحتياطي أيضًا: {str(fallback_error)}")

def read_from_excel(excel_path):
    """
    Read data from Excel file with proper error handling

    Args:
        excel_path (str): Path to the Excel file

    Returns:
        tuple: (success, data or error message)
    """
    try:
        # Check if file exists and is accessible
        if os.path.exists(excel_path):
            try:
                # Print file details for debugging
                print(f"Found Excel file at: {excel_path}")
                print(f"File size: {os.path.getsize(excel_path)} bytes")

                # Read Excel file with explicit engine specification
                try:
                    # Try with openpyxl engine first (better for newer Excel files)
                    df = pd.read_excel(excel_path, engine='openpyxl')
                except Exception as e1:
                    print(f"Failed to read with openpyxl: {e1}")
                    try:
                        # Fall back to xlrd engine (better for older Excel files)
                        df = pd.read_excel(excel_path, engine='xlrd')
                    except Exception as e2:
                        print(f"Failed to read with xlrd: {e2}")
                        # Last resort - try without specifying engine
                        df = pd.read_excel(excel_path)

                # Print dataframe info for debugging
                print(f"Successfully read Excel file. Shape: {df.shape}")
                print(f"Columns: {df.columns.tolist()}")

                return (True, df)
            except (PermissionError, OSError) as e:
                if isinstance(e, PermissionError) or (hasattr(e, 'errno') and e.errno == errno.EACCES):
                    # Handle permission error
                    print(f"Permission denied when accessing {excel_path}: {e}")
                    return (False, f"ليس لديك إذن للوصول إلى الملف: {excel_path}")
                else:
                    # Handle other OS errors
                    print(f"OS error when accessing {excel_path}: {e}")
                    return (False, f"حدث خطأ في نظام التشغيل عند محاولة الوصول إلى الملف: {str(e)}")
            except Exception as e:
                # Handle other errors
                print(f"Error reading Excel file {excel_path}: {e}")
                import traceback
                traceback.print_exc()
                return (False, f"حدث خطأ أثناء قراءة الملف: {str(e)}")
        else:
            # File doesn't exist
            print(f"File does not exist: {excel_path}")
            return (False, f"الملف غير موجود: {excel_path}")

    except Exception as e:
        print(f"Error accessing Excel {excel_path}: {e}")
        import traceback
        traceback.print_exc()
        return (False, f"حدث خطأ أثناء الوصول إلى الملف: {str(e)}")
