from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.models import User
from .models import UserProfile, InterventionUnit, WILAYA_CHOICES

class UserCreationFormWithWilaya(UserCreationForm):
    wilaya = forms.ChoiceField(choices=WILAYA_CHOICES, label="الولاية")
    role = forms.ChoiceField(choices=UserProfile.ROLES, label="الدور")

    class Meta:
        model = User
        fields = ('username', 'password1', 'password2', 'wilaya', 'role')

class InterventionUnitForm(forms.ModelForm):
    class Meta:
        model = InterventionUnit
        fields = ('code', 'name', 'wilaya')
        labels = {
            'code': 'الكود',
            'name': 'اسم الوحدة',
            'wilaya': 'الولاية'
        }
        widgets = {
            'wilaya': forms.Select(choices=WILAYA_CHOICES)
        }
