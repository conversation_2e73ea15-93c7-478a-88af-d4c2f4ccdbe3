"""
Data Statistics Service
Clean implementation for generating statistics from various data models
"""

import datetime
from django.db.models import Sum, Count, Q
from django.core.exceptions import ObjectDoesNotExist

from .models import (
    GeneralFireData, ResidentialFireData, PublicAreaFireData,
    ForestAgriculturalFireData, MiscOperationsData, InstitutionalFireData,
    TrafficAccident, InterventionUnit
)
from data_entry.models import MedicalEvacuation, InterventionType, InterventionNature


class StatisticsService:
    """Service class for generating statistics from various data models"""

    def __init__(self, user, is_admin=False):
        self.user = user
        self.is_admin = is_admin
        self.user_wilaya = getattr(user.userprofile, 'wilaya', None) if hasattr(user, 'userprofile') else None

    def get_user_units(self):
        """Get intervention units based on user permissions"""
        if self.is_admin:
            return InterventionUnit.objects.all().order_by('wilaya', 'name')
        elif self.user_wilaya:
            return InterventionUnit.objects.filter(wilaya=self.user_wilaya).order_by('name')
        return InterventionUnit.objects.none()

    def apply_base_filters(self, filters):
        """Apply common filters like date, wilaya, unit"""
        base_filters = {}

        # Date filters
        if filters.get('year'):
            year = int(filters['year'])
            start_date = datetime.date(year, 1, 1)
            end_date = datetime.date(year, 12, 31)
            base_filters['date__range'] = (start_date, end_date)

        if filters.get('month'):
            base_filters['date__month'] = int(filters['month'])

        # Unit filters
        user_units = self.get_user_units()
        if filters.get('unit'):
            base_filters['intervening_unit'] = filters['unit']
        elif filters.get('wilaya') and self.is_admin:
            wilaya_units = InterventionUnit.objects.filter(wilaya=filters['wilaya']).values_list('name', flat=True)
            base_filters['intervening_unit__in'] = list(wilaya_units)
        elif not self.is_admin and self.user_wilaya:
            base_filters['intervening_unit__in'] = list(user_units.values_list('name', flat=True))

        return base_filters

    def get_medical_evacuation_statistics(self, filters):
        """Get statistics for medical evacuation data"""
        try:
            # Convert base filters for medical evacuation model
            medical_filters = {}
            base_filters = self.apply_base_filters(filters)

            for key, value in base_filters.items():
                if key == 'intervening_unit':
                    try:
                        unit_obj = InterventionUnit.objects.get(name=value)
                        medical_filters['unit'] = unit_obj
                    except InterventionUnit.DoesNotExist:
                        continue
                elif key == 'intervening_unit__in':
                    unit_objs = InterventionUnit.objects.filter(name__in=value)
                    if unit_objs.exists():
                        medical_filters['unit__in'] = unit_objs
                else:
                    medical_filters[key] = value

            # Apply specific filters
            if filters.get('intervention_type'):
                medical_filters['intervention_type__name'] = filters['intervention_type']
            if filters.get('intervention_nature'):
                medical_filters['intervention_nature__name'] = filters['intervention_nature']

            # Get filtered data
            evacuations = MedicalEvacuation.objects.filter(**medical_filters)

            # Calculate aggregated statistics
            stats = evacuations.aggregate(
                total_operations=Sum('operations_count'),
                total_interventions=Sum('interventions_count'),
                total_paramedics_children=Sum('paramedics_children_count'),
                total_paramedics_women=Sum('paramedics_women_count'),
                total_paramedics_men=Sum('paramedics_men_count'),
                total_deaths_children=Sum('deaths_children_count'),
                total_deaths_women=Sum('deaths_women_count'),
                total_deaths_men=Sum('deaths_men_count'),
                total_records=Count('id')
            )

            # Calculate totals
            total_paramedics = (stats['total_paramedics_children'] or 0) + \
                             (stats['total_paramedics_women'] or 0) + \
                             (stats['total_paramedics_men'] or 0)
            total_deaths = (stats['total_deaths_children'] or 0) + \
                          (stats['total_deaths_women'] or 0) + \
                          (stats['total_deaths_men'] or 0)

            # Group by intervention type
            intervention_types = {}
            for evacuation in evacuations:
                type_name = evacuation.intervention_type.name if evacuation.intervention_type else "غير محدد"
                intervention_types[type_name] = intervention_types.get(type_name, 0) + evacuation.operations_count

            # Group by intervention nature
            intervention_natures = {}
            deaths_by_nature = {}
            paramedics_by_nature = {}

            for evacuation in evacuations:
                nature_name = evacuation.intervention_nature.name if evacuation.intervention_nature else "غير محدد"
                intervention_natures[nature_name] = intervention_natures.get(nature_name, 0) + evacuation.operations_count

                # Calculate deaths by nature
                total_deaths_for_record = (evacuation.deaths_children_count or 0) + (evacuation.deaths_women_count or 0) + (evacuation.deaths_men_count or 0)
                deaths_by_nature[nature_name] = deaths_by_nature.get(nature_name, 0) + total_deaths_for_record

                # Calculate paramedics by nature
                total_paramedics_for_record = (evacuation.paramedics_children_count or 0) + (evacuation.paramedics_women_count or 0) + (evacuation.paramedics_men_count or 0)
                paramedics_by_nature[nature_name] = paramedics_by_nature.get(nature_name, 0) + total_paramedics_for_record

            # Group by location
            locations = {'داخل المنزل': 0, 'خارج المنزل': 0}
            for evacuation in evacuations:
                if evacuation.is_indoor:
                    locations['داخل المنزل'] += evacuation.operations_count
                else:
                    locations['خارج المنزل'] += evacuation.operations_count

            return {
                'total_operations': stats['total_operations'] or 0,
                'total_interventions': stats['total_interventions'] or 0,
                'total_deaths': total_deaths,
                'total_paramedics': total_paramedics,
                'total_paramedics_detailed': total_paramedics,
                'total_paramedics_children': stats['total_paramedics_children'] or 0,
                'total_paramedics_women': stats['total_paramedics_women'] or 0,
                'total_paramedics_men': stats['total_paramedics_men'] or 0,
                'total_deaths_detailed': total_deaths,
                'total_deaths_children': stats['total_deaths_children'] or 0,
                'total_deaths_women': stats['total_deaths_women'] or 0,
                'total_deaths_men': stats['total_deaths_men'] or 0,
                'intervention_types': intervention_types,
                'intervention_natures': intervention_natures,
                'locations': locations,
                'medical_stats': {'المسعفين': total_paramedics, 'الوفيات': total_deaths},
                'paramedics_breakdown': {
                    'أطفال': stats['total_paramedics_children'] or 0,
                    'نساء': stats['total_paramedics_women'] or 0,
                    'رجال': stats['total_paramedics_men'] or 0
                },
                'deaths_breakdown': {
                    'أطفال': stats['total_deaths_children'] or 0,
                    'نساء': stats['total_deaths_women'] or 0,
                    'رجال': stats['total_deaths_men'] or 0
                },
                'deaths_by_nature': deaths_by_nature,
                'paramedics_by_nature': paramedics_by_nature,
                'total_records': stats['total_records'] or 0
            }

        except Exception as e:
            print(f"Error in get_medical_evacuation_statistics: {e}")
            return self._get_empty_medical_stats()

    def _get_empty_medical_stats(self):
        """Return empty medical evacuation statistics"""
        return {
            'total_operations': 0,
            'total_interventions': 0,
            'total_deaths': 0,
            'total_paramedics': 0,
            'total_paramedics_detailed': 0,
            'total_paramedics_children': 0,
            'total_paramedics_women': 0,
            'total_paramedics_men': 0,
            'total_deaths_detailed': 0,
            'total_deaths_children': 0,
            'total_deaths_women': 0,
            'total_deaths_men': 0,
            'intervention_types': {},
            'intervention_natures': {},
            'locations': {'داخل المنزل': 0, 'خارج المنزل': 0},
            'medical_stats': {'المسعفين': 0, 'الوفيات': 0},
            'paramedics_breakdown': {'أطفال': 0, 'نساء': 0, 'رجال': 0},
            'deaths_breakdown': {'أطفال': 0, 'نساء': 0, 'رجال': 0},
            'total_records': 0
        }

    def get_traffic_accident_statistics(self, filters):
        """Get statistics for traffic accident data"""
        try:
            # Convert base filters for traffic accident model
            traffic_filters = {}
            base_filters = self.apply_base_filters(filters)

            for key, value in base_filters.items():
                if key == 'intervening_unit':
                    traffic_filters['unit'] = value
                elif key == 'intervening_unit__in':
                    traffic_filters['unit__in'] = value
                else:
                    traffic_filters[key] = value

            # Apply specific filters
            if filters.get('accident_type'):
                traffic_filters['accident_type'] = filters['accident_type']
            if filters.get('accident_nature'):
                traffic_filters['accident_nature'] = filters['accident_nature']

            # Get filtered data
            accidents = TrafficAccident.objects.filter(**traffic_filters)

            # Calculate aggregated statistics
            stats = accidents.aggregate(
                total_accidents=Sum('accidents_count'),
                total_casualties_men=Sum('casualties_men'),
                total_casualties_women=Sum('casualties_women'),
                total_casualties_children=Sum('casualties_children'),
                total_fatalities_men=Sum('fatalities_men'),
                total_fatalities_women=Sum('fatalities_women'),
                total_fatalities_children=Sum('fatalities_children'),
                total_fuel_cars=Sum('fuel_cars'),
                total_lpg_cars=Sum('lpg_cars'),
                total_trucks=Sum('trucks'),
                total_buses=Sum('buses'),
                total_motorcycles=Sum('motorcycles'),
                total_tractors=Sum('tractors'),
                total_directed_transport=Sum('directed_transport'),
                total_other_vehicles=Sum('other_vehicles'),
                total_records=Count('id')
            )

            # Calculate totals
            total_injuries = (stats['total_casualties_men'] or 0) + \
                           (stats['total_casualties_women'] or 0) + \
                           (stats['total_casualties_children'] or 0)
            total_deaths = (stats['total_fatalities_men'] or 0) + \
                          (stats['total_fatalities_women'] or 0) + \
                          (stats['total_fatalities_children'] or 0)
            total_material_losses = (stats['total_fuel_cars'] or 0) + \
                                  (stats['total_lpg_cars'] or 0) + \
                                  (stats['total_trucks'] or 0) + \
                                  (stats['total_buses'] or 0) + \
                                  (stats['total_motorcycles'] or 0) + \
                                  (stats['total_tractors'] or 0) + \
                                  (stats['total_directed_transport'] or 0) + \
                                  (stats['total_other_vehicles'] or 0)

            # Group by accident type
            accident_types = {}
            for accident in accidents:
                type_name = accident.accident_type or "غير محدد"
                accident_types[type_name] = accident_types.get(type_name, 0) + (accident.accidents_count or 0)

            # Group by accident nature
            accident_natures = {}
            for accident in accidents:
                nature_name = accident.accident_nature or "غير محدد"
                accident_natures[nature_name] = accident_natures.get(nature_name, 0) + (accident.accidents_count or 0)

            # Group by time periods
            time_periods = {}
            for accident in accidents:
                time_periods['06:00 - 09:00'] = time_periods.get('06:00 - 09:00', 0) + (accident.time_06_09 or 0)
                time_periods['09:00 - 12:00'] = time_periods.get('09:00 - 12:00', 0) + (accident.time_09_12 or 0)
                time_periods['12:00 - 14:00'] = time_periods.get('12:00 - 14:00', 0) + (accident.time_12_14 or 0)
                time_periods['14:00 - 16:00'] = time_periods.get('14:00 - 16:00', 0) + (accident.time_14_16 or 0)
                time_periods['16:00 - 20:00'] = time_periods.get('16:00 - 20:00', 0) + (accident.time_16_20 or 0)
                time_periods['20:00 - 00:00'] = time_periods.get('20:00 - 00:00', 0) + (accident.time_20_00 or 0)
                time_periods['00:00 - 06:00'] = time_periods.get('00:00 - 06:00', 0) + (accident.time_00_06 or 0)

            # Group by days of week
            days_of_week = {}
            for accident in accidents:
                days_of_week['الأحد'] = days_of_week.get('الأحد', 0) + (accident.sunday or 0)
                days_of_week['الاثنين'] = days_of_week.get('الاثنين', 0) + (accident.monday or 0)
                days_of_week['الثلاثاء'] = days_of_week.get('الثلاثاء', 0) + (accident.tuesday or 0)
                days_of_week['الأربعاء'] = days_of_week.get('الأربعاء', 0) + (accident.wednesday or 0)
                days_of_week['الخميس'] = days_of_week.get('الخميس', 0) + (accident.thursday or 0)
                days_of_week['الجمعة'] = days_of_week.get('الجمعة', 0) + (accident.friday or 0)
                days_of_week['السبت'] = days_of_week.get('السبت', 0) + (accident.saturday or 0)

            # Group by driver categories
            driver_categories = {}
            for accident in accidents:
                category = accident.driver_age or "غير محدد"
                driver_categories[category] = driver_categories.get(category, 0) + (accident.accidents_count or 0)

            # Group by road types
            road_types = {}
            for accident in accidents:
                road_type = accident.road_type or "غير محدد"
                road_types[road_type] = road_types.get(road_type, 0) + (accident.accidents_count or 0)

            # Injuries by accident type and gender
            injuries_by_type_gender = {}
            deaths_by_type_gender = {}
            for accident in accidents:
                acc_type = accident.accident_type or "غير محدد"
                if acc_type not in injuries_by_type_gender:
                    injuries_by_type_gender[acc_type] = {'رجال': 0, 'نساء': 0, 'أطفال': 0}
                    deaths_by_type_gender[acc_type] = {'رجال': 0, 'نساء': 0, 'أطفال': 0}

                injuries_by_type_gender[acc_type]['رجال'] += accident.casualties_men or 0
                injuries_by_type_gender[acc_type]['نساء'] += accident.casualties_women or 0
                injuries_by_type_gender[acc_type]['أطفال'] += accident.casualties_children or 0

                deaths_by_type_gender[acc_type]['رجال'] += accident.fatalities_men or 0
                deaths_by_type_gender[acc_type]['نساء'] += accident.fatalities_women or 0
                deaths_by_type_gender[acc_type]['أطفال'] += accident.fatalities_children or 0

            return {
                'total_accidents': stats['total_accidents'] or 0,
                'total_operations': stats['total_accidents'] or 0,  # Changed from total_records
                'total_injuries': total_injuries,
                'total_deaths': total_deaths,
                'total_material_losses': total_material_losses,
                'accident_types': accident_types,
                'accident_natures': accident_natures,
                'human_losses': {'الجرحى': total_injuries, 'الوفيات': total_deaths},
                'human_losses_detailed': {
                    'عدد الجرحى رجال': stats['total_casualties_men'] or 0,
                    'عدد الجرحى نساء': stats['total_casualties_women'] or 0,
                    'عدد الجرحى أطفال': stats['total_casualties_children'] or 0,
                    'مجموع الجرحى': total_injuries,
                    'عدد الوفيات رجال': stats['total_fatalities_men'] or 0,
                    'عدد الوفيات نساء': stats['total_fatalities_women'] or 0,
                    'عدد الوفيات أطفال': stats['total_fatalities_children'] or 0,
                    'مجموع الوفيات': total_deaths
                },
                'material_losses': {
                    'سيارات وقود': stats['total_fuel_cars'] or 0,
                    'سيارات غاز مميع': stats['total_lpg_cars'] or 0,
                    'شاحنات': stats['total_trucks'] or 0,
                    'حافلات': stats['total_buses'] or 0,
                    'دراجات': stats['total_motorcycles'] or 0,
                    'جرارات': stats['total_tractors'] or 0,
                    'النقل موجه': stats['total_directed_transport'] or 0,
                    'أخرى': stats['total_other_vehicles'] or 0
                },
                'time_periods': time_periods,
                'days_of_week': days_of_week,
                'driver_categories': driver_categories,
                'road_types': road_types,
                'injuries_by_type_gender': injuries_by_type_gender,
                'deaths_by_type_gender': deaths_by_type_gender,
                'total_records': stats['total_records'] or 0
            }

        except Exception as e:
            print(f"Error in get_traffic_accident_statistics: {e}")
            return self._get_empty_traffic_stats()

    def _get_empty_traffic_stats(self):
        """Return empty traffic accident statistics"""
        return {
            'total_accidents': 0,
            'total_operations': 0,
            'total_injuries': 0,
            'total_deaths': 0,
            'total_material_losses': 0,
            'accident_types': {},
            'accident_natures': {},
            'human_losses': {'الجرحى': 0, 'الوفيات': 0},
            'human_losses_detailed': {},
            'material_losses': {},
            'time_periods': {},
            'days_of_week': {},
            'driver_categories': {},
            'road_types': {},
            'injuries_by_type_gender': {},
            'deaths_by_type_gender': {},
            'total_records': 0
        }

    def get_general_fire_statistics(self, filters):
        """Get statistics for general fire data"""
        try:
            base_filters = self.apply_base_filters(filters)

            # Apply specific filters
            if filters.get('fire_type'):
                base_filters['fire_type'] = filters['fire_type']

            # Get filtered data
            fires = GeneralFireData.objects.filter(**base_filters)

            # Calculate aggregated statistics
            stats = fires.aggregate(
                total_fires=Sum('number_of_fires'),
                total_interventions=Sum('number_of_interventions'),
                total_injuries=Sum('number_of_injured'),
                total_deaths=Sum('number_of_deaths'),
                total_records=Count('id')
            )

            # Group by fire type
            fire_types = {}
            for fire in fires:
                type_name = fire.fire_type or "غير محدد"
                fire_types[type_name] = fire_types.get(type_name, 0) + (fire.number_of_fires or 0)

            return {
                'total_fires': stats['total_fires'] or 0,
                'total_interventions': stats['total_interventions'] or 0,
                'total_injuries': stats['total_injuries'] or 0,
                'total_deaths': stats['total_deaths'] or 0,
                'fire_types': fire_types,
                'casualties': {
                    'الجرحى': stats['total_injuries'] or 0,
                    'الوفيات': stats['total_deaths'] or 0
                },
                'total_records': stats['total_records'] or 0
            }

        except Exception as e:
            print(f"Error in get_general_fire_statistics: {e}")
            return self._get_empty_fire_stats()

    def get_misc_operations_statistics(self, filters):
        """Get statistics for misc operations data"""
        try:
            base_filters = self.apply_base_filters(filters)
            # Filter for misc operations specifically
            base_filters['operation_type__in'] = [
                'إنقاذ الأشخاص', 'إنقاذ الحيوانات', 'إزالة الأشجار المتساقطة',
                'إزالة الأجسام الغريبة', 'فتح الأبواب', 'أخرى'
            ]

            # Apply specific filters
            if filters.get('operation_type'):
                base_filters['operation_type'] = filters['operation_type']

            # Get filtered data
            operations = MiscOperationsData.objects.filter(**base_filters)

            # Calculate aggregated statistics
            stats = operations.aggregate(
                total_operations=Sum('number_of_operations'),
                total_interventions=Sum('number_of_interventions'),
                total_rescuers=Sum('number_of_rescuers'),
                total_deaths=Sum('number_of_deaths'),
                total_records=Count('id')
            )

            # Group by operation type
            operation_types = {}
            for operation in operations:
                type_name = operation.operation_type or "غير محدد"
                operation_types[type_name] = operation_types.get(type_name, 0) + (operation.number_of_operations or 0)

            # Monthly distribution
            monthly_distribution = {}
            for operation in operations:
                month_name = operation.date.strftime('%m') if operation.date else "غير محدد"
                monthly_distribution[month_name] = monthly_distribution.get(month_name, 0) + (operation.number_of_operations or 0)

            return {
                'total_operations': stats['total_operations'] or 0,
                'total_interventions': stats['total_interventions'] or 0,
                'total_rescuers': stats['total_rescuers'] or 0,
                'total_deaths': stats['total_deaths'] or 0,
                'operation_types': operation_types,
                'monthly_distribution': monthly_distribution,
                'total_records': stats['total_records'] or 0
            }

        except Exception as e:
            print(f"Error in get_misc_operations_statistics: {e}")
            return self._get_empty_operations_stats()

    def get_security_device_statistics(self, filters):
        """Get statistics for security device data"""
        try:
            base_filters = self.apply_base_filters(filters)
            # Filter for security device operations specifically
            base_filters['operation_type__in'] = [
                'النشاطات الثقافية و الدينية', 'المؤسسات التربوية', 'النظام الأمن العام',
                'الزيارات الرسمية', 'النشاطات الرياضية'
            ]

            # Apply specific filters
            if filters.get('operation_type'):
                base_filters['operation_type'] = filters['operation_type']

            # Get filtered data
            operations = MiscOperationsData.objects.filter(**base_filters)

            # Calculate aggregated statistics
            stats = operations.aggregate(
                total_operations=Sum('number_of_operations'),
                total_interventions=Sum('number_of_interventions'),
                total_rescuers=Sum('number_of_rescuers'),
                total_deaths=Sum('number_of_deaths'),
                total_records=Count('id')
            )

            # Group by security device type
            security_device_types = {}
            for operation in operations:
                type_name = operation.operation_type or "غير محدد"
                security_device_types[type_name] = security_device_types.get(type_name, 0) + (operation.number_of_operations or 0)

            # Monthly distribution
            monthly_distribution = {}
            for operation in operations:
                month_name = operation.date.strftime('%m') if operation.date else "غير محدد"
                monthly_distribution[month_name] = monthly_distribution.get(month_name, 0) + (operation.number_of_operations or 0)

            return {
                'total_operations': stats['total_operations'] or 0,
                'total_interventions': stats['total_interventions'] or 0,
                'total_rescuers': stats['total_rescuers'] or 0,
                'total_deaths': stats['total_deaths'] or 0,
                'security_device_types': security_device_types,
                'monthly_distribution': monthly_distribution,
                'total_records': stats['total_records'] or 0
            }

        except Exception as e:
            print(f"Error in get_security_device_statistics: {e}")
            return self._get_empty_operations_stats()

    def get_exceptional_operations_statistics(self, filters):
        """Get statistics for exceptional operations data"""
        try:
            base_filters = self.apply_base_filters(filters)
            # Filter for exceptional operations specifically
            base_filters['operation_type__in'] = [
                'إنقاذ الحيوانات', 'عمليات استثنائية أخرى'
            ]

            # Apply specific filters
            if filters.get('operation_type'):
                base_filters['operation_type'] = filters['operation_type']

            # Get filtered data
            operations = MiscOperationsData.objects.filter(**base_filters)

            # Calculate aggregated statistics
            stats = operations.aggregate(
                total_operations=Sum('number_of_operations'),
                total_interventions=Sum('number_of_interventions'),
                total_ambulances=Sum('ambulances'),
                total_fire_trucks=Sum('fire_trucks'),
                total_mechanical_ladders=Sum('mechanical_ladders'),
                total_other_resources=Sum('other_resources'),
                total_records=Count('id')
            )

            # Group by operation type
            exceptional_operation_types = {}
            for operation in operations:
                type_name = operation.operation_type or "غير محدد"
                exceptional_operation_types[type_name] = exceptional_operation_types.get(type_name, 0) + (operation.number_of_operations or 0)

            # Resource distribution
            resource_distribution = {
                'سيارة إسعاف': stats['total_ambulances'] or 0,
                'شاحنة إطفاء': stats['total_fire_trucks'] or 0,
                'السلم الميكانيكي': stats['total_mechanical_ladders'] or 0,
                'أخرى': stats['total_other_resources'] or 0
            }

            return {
                'total_operations': stats['total_operations'] or 0,
                'total_interventions': stats['total_interventions'] or 0,
                'exceptional_operation_types': exceptional_operation_types,
                'resource_distribution': resource_distribution,
                'total_records': stats['total_records'] or 0
            }

        except Exception as e:
            print(f"Error in get_exceptional_operations_statistics: {e}")
            return self._get_empty_operations_stats()

    def get_interventions_without_work_statistics(self, filters):
        """Get statistics for interventions without work data"""
        try:
            base_filters = self.apply_base_filters(filters)
            # Filter for interventions without work specifically
            base_filters['operation_type__in'] = [
                'إنذار كاذب', 'عدم وجود خطر', 'تدخل غير مبرر', 'أخرى'
            ]

            # Apply specific filters
            if filters.get('operation_type'):
                base_filters['operation_type'] = filters['operation_type']

            # Get filtered data
            operations = MiscOperationsData.objects.filter(**base_filters)

            # Calculate aggregated statistics
            stats = operations.aggregate(
                total_operations=Sum('number_of_operations'),
                total_cars=Sum('cars'),
                total_trucks=Sum('trucks'),
                total_other_resources=Sum('other_resources'),
                total_records=Count('id')
            )

            # Group by intervention type
            intervention_types = {}
            for operation in operations:
                type_name = operation.operation_type or "غير محدد"
                intervention_types[type_name] = intervention_types.get(type_name, 0) + (operation.number_of_operations or 0)

            # Vehicle distribution
            vehicle_distribution = {
                'سيارة': stats['total_cars'] or 0,
                'شاحنة': stats['total_trucks'] or 0,
                'أخرى': stats['total_other_resources'] or 0
            }

            return {
                'total_operations': stats['total_operations'] or 0,
                'intervention_types': intervention_types,
                'vehicle_distribution': vehicle_distribution,
                'total_records': stats['total_records'] or 0
            }

        except Exception as e:
            print(f"Error in get_interventions_without_work_statistics: {e}")
            return self._get_empty_operations_stats()

    def _get_empty_fire_stats(self):
        """Return empty fire statistics"""
        return {
            'total_fires': 0,
            'total_interventions': 0,
            'total_injuries': 0,
            'total_deaths': 0,
            'fire_types': {},
            'casualties': {'الجرحى': 0, 'الوفيات': 0},
            'total_records': 0
        }

    def _get_empty_operations_stats(self):
        """Return empty operations statistics"""
        return {
            'total_operations': 0,
            'total_interventions': 0,
            'total_rescuers': 0,
            'total_deaths': 0,
            'operation_types': {},
            'intervention_types': {},
            'monthly_distribution': {},
            'security_device_types': {},
            'exceptional_operation_types': {},
            'resource_distribution': {},
            'vehicle_distribution': {},
            'total_records': 0
        }

    def get_forest_agricultural_fire_statistics(self, filters):
        """Get statistics for forest agricultural fires with loss conversion"""
        try:
            base_filters = self.apply_base_filters(filters)

            # Apply specific filters
            if filters.get('fire_type'):
                base_filters['fire_type'] = filters['fire_type']

            # Get filtered data
            fires = ForestAgriculturalFireData.objects.filter(**base_filters)

            # Calculate aggregated statistics
            stats = fires.aggregate(
                total_fires=Sum('number_of_fires'),
                total_interventions=Sum('number_of_interventions'),
                total_hectares=Sum('losses_hectare'),
                total_ares=Sum('losses_are'),
                total_square_meters=Sum('losses_square_meter'),
                total_records=Count('id')
            )

            # Calculate losses with automatic conversion
            # 1 هكتار = 100 آر = 10000 متر مربع
            total_hectares = stats['total_hectares'] or 0
            total_ares = stats['total_ares'] or 0
            total_square_meters = stats['total_square_meters'] or 0

            # Convert ares to hectares if >= 100
            if total_ares >= 100:
                additional_hectares = total_ares // 100
                remaining_ares = total_ares % 100
                total_hectares += additional_hectares
                total_ares = remaining_ares

            # Convert square meters to ares if >= 10000
            if total_square_meters >= 10000:
                additional_ares = total_square_meters // 10000
                remaining_square_meters = total_square_meters % 10000
                total_ares += additional_ares
                total_square_meters = remaining_square_meters

                # Check again if ares need to be converted to hectares
                if total_ares >= 100:
                    additional_hectares = total_ares // 100
                    remaining_ares = total_ares % 100
                    total_hectares += additional_hectares
                    total_ares = remaining_ares

            # Group by fire type
            fire_types = {}
            fire_interventions = {}
            for fire in fires:
                type_name = fire.fire_type or "غير محدد"
                fire_types[type_name] = fire_types.get(type_name, 0) + (fire.number_of_fires or 0)
                fire_interventions[type_name] = fire_interventions.get(type_name, 0) + (fire.number_of_interventions or 0)

            # Group by loss type with conversion
            loss_types = {}
            for fire in fires:
                loss_type = fire.loss_type or "غير محدد"
                loss_types[loss_type] = loss_types.get(loss_type, 0) + (fire.number_of_fires or 0)

            # Detailed losses breakdown with converted values
            losses_detailed = {}
            if total_hectares > 0:
                losses_detailed['الخسائر بالهكتار'] = total_hectares
            if total_ares > 0:
                losses_detailed['الخسائر بالآر'] = total_ares
            if total_square_meters > 0:
                losses_detailed['الخسائر بالمتر مربع'] = total_square_meters

            return {
                'total_fires': stats['total_fires'] or 0,
                'total_interventions': stats['total_interventions'] or 0,
                'fire_types': fire_types,
                'fire_interventions': fire_interventions,
                'loss_types': loss_types,
                'losses_detailed': losses_detailed,
                'total_hectares': total_hectares,
                'total_ares': total_ares,
                'total_square_meters': total_square_meters,
                'conversion_note': 'تم تطبيق التحويل التلقائي: 100 آر = 1 هكتار، 10000 متر مربع = 1 هكتار',
                'total_records': stats['total_records'] or 0
            }

        except Exception as e:
            print(f"Error in get_forest_agricultural_fire_statistics: {e}")
            return self._get_empty_fire_stats()

    def get_residential_fire_statistics(self, filters):
        """Get statistics for residential fires"""
        try:
            base_filters = self.apply_base_filters(filters)
            fires = ResidentialFireData.objects.filter(**base_filters)

            stats = fires.aggregate(
                total_fires=Sum('number_of_fires'),
                total_interventions=Sum('number_of_interventions'),
                total_injuries=Sum('number_of_injured'),
                total_deaths=Sum('number_of_deaths'),
                total_records=Count('id')
            )

            # Group by fire type if available
            fire_types = {}
            for fire in fires:
                type_name = getattr(fire, 'fire_type', None) or "حرائق سكنية"
                fire_types[type_name] = fire_types.get(type_name, 0) + (fire.number_of_fires or 0)

            return {
                'total_fires': stats['total_fires'] or 0,
                'total_interventions': stats['total_interventions'] or 0,
                'total_injuries': stats['total_injuries'] or 0,
                'total_deaths': stats['total_deaths'] or 0,
                'fire_types': fire_types,
                'casualties': {
                    'الجرحى': stats['total_injuries'] or 0,
                    'الوفيات': stats['total_deaths'] or 0
                },
                'total_records': stats['total_records'] or 0
            }

        except Exception as e:
            print(f"Error in get_residential_fire_statistics: {e}")
            return self._get_empty_fire_stats()

    def get_institutional_fire_statistics(self, filters):
        """Get statistics for institutional fires"""
        try:
            base_filters = self.apply_base_filters(filters)
            fires = InstitutionalFireData.objects.filter(**base_filters)

            stats = fires.aggregate(
                total_fires=Sum('number_of_fires'),
                total_interventions=Sum('number_of_interventions'),
                total_injuries=Sum('number_of_injured'),
                total_deaths=Sum('number_of_deaths'),
                total_records=Count('id')
            )

            # Group by fire type if available
            fire_types = {}
            for fire in fires:
                type_name = getattr(fire, 'fire_type', None) or "حرائق مؤسسات"
                fire_types[type_name] = fire_types.get(type_name, 0) + (fire.number_of_fires or 0)

            return {
                'total_fires': stats['total_fires'] or 0,
                'total_interventions': stats['total_interventions'] or 0,
                'total_injuries': stats['total_injuries'] or 0,
                'total_deaths': stats['total_deaths'] or 0,
                'fire_types': fire_types,
                'casualties': {
                    'الجرحى': stats['total_injuries'] or 0,
                    'الوفيات': stats['total_deaths'] or 0
                },
                'total_records': stats['total_records'] or 0
            }

        except Exception as e:
            print(f"Error in get_institutional_fire_statistics: {e}")
            return self._get_empty_fire_stats()

    def get_public_area_fire_statistics(self, filters):
        """Get statistics for public area fires"""
        try:
            base_filters = self.apply_base_filters(filters)
            fires = PublicAreaFireData.objects.filter(**base_filters)

            stats = fires.aggregate(
                total_fires=Sum('number_of_fires'),
                total_interventions=Sum('number_of_interventions'),
                total_injuries=Sum('number_of_injured'),
                total_deaths=Sum('number_of_deaths'),
                total_records=Count('id')
            )

            # Group by fire type if available
            fire_types = {}
            for fire in fires:
                type_name = getattr(fire, 'fire_type', None) or "حرائق أماكن عامة"
                fire_types[type_name] = fire_types.get(type_name, 0) + (fire.number_of_fires or 0)

            return {
                'total_fires': stats['total_fires'] or 0,
                'total_interventions': stats['total_interventions'] or 0,
                'total_injuries': stats['total_injuries'] or 0,
                'total_deaths': stats['total_deaths'] or 0,
                'fire_types': fire_types,
                'casualties': {
                    'الجرحى': stats['total_injuries'] or 0,
                    'الوفيات': stats['total_deaths'] or 0
                },
                'total_records': stats['total_records'] or 0
            }

        except Exception as e:
            print(f"Error in get_public_area_fire_statistics: {e}")
            return self._get_empty_fire_stats()

    def get_statistics_for_table_type(self, table_type, filters):
        """Main method to get statistics for any table type"""
        try:
            if table_type == 'medical_evacuation':
                return self.get_medical_evacuation_statistics(filters)
            elif table_type == 'traffic_accidents':
                return self.get_traffic_accident_statistics(filters)
            elif table_type == 'general_fire':
                return self.get_general_fire_statistics(filters)
            elif table_type == 'forest_agricultural_fires':
                return self.get_forest_agricultural_fire_statistics(filters)
            elif table_type == 'residential_fires':
                return self.get_residential_fire_statistics(filters)
            elif table_type == 'institutional_fires':
                return self.get_institutional_fire_statistics(filters)
            elif table_type == 'public_area_fires':
                return self.get_public_area_fire_statistics(filters)
            elif table_type == 'misc_operations':
                return self.get_misc_operations_statistics(filters)
            elif table_type == 'security_device':
                return self.get_security_device_statistics(filters)
            elif table_type == 'exceptional_operations':
                return self.get_exceptional_operations_statistics(filters)
            elif table_type == 'interventions_without_work':
                return self.get_interventions_without_work_statistics(filters)
            else:
                return {}
        except Exception as e:
            print(f"Error getting statistics for {table_type}: {e}")
            return {}
