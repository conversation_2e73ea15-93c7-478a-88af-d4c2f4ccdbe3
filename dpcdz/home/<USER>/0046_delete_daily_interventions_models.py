# Generated manually on 2025-08-01 15:30

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0044_agriculturalfiredetail_material_damage_notes'),
    ]

    operations = [
        # حذف الجداول مباشرة من قاعدة البيانات
        migrations.RunSQL(
            "DROP TABLE IF EXISTS home_interventioncasualty;",
            reverse_sql="-- Cannot reverse this operation"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS home_interventionreport;",
            reverse_sql="-- Cannot reverse this operation"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS home_agriculturalfiredetail;",
            reverse_sql="-- Cannot reverse this operation"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS home_buildingfiredetail;",
            reverse_sql="-- Cannot reverse this operation"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS home_trafficaccidentdetail;",
            reverse_sql="-- Cannot reverse this operation"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS home_medicalevacuationdetail;",
            reverse_sql="-- Cannot reverse this operation"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS home_interventionvehicle;",
            reverse_sql="-- Cannot reverse this operation"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS home_vehicleinterventionstatus;",
            reverse_sql="-- Cannot reverse this operation"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS home_dailyintervention;",
            reverse_sql="-- Cannot reverse this operation"
        ),
    ]
