# Generated by Django 5.2 on 2025-08-01 19:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0050_restore_daily_intervention'),
    ]

    operations = [
        migrations.AddField(
            model_name='dailyintervention',
            name='accident_nature',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='طبيعة الحادث'),
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='accident_specific_location',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='موقع الحادث المحدد'),
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='accident_type',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='نوع الحادث'),
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='affected_families_count',
            field=models.IntegerField(blank=True, null=True, verbose_name='عدد العائلات المتأثرة'),
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='assistance_provided',
            field=models.TextField(blank=True, null=True, verbose_name='المساعدات المقدمة للسكان'),
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='assistance_provided_building',
            field=models.TextField(blank=True, null=True, verbose_name='المساعدات المقدمة للسكان'),
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='building_fire_location',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='موقع الحريق'),
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='building_fire_nature',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='طبيعة الحريق'),
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='building_fire_type',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='نوع الحريق'),
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='crop_types',
            field=models.TextField(blank=True, null=True, verbose_name='أنواع المحاصيل المحترقة'),
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='evacuation_location',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='مكان إجلاء السكان'),
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='evacuation_nature',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='طبيعة الإجلاء'),
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='evacuation_type',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='نوع الإجلاء'),
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='fire_sources_count',
            field=models.IntegerField(blank=True, null=True, verbose_name='عدد البؤر'),
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='fire_specific_location',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='موقع الحريق المحدد'),
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='ignition_points_count',
            field=models.IntegerField(blank=True, null=True, verbose_name='عدد نقاط الاشتعال'),
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='misc_operation_nature',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='طبيعة العملية'),
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='misc_operation_type',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='نوع العملية'),
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='misc_specific_location',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='موقع العملية المحدد'),
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='population_evacuated',
            field=models.BooleanField(default=False, verbose_name='تم إجلاء السكان'),
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='population_threat',
            field=models.BooleanField(default=False, verbose_name='تهديد للسكان'),
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='population_threat_building',
            field=models.BooleanField(default=False, verbose_name='تهديد السكان'),
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='present_authorities',
            field=models.TextField(blank=True, null=True, verbose_name='الجهات الحاضرة'),
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='specialized_team',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='الفريق المتخصص'),
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='specific_floor',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='طابق معين'),
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='specific_location',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='موقع الإجلاء المحدد'),
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='specific_room',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='غرفة محددة'),
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='support_request',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='طلب الدعم'),
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='wind_direction',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='اتجاه الرياح'),
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='wind_speed',
            field=models.IntegerField(blank=True, null=True, verbose_name='سرعة الرياح (كم/سا)'),
        ),
    ]
