# Generated by Django 5.2 on 2025-06-17 13:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0013_miscoperationsdata_ambulances_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='CoordinationCenterForestFire',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('telegram_number', models.PositiveIntegerField(verbose_name='رقم البرقية')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('intervention_time', models.CharField(max_length=20, verbose_name='ساعة التدخل')),
                ('intervening_unit', models.CharField(max_length=100, verbose_name='الوحدة المتدخلة')),
                ('municipality', models.CharField(max_length=100, verbose_name='البلدية')),
                ('location_name', models.CharField(max_length=200, verbose_name='المكان المسمى')),
                ('operation_duration', models.CharField(max_length=20, verbose_name='مدة العملية')),
                ('intervention_means', models.TextField(verbose_name='الوسائل المتدخلة')),
                ('loss_nature', models.TextField(verbose_name='طبيعة الخسائر')),
                ('losses_hectare', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الخسائر بالهكتار')),
                ('losses_are', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الخسائر بالآر')),
                ('losses_square_meter', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الخسائر بالمتر مربع')),
                ('other_loss_nature', models.TextField(blank=True, null=True, verbose_name='طبيعة الخسائر الأخرى')),
                ('other_loss_count', models.PositiveIntegerField(default=0, verbose_name='عدد الخسائر')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'حريق غابات - مركز التنسيق',
                'verbose_name_plural': 'حرائق الغابات - مركز التنسيق',
                'ordering': ['-date', '-telegram_number'],
            },
        ),
    ]
