# Generated by Django 5.2 on 2025-06-18 13:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0014_coordinationcenterforestfire'),
    ]

    operations = [
        migrations.AddField(
            model_name='coordinationcenterforestfire',
            name='deaths_count',
            field=models.PositiveIntegerField(default=0, verbose_name='عدد الوفيات'),
        ),
        migrations.AddField(
            model_name='coordinationcenterforestfire',
            name='evacuated_families_count',
            field=models.PositiveIntegerField(default=0, verbose_name='عدد العائلات الذين تم إجلاءهم'),
        ),
        migrations.AddField(
            model_name='coordinationcenterforestfire',
            name='evacuated_people_count',
            field=models.PositiveIntegerField(default=0, verbose_name='عدد الأشخاص الذين تم إجلاءهم'),
        ),
        migrations.AddField(
            model_name='coordinationcenterforestfire',
            name='evacuation_locations',
            field=models.TextField(blank=True, null=True, verbose_name='أماكن إجلاء العائلات بالتفصيل'),
        ),
        migrations.AddField(
            model_name='coordinationcenterforestfire',
            name='family_care_measures',
            field=models.TextField(blank=True, null=True, verbose_name='الإجراءات المتخذة للتكفل بالعائلات المتضررة'),
        ),
        migrations.AddField(
            model_name='coordinationcenterforestfire',
            name='fire_control_status',
            field=models.CharField(choices=[('أخمد نهائيا', 'أخمد نهائيا'), ('مسيطر عليه', 'مسيطر عليه'), ('خارج عن السيطرة', 'خارج عن السيطرة')], default='أخمد نهائيا', max_length=50, verbose_name='وضعية التحكم في الحريق'),
        ),
        migrations.AddField(
            model_name='coordinationcenterforestfire',
            name='injured_count',
            field=models.PositiveIntegerField(default=0, verbose_name='عدد الجرحى'),
        ),
    ]
