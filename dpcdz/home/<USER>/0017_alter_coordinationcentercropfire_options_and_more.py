# Generated by Django 5.2 on 2025-07-14 13:38

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0016_coordinationcentercropfire_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='coordinationcentercropfire',
            options={},
        ),
        migrations.AlterField(
            model_name='userprofile',
            name='role',
            field=models.CharField(choices=[('admin', 'مدير النظام'), ('wilaya_manager', 'مدير الولاية'), ('unit_manager', 'مدير الوحدة'), ('unit_coordinator', 'مركز تنسيق العمليات الوحدة')], max_length=20),
        ),
        migrations.CreateModel(
            name='DailyUnitCount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='home.interventionunit', verbose_name='الوحدة')),
            ],
            options={
                'verbose_name': 'التعداد الصباحي للوحدة',
                'verbose_name_plural': 'التعداد الصباحي للوحدات',
                'ordering': ['-date', 'unit'],
                'unique_together': {('unit', 'date')},
            },
        ),
        migrations.CreateModel(
            name='EquipmentCount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('serial_number', models.CharField(max_length=50, verbose_name='الرقم التسلسلي')),
                ('equipment_type', models.CharField(max_length=100, verbose_name='نوع الوسيلة')),
                ('radio_number', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم إشارة الراديو')),
                ('status', models.CharField(choices=[('operational', 'تعمل'), ('broken', 'معطلة'), ('maintenance', 'تحت الصيانة')], max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('daily_count', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='equipment', to='home.dailyunitcount')),
            ],
            options={
                'verbose_name': 'وسيلة',
                'verbose_name_plural': 'الوسائل',
            },
        ),
        migrations.CreateModel(
            name='PersonnelCount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('registration_number', models.CharField(max_length=20, verbose_name='رقم القيد')),
                ('full_name', models.CharField(max_length=100, verbose_name='الاسم الكامل')),
                ('rank', models.CharField(max_length=50, verbose_name='الرتبة')),
                ('status', models.CharField(choices=[('present', 'حاضر'), ('absent', 'غائب'), ('on_mission', 'في مهمة')], max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('daily_count', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='personnel', to='home.dailyunitcount')),
            ],
            options={
                'verbose_name': 'عون',
                'verbose_name_plural': 'الأعوان',
            },
        ),
        migrations.CreateModel(
            name='TransferRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transfer_type', models.CharField(choices=[('personnel', 'عون'), ('equipment', 'وسيلة')], max_length=20, verbose_name='نوع التحويل')),
                ('item_name', models.CharField(max_length=100, verbose_name='العنصر')),
                ('transfer_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التحويل')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('from_unit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfers_from', to='home.interventionunit', verbose_name='من وحدة')),
                ('to_unit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfers_to', to='home.interventionunit', verbose_name='إلى وحدة')),
                ('transferred_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='تم التحويل بواسطة')),
            ],
            options={
                'verbose_name': 'سجل التحويل',
                'verbose_name_plural': 'سجلات التحويلات',
                'ordering': ['-transfer_date'],
            },
        ),
    ]
