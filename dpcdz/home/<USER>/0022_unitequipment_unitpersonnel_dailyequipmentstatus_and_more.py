# Generated by Django 5.2.4 on 2025-07-16 11:06

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0021_personnelposition_personnelrank'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='UnitEquipment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('serial_number', models.CharField(max_length=50, verbose_name='الرقم التسلسلي')),
                ('equipment_type', models.CharField(max_length=100, verbose_name='نوع الوسيلة')),
                ('radio_number', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الراديو')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='home.interventionunit', verbose_name='الوحدة')),
            ],
            options={
                'verbose_name': 'وسيلة الوحدة',
                'verbose_name_plural': 'وسائل الوحدة',
                'ordering': ['equipment_type'],
                'unique_together': {('unit', 'serial_number')},
            },
        ),
        migrations.CreateModel(
            name='UnitPersonnel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('registration_number', models.CharField(max_length=20, verbose_name='رقم القيد')),
                ('full_name', models.CharField(max_length=100, verbose_name='الاسم الكامل')),
                ('rank', models.CharField(blank=True, max_length=50, null=True, verbose_name='الرتبة')),
                ('position', models.CharField(blank=True, max_length=50, null=True, verbose_name='المنصب')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='home.interventionunit', verbose_name='الوحدة')),
            ],
            options={
                'verbose_name': 'عون الوحدة',
                'verbose_name_plural': 'أعوان الوحدة',
                'ordering': ['full_name'],
                'unique_together': {('unit', 'registration_number')},
            },
        ),
        migrations.CreateModel(
            name='DailyEquipmentStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('status', models.CharField(choices=[('operational', 'تعمل'), ('broken', 'معطلة'), ('maintenance', 'تحت الصيانة')], default='operational', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('updated_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='حدث بواسطة')),
                ('equipment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='home.unitequipment', verbose_name='الوسيلة')),
            ],
            options={
                'verbose_name': 'حالة الوسيلة اليومية',
                'verbose_name_plural': 'حالات الوسائل اليومية',
                'ordering': ['-date', 'equipment__equipment_type'],
                'unique_together': {('equipment', 'date')},
            },
        ),
        migrations.CreateModel(
            name='DailyPersonnelStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('status', models.CharField(choices=[('present', 'حاضر'), ('absent', 'غائب'), ('on_mission', 'في مهمة')], default='present', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('updated_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='حدث بواسطة')),
                ('personnel', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='home.unitpersonnel', verbose_name='العون')),
            ],
            options={
                'verbose_name': 'حالة العون اليومية',
                'verbose_name_plural': 'حالات الأعوان اليومية',
                'ordering': ['-date', 'personnel__full_name'],
                'unique_together': {('personnel', 'date')},
            },
        ),
    ]
