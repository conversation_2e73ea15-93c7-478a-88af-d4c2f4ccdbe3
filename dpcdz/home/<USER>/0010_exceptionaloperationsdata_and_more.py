# Generated by Django 5.2 on 2025-05-17 16:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0009_add_souk_ahras_default_units'),
    ]

    operations = [
        migrations.CreateModel(
            name='ExceptionalOperationsData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('intervening_unit', models.CharField(max_length=100)),
                ('operation_type', models.CharField(max_length=100)),
                ('number_of_operations', models.PositiveIntegerField()),
                ('number_of_interventions', models.PositiveIntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='InterventionsWithoutWorkData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('intervening_unit', models.CharField(max_length=100)),
                ('intervention_type', models.CharField(max_length=100)),
                ('number_of_interventions', models.PositiveIntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='TrafficAccident',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('wilaya', models.CharField(choices=[('01', 'أدرار'), ('02', 'الشلف'), ('03', 'الأغواط'), ('04', 'أم البواقي'), ('05', 'باتنة'), ('06', 'بجاية'), ('07', 'بسكرة'), ('08', 'بشار'), ('09', 'البليدة'), ('10', 'البويرة'), ('11', 'تمنراست'), ('12', 'تبسة'), ('13', 'تلمسان'), ('14', 'تيارت'), ('15', 'تيزي وزو'), ('16', 'الجزائر العاصمة'), ('17', 'الجلفة'), ('18', 'جيجل'), ('19', 'سطيف'), ('20', 'سعيدة'), ('21', 'سكيكدة'), ('22', 'سيدي بلعباس'), ('23', 'عنابة'), ('24', 'قالمة'), ('25', 'قسنطينة'), ('26', 'المدية'), ('27', 'مستغانم'), ('28', 'المسيلة'), ('29', 'معسكر'), ('30', 'ورقلة'), ('31', 'وهران'), ('32', 'البيض'), ('33', 'إليزي'), ('34', 'برج بوعريريج'), ('35', 'بومرداس'), ('36', 'الطارف'), ('37', 'تندوف'), ('38', 'تيسمسيلت'), ('39', 'الوادي'), ('40', 'خنشلة'), ('41', 'سوق أهراس'), ('42', 'تيبازة'), ('43', 'ميلة'), ('44', 'عين الدفلى'), ('45', 'النعامة'), ('46', 'عين تموشنت'), ('47', 'غرداية'), ('48', 'غليزان'), ('49', 'تيميمون'), ('50', 'برج باجي مختار'), ('51', 'أولاد جلال'), ('52', 'بني عباس'), ('53', 'عين صالح'), ('54', 'عين قزام'), ('55', 'تقرت'), ('56', 'جانت'), ('57', 'المغير'), ('58', 'المنيعة')], max_length=2)),
                ('municipality', models.CharField(max_length=100)),
                ('intervening_unit', models.CharField(max_length=100)),
                ('accident_type', models.CharField(max_length=100)),
                ('accident_location', models.CharField(max_length=200)),
                ('number_of_accidents', models.PositiveIntegerField()),
                ('number_of_interventions', models.PositiveIntegerField()),
                ('number_of_injured', models.PositiveIntegerField()),
                ('number_of_deaths', models.PositiveIntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
