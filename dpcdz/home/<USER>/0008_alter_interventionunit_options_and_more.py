# Generated by Django 5.2 on 2025-05-15 21:21

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0007_interventionunit_userprofile'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='interventionunit',
            options={'ordering': ['wilaya', 'name']},
        ),
        migrations.AddField(
            model_name='interventionunit',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='interventionunit',
            name='is_default',
            field=models.BooleanField(default=False),
        ),
    ]
