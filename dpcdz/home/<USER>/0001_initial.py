# Generated by Django 5.2 on 2025-05-07 10:55

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='GeneralFireData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('intervening_unit', models.CharField(max_length=100)),
                ('fire_type', models.CharField(max_length=100)),
                ('number_of_fires', models.PositiveIntegerField()),
                ('number_of_interventions', models.PositiveIntegerField()),
                ('number_of_injured', models.PositiveIntegerField()),
                ('number_of_deaths', models.PositiveIntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
