# Generated by Django 5.2.4 on 2025-07-17 07:23

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0025_alter_vehiclecrewassignment_role_readinessalert_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='eighthourpersonnel',
            options={'ordering': ['-date', 'work_period', 'full_name'], 'verbose_name': 'عون نظام 8 ساعات', 'verbose_name_plural': 'أعوان نظام 8 ساعات'},
        ),
        migrations.AlterUniqueTogether(
            name='eighthourpersonnel',
            unique_together=set(),
        ),
        migrations.AlterUniqueTogether(
            name='unitpersonnel',
            unique_together=set(),
        ),
        migrations.AddField(
            model_name='eighthourpersonnel',
            name='age',
            field=models.PositiveIntegerField(blank=True, null=True, verbose_name='العمر'),
        ),
        migrations.AddField(
            model_name='eighthourpersonnel',
            name='full_name',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='الاسم الكامل'),
        ),
        migrations.AddField(
            model_name='eighthourpersonnel',
            name='gender',
            field=models.CharField(blank=True, choices=[('male', 'ذكر'), ('female', 'أنثى')], max_length=10, null=True, verbose_name='الجنس'),
        ),
        migrations.AddField(
            model_name='eighthourpersonnel',
            name='personnel_registration_number',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم التسجيل'),
        ),
        migrations.AddField(
            model_name='eighthourpersonnel',
            name='phone_number',
            field=models.CharField(blank=True, max_length=15, null=True, verbose_name='رقم الهاتف'),
        ),
        migrations.AddField(
            model_name='unitpersonnel',
            name='age',
            field=models.PositiveIntegerField(blank=True, null=True, verbose_name='العمر'),
        ),
        migrations.AddField(
            model_name='unitpersonnel',
            name='assigned_shift',
            field=models.CharField(blank=True, choices=[('shift_1', 'الفرقة الأولى'), ('shift_2', 'الفرقة الثانية'), ('shift_3', 'الفرقة الثالثة')], max_length=20, null=True, verbose_name='الفرقة المخصصة'),
        ),
        migrations.AddField(
            model_name='unitpersonnel',
            name='gender',
            field=models.CharField(blank=True, choices=[('male', 'ذكر'), ('female', 'أنثى')], max_length=10, null=True, verbose_name='الجنس'),
        ),
        migrations.AddField(
            model_name='unitpersonnel',
            name='personnel_registration_number',
            field=models.CharField(blank=True, max_length=20, null=True, unique=True, verbose_name='رقم التسجيل'),
        ),
        migrations.AddField(
            model_name='unitpersonnel',
            name='phone_number',
            field=models.CharField(blank=True, max_length=15, null=True, verbose_name='رقم الهاتف'),
        ),
        migrations.AddField(
            model_name='unitpersonnel',
            name='work_system',
            field=models.CharField(choices=[('24_hours', 'نظام 24 ساعة'), ('8_hours', 'نظام 8 ساعات')], default='24_hours', max_length=20, verbose_name='نظام العمل'),
        ),
        migrations.AddField(
            model_name='workshift',
            name='color_code',
            field=models.CharField(blank=True, max_length=7, null=True, verbose_name='رمز اللون'),
        ),
        migrations.AddField(
            model_name='workshift',
            name='description',
            field=models.TextField(blank=True, null=True, verbose_name='وصف الفرقة'),
        ),
        migrations.AlterField(
            model_name='unitpersonnel',
            name='registration_number',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم القيد'),
        ),
        migrations.AlterField(
            model_name='workshift',
            name='name',
            field=models.CharField(choices=[('shift_1', 'الفرقة الأولى'), ('shift_2', 'الفرقة الثانية'), ('shift_3', 'الفرقة الثالثة'), ('A', 'فصيلة A'), ('B', 'فصيلة B'), ('C', 'فصيلة C'), ('morning', 'فترة صباحية'), ('evening', 'فترة مسائية'), ('night', 'فترة ليلية')], max_length=20, verbose_name='اسم الفرقة'),
        ),
        migrations.AlterField(
            model_name='workshift',
            name='shift_type',
            field=models.CharField(choices=[('24_hours', 'نظام 24 ساعة'), ('8_hours', 'نظام 8 ساعات')], default='24_hours', max_length=20, verbose_name='نوع النظام'),
        ),
        migrations.AlterUniqueTogether(
            name='unitpersonnel',
            unique_together={('unit', 'personnel_registration_number')},
        ),
        migrations.CreateModel(
            name='PersonnelTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('from_shift', models.CharField(choices=[('shift_1', 'الفرقة الأولى'), ('shift_2', 'الفرقة الثانية'), ('shift_3', 'الفرقة الثالثة')], max_length=20, verbose_name='من الفرقة')),
                ('to_shift', models.CharField(choices=[('shift_1', 'الفرقة الأولى'), ('shift_2', 'الفرقة الثانية'), ('shift_3', 'الفرقة الثالثة')], max_length=20, verbose_name='إلى الفرقة')),
                ('transfer_reason', models.TextField(verbose_name='سبب التحويل')),
                ('transfer_date', models.DateField(verbose_name='تاريخ التحويل')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('approved', 'موافق عليه'), ('rejected', 'مرفوض'), ('completed', 'مكتمل')], default='pending', max_length=20, verbose_name='حالة التحويل')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_transfers', to=settings.AUTH_USER_MODEL, verbose_name='وافق عليه')),
                ('personnel', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='home.unitpersonnel', verbose_name='العون')),
                ('requested_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='requested_transfers', to=settings.AUTH_USER_MODEL, verbose_name='طلب بواسطة')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='home.interventionunit', verbose_name='الوحدة')),
            ],
            options={
                'verbose_name': 'تحويل عون',
                'verbose_name_plural': 'تحويلات الأعوان',
                'ordering': ['-created_at'],
            },
        ),
        migrations.RemoveField(
            model_name='eighthourpersonnel',
            name='personnel',
        ),
    ]
