# Generated by Django 5.2 on 2025-05-07 13:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0002_residentialfiredata'),
    ]

    operations = [
        migrations.CreateModel(
            name='PublicAreaFireData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('intervening_unit', models.CharField(max_length=100)),
                ('number_of_fires', models.PositiveIntegerField()),
                ('number_of_interventions', models.PositiveIntegerField()),
                ('number_of_injured', models.PositiveIntegerField()),
                ('number_of_deaths', models.PositiveIntegerField()),
                ('institution_name', models.CharField(max_length=200)),
                ('institution_type', models.CharField(max_length=100)),
                ('institution_category', models.CharField(max_length=100)),
                ('ambulances', models.PositiveIntegerField()),
                ('fire_trucks', models.PositiveIntegerField()),
                ('mechanical_ladders', models.PositiveIntegerField()),
                ('other_resources', models.PositiveIntegerField()),
                ('intervention_duration', models.CharField(max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
