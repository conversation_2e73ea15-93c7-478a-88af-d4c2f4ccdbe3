# Generated by Django 5.2 on 2025-07-15 20:38

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0019_personnelcount_position_alter_personnelcount_rank'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='personnelcount',
            name='experience_level',
            field=models.CharField(choices=[('beginner', 'مبتدئ'), ('intermediate', 'متوسط'), ('advanced', 'متقدم'), ('expert', 'خبير')], default='intermediate', max_length=20, verbose_name='مستوى الخبرة'),
        ),
        migrations.AddField(
            model_name='personnelcount',
            name='job_function',
            field=models.CharField(choices=[('driver', 'سائق'), ('crew_chief', 'رئيس عدد'), ('agent', 'عون'), ('specialist', 'متخصص'), ('officer', 'ضابط'), ('admin', 'إداري')], default='agent', max_length=20, verbose_name='الوظيفة'),
        ),
        migrations.AddField(
            model_name='personnelcount',
            name='specialization',
            field=models.CharField(choices=[('general', 'عام'), ('medical', 'طبي'), ('technical', 'تقني'), ('rescue', 'إنقاذ'), ('diving', 'غطس'), ('cynotechnical', 'سينوتقنية'), ('communications', 'اتصالات'), ('maintenance', 'صيانة')], default='general', max_length=20, verbose_name='التخصص'),
        ),
        migrations.CreateModel(
            name='VehicleRequirements',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('vehicle_type', models.CharField(max_length=100, verbose_name='نوع الوسيلة')),
                ('required_driver_count', models.IntegerField(default=1, verbose_name='عدد السائقين المطلوب')),
                ('required_crew_chief_count', models.IntegerField(default=1, verbose_name='عدد رؤساء العدد المطلوب')),
                ('required_agent_count', models.IntegerField(default=2, verbose_name='عدد الأعوان المطلوب')),
                ('minimum_agent_count', models.IntegerField(default=1, verbose_name='الحد الأدنى للأعوان')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'متطلبات الوسيلة',
                'verbose_name_plural': 'متطلبات الوسائل',
                'unique_together': {('vehicle_type',)},
            },
        ),
        migrations.CreateModel(
            name='VehicleCrewAssignment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(choices=[('driver', 'سائق'), ('crew_chief', 'رئيس عدد'), ('agent', 'عون'), ('specialist', 'متخصص')], max_length=50, verbose_name='الدور')),
                ('assignment_date', models.DateField(verbose_name='تاريخ التعيين')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('personnel', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='vehicle_assignments', to='home.personnelcount', verbose_name='العون')),
                ('vehicle', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='crew_assignments', to='home.equipmentcount', verbose_name='الوسيلة')),
            ],
            options={
                'verbose_name': 'تعيين العون على الوسيلة',
                'verbose_name_plural': 'تعيينات الأعوان على الوسائل',
                'ordering': ['assignment_date', 'role'],
                'unique_together': {('vehicle', 'personnel', 'assignment_date')},
            },
        ),
        migrations.CreateModel(
            name='VehicleReadiness',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('is_automatically_ready', models.BooleanField(default=False, verbose_name='جاهز تلقائياً')),
                ('is_manually_confirmed', models.BooleanField(default=False, verbose_name='مؤكد يدوياً')),
                ('confirmation_reason', models.TextField(blank=True, null=True, verbose_name='سبب التأكيد اليدوي')),
                ('missing_roles', models.JSONField(default=list, verbose_name='الأدوار المفقودة')),
                ('readiness_score', models.IntegerField(default=0, verbose_name='نسبة الجاهزية')),
                ('status', models.CharField(choices=[('ready', 'جاهز'), ('not_ready', 'غير جاهز'), ('manually_confirmed', 'مؤكد يدوياً'), ('under_maintenance', 'تحت الصيانة')], default='not_ready', max_length=20, verbose_name='الحالة')),
                ('last_check_time', models.DateTimeField(auto_now=True, verbose_name='آخر فحص')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('confirmed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم التأكيد بواسطة')),
                ('vehicle', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='readiness_records', to='home.equipmentcount', verbose_name='الوسيلة')),
            ],
            options={
                'verbose_name': 'جاهزية الوسيلة',
                'verbose_name_plural': 'جاهزية الوسائل',
                'ordering': ['-date', 'vehicle'],
                'unique_together': {('vehicle', 'date')},
            },
        ),
    ]
