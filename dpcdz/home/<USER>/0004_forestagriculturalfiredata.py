# Generated by Django 5.2 on 2025-05-07 14:29

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0003_publicareafiredata'),
    ]

    operations = [
        migrations.CreateModel(
            name='ForestAgriculturalFireData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('municipality', models.CharField(max_length=100)),
                ('intervening_unit', models.CharField(max_length=100)),
                ('fire_type', models.CharField(max_length=100)),
                ('number_of_fires', models.PositiveIntegerField()),
                ('number_of_interventions', models.PositiveIntegerField()),
                ('losses_hectare', models.PositiveIntegerField(blank=True, null=True)),
                ('losses_are', models.PositiveIntegerField(blank=True, null=True)),
                ('losses_square_meter', models.PositiveIntegerField(blank=True, null=True)),
                ('losses_count', models.PositiveIntegerField(blank=True, null=True)),
                ('loss_type', models.CharField(max_length=200)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
