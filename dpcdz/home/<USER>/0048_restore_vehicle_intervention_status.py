# Generated manually on 2025-08-01 16:30

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0046_delete_daily_interventions_models'),
    ]

    operations = [
        migrations.CreateModel(
            name='VehicleInterventionStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('available', 'متاحة'), ('in_intervention', 'في تدخل'), ('returning', 'في طريق العودة'), ('maintenance', 'صيانة'), ('out_of_service', 'خارج الخدمة')], default='available', max_length=20, verbose_name='الحالة')),
                ('date', models.DateField(auto_now_add=True, verbose_name='التاريخ')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='آخر تحديث')),
                ('vehicle', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='intervention_status', to='home.unitequipment', verbose_name='الوسيلة')),
            ],
            options={
                'verbose_name': 'حالة الوسيلة في التدخل',
                'verbose_name_plural': 'حالات الوسائل في التدخل',
            },
        ),
        migrations.AlterUniqueTogether(
            name='vehicleinterventionstatus',
            unique_together={('vehicle', 'date')},
        ),
    ]
