from django import template

register = template.Library()

@register.filter
def dict_total(dictionary):
    """Calculate the total sum of values in a dictionary"""
    if not dictionary or not isinstance(dictionary, dict):
        return 0
    return sum(value for value in dictionary.values() if isinstance(value, (int, float)))

@register.filter
def get_item(dictionary, key):
    """Get an item from a dictionary by key"""
    if not dictionary or not isinstance(dictionary, dict):
        return None
    return dictionary.get(key)

@register.filter
def dict_length(dictionary):
    """Get the length of a dictionary"""
    if not dictionary or not isinstance(dictionary, dict):
        return 0
    return len(dictionary)

@register.filter
def dict_keys(dictionary):
    """Get the keys of a dictionary"""
    if not dictionary or not isinstance(dictionary, dict):
        return []
    return list(dictionary.keys())

@register.filter
def dict_values(dictionary):
    """Get the values of a dictionary"""
    if not dictionary or not isinstance(dictionary, dict):
        return []
    return list(dictionary.values())

@register.filter
def dict_items(dictionary):
    """Get the items of a dictionary"""
    if not dictionary or not isinstance(dictionary, dict):
        return []
    return list(dictionary.items())

@register.filter
def format_number(value):
    """Format a number with thousands separator"""
    if not isinstance(value, (int, float)):
        return value
    return "{:,}".format(value)

@register.filter
def percentage(value, total):
    """Calculate percentage"""
    if not isinstance(value, (int, float)) or not isinstance(total, (int, float)) or total == 0:
        return 0
    return round((value / total) * 100, 1)

@register.filter
def non_zero_items(dictionary):
    """Filter dictionary items to only include non-zero values"""
    if not dictionary or not isinstance(dictionary, dict):
        return {}
    return {k: v for k, v in dictionary.items() if v and v > 0}

@register.filter
def top_items(dictionary, count=5):
    """Get top N items from dictionary by value"""
    if not dictionary or not isinstance(dictionary, dict):
        return {}
    sorted_items = sorted(dictionary.items(), key=lambda x: x[1], reverse=True)
    return dict(sorted_items[:count])
