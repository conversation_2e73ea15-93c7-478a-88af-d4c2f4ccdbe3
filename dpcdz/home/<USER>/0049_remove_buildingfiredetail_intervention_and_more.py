# Generated by Django 5.2 on 2025-08-01 17:04

import datetime
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0048_restore_vehicle_intervention_status'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='buildingfiredetail',
            name='intervention',
        ),
        migrations.RemoveField(
            model_name='interventioncasualty',
            name='intervention',
        ),
        migrations.RemoveField(
            model_name='interventionreport',
            name='intervention',
        ),
        migrations.RemoveField(
            model_name='medicalevacuationdetail',
            name='intervention',
        ),
        migrations.RemoveField(
            model_name='trafficaccidentdetail',
            name='intervention',
        ),
        migrations.AlterModelOptions(
            name='dailyintervention',
            options={'ordering': ['-created_at'], 'verbose_name': 'تدخل يومي', 'verbose_name_plural': 'التدخلات اليومية'},
        ),
        migrations.RemoveField(
            model_name='dailyintervention',
            name='caller_name',
        ),
        migrations.RemoveField(
            model_name='dailyintervention',
            name='date',
        ),
        migrations.RemoveField(
            model_name='dailyintervention',
            name='deaths_count',
        ),
        migrations.RemoveField(
            model_name='dailyintervention',
            name='departure_time',
        ),
        migrations.RemoveField(
            model_name='dailyintervention',
            name='fatalities_details',
        ),
        migrations.RemoveField(
            model_name='dailyintervention',
            name='incident_location',
        ),
        migrations.RemoveField(
            model_name='dailyintervention',
            name='initial_notes',
        ),
        migrations.RemoveField(
            model_name='dailyintervention',
            name='injured_count',
        ),
        migrations.RemoveField(
            model_name='dailyintervention',
            name='injured_details',
        ),
        migrations.RemoveField(
            model_name='dailyintervention',
            name='intervention_subtype',
        ),
        migrations.RemoveField(
            model_name='dailyintervention',
            name='location',
        ),
        migrations.RemoveField(
            model_name='dailyintervention',
            name='location_type',
        ),
        migrations.RemoveField(
            model_name='dailyintervention',
            name='material_damage',
        ),
        migrations.RemoveField(
            model_name='dailyintervention',
            name='patient_condition',
        ),
        migrations.RemoveField(
            model_name='dailyintervention',
            name='total_interventions',
        ),
        migrations.RemoveField(
            model_name='dailyintervention',
            name='victims_details',
        ),
        migrations.RemoveField(
            model_name='interventionvehicle',
            name='crew_count',
        ),
        migrations.RemoveField(
            model_name='interventionvehicle',
            name='crew_details',
        ),
        migrations.RemoveField(
            model_name='interventionvehicle',
            name='vehicle_role',
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='additional_notes',
            field=models.TextField(blank=True, null=True, verbose_name='ملاحظات إضافية'),
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='exit_time',
            field=models.TimeField(default=datetime.time(17, 3, 11, 903950), verbose_name='ساعة الخروج'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='intervention_location',
            field=models.CharField(default='غير محدد', max_length=200, verbose_name='مكان التدخل'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='reconnaissance_notes',
            field=models.TextField(blank=True, null=True, verbose_name='ملاحظات التعرف'),
        ),
        migrations.AddField(
            model_name='dailyintervention',
            name='support_requested',
            field=models.BooleanField(default=False, verbose_name='تم طلب الدعم'),
        ),
        migrations.AlterField(
            model_name='dailyintervention',
            name='contact_type',
            field=models.CharField(choices=[('phone', 'هاتف'), ('radio', 'راديو'), ('unit-request', 'وحدة تطلب الدعم'), ('direct', 'مباشر')], max_length=20, verbose_name='نوع الاتصال'),
        ),
        migrations.AlterField(
            model_name='dailyintervention',
            name='final_deaths_count',
            field=models.IntegerField(default=0, verbose_name='عدد الوفيات النهائي'),
        ),
        migrations.AlterField(
            model_name='dailyintervention',
            name='final_injured_count',
            field=models.IntegerField(default=0, verbose_name='عدد المسعفين النهائي'),
        ),
        migrations.AlterField(
            model_name='dailyintervention',
            name='intervention_type',
            field=models.CharField(choices=[('medical', 'إجلاء صحي'), ('accident', 'حادث مرور'), ('agricultural-fire', 'حريق محاصيل زراعية'), ('building-fire', 'حرائق البنايات والمؤسسات'), ('other', 'عمليات مختلفة')], max_length=30, verbose_name='نوع التدخل'),
        ),
        migrations.AlterField(
            model_name='dailyintervention',
            name='phone_number',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف'),
        ),
        migrations.AlterField(
            model_name='dailyintervention',
            name='total_duration',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='مدة التدخل الإجمالية'),
        ),
        migrations.AlterField(
            model_name='interventionvehicle',
            name='assigned_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التعيين'),
        ),
        migrations.DeleteModel(
            name='AgriculturalFireDetail',
        ),
        migrations.DeleteModel(
            name='BuildingFireDetail',
        ),
        migrations.DeleteModel(
            name='InterventionCasualty',
        ),
        migrations.DeleteModel(
            name='InterventionReport',
        ),
        migrations.DeleteModel(
            name='MedicalEvacuationDetail',
        ),
        migrations.DeleteModel(
            name='TrafficAccidentDetail',
        ),
    ]
