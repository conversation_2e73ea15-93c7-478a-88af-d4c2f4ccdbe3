{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>نموذج حادث مرور</title>
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="{% static 'css/traffic_accidents.css' %}">
    <link rel="stylesheet" href="{% static 'css/button-override.css' %}?v=1.0">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
<header>
    <div class="header-container">
        <div class="flag"><img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري"></div>
        <div class="title"><h1>المديرية العامة للحماية المدنية</h1></div>
        <div class="logo"><img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle" id="sidebar-toggle" style="cursor: pointer;"></div>
    </div>
</header>

{% include 'includes/sidebar.html' %}

<div class="form-container">
    <h1 class="form-title">حوادث المرور</h1>

    <form id="trafficAccidentForm" method="post">
        {% csrf_token %}

        <!-- Header Row -->
        <div class="form-row">
            <div class="form-group">
                <label for="accidentDate">تاريخ:</label>
                <input type="text" id="accidentDate" name="date" class="form-control date-picker" placeholder="اختر التاريخ" required>
            </div>
            <div class="form-group">
                <label for="unit">الوحدة المتدخلة:</label>
                <select id="unit" name="unit" class="form-select" required>
                    <option value="" disabled selected>اختر الوحدة</option>
                    {% for unit in units %}
                    <option value="{{ unit.name }}">{{ unit.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="form-group">
                <label for="accidentType">نوع الحادث:</label>
                <select id="accidentType" name="accident_type" class="form-select" required>
                    <option value="" disabled selected>اختر نوع الحادث</option>
                    <option value="ضحايا مصدومة بالمركبات">ضحايا مصدومة بالمركبات</option>
                    <option value="ضحايا تصادم المركبات">ضحايا تصادم المركبات</option>
                    <option value="ضحايا إنقلاب المركبات">ضحايا إنقلاب المركبات</option>
                    <option value="ضحايا مصدومة بالقطار">ضحايا مصدومة بالقطار</option>
                    <option value="ضحايا حوادث أخرى">ضحايا حوادث أخرى</option>
                </select>
            </div>
        </div>

        <!-- Accident Nature Row -->
        <div class="form-row">
            <div class="form-group">
                <label for="accidentNature">طبيعة الحادث:</label>
                <select id="accidentNature" name="accident_nature" class="form-select" required disabled>
                    <option value="" disabled selected>اختر نوع الحادث أولاً</option>
                </select>
            </div>
        </div>

        <!-- Counts Row -->
        <div class="form-row">
            <div class="form-group count-field">
                <label for="accidentsCount">عدد الحوادث:</label>
                <input type="number" id="accidentsCount" name="accidents_count" min="0" value="0" class="form-control" required>
            </div>
            <div class="form-group count-field">
                <label for="operationsCount">عدد العمليات:</label>
                <input type="number" id="operationsCount" name="operations_count" min="0" value="0" class="form-control" required>
            </div>
        </div>

        <!-- Road and Driver Age Row -->
        <div class="form-row">
            <div class="form-group">
                <label for="roadType">حسب نوع الطريق:</label>
                <select id="roadType" name="road_type" class="form-select" required>
                    <option value="" disabled selected>اختر نوع الطريق</option>
                    <option value="الطريق السيار">الطريق السيار</option>
                    <option value="الطريق الوطني">الطريق الوطني</option>
                    <option value="الطريق الولائي">الطريق الولائي</option>
                    <option value="الطريق البلدي">الطريق البلدي</option>
                    <option value="طرق أخرى">طرق أخرى</option>
                </select>
            </div>
            <div class="form-group">
                <label for="driverAge">حسب فئة السائقين:</label>
                <select id="driverAge" name="driver_age" class="form-select" required>
                    <option value="" disabled selected>اختر فئة السائقين</option>
                    <option value="من 21 سنة إلى 30 سنة">من 21 سنة إلى 30 سنة</option>
                    <option value="من 31 سنة إلى 40 سنة">من 31 سنة إلى 40 سنة</option>
                    <option value="من 41 سنة إلى 50 سنة">من 41 سنة إلى 50 سنة</option>
                    <option value="من 51 سنة إلى 60 سنة">من 51 سنة إلى 60 سنة</option>
                    <option value="فوق 60 سنة">فوق 60 سنة</option>
                </select>
            </div>
        </div>

    <!-- Human Losses Section -->
    <h2 class="section-title">الخسائر البشرية</h2>

    <!-- Injured Row -->
    <div class="form-row">
        <div class="form-group count-field">
            <label for="casualtiesMen">عدد الجرحى رجال:</label>
            <input type="number" id="casualtiesMen" name="casualties_men" min="0" value="0" class="form-control casualties-men" required>
        </div>
        <div class="form-group count-field">
            <label for="casualtiesWomen">عدد الجرحى نساء:</label>
            <input type="number" id="casualtiesWomen" name="casualties_women" min="0" value="0" class="form-control casualties-women" required>
        </div>
        <div class="form-group count-field">
            <label for="casualtiesChildren">عدد الجرحى أطفال:</label>
            <input type="number" id="casualtiesChildren" name="casualties_children" min="0" value="0" class="form-control casualties-children" required>
        </div>
        <div class="form-group count-field">
            <label for="totalCasualties">مجموع الجرحى:</label>
            <input type="number" id="totalCasualties" name="total_casualties" value="0" class="form-control total-casualties" readonly>
        </div>
    </div>

    <!-- Fatalities Row -->
    <div class="form-row">
        <div class="form-group count-field">
            <label for="fatalitiesMen">عدد الوفيات رجال:</label>
            <input type="number" id="fatalitiesMen" name="fatalities_men" min="0" value="0" class="form-control fatalities-men" required>
        </div>
        <div class="form-group count-field">
            <label for="fatalitiesWomen">عدد الوفيات نساء:</label>
            <input type="number" id="fatalitiesWomen" name="fatalities_women" min="0" value="0" class="form-control fatalities-women" required>
        </div>
        <div class="form-group count-field">
            <label for="fatalitiesChildren">عدد الوفيات أطفال:</label>
            <input type="number" id="fatalitiesChildren" name="fatalities_children" min="0" value="0" class="form-control fatalities-children" required>
        </div>
        <div class="form-group count-field">
            <label for="totalFatalities">مجموع الوفيات:</label>
            <input type="number" id="totalFatalities" name="total_fatalities" value="0" class="form-control total-fatalities" readonly>
        </div>
    </div>

    <!-- Material Losses Section -->
    <h2 class="section-title">الخسائر المادية</h2>

    <!-- Vehicles Row 1 -->
    <div class="form-row">
        <div class="form-group count-field">
            <label for="fuelCars">سيارات وقود:</label>
            <input type="number" id="fuelCars" name="fuel_cars" min="0" value="0" class="form-control" required>
        </div>
        <div class="form-group count-field">
            <label for="lpgCars">سيارات غاز مميع:</label>
            <input type="number" id="lpgCars" name="lpg_cars" min="0" value="0" class="form-control" required>
        </div>
        <div class="form-group count-field">
            <label for="trucks">شاحنات:</label>
            <input type="number" id="trucks" name="trucks" min="0" value="0" class="form-control" required>
        </div>
        <div class="form-group count-field">
            <label for="buses">حافلات:</label>
            <input type="number" id="buses" name="buses" min="0" value="0" class="form-control" required>
        </div>
    </div>

    <!-- Vehicles Row 2 -->
    <div class="form-row">
        <div class="form-group count-field">
            <label for="motorcycles">دراجات:</label>
            <input type="number" id="motorcycles" name="motorcycles" min="0" value="0" class="form-control" required>
        </div>
        <div class="form-group count-field">
            <label for="tractors">جرارات:</label>
            <input type="number" id="tractors" name="tractors" min="0" value="0" class="form-control" required>
        </div>
        <div class="form-group count-field">
            <label for="directedTransport">النقل موجه:</label>
            <input type="number" id="directedTransport" name="directed_transport" min="0" value="0" class="form-control" required>
        </div>
        <div class="form-group count-field">
            <label for="otherVehicles">أخرى:</label>
            <input type="number" id="otherVehicles" name="other_vehicles" min="0" value="0" class="form-control" required>
        </div>
    </div>

    <!-- Time Slots Section -->
    <h2 class="section-title">حسب التوقيت الزمني</h2>

    <!-- Time Slots Row 1 -->
    <div class="form-row">
        <div class="form-group count-field">
            <label for="time0609">06 سا إلى 09 سا:</label>
            <input type="number" id="time0609" name="time_06_09" min="0" value="0" class="form-control" required>
        </div>
        <div class="form-group count-field">
            <label for="time0912">09 سا إلى 12 سا:</label>
            <input type="number" id="time0912" name="time_09_12" min="0" value="0" class="form-control" required>
        </div>
        <div class="form-group count-field">
            <label for="time1214">12 سا إلى 14 سا:</label>
            <input type="number" id="time1214" name="time_12_14" min="0" value="0" class="form-control" required>
        </div>
        <div class="form-group count-field">
            <label for="time1416">14 سا إلى 16 سا:</label>
            <input type="number" id="time1416" name="time_14_16" min="0" value="0" class="form-control" required>
        </div>
    </div>

    <!-- Time Slots Row 2 -->
    <div class="form-row">
        <div class="form-group count-field">
            <label for="time1620">16 سا إلى 20 سا:</label>
            <input type="number" id="time1620" name="time_16_20" min="0" value="0" class="form-control" required>
        </div>
        <div class="form-group count-field">
            <label for="time2000">20 سا إلى 00 سا:</label>
            <input type="number" id="time2000" name="time_20_00" min="0" value="0" class="form-control" required>
        </div>
        <div class="form-group count-field">
            <label for="time0006">00 سا إلى 06 سا:</label>
            <input type="number" id="time0006" name="time_00_06" min="0" value="0" class="form-control" required>
        </div>
    </div>

    <!-- Days of Week Section -->
    <h2 class="section-title">حسب أيام الأسبوع</h2>

    <!-- Days Row 1 -->
    <div class="form-row">
        <div class="form-group count-field">
            <label for="sunday">الأحد:</label>
            <input type="number" id="sunday" name="sunday" min="0" value="0" class="form-control" required>
        </div>
        <div class="form-group count-field">
            <label for="monday">الإثنين:</label>
            <input type="number" id="monday" name="monday" min="0" value="0" class="form-control" required>
        </div>
        <div class="form-group count-field">
            <label for="tuesday">الثلاثاء:</label>
            <input type="number" id="tuesday" name="tuesday" min="0" value="0" class="form-control" required>
        </div>
    </div>

    <!-- Days Row 2 -->
    <div class="form-row">
        <div class="form-group count-field">
            <label for="wednesday">الإربعاء:</label>
            <input type="number" id="wednesday" name="wednesday" min="0" value="0" class="form-control" required>
        </div>
        <div class="form-group count-field">
            <label for="thursday">الخميس:</label>
            <input type="number" id="thursday" name="thursday" min="0" value="0" class="form-control" required>
        </div>
        <div class="form-group count-field">
            <label for="friday">الجمعة:</label>
            <input type="number" id="friday" name="friday" min="0" value="0" class="form-control" required>
        </div>
    </div>

    <!-- Days Row 3 -->
    <div class="form-row">
        <div class="form-group count-field">
            <label for="saturday">السبت:</label>
            <input type="number" id="saturday" name="saturday" min="0" value="0" class="form-control" required>
        </div>
        <div class="form-group">
            <label for="selectedDateDisplay">اليوم المحدد:</label>
            <div id="selectedDateDisplay" class="form-control">الإثنين 06 مايو 2025</div>
        </div>
    </div>

    <!-- Action Buttons Row -->
    <div class="main-buttons-container">
        <div class="main-buttons">
            <a href="{% url 'home' %}" class="main-btn home-btn">
                <i class="fas fa-home"></i> الصفحة الرئيسية
            </a>
            <button type="button" id="saveToBoth" class="main-btn save-btn">
                <i class="fas fa-save"></i> حفظ
            </button>
        </div>
    </div>

    <!-- الأزرار العائمة -->
    <div class="floating-buttons">
        <a href="{% url 'table_traffic_accidents' %}" class="floating-btn tables-btn" title="لوحة الجداول">
            <i class="fas fa-th"></i>
        </a>
        <a href="{% url 'home' %}" class="floating-btn home-btn" title="الصفحة الرئيسية">
            <i class="fas fa-home"></i>
        </a>
        <button type="button" id="floatingSaveBtn" class="floating-btn save-btn" title="حفظ">
            <i class="fas fa-save"></i>
        </button>
        <a href="#" id="back-to-top" class="floating-btn top-btn" title="العودة إلى الأعلى">
            <i class="fas fa-arrow-up"></i>
        </a>
    </div>
    </form>
</div>

<!-- Custom Confirmation Modal -->
<div id="confirmationModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2>تأكيد الحفظ</h2>
        <p>هل أنت متأكد من حفظ هذه البيانات؟</p>
        <div class="modal-buttons">
            <button id="confirmYes" class="btn" style="background-color: #4CAF50; color: white; margin-left: 10px;">نعم</button>
            <button id="confirmNo" class="btn" style="background-color: #f44336; color: white;">لا</button>
        </div>
    </div>
</div>

<footer>
    <p>حقوق النشر © 2025 جميع الحقوق محفوظة - تم التطوير بواسطة عبد الرزاق مختاري</p>
</footer>

<script src="{% static 'js/sidebar.js' %}"></script>
<script src="{% static 'js/common-buttons.js' %}"></script>
<script src="{% static 'js/traffic_accidents.js' %}"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/ar.js"></script>
</body>
</html>