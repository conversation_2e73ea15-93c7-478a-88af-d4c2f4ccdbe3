{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحماية المدنية الجزائرية - لوحة الجداول</title>
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/home.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="{% static 'css/button-override.css' %}?v=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .tables-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .tables-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-top: 30px;
        }

        /* Floating buttons styles */
        .floating-buttons {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .floating-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            transition: all 0.3s;
            text-decoration: none;
            font-size: 18px;
            border: none;
            cursor: pointer;
        }

        .floating-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
        }

        .home-btn {
            background-color: #0d47a1;
        }

        .home-btn:hover {
            background-color: #0a3880;
        }

        .top-btn {
            background-color: #0d6efd;
        }

        .top-btn:hover {
            background-color: #0a58ca;
        }

        .table-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 30px 20px;
            text-decoration: none;
            color: #333;
        }

        .table-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .table-icon {
            font-size: 40px;
            margin-bottom: 15px;
        }

        .table-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        h2.page-title {
            text-align: center;
            margin-bottom: 30px;
            color: #0d47a1;
            border-bottom: 2px solid #0d47a1;
            padding-bottom: 10px;
        }

        .home-button {
            text-align: center;
            margin-top: 30px;
        }

        /* تخصيص ألوان الأيقونات */
        .icon-medical {
            color: #e63946; /* أحمر */
        }
        .icon-traffic {
            color: #f4a261; /* برتقالي */
        }
        .icon-fire {
            color: #e76f51; /* أحمر-برتقالي */
        }
        .icon-residential {
            color: #2a9d8f; /* أخضر-أزرق */
        }
        .icon-institutional {
            color: #457b9d; /* أزرق */
        }
        .icon-public {
            color: #1d3557; /* أزرق داكن */
        }
        .icon-forest {
            color: #2a9d8f; /* أخضر-أزرق */
        }
        .icon-misc {
            color: #6c757d; /* رمادي */
        }
        .icon-exceptional {
            color: #d62828; /* أحمر داكن */
        }
        .icon-no-work {
            color: #adb5bd; /* رمادي فاتح */
        }

        /* تصميم متجاوب */
        @media (max-width: 992px) {
            .tables-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 576px) {
            .tables-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle" id="sidebar-toggle" style="cursor: pointer;">
            </div>
        </div>
    </header>

    {% include 'includes/sidebar.html' %}

    <main>
        <div class="tables-container">
            <h2 class="page-title">لوحة الجداول</h2>

            <div class="tables-grid">
                <!-- 1. الجدول العام للحرائق -->
                <a href="/tables/general-fire/" class="table-card">
                    <div class="table-icon icon-fire">
                        <i class="fas fa-fire"></i>
                    </div>
                    <div class="table-title">الجدول العام للحرائق</div>
                </a>

                <!-- 2. الإجلاء الصحي -->
                <a href="/tables/medical-evacuation/" class="table-card">
                    <div class="table-icon icon-medical">
                        <i class="fas fa-ambulance"></i>
                    </div>
                    <div class="table-title">الإجلاء الصحي</div>
                </a>

                <!-- 3. حوادث المرور -->
                <a href="/tables/traffic-accidents/" class="table-card">
                    <div class="table-icon icon-traffic">
                        <i class="fas fa-car-crash"></i>
                    </div>
                    <div class="table-title">حوادث المرور</div>
                </a>




                <!-- 4. الحرائق في الأماكن المستقبلة للجمهور -->
                <a href="/tables/public-area-fires/" class="table-card">
                    <div class="table-icon icon-public">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="table-title">الحرائق في الأماكن المستقبلة للجمهور</div>
                </a>

                <!-- 5. الحرائق في المؤسسات المصنفة -->
                <a href="/tables/institutional-fires/" class="table-card">
                    <div class="table-icon icon-institutional">
                        <i class="fas fa-industry"></i>
                    </div>
                    <div class="table-title">الحرائق في المؤسسات المصنفة</div>
                </a>

                <!-- 6. الحرائق في البنايات المخصصة للسكن -->
                <a href="/tables/residential-fires/" class="table-card">
                    <div class="table-icon icon-residential">
                        <i class="fas fa-home"></i>
                    </div>
                    <div class="table-title">الحرائق في البنايات المخصصة للسكن</div>
                </a>

                <!-- 7. إحصاء العمليات الإستثنائية -->
                <a href="/tables/exceptional-operations/" class="table-card">
                    <div class="table-icon icon-exceptional">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="table-title">إحصاء العمليات الإستثنائية</div>
                </a>

                <!-- 8. إحصاء العمليات المختلفة -->
                <a href="/tables/misc-operations/" class="table-card">
                    <div class="table-icon icon-misc">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="table-title">إحصاء العمليات المختلفة</div>
                </a>

                <!-- 9. حرائق الغابات والمحاصيل الزراعية -->
                <a href="/tables/forest-agricultural-fires/" class="table-card">
                    <div class="table-icon icon-forest">
                        <i class="fas fa-tree"></i>
                    </div>
                    <div class="table-title">حرائق الغابات والمحاصيل الزراعية</div>
                </a>

                <!-- 10. إحصاء التدخلات بدون عمل -->
                <a href="/tables/interventions-without-work/" class="table-card">
                    <div class="table-icon icon-no-work">
                        <i class="fas fa-ban"></i>
                    </div>
                    <div class="table-title">إحصاء التدخلات بدون عمل</div>
                </a>
            </div>

            <!-- الأزرار العائمة -->
            <div class="floating-buttons">
                <a href="{% url 'home' %}" class="floating-btn home-btn" title="الصفحة الرئيسية">
                    <i class="fas fa-home"></i>
                </a>
                <a href="#" id="back-to-top" class="floating-btn top-btn" title="العودة إلى الأعلى">
                    <i class="fas fa-arrow-up"></i>
                </a>
            </div>
        </div>
    </main>

    <footer>
        <p>حقوق النشر © 2025 جميع الحقوق محفوظة - تم التطوير بواسطة عبد الرزاق مختاري</p>
    </footer>

    <script src="{% static 'js/sidebar.js' %}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Back to top button functionality
            const backToTopButton = document.getElementById('back-to-top');
            if (backToTopButton) {
                backToTopButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                });
            }
        });
    </script>
</body>
</html>
