<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إحصاء البيانات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');

        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .main-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 20px;
            padding: 30px;
        }

        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }

        .filter-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
        }

        .filter-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .filter-group {
            flex: 1;
            min-width: 200px;
        }

        .filter-group label {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
            display: block;
        }

        .filter-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            background: white;
        }

        .btn-apply-filters {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 30px;
            border-radius: 5px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-apply-filters:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .statistics-container {
            margin-top: 30px;
        }

        .info-cards-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .info-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }

        .info-card h3 {
            color: #667eea;
            margin-bottom: 10px;
            font-size: 1.2rem;
        }

        .info-card .value {
            font-size: 2rem;
            font-weight: bold;
            color: #495057;
        }

        .charts-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 25px;
        }

        .charts-container.single-chart {
            grid-template-columns: 1fr;
            justify-items: stretch;
        }

        .charts-container .chart-card {
            max-width: 100%;
        }

        .charts-container.single-chart .chart-card {
            max-width: 100%;
            width: 100%;
        }

        .chart-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border: 1px solid #f0f0f0;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .chart-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .chart-card h4 {
            color: #495057;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
            font-size: 1.1rem;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        .chart-container {
            position: relative;
            height: 320px;
        }

        .circle-chart {
            height: 280px;
        }

        .losses-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .loss-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid #667eea;
        }

        .loss-item .label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .loss-item .value {
            font-size: 1.3rem;
            font-weight: bold;
            color: #495057;
        }

        .no-data-message {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
            font-size: 1.1rem;
        }

        .no-data-message i {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
            color: #dee2e6;
        }

        .floating-buttons {
            position: fixed;
            bottom: 30px;
            right: 30px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .floating-btn {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: none;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .floating-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .btn-home {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .btn-export {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
        }

        .btn-scroll-top {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        }

        @media (max-width: 768px) {
            .filter-row {
                flex-direction: column;
            }

            .charts-container {
                grid-template-columns: 1fr !important;
            }

            .charts-container.single-chart {
                grid-template-columns: 1fr !important;
            }

            .floating-buttons {
                bottom: 20px;
                right: 20px;
            }

            .chart-card {
                margin: 0 auto;
                max-width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Page Header -->
        <div class="page-header">
            <h1><i class="fas fa-chart-bar"></i> إحصاء البيانات</h1>
            <p>عرض وتحليل البيانات الإحصائية للعمليات والتدخلات</p>
        </div>

        <!-- Filter Section -->
        <div class="filter-section">
            <h3><i class="fas fa-filter"></i> فلاتر البحث</h3>
            <form method="GET" id="filterForm">
                <div class="filter-row">
                    <div class="filter-group">
                        <label for="table_type">نوع الجدول:</label>
                        <select name="table_type" id="table_type" required>
                            <option value="">اختر نوع الجدول</option>
                            <option value="medical_evacuation" {% if filter_table_type == 'medical_evacuation' %}selected{% endif %}>الإجلاء الصحي</option>
                            <option value="traffic_accidents" {% if filter_table_type == 'traffic_accidents' %}selected{% endif %}>حوادث المرور</option>
                            <option value="general_fire" {% if filter_table_type == 'general_fire' %}selected{% endif %}>الجدول العام للحرائق</option>
                            <option value="residential_fires" {% if filter_table_type == 'residential_fires' %}selected{% endif %}>الحرائق في البنايات المخصصة للسكن</option>
                            <option value="institutional_fires" {% if filter_table_type == 'institutional_fires' %}selected{% endif %}>الحرائق في المؤسسات المصنفة</option>
                            <option value="public_area_fires" {% if filter_table_type == 'public_area_fires' %}selected{% endif %}>الحرائق في الأماكن المستقبِلة للجمهور</option>
                            <option value="forest_agricultural_fires" {% if filter_table_type == 'forest_agricultural_fires' %}selected{% endif %}>حرائق الغابات و المحاصيل</option>
                            <option value="misc_operations" {% if filter_table_type == 'misc_operations' %}selected{% endif %}>العمليات المختلفة</option>
                            <option value="security_device" {% if filter_table_type == 'security_device' %}selected{% endif %}>الجهاز الأمني</option>
                            <option value="exceptional_operations" {% if filter_table_type == 'exceptional_operations' %}selected{% endif %}>العمليات الاستثنائية</option>
                            <option value="interventions_without_work" {% if filter_table_type == 'interventions_without_work' %}selected{% endif %}>التدخلات بدون عمل</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="year">السنة:</label>
                        <select name="year" id="year">
                            <option value="">جميع السنوات</option>
                            {% for year in years %}
                                <option value="{{ year }}" {% if filter_year == year|stringformat:"s" %}selected{% endif %}>{{ year }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="month">الشهر:</label>
                        <select name="month" id="month">
                            <option value="">جميع الشهور</option>
                            {% for month_num, month_name in months %}
                                <option value="{{ month_num }}" {% if filter_month == month_num %}selected{% endif %}>{{ month_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>

                <div class="filter-row">
                    {% if is_admin %}
                    <div class="filter-group">
                        <label for="wilaya">الولاية:</label>
                        <select name="wilaya" id="wilaya">
                            <option value="">جميع الولايات</option>
                            {% for wilaya_code, wilaya_name in wilaya_choices %}
                                <option value="{{ wilaya_code }}" {% if filter_wilaya == wilaya_code %}selected{% endif %}>{{ wilaya_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    {% endif %}

                    <div class="filter-group">
                        <label for="unit">الوحدة:</label>
                        <select name="unit" id="unit">
                            <option value="">جميع الوحدات</option>
                            {% for unit in user_units %}
                                <option value="{{ unit.name }}" {% if filter_unit == unit.name %}selected{% endif %}>{{ unit.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>

                <!-- Dynamic sub-filters will be added here by JavaScript -->
                <div id="dynamic-filters"></div>

                <div class="filter-row">
                    <button type="submit" class="btn-apply-filters">
                        <i class="fas fa-search"></i> تطبيق الفلاتر
                    </button>
                </div>
            </form>
        </div>

        <!-- Statistics Display -->
        <div class="statistics-container">
            <!-- Info Cards -->
            <div id="info-cards-container" class="info-cards-container">
                <!-- Cards will be populated by JavaScript -->
            </div>

            <!-- Charts -->
            <div id="charts-container" class="charts-container">
                <!-- Charts will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Floating Action Buttons -->
    <div class="floating-buttons">
        <button class="floating-btn btn-home" onclick="window.location.href='{% url 'home' %}'" title="الصفحة الرئيسية">
            <i class="fas fa-home"></i>
        </button>
        <button class="floating-btn btn-export" onclick="exportPDF()" title="تصدير PDF">
            <i class="fas fa-file-pdf"></i>
        </button>
        <button class="floating-btn btn-scroll-top" onclick="scrollToTop()" title="العودة للأعلى">
            <i class="fas fa-arrow-up"></i>
        </button>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Dynamic data from Django
        const dynamicData = {{ dynamic_data|safe }};
        const selectedTableType = '{{ filter_table_type }}';

        // Debug: Log the data received from Django
        console.log('Dynamic data received:', dynamicData);
        console.log('Selected table type:', selectedTableType);

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            initializeDynamicFilters();

            if (selectedTableType) {
                console.log('Updating charts and cards for:', selectedTableType);
                updateChartsAndCards(selectedTableType);
            } else {
                console.log('No table type selected');
                showNoDataMessage();
            }
        });

        function initializeDynamicFilters() {
            const tableTypeSelect = document.getElementById('table_type');
            tableTypeSelect.addEventListener('change', function() {
                updateDynamicFilters(this.value);
            });

            // Initialize with current selection
            if (selectedTableType) {
                updateDynamicFilters(selectedTableType);
            }
        }

        function updateDynamicFilters(tableType) {
            const dynamicFiltersContainer = document.getElementById('dynamic-filters');
            dynamicFiltersContainer.innerHTML = '';

            if (!tableType) return;

            const filterRow = document.createElement('div');
            filterRow.className = 'filter-row';

            // Add specific filters based on table type
            if (tableType === 'medical_evacuation') {
                filterRow.innerHTML = `
                    <div class="filter-group">
                        <label for="intervention_type">نوع التدخل:</label>
                        <select name="intervention_type" id="intervention_type">
                            <option value="">جميع الأنواع</option>
                            {% for type in intervention_types %}
                                <option value="{{ type.name }}" {% if filter_intervention_type == type.name %}selected{% endif %}>{{ type.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="intervention_nature">طبيعة التدخل:</label>
                        <select name="intervention_nature" id="intervention_nature">
                            <option value="">جميع الطبائع</option>
                            {% for nature in intervention_natures %}
                                <option value="{{ nature.name }}" {% if filter_intervention_nature == nature.name %}selected{% endif %}>{{ nature.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                `;
            } else if (tableType === 'traffic_accidents') {
                filterRow.innerHTML = `
                    <div class="filter-group">
                        <label for="accident_type">نوع الحادث:</label>
                        <select name="accident_type" id="accident_type">
                            <option value="">جميع الأنواع</option>
                            {% for type in accident_types %}
                                <option value="{{ type }}" {% if filter_accident_type == type %}selected{% endif %}>{{ type }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="accident_nature">طبيعة الحادث:</label>
                        <select name="accident_nature" id="accident_nature">
                            <option value="">جميع الطبائع</option>
                            {% for nature in accident_natures %}
                                <option value="{{ nature }}" {% if filter_accident_nature == nature %}selected{% endif %}>{{ nature }}</option>
                            {% endfor %}
                        </select>
                    </div>
                `;
            } else if (tableType === 'general_fire') {
                filterRow.innerHTML = `
                    <div class="filter-group">
                        <label for="fire_type">نوع الحريق:</label>
                        <select name="fire_type" id="fire_type">
                            <option value="">جميع الأنواع</option>
                            {% for type in fire_types %}
                                <option value="{{ type }}" {% if filter_fire_type == type %}selected{% endif %}>{{ type }}</option>
                            {% endfor %}
                        </select>
                    </div>
                `;
            } else if (tableType.includes('operations')) {
                filterRow.innerHTML = `
                    <div class="filter-group">
                        <label for="operation_type">نوع العملية:</label>
                        <select name="operation_type" id="operation_type">
                            <option value="">جميع الأنواع</option>
                            {% for type in operation_types %}
                                <option value="{{ type }}" {% if filter_operation_type == type %}selected{% endif %}>{{ type }}</option>
                            {% endfor %}
                        </select>
                    </div>
                `;
            }

            if (filterRow.innerHTML.trim()) {
                dynamicFiltersContainer.appendChild(filterRow);
            }
        }

        function updateChartsAndCards(tableType) {
            console.log('updateChartsAndCards called with:', tableType);
            const chartsContainer = document.getElementById('charts-container');
            const infoCardsContainer = document.getElementById('info-cards-container');

            if (!dynamicData || !dynamicData[tableType]) {
                console.log('No data available for table type:', tableType);
                showNoDataMessage();
                return;
            }

            const data = dynamicData[tableType];
            console.log('Data for table type:', data);

            // Update info cards
            updateInfoCards(tableType, data);

            // Update charts
            updateCharts(tableType, data);
        }

        function showNoDataMessage() {
            const chartsContainer = document.getElementById('charts-container');
            const infoCardsContainer = document.getElementById('info-cards-container');

            chartsContainer.innerHTML = `
                <div class="no-data-message">
                    <i class="fas fa-chart-bar"></i>
                    يرجى اختيار نوع الجدول وتطبيق الفلاتر لعرض الإحصائيات
                </div>
            `;
            infoCardsContainer.innerHTML = '';
        }

        function updateInfoCards(tableType, data) {
            const container = document.getElementById('info-cards-container');
            container.innerHTML = '';

            // Create info cards based on data
            const cards = [];

            if (data.total_operations !== undefined) {
                cards.push({ title: 'إجمالي العمليات', value: data.total_operations });
            }
            if (data.total_interventions !== undefined) {
                cards.push({ title: 'إجمالي التدخلات', value: data.total_interventions });
            }
            if (data.total_paramedics !== undefined) {
                cards.push({ title: 'إجمالي المسعفين', value: data.total_paramedics });
            }
            if (data.total_deaths !== undefined) {
                cards.push({ title: 'إجمالي الوفيات', value: data.total_deaths });
            }
            if (data.total_accidents !== undefined) {
                cards.push({ title: 'إجمالي الحوادث', value: data.total_accidents });
            }
            if (data.total_injuries !== undefined) {
                cards.push({ title: 'إجمالي الجرحى', value: data.total_injuries });
            }
            if (data.total_fires !== undefined) {
                cards.push({ title: 'إجمالي الحرائق', value: data.total_fires });
            }
            if (data.total_records !== undefined) {
                cards.push({ title: 'عدد العمليات', value: data.total_records });
            }

            cards.forEach(card => {
                const cardElement = document.createElement('div');
                cardElement.className = 'info-card';
                cardElement.innerHTML = `
                    <h3>${card.title}</h3>
                    <div class="value">${card.value || 0}</div>
                `;
                container.appendChild(cardElement);
            });
        }

        function updateCharts(tableType, data) {
            const container = document.getElementById('charts-container');
            container.innerHTML = '';

            // Count total charts to be created
            let chartCount = 0;
            const chartsToCreate = [];

            // Medical Evacuation Charts
            if (tableType === 'medical_evacuation') {
                if (data.intervention_types && Object.keys(data.intervention_types).length > 0) {
                    chartsToCreate.push({ title: 'أنواع التدخلات', data: data.intervention_types, type: 'doughnut' });
                }
                if (data.intervention_natures && Object.keys(data.intervention_natures).length > 0) {
                    chartsToCreate.push({ title: 'طبيعة التدخلات', data: data.intervention_natures, type: 'pie' });
                }
                if (data.locations && Object.keys(data.locations).length > 0) {
                    chartsToCreate.push({ title: 'مكان التدخل', data: data.locations, type: 'doughnut' });
                }
                if (data.paramedics_breakdown && Object.keys(data.paramedics_breakdown).length > 0) {
                    chartsToCreate.push({ title: 'توزيع المسعفين حسب الفئة', data: data.paramedics_breakdown, type: 'doughnut' });
                }
                if (data.deaths_breakdown && Object.keys(data.deaths_breakdown).length > 0) {
                    chartsToCreate.push({ title: 'توزيع الوفيات حسب الفئة', data: data.deaths_breakdown, type: 'doughnut' });
                }
                // Add deaths by nature chart if data exists
                if (data.deaths_by_nature && Object.keys(data.deaths_by_nature).some(key => data.deaths_by_nature[key] > 0)) {
                    const filteredData = {};
                    Object.entries(data.deaths_by_nature).forEach(([key, value]) => {
                        if (value > 0) filteredData[key] = value;
                    });
                    chartsToCreate.push({ title: 'توزيع الوفيات حسب طبيعة التدخل', data: filteredData, type: 'doughnut' });
                }
                // Add paramedics by nature chart if data exists
                if (data.paramedics_by_nature && Object.keys(data.paramedics_by_nature).some(key => data.paramedics_by_nature[key] > 0)) {
                    const filteredData = {};
                    Object.entries(data.paramedics_by_nature).forEach(([key, value]) => {
                        if (value > 0) filteredData[key] = value;
                    });
                    chartsToCreate.push({ title: 'توزيع المسعفين حسب طبيعة التدخل', data: filteredData, type: 'doughnut' });
                }
            }

            // Traffic Accidents Charts
            else if (tableType === 'traffic_accidents') {
                if (data.accident_types && Object.keys(data.accident_types).length > 0) {
                    chartsToCreate.push({ title: 'أنواع الحوادث', data: data.accident_types, type: 'pie' });
                }
                if (data.accident_natures && Object.keys(data.accident_natures).length > 0) {
                    chartsToCreate.push({ title: 'طبيعة الحوادث', data: data.accident_natures, type: 'doughnut' });
                }
                if (data.human_losses && Object.keys(data.human_losses).length > 0) {
                    chartsToCreate.push({ title: 'الخسائر البشرية', data: data.human_losses, type: 'bar' });
                }
                if (data.material_losses && Object.keys(data.material_losses).length > 0) {
                    chartsToCreate.push({ title: 'الخسائر المادية حسب نوع المركبة', data: data.material_losses, type: 'bar' });
                }
                if (data.human_losses_detailed) {
                    const casualties = {
                        'جرحى رجال': data.human_losses_detailed['عدد الجرحى رجال'] || 0,
                        'جرحى نساء': data.human_losses_detailed['عدد الجرحى نساء'] || 0,
                        'جرحى أطفال': data.human_losses_detailed['عدد الجرحى أطفال'] || 0
                    };
                    const fatalities = {
                        'وفيات رجال': data.human_losses_detailed['عدد الوفيات رجال'] || 0,
                        'وفيات نساء': data.human_losses_detailed['عدد الوفيات نساء'] || 0,
                        'وفيات أطفال': data.human_losses_detailed['عدد الوفيات أطفال'] || 0
                    };
                    if (Object.values(casualties).some(v => v > 0)) {
                        chartsToCreate.push({ title: 'توزيع الجرحى', data: casualties, type: 'doughnut' });
                    }
                    if (Object.values(fatalities).some(v => v > 0)) {
                        chartsToCreate.push({ title: 'توزيع الوفيات', data: fatalities, type: 'doughnut' });
                    }
                }

                // New charts for traffic accidents
                if (data.time_periods && Object.keys(data.time_periods).length > 0) {
                    chartsToCreate.push({ title: 'عدد الحوادث حسب التوقيت الزمني', data: data.time_periods, type: 'bar' });
                }
                if (data.days_of_week && Object.keys(data.days_of_week).length > 0) {
                    chartsToCreate.push({ title: 'عدد الحوادث حسب أيام الأسبوع', data: data.days_of_week, type: 'doughnut' });
                }
                if (data.driver_categories && Object.keys(data.driver_categories).length > 0) {
                    chartsToCreate.push({ title: 'عدد الحوادث حسب فئة السائقين', data: data.driver_categories, type: 'pie' });
                }
                if (data.road_types && Object.keys(data.road_types).length > 0) {
                    chartsToCreate.push({ title: 'عدد الحوادث حسب نوع الطريق', data: data.road_types, type: 'doughnut' });
                }

                // Detailed injury/death charts by accident type
                if (data.injuries_by_type_gender && Object.keys(data.injuries_by_type_gender).length > 0) {
                    // Create combined chart for injuries by gender across accident types
                    const injuriesByGender = { 'رجال': 0, 'نساء': 0, 'أطفال': 0 };
                    Object.values(data.injuries_by_type_gender).forEach(typeData => {
                        injuriesByGender['رجال'] += typeData['رجال'] || 0;
                        injuriesByGender['نساء'] += typeData['نساء'] || 0;
                        injuriesByGender['أطفال'] += typeData['أطفال'] || 0;
                    });
                    if (Object.values(injuriesByGender).some(v => v > 0)) {
                        chartsToCreate.push({ title: 'الجرحى حسب الفئة والنوع', data: injuriesByGender, type: 'doughnut' });
                    }
                }

                if (data.deaths_by_type_gender && Object.keys(data.deaths_by_type_gender).length > 0) {
                    // Create combined chart for deaths by gender across accident types
                    const deathsByGender = { 'رجال': 0, 'نساء': 0, 'أطفال': 0 };
                    Object.values(data.deaths_by_type_gender).forEach(typeData => {
                        deathsByGender['رجال'] += typeData['رجال'] || 0;
                        deathsByGender['نساء'] += typeData['نساء'] || 0;
                        deathsByGender['أطفال'] += typeData['أطفال'] || 0;
                    });
                    if (Object.values(deathsByGender).some(v => v > 0)) {
                        chartsToCreate.push({ title: 'الوفيات حسب الفئة والنوع', data: deathsByGender, type: 'doughnut' });
                    }
                }
            }

            // Fire Charts (General, Residential, Institutional, Public Area)
            else if (tableType.includes('fire')) {
                if (data.fire_types && Object.keys(data.fire_types).length > 0) {
                    chartsToCreate.push({ title: 'أنواع الحرائق', data: data.fire_types, type: 'bar' });
                }
                if (data.fire_interventions && Object.keys(data.fire_interventions).length > 0) {
                    chartsToCreate.push({ title: 'عدد التدخلات حسب النوع', data: data.fire_interventions, type: 'doughnut' });
                }
                if (data.casualties && Object.keys(data.casualties).length > 0) {
                    chartsToCreate.push({ title: 'الخسائر البشرية', data: data.casualties, type: 'bar' });
                }

                // Special handling for forest fires with losses
                if (tableType === 'forest_agricultural_fires') {
                    if (data.loss_types && Object.keys(data.loss_types).length > 0) {
                        chartsToCreate.push({ title: 'أنواع الخسائر', data: data.loss_types, type: 'pie' });
                    }
                    // Losses display will be handled separately
                }
            }

            // Operations Charts - Only 2 charts and 2 circles for each operation type
            else if (tableType.includes('operations') || tableType === 'security_device') {

                // Misc Operations (العمليات المختلفة)
                if (tableType === 'misc_operations') {
                    if (data.operation_types && Object.keys(data.operation_types).length > 0) {
                        chartsToCreate.push({ title: 'أنواع العمليات المختلفة', data: data.operation_types, type: 'doughnut' });
                    }
                    if (data.monthly_distribution && Object.keys(data.monthly_distribution).length > 0) {
                        chartsToCreate.push({ title: 'التوزيع الشهري', data: data.monthly_distribution, type: 'pie' });
                    }
                    // Add summary circles
                    const operationsSummary = {
                        'إجمالي العمليات': data.total_operations || 0,
                        'إجمالي التدخلات': data.total_interventions || 0
                    };
                    if (Object.values(operationsSummary).some(v => v > 0)) {
                        chartsToCreate.push({ title: 'ملخص العمليات', data: operationsSummary, type: 'bar' });
                    }
                    const rescuersSummary = {
                        'المسعفين': data.total_rescuers || 0,
                        'الوفيات': data.total_deaths || 0
                    };
                    if (Object.values(rescuersSummary).some(v => v > 0)) {
                        chartsToCreate.push({ title: 'الخسائر البشرية', data: rescuersSummary, type: 'doughnut' });
                    }
                }

                // Security Device (الجهاز الأمني)
                else if (tableType === 'security_device') {
                    if (data.security_device_types && Object.keys(data.security_device_types).length > 0) {
                        chartsToCreate.push({ title: 'أنواع الأجهزة الأمنية', data: data.security_device_types, type: 'doughnut' });
                    }
                    if (data.monthly_distribution && Object.keys(data.monthly_distribution).length > 0) {
                        chartsToCreate.push({ title: 'التوزيع الشهري', data: data.monthly_distribution, type: 'pie' });
                    }
                    // Add summary circles
                    const securitySummary = {
                        'العمليات الأمنية': data.total_operations || 0,
                        'التدخلات': data.total_interventions || 0
                    };
                    if (Object.values(securitySummary).some(v => v > 0)) {
                        chartsToCreate.push({ title: 'ملخص الأمن', data: securitySummary, type: 'bar' });
                    }
                    const securityPersonnel = {
                        'المسعفين': data.total_rescuers || 0,
                        'الوفيات': data.total_deaths || 0
                    };
                    if (Object.values(securityPersonnel).some(v => v > 0)) {
                        chartsToCreate.push({ title: 'الأفراد', data: securityPersonnel, type: 'doughnut' });
                    }
                }

                // Exceptional Operations (العمليات الاستثنائية)
                else if (tableType === 'exceptional_operations') {
                    if (data.exceptional_operation_types && Object.keys(data.exceptional_operation_types).length > 0) {
                        chartsToCreate.push({ title: 'أنواع العمليات الاستثنائية', data: data.exceptional_operation_types, type: 'doughnut' });
                    }
                    if (data.resource_distribution && Object.keys(data.resource_distribution).length > 0) {
                        chartsToCreate.push({ title: 'توزيع الموارد', data: data.resource_distribution, type: 'pie' });
                    }
                    // Add summary circles
                    const exceptionalSummary = {
                        'العمليات الاستثنائية': data.total_operations || 0,
                        'التدخلات الطارئة': data.total_interventions || 0
                    };
                    if (Object.values(exceptionalSummary).some(v => v > 0)) {
                        chartsToCreate.push({ title: 'ملخص العمليات الاستثنائية', data: exceptionalSummary, type: 'bar' });
                    }
                    // Resource totals circle
                    const resourceTotals = data.resource_distribution || {};
                    const totalResources = Object.values(resourceTotals).reduce((a, b) => a + b, 0);
                    if (totalResources > 0) {
                        chartsToCreate.push({ title: 'إجمالي الموارد', data: {'الموارد المستخدمة': totalResources}, type: 'doughnut' });
                    }
                }

                // Interventions Without Work (التدخلات بدون عمل)
                else if (tableType === 'interventions_without_work') {
                    if (data.intervention_types && Object.keys(data.intervention_types).length > 0) {
                        chartsToCreate.push({ title: 'أنواع التدخلات بدون عمل', data: data.intervention_types, type: 'doughnut' });
                    }
                    if (data.vehicle_distribution && Object.keys(data.vehicle_distribution).length > 0) {
                        chartsToCreate.push({ title: 'توزيع المركبات', data: data.vehicle_distribution, type: 'pie' });
                    }
                    // Add summary circles
                    const interventionSummary = {
                        'التدخلات بدون عمل': data.total_operations || 0
                    };
                    if (Object.values(interventionSummary).some(v => v > 0)) {
                        chartsToCreate.push({ title: 'ملخص التدخلات', data: interventionSummary, type: 'bar' });
                    }
                    // Vehicle totals circle
                    const vehicleTotals = data.vehicle_distribution || {};
                    const totalVehicles = Object.values(vehicleTotals).reduce((a, b) => a + b, 0);
                    if (totalVehicles > 0) {
                        chartsToCreate.push({ title: 'إجمالي المركبات', data: {'المركبات المستخدمة': totalVehicles}, type: 'doughnut' });
                    }
                }
            }

            // Handle special displays (like forest fire losses) - count them too
            let specialDisplays = 0;
            if (tableType === 'forest_agricultural_fires' && data.losses_detailed && Object.keys(data.losses_detailed).length > 0) {
                specialDisplays = 1;
            }

            // Apply the correct CSS class based on total chart count
            chartCount = chartsToCreate.length + specialDisplays;
            if (chartCount === 1) {
                container.className = 'charts-container single-chart';
            } else {
                container.className = 'charts-container';
            }

            // Create all charts
            chartsToCreate.forEach(chart => {
                createChart(container, chart.title, chart.data, chart.type);
            });

            // Handle special displays (like forest fire losses)
            if (tableType === 'forest_agricultural_fires' && data.losses_detailed && Object.keys(data.losses_detailed).length > 0) {
                createLossesDisplay(container, 'الخسائر المسجلة', data.losses_detailed, data.conversion_note);
            }
        }

        function createChart(container, title, data, type) {
            const chartCard = document.createElement('div');
            chartCard.className = 'chart-card';

            const chartId = 'chart_' + Math.random().toString(36).substr(2, 9);
            const containerClass = type === 'doughnut' || type === 'pie' ? 'circle-chart' : 'chart-container';

            chartCard.innerHTML = `
                <h4>${title}</h4>
                <div class="${containerClass}">
                    <canvas id="${chartId}"></canvas>
                </div>
            `;

            container.appendChild(chartCard);

            // Limit colors to reduce legend size for circles
            const dataKeys = Object.keys(data);
            const maxColors = type === 'doughnut' || type === 'pie' ? Math.min(dataKeys.length, 8) : 16;
            const colors = [
                '#667eea', '#764ba2', '#f093fb', '#f5576c',
                '#4facfe', '#00f2fe', '#43e97b', '#38f9d7'
            ].slice(0, maxColors);

            // If we have more data than colors, group smaller values into "أخرى"
            let processedData = data;
            if (dataKeys.length > maxColors && (type === 'doughnut' || type === 'pie')) {
                const sortedEntries = Object.entries(data).sort((a, b) => b[1] - a[1]);
                const topEntries = sortedEntries.slice(0, maxColors - 1);
                const otherEntries = sortedEntries.slice(maxColors - 1);
                const otherSum = otherEntries.reduce((sum, [key, value]) => sum + value, 0);

                processedData = {};
                topEntries.forEach(([key, value]) => {
                    processedData[key] = value;
                });
                if (otherSum > 0) {
                    processedData['أخرى'] = otherSum;
                }
            }

            // Create the chart
            const ctx = document.getElementById(chartId).getContext('2d');
            const chartConfig = {
                type: type,
                data: {
                    labels: Object.keys(processedData),
                    datasets: [{
                        data: Object.values(processedData),
                        backgroundColor: colors,
                        borderWidth: 3,
                        borderColor: '#fff',
                        hoverBorderWidth: 4,
                        hoverBorderColor: '#333'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: type === 'bar' || type === 'line' ? 'top' : 'bottom',
                            labels: {
                                padding: 12,
                                usePointStyle: true,
                                font: {
                                    family: 'Cairo',
                                    size: 11,
                                    weight: 'bold'
                                },
                                boxWidth: 15,
                                boxHeight: 15
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0,0,0,0.8)',
                            titleColor: '#fff',
                            bodyColor: '#fff',
                            borderColor: '#667eea',
                            borderWidth: 2,
                            cornerRadius: 8,
                            displayColors: true,
                            titleFont: {
                                family: 'Cairo',
                                size: 14,
                                weight: 'bold'
                            },
                            bodyFont: {
                                family: 'Cairo',
                                size: 13,
                                weight: 'bold'
                            }
                        },
                        // Add data labels plugin for showing numbers inside charts
                        datalabels: {
                            display: true,
                            color: '#fff',
                            font: {
                                family: 'Cairo',
                                size: type === 'doughnut' || type === 'pie' ? 20 : 16,
                                weight: 'bold'
                            },
                            formatter: (value, context) => {
                                if (type === 'doughnut' || type === 'pie') {
                                    return value > 0 ? value : '';
                                } else {
                                    return value > 0 ? value : '';
                                }
                            },
                            textStrokeColor: 'rgba(0,0,0,0.3)',
                            textStrokeWidth: 1
                        }
                    }
                }
            };

            // Special configuration for different chart types
            if (type === 'bar') {
                chartConfig.options.scales = {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        },
                        ticks: {
                            font: {
                                weight: 'bold'
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                weight: 'bold'
                            }
                        }
                    }
                };

                // Update datalabels for bar charts
                chartConfig.options.plugins.datalabels = {
                    display: true,
                    color: '#000',
                    font: {
                        family: 'Cairo',
                        size: 14,
                        weight: 'bold'
                    },
                    anchor: 'end',
                    align: 'top',
                    formatter: (value) => {
                        return value > 0 ? value : '';
                    }
                };
            } else if (type === 'line') {
                chartConfig.data.datasets[0].fill = true;
                chartConfig.data.datasets[0].backgroundColor = 'rgba(102, 126, 234, 0.1)';
                chartConfig.data.datasets[0].borderColor = '#667eea';
                chartConfig.data.datasets[0].pointBackgroundColor = '#667eea';
                chartConfig.data.datasets[0].pointBorderColor = '#fff';
                chartConfig.data.datasets[0].pointBorderWidth = 3;
                chartConfig.data.datasets[0].tension = 0.4;

                chartConfig.options.scales = {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                };
            }

            // Register the datalabels plugin
            Chart.register(ChartDataLabels);

            new Chart(ctx, chartConfig);
        }



        function createLossesDisplay(container, title, lossesData, conversionNote) {
            const chartCard = document.createElement('div');
            chartCard.className = 'chart-card';

            let lossesHtml = '';
            for (const [key, value] of Object.entries(lossesData)) {
                // Apply automatic conversion display
                let displayValue = value;
                let displayUnit = key;

                if (key === 'الخسائر بالآر' && value >= 100) {
                    displayValue = (value / 100).toFixed(2);
                    displayUnit = 'الخسائر بالهكتار';
                } else if (key === 'الخسائر بالمتر مربع' && value >= 10000) {
                    displayValue = (value / 10000).toFixed(2);
                    displayUnit = 'الخسائر بالهكتار';
                }

                lossesHtml += `
                    <div class="loss-item">
                        <div class="label">${displayUnit}</div>
                        <div class="value">${displayValue}</div>
                    </div>
                `;
            }

            chartCard.innerHTML = `
                <h4>${title}</h4>
                <div class="losses-grid">
                    ${lossesHtml}
                </div>
                ${conversionNote ? `<p style="font-size: 0.9rem; color: #6c757d; text-align: center; margin-top: 15px; font-style: italic;">${conversionNote}</p>` : ''}
            `;

            container.appendChild(chartCard);
        }

        function exportPDF() {
            const params = new URLSearchParams(window.location.search);
            const exportUrl = '{% url "export_statistics_pdf" %}?' + params.toString();
            window.open(exportUrl, '_blank');
        }

        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
    </script>
</body>
</html>
