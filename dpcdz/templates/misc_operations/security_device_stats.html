{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحماية المدنية الجزائرية - إحصاء الجهاز الأمني</title>
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/home.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="{% static 'css/button-override.css' %}?v=1.0">
    <style>
        .stats-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }



        .stats-content {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-top: 20px;
        }

        .stats-header {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }

        .stats-icon {
            font-size: 2.5rem;
            color: #007bff;
            margin-left: 15px;
        }

        .stats-title {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .stats-form {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .form-row {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 20px;
            justify-content: space-between;
        }

        .form-group {
            margin-bottom: 15px;
            flex: 1;
            min-width: 200px;
            margin-left: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #1d3557;
        }

        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            font-size: 1rem;
        }

        .form-control:focus {
            border-color: #1d3557;
            outline: none;
            box-shadow: 0 0 0 0.2rem rgba(29, 53, 87, 0.25);
        }

        /* Main buttons container */
        .main-buttons-container {
            display: flex;
            justify-content: center;
            margin-top: 30px;
            margin-bottom: 20px;
        }

        /* Main buttons */
        .main-buttons {
            display: flex;
            gap: 20px;
            width: 100%;
            max-width: 800px;
            direction: ltr; /* Force left-to-right layout */
        }

        .main-btn {
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: bold;
            font-size: 18px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            transition: all 0.3s;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
            flex: 1;
            min-width: 200px;
            border: none;
        }

        .main-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .save-btn {
            background-color: #e63946;
            color: white;
        }

        .save-btn:hover {
            background-color: #d62c3b;
        }

        .home-btn {
            background-color: #2a9d46;
            color: white;
        }

        .home-btn:hover {
            background-color: #218838;
        }

        .back-btn {
            background-color: #1d3557;
            color: white;
        }

        .back-btn:hover {
            background-color: #457b9d;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .hidden {
            display: none;
        }

        select.form-control {
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%231d3557' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 0.75rem center;
            background-size: 16px 12px;
            padding-right: 2rem;
        }

        .section-title {
            text-align: center;
            font-weight: bold;
            margin: 20px 0;
            color: #1d3557;
            font-size: 1.2rem;
        }

        .date-display {
            text-align: center;
            margin: 10px 0;
            font-weight: bold;
            font-size: 1.1rem;
        }

        /* Floating buttons styles */
        .floating-buttons {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .floating-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            transition: all 0.3s;
            text-decoration: none;
            font-size: 18px;
            border: none;
            cursor: pointer;
        }

        .floating-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
        }

        .tables-btn {
            background-color: #0d47a1;
        }

        .tables-btn:hover {
            background-color: #0a3880;
        }

        .top-btn {
            background-color: #0d6efd;
        }

        .top-btn:hover {
            background-color: #0a58ca;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
            }

            .form-group {
                width: 100%;
            }

            .floating-buttons {
                bottom: 10px;
                right: 10px;
                gap: 8px;
            }

            .floating-btn {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }

            .main-buttons {
                flex-direction: column; /* Stack buttons vertically */
                gap: 15px;
                width: 90%;
            }

            .main-btn {
                width: 100%;
                padding: 12px 20px;
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle" id="sidebar-toggle" style="cursor: pointer;">
            </div>
        </div>
    </header>

    {% include 'includes/sidebar.html' %}

    <main>
        <div class="stats-container">


            <div class="stats-header">
                <div class="stats-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="stats-title">إحصاء الجهاز الأمني</div>
            </div>

            <div id="alert-container" class="hidden"></div>

            <div class="stats-content">
                <div class="date-display" id="formatted-date-display"></div>

                <div class="stats-form">
                    <form id="securityDeviceForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="date">التاريخ</label>
                                <input type="date" class="form-control" id="date" name="date" required lang="ar">
                            </div>

                            <div class="form-group">
                                <label for="intervening_unit">الوحدات المتدخلة</label>
                                <select class="form-control" id="intervening_unit" name="intervening_unit" required>
                                    <option value="">اختر الوحدة المتدخلة</option>
                                    {% for unit in units %}
                                    <option value="{{ unit.name }}">{{ unit.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="security_device_type">أنواع الأجهزة الأمنية</label>
                                <select class="form-control" id="security_device_type" name="security_device_type" required>
                                    <option value="">اختر نوع الجهاز الأمني</option>
                                    <option value="النشاطات الثقافية و الدينية">النشاطات الثقافية و الدينية</option>
                                    <option value="المؤسسات التربوية">المؤسسات التربوية</option>
                                    <option value="النظام الأمن العام">النظام الأمن العام</option>
                                    <option value="الزيارات الرسمية">الزيارات الرسمية</option>
                                    <option value="النشاطات الرياضية">النشاطات الرياضية</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="number_of_operations">عدد العمليات</label>
                                <input type="number" class="form-control" id="number_of_operations" name="number_of_operations" min="0" value="0" required>
                            </div>

                            <div class="form-group">
                                <label for="number_of_interventions">عدد التدخلات</label>
                                <input type="number" class="form-control" id="number_of_interventions" name="number_of_interventions" min="0" value="0" required>
                            </div>

                            <div class="form-group">
                                <label for="number_of_rescuers">عدد المسعفين</label>
                                <input type="number" class="form-control" id="number_of_rescuers" name="number_of_rescuers" min="0" value="0" required>
                            </div>

                            <div class="form-group">
                                <label for="number_of_deaths">عدد الوفيات</label>
                                <input type="number" class="form-control" id="number_of_deaths" name="number_of_deaths" min="0" value="0" required>
                            </div>
                        </div>

                        <div class="main-buttons-container">
                            <div class="main-buttons">
                                <a href="{% url 'home' %}" class="main-btn home-btn">
                                    <i class="fas fa-home"></i> الصفحة الرئيسية
                                </a>
                                <a href="{% url 'misc_operations' %}" class="main-btn back-btn">
                                    <i class="fas fa-arrow-right"></i> العمليات المختلفة
                                </a>
                                <button type="button" id="saveButton" class="main-btn save-btn">
                                    <i class="fas fa-save"></i> حفظ
                                </button>
                            </div>
                        </div>

                        <!-- الأزرار العائمة -->
                        <div class="floating-buttons">
                            <a href="{% url 'table_misc_operations' %}" class="floating-btn tables-btn" title="لوحة الجداول">
                                <i class="fas fa-th"></i>
                            </a>
                            <a href="{% url 'home' %}" class="floating-btn home-btn" title="الصفحة الرئيسية">
                                <i class="fas fa-home"></i>
                            </a>
                            <button type="button" id="floatingSaveBtn" class="floating-btn save-btn" title="حفظ">
                                <i class="fas fa-save"></i>
                            </button>
                            <a href="#" id="back-to-top" class="floating-btn top-btn" title="العودة إلى الأعلى">
                                <i class="fas fa-arrow-up"></i>
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <p>حقوق النشر © 2025 جميع الحقوق محفوظة - تم التطوير بواسطة عبد الرزاق مختاري</p>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('securityDeviceForm');
            const saveButton = document.getElementById('saveButton');
            const alertContainer = document.getElementById('alert-container');
            const dateInput = document.getElementById('date');
            const formattedDateDisplay = document.getElementById('formatted-date-display');

            // Back to top button functionality
            const backToTopButton = document.getElementById('back-to-top');
            if (backToTopButton) {
                backToTopButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                });
            }

            // Floating save button functionality
            const floatingSaveButton = document.getElementById('floatingSaveBtn');
            if (floatingSaveButton) {
                floatingSaveButton.addEventListener('click', function() {
                    // Trigger the regular save button
                    document.getElementById('saveButton').click();
                });
            }

            // Set up Arabic month names
            const arabicMonths = {
                '01': 'جانفي',
                '02': 'فيفري',
                '03': 'مارس',
                '04': 'أفريل',
                '05': 'ماي',
                '06': 'جوان',
                '07': 'جويلية',
                '08': 'أوت',
                '09': 'سبتمبر',
                '10': 'أكتوبر',
                '11': 'نوفمبر',
                '12': 'ديسمبر'
            };

            // Format date display in Arabic
            dateInput.addEventListener('change', function() {
                if (this.value) {
                    const dateParts = this.value.split('-');
                    const year = dateParts[0];
                    const month = dateParts[1];
                    const day = dateParts[2];

                    const formattedDate = `${day} ${arabicMonths[month]} ${year}`;

                    // Display the formatted date
                    formattedDateDisplay.textContent = formattedDate;

                    // Store the formatted date as a data attribute
                    this.setAttribute('data-formatted-date', formattedDate);
                } else {
                    formattedDateDisplay.textContent = '';
                }
            });

            // Function to show alert
            function showAlert(message, type) {
                alertContainer.className = `alert alert-${type}`;
                alertContainer.textContent = message;
                alertContainer.classList.remove('hidden');

                // Auto hide after 5 seconds
                setTimeout(() => {
                    alertContainer.classList.add('hidden');
                }, 5000);
            }

            // Function to validate form
            function validateForm() {
                const date = document.getElementById('date').value;
                const interveningUnit = document.getElementById('intervening_unit').value;
                const securityDeviceType = document.getElementById('security_device_type').value;
                const numberOfOperations = document.getElementById('number_of_operations').value;
                const numberOfInterventions = document.getElementById('number_of_interventions').value;
                const numberOfRescuers = document.getElementById('number_of_rescuers').value;
                const numberOfDeaths = document.getElementById('number_of_deaths').value;

                const missingFields = [];

                if (!date) missingFields.push('التاريخ');
                if (!interveningUnit) missingFields.push('الوحدات المتدخلة');
                if (!securityDeviceType) missingFields.push('أنواع الأجهزة الأمنية');
                if (numberOfOperations === '') missingFields.push('عدد العمليات');
                if (numberOfInterventions === '') missingFields.push('عدد التدخلات');
                if (numberOfRescuers === '') missingFields.push('عدد المسعفين');
                if (numberOfDeaths === '') missingFields.push('عدد الوفيات');

                if (missingFields.length > 0) {
                    showAlert('يرجى ملء الحقول التالية: ' + missingFields.join('، '), 'danger');
                    return false;
                }

                // Check for negative numbers
                if (parseInt(numberOfOperations) < 0 ||
                    parseInt(numberOfInterventions) < 0 ||
                    parseInt(numberOfRescuers) < 0 ||
                    parseInt(numberOfDeaths) < 0) {
                    showAlert('لا يمكن إدخال أرقام سالبة', 'danger');
                    return false;
                }

                return true;
            }

            // Save button click handler
            saveButton.addEventListener('click', function() {
                if (!validateForm()) {
                    return;
                }

                // Prepare data for submission
                const dateInput = document.getElementById('date');

                const formData = {
                    date: dateInput.value,
                    formatted_date: dateInput.getAttribute('data-formatted-date') || dateInput.value,
                    intervening_unit: document.getElementById('intervening_unit').value,
                    security_device_type: document.getElementById('security_device_type').value,
                    number_of_operations: parseInt(document.getElementById('number_of_operations').value),
                    number_of_interventions: parseInt(document.getElementById('number_of_interventions').value),
                    number_of_rescuers: parseInt(document.getElementById('number_of_rescuers').value),
                    number_of_deaths: parseInt(document.getElementById('number_of_deaths').value)
                };

                // Send data to server
                fetch(window.location.href, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        showAlert(data.message, 'success');
                        // Reset form
                        form.reset();
                        formattedDateDisplay.textContent = '';
                    } else {
                        showAlert(data.message, 'danger');
                    }
                })
                .catch(error => {
                    showAlert('حدث خطأ أثناء حفظ البيانات: ' + error, 'danger');
                });
            });

            // Function to get CSRF token
            function getCookie(name) {
                let cookieValue = null;
                if (document.cookie && document.cookie !== '') {
                    const cookies = document.cookie.split(';');
                    for (let i = 0; i < cookies.length; i++) {
                        const cookie = cookies[i].trim();
                        if (cookie.substring(0, name.length + 1) === (name + '=')) {
                            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                            break;
                        }
                    }
                }
                return cookieValue;
            }
        });
    </script>
    <script src="{% static 'js/sidebar.js' %}"></script>
</body>
</html>
