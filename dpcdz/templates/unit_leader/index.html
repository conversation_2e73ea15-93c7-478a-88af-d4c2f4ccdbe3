{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحماية المدنية الجزائرية - قائد الوحدة</title>
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/home.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle" id="sidebar-toggle" style="cursor: pointer;">
            </div>
        </div>
    </header>

    {% include 'includes/sidebar.html' %}

    <main>
        <div class="home-container">
            <div class="page-header">
                <div class="page-icon">
                    <i class="fas fa-user-cog"></i>
                </div>
                <h2>قائد الوحدة - الدعم</h2>
                <p class="page-description">واجهة قائد الوحدة لإدارة الدعم والتنسيق بين الوحدات</p>
            </div>

            <!-- معلومات التدخل الحالي -->
            <div class="current-intervention">
                <h3><i class="fas fa-info-circle"></i> التدخل الحالي</h3>
                <div class="intervention-card">
                    <div class="intervention-header">
                        <h4>حريق غابة - جبل بوكرع</h4>
                        <span class="priority high">أولوية عالية</span>
                    </div>
                    <div class="intervention-details">
                        <div class="detail-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>دائرة المشروحة، ولاية سوق أهراس</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-clock"></i>
                            <span>بدء التدخل: 2025/07/15 - 14:22</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-building"></i>
                            <span>الوحدة الطالبة: وحدة المشروحة</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-users"></i>
                            <span>الوحدات المتدخلة: 3 وحدات</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الأزرار الرئيسية -->
            <div class="action-buttons">
                <button class="action-btn support-btn" id="manage-support">
                    <div class="btn-icon">
                        <i class="fas fa-hands-helping"></i>
                    </div>
                    <div class="btn-content">
                        <h3>إدارة الدعم</h3>
                        <p>تنسيق الوحدات الداعمة والوسائل</p>
                    </div>
                </button>

                <button class="action-btn report-btn" id="support-report">
                    <div class="btn-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="btn-content">
                        <h3>تقرير الدعم</h3>
                        <p>كتابة التقرير الرسمي للتدخل الداعم</p>
                    </div>
                </button>

                <button class="action-btn status-btn" id="update-status">
                    <div class="btn-icon">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div class="btn-content">
                        <h3>تحديث الحالة</h3>
                        <p>تحديث حالة الوسائل والأعوان</p>
                    </div>
                </button>
            </div>

            <!-- الوحدات الداعمة -->
            <div class="support-units">
                <h3><i class="fas fa-truck"></i> الوحدات الداعمة</h3>
                <div class="units-grid">
                    <div class="unit-card">
                        <div class="unit-header">
                            <h4>وحدة سوق أهراس</h4>
                            <span class="status active">نشطة</span>
                        </div>
                        <div class="unit-details">
                            <p><i class="fas fa-truck"></i> شاحنة إطفاء FPT-01</p>
                            <p><i class="fas fa-users"></i> 6 أعوان</p>
                            <p><i class="fas fa-clock"></i> وصلت: 14:45</p>
                        </div>
                        <div class="unit-actions">
                            <button class="btn btn-sm btn-primary">تفاصيل</button>
                            <button class="btn btn-sm btn-warning">تحديث</button>
                        </div>
                    </div>

                    <div class="unit-card">
                        <div class="unit-header">
                            <h4>وحدة تبسة</h4>
                            <span class="status en-route">في الطريق</span>
                        </div>
                        <div class="unit-details">
                            <p><i class="fas fa-truck"></i> شاحنة إطفاء FPT-03</p>
                            <p><i class="fas fa-users"></i> 8 أعوان</p>
                            <p><i class="fas fa-clock"></i> الوصول المتوقع: 15:30</p>
                        </div>
                        <div class="unit-actions">
                            <button class="btn btn-sm btn-primary">تفاصيل</button>
                            <button class="btn btn-sm btn-info">تتبع</button>
                        </div>
                    </div>

                    <div class="unit-card">
                        <div class="unit-header">
                            <h4>طائرة إطفاء</h4>
                            <span class="status requested">مطلوبة</span>
                        </div>
                        <div class="unit-details">
                            <p><i class="fas fa-plane"></i> طائرة قاذفة مياه</p>
                            <p><i class="fas fa-users"></i> طيار + مساعد</p>
                            <p><i class="fas fa-clock"></i> في انتظار الموافقة</p>
                        </div>
                        <div class="unit-actions">
                            <button class="btn btn-sm btn-success">طلب</button>
                            <button class="btn btn-sm btn-secondary">إلغاء</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول التدخلات السابقة -->
            <div class="previous-interventions">
                <h3><i class="fas fa-history"></i> التدخلات السابقة</h3>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>رقم</th>
                                <th>نوع التدخل</th>
                                <th>الوحدة الطالبة</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                                <th>إجراء</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>001</td>
                                <td>حريق محاصيل</td>
                                <td>وحدة بئر بوحوش</td>
                                <td>2025/07/14 - 16:30</td>
                                <td><span class="status completed">مكتمل</span></td>
                                <td>
                                    <button class="btn btn-sm btn-primary">عرض التقرير</button>
                                </td>
                            </tr>
                            <tr>
                                <td>002</td>
                                <td>حادث مرور</td>
                                <td>وحدة الحدادة</td>
                                <td>2025/07/13 - 09:15</td>
                                <td><span class="status completed">مكتمل</span></td>
                                <td>
                                    <button class="btn btn-sm btn-primary">عرض التقرير</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- الأزرار العائمة -->
            <div class="floating-buttons">
                <a href="{% url 'major_disasters' %}" class="floating-btn disaster-btn" title="الكوارث الكبرى">
                    <i class="fas fa-exclamation-circle"></i>
                </a>
                <a href="{% url 'coordination_center' %}" class="floating-btn coordination-btn" title="مركز التنسيق">
                    <i class="fas fa-building"></i>
                </a>
                <a href="{% url 'home' %}" class="floating-btn home-btn" title="الصفحة الرئيسية">
                    <i class="fas fa-home"></i>
                </a>
                <a href="#" id="back-to-top" class="floating-btn top-btn" title="العودة إلى الأعلى">
                    <i class="fas fa-arrow-up"></i>
                </a>
            </div>
        </div>
    </main>

    <!-- Modal for Support Report -->
    <div class="modal fade" id="supportReportModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تقرير الدعم الرسمي</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="supportReportForm">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">عدد الأعوان المتدخلين</label>
                                <input type="number" class="form-control" id="personnel-count">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">الوسيلة المستخدمة</label>
                                <select class="form-control" id="vehicle-used">
                                    <option value="">اختر الوسيلة</option>
                                    <option value="FPT-01">شاحنة إطفاء FPT-01</option>
                                    <option value="FPT-03">شاحنة إطفاء FPT-03</option>
                                    <option value="AMB-02">سيارة إسعاف AMB-02</option>
                                </select>
                            </div>
                        </div>
                        <div class="mt-3">
                            <label class="form-label">الخسائر المُشاهدة</label>
                            <textarea class="form-control" rows="3" id="observed-damages"></textarea>
                        </div>
                        <div class="mt-3">
                            <label class="form-label">الأملاك المنقذة</label>
                            <textarea class="form-control" rows="3" id="saved-property"></textarea>
                        </div>
                        <div class="mt-3">
                            <label class="form-label">ملاحظات ميدانية أو تقنية</label>
                            <textarea class="form-control" rows="4" id="technical-notes"></textarea>
                        </div>
                        <div class="mt-3">
                            <label class="form-label">إرفاق وثائق</label>
                            <input type="file" class="form-control" multiple accept="image/*,.pdf" id="support-documents">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="save-support-report">حفظ التقرير</button>
                </div>
            </div>
        </div>
    </div>

    <footer>
        <p>حقوق النشر © 2025 جميع الحقوق محفوظة - تم التطوير بواسطة عبد الرزاق مختاري</p>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{% static 'js/sidebar.js' %}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Action button handlers
            document.getElementById('support-report').addEventListener('click', function() {
                const modal = new bootstrap.Modal(document.getElementById('supportReportModal'));
                modal.show();
            });

            document.getElementById('manage-support').addEventListener('click', function() {
                alert('فتح واجهة إدارة الدعم...');
            });

            document.getElementById('update-status').addEventListener('click', function() {
                alert('فتح واجهة تحديث الحالة...');
            });

            // Save support report
            document.getElementById('save-support-report').addEventListener('click', function() {
                const personnelCount = document.getElementById('personnel-count').value;
                const vehicleUsed = document.getElementById('vehicle-used').value;
                
                if (!personnelCount || !vehicleUsed) {
                    alert('يرجى ملء الحقول المطلوبة');
                    return;
                }
                
                alert('تم حفظ تقرير الدعم بنجاح');
                bootstrap.Modal.getInstance(document.getElementById('supportReportModal')).hide();
            });

            // Back to top button
            const backToTopButton = document.getElementById('back-to-top');
            if (backToTopButton) {
                backToTopButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                });
            }
        });
    </script>

    <style>
        /* Page header styles */
        .page-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #007bff, #6610f2);
            border-radius: 10px;
            color: white;
        }

        .page-icon {
            font-size: 48px;
            margin-bottom: 10px;
        }

        .page-description {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }

        /* Current intervention */
        .current-intervention {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .intervention-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
        }

        .intervention-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .intervention-header h4 {
            margin: 0;
            color: #333;
        }

        .priority {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .priority.high {
            background: #dc3545;
            color: white;
        }

        .intervention-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 10px;
        }

        .detail-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #666;
        }

        .detail-item i {
            color: #007bff;
            width: 16px;
        }

        /* Action buttons */
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .action-btn {
            display: flex;
            align-items: center;
            gap: 20px;
            padding: 25px;
            border: none;
            border-radius: 10px;
            background: white;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            text-align: right;
        }

        .action-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .support-btn {
            border-left: 5px solid #17a2b8;
        }

        .support-btn:hover {
            background: #d1ecf1;
        }

        .report-btn {
            border-left: 5px solid #28a745;
        }

        .report-btn:hover {
            background: #d4edda;
        }

        .status-btn {
            border-left: 5px solid #ffc107;
        }

        .status-btn:hover {
            background: #fff3cd;
        }

        .btn-icon {
            font-size: 32px;
            width: 60px;
            text-align: center;
        }

        .support-btn .btn-icon {
            color: #17a2b8;
        }

        .report-btn .btn-icon {
            color: #28a745;
        }

        .status-btn .btn-icon {
            color: #ffc107;
        }

        .btn-content h3 {
            margin: 0 0 5px 0;
            color: #333;
        }

        .btn-content p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }

        /* Support units */
        .support-units {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .units-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .unit-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            background: #f8f9fa;
        }

        .unit-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .unit-header h4 {
            margin: 0;
            color: #333;
            font-size: 16px;
        }

        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .status.active {
            background: #28a745;
            color: white;
        }

        .status.en-route {
            background: #ffc107;
            color: #333;
        }

        .status.requested {
            background: #6c757d;
            color: white;
        }

        .status.completed {
            background: #28a745;
            color: white;
        }

        .unit-details p {
            margin: 5px 0;
            color: #666;
            font-size: 14px;
        }

        .unit-details i {
            color: #007bff;
            width: 16px;
        }

        .unit-actions {
            margin-top: 10px;
            display: flex;
            gap: 5px;
        }

        /* Previous interventions */
        .previous-interventions {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .previous-interventions h3 {
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f8f9fa;
        }

        /* Floating buttons */
        .floating-buttons {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .floating-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            transition: all 0.3s;
            text-decoration: none;
            font-size: 18px;
            border: none;
            cursor: pointer;
        }

        .floating-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
        }

        .floating-btn.disaster-btn {
            background-color: #dc3545;
        }

        .floating-btn.coordination-btn {
            background-color: #28a745;
        }

        .floating-btn.home-btn {
            background-color: #0d47a1;
        }

        .floating-btn.top-btn {
            background-color: #0d6efd;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .action-buttons {
                grid-template-columns: 1fr;
            }

            .action-btn {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }

            .units-grid {
                grid-template-columns: 1fr;
            }

            .intervention-details {
                grid-template-columns: 1fr;
            }
        }
    </style>
</body>
</html>
