{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحماية المدنية الجزائرية - جدول الإجلاء الصحي</title>
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/home.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="{% static 'css/button-override.css' %}?v=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .table-container {
            max-width: 100%;
            margin: 0 auto;
            padding: 20px;
        }

        .table-wrapper {
            overflow-x: auto;
            margin-top: 20px;
            max-height: 70vh;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        /* إضافة تلميح للمستخدم بإمكانية التمرير */
        .table-wrapper::after {
            content: "← قم بالتمرير للمزيد →";
            position: absolute;
            bottom: 10px;
            right: 50%;
            transform: translateX(50%);
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            opacity: 0.8;
            pointer-events: none; /* لا يمنع التفاعل مع الجدول */
            animation: fadeOut 3s forwards 3s; /* اختفاء بعد 6 ثوان */
        }

        @keyframes fadeOut {
            from { opacity: 0.8; }
            to { opacity: 0; }
        }

        h2.page-title {
            text-align: center;
            margin-bottom: 30px;
            color: #0d47a1;
            border-bottom: 2px solid #0d47a1;
            padding-bottom: 10px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 80px; /* زيادة المساحة أسفل الجدول للأزرار العائمة */
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            overflow: hidden;
            font-size: 14px;
        }

        .data-table th,
        .data-table td {
            padding: 8px;
            text-align: center;
            border: 1px solid #ddd;
            white-space: nowrap;
        }

        /* تنسيق خاص لعمود التاريخ */
        .data-table td:first-child {
            min-width: 150px; /* عرض أكبر لعمود التاريخ */
            font-weight: bold; /* خط أكثر سمكًا */
            font-size: 15px; /* حجم خط أكبر */
            padding: 10px; /* تباعد داخلي أكبر */
            background-color: #f8f9fa; /* خلفية مميزة */
        }

        .data-table th:first-child {
            min-width: 150px; /* عرض أكبر لعمود التاريخ */
            font-weight: bold; /* خط أكثر سمكًا */
            font-size: 15px; /* حجم خط أكبر */
            padding: 10px; /* تباعد داخلي أكبر */
            background-color: #0d47a1; /* خلفية زرقاء */
            color: white; /* لون النص أبيض */
        }

        .data-table th {
            background-color: #0d47a1;
            position: sticky;
            top: 0;
            z-index: 10;
            font-weight: bold;
            color: white;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        /* تثبيت عمود الإجراءات على اليسار */
        .data-table th:last-child,
        .data-table td:last-child {
            position: sticky;
            left: 0;
            background-color: #f8f9fa;
            z-index: 5;
            box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
            min-width: 80px;
            text-align: center;
        }

        /* تغيير خلفية خلية الإجراءات عند التحويم */
        .data-table tr:hover td:last-child {
            background-color: #e9f5ff;
        }

        /* تغيير خلفية خلية الإجراءات للصفوف الزوجية */
        .data-table tr:nth-child(even) td:last-child {
            background-color: #f9f9f9;
        }

        /* تنسيق خاص لخلية الإجراءات */
        .actions-cell {
            padding: 5px !important;
            white-space: nowrap;
            width: 80px;
        }

        /* تنسيق صف الفلاتر */
        .filter-row th {
            padding: 5px;
            background-color: #e6f0ff;
            color: #0d47a1;
            position: sticky;
            top: 40px; /* يجب أن تكون أكبر من ارتفاع الصف الأول */
            z-index: 9;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .filter-select {
            width: 100%;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: white;
            font-size: 12px;
            text-align: center;
        }

        .date-filter {
            display: flex;
            gap: 5px;
        }

        .date-filter .filter-select {
            width: 50%;
        }

        .reset-filters-btn {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
            width: 100%;
        }

        .reset-filters-btn:hover {
            background-color: #e9ecef;
            border-color: #ced4da;
        }

        .data-table tr:hover {
            background-color: #f5f5f5;
        }

        .data-table tr:last-child td {
            border-bottom: none;
        }

        .table-actions {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .btn-add {
            background-color: #28a745;
            color: white;
            padding: 12px 20px;
            border-radius: 50px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .btn-add:hover {
            background-color: #218838;
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
        }

        .btn-export {
            background-color: #6c757d;
            color: white;
            padding: 12px 20px;
            border-radius: 50px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .btn-export:hover {
            background-color: #5a6268;
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
        }

        .btn-edit {
            color: #0d6efd;
            text-decoration: none;
            margin-right: 10px;
            display: inline-block;
            width: 30px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            border-radius: 50%;
            transition: background-color 0.3s;
        }

        .btn-edit:hover {
            background-color: rgba(13, 110, 253, 0.1);
        }

        .btn-delete {
            color: #dc3545;
            text-decoration: none;
            display: inline-block;
            width: 30px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            border-radius: 50%;
            transition: background-color 0.3s;
        }

        .btn-delete:hover {
            background-color: rgba(220, 53, 69, 0.1);
        }

        .empty-data {
            padding: 30px;
            text-align: center;
            color: #6c757d;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .navigation-buttons {
            display: flex;
            justify-content: center;
            gap: 10px;
            position: fixed;
            bottom: 20px;
            left: 20px;
            z-index: 100;
        }

        .btn {
            padding: 10px 15px;
            border-radius: 50px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
        }

        .btn-blue {
            background-color: #0d47a1;
            color: white;
        }

        .btn-gray {
            background-color: #6c757d;
            color: white;
        }

        /* تنسيق للأزرار العائمة */
        .floating-buttons {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .floating-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            transition: all 0.3s;
        }

        .floating-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
        }

        .add-btn {
            background-color: #28a745;
        }

        .add-btn:hover {
            background-color: #218838;
        }

        .export-btn {
            background-color: #6c757d;
        }

        .export-btn:hover {
            background-color: #5a6268;
        }

        .top-btn {
            background-color: #0d6efd;
        }

        .top-btn:hover {
            background-color: #0a58ca;
        }

        @media (max-width: 768px) {
            .data-table {
                display: block;
                overflow-x: auto;
                white-space: nowrap;
            }

            .navigation-buttons {
                left: 10px;
                bottom: 70px;
            }

            .floating-buttons {
                bottom: 10px;
                right: 10px;
            }

            .floating-btn {
                width: 40px;
                height: 40px;
                font-size: 14px;
            }

            .btn-add, .btn-export, .btn {
                padding: 8px 12px;
                font-size: 12px;
            }

            .btn-add i, .btn-export i, .btn i {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle" id="sidebar-toggle" style="cursor: pointer;">
            </div>
        </div>
    </header>

    {% include 'includes/sidebar.html' %}

    <main>
        <div class="table-container">
            <h2 class="page-title">جدول الإجلاء الصحي</h2>

            <div class="table-actions">
                <a href="{% url 'medical_evacuation' %}" class="btn-add">
                    <i class="fas fa-plus"></i> إضافة جديد
                </a>
                <a href="#" id="export-excel-btn" class="btn-export">
                    <i class="fas fa-file-excel"></i> تصدير إلى Excel
                </a>
            </div>

            {% if medical_evacuation_data %}
            <div class="table-wrapper">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            {% if is_admin %}
                            <th>الولاية</th>
                            {% endif %}
                            <th>الوحدة المتدخلة</th>
                            <th>نوع التدخل</th>
                            <th>طبيعة التدخل</th>
                            <th>داخل المنزل</th>
                            <th>خارج المنزل</th>
                            <th>عدد العمليات</th>
                            <th>عدد التدخلات</th>
                            <!-- Detailed columns for all intervention types (hidden by default) -->
                            <th class="detailed-column" style="display: none;">عدد المسعفين أطفال</th>
                            <th class="detailed-column" style="display: none;">عدد المسعفين نساء</th>
                            <th class="detailed-column" style="display: none;">عدد المسعفين رجال</th>
                            <th class="detailed-column" style="display: none;">مجموع المسعفين التفصيلي</th>
                            <th class="detailed-column" style="display: none;">عدد الوفيات أطفال</th>
                            <th class="detailed-column" style="display: none;">عدد الوفيات نساء</th>
                            <th class="detailed-column" style="display: none;">عدد الوفيات رجال</th>
                            <th class="detailed-column" style="display: none;">مجموع الوفيات التفصيلي</th>
                            <th>الإجراءات</th>
                        </tr>
                        <tr class="filter-row">
                            <th>
                                <div class="date-filter">
                                    <select id="month-filter" class="filter-select">
                                        <option value="">الشهر</option>
                                        {% for month_code, month_name in months %}
                                        <option value="{{ month_code }}">{{ month_name }}</option>
                                        {% endfor %}
                                    </select>
                                    <select id="year-filter" class="filter-select">
                                        <option value="">السنة</option>
                                        {% for year in years %}
                                        <option value="{{ year }}">{{ year }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </th>
                            {% if is_admin %}
                            <th>
                                <select id="wilaya-filter" class="filter-select">
                                    <option value="">الكل</option>
                                    {% for wilaya_code, wilaya_name in wilaya_choices %}
                                    <option value="{{ wilaya_code }}">{{ wilaya_name }}</option>
                                    {% endfor %}
                                </select>
                            </th>
                            {% endif %}
                            <th>
                                <select id="unit-filter" class="filter-select">
                                    <option value="">الكل</option>
                                    {% for unit in user_units %}
                                    <option value="{{ unit.name }}">{{ unit.name }}</option>
                                    {% endfor %}
                                </select>
                            </th>
                            <th>
                                <select id="intervention-type-filter" class="filter-select">
                                    <option value="">الكل</option>
                                    {% for intervention_type in intervention_types %}
                                    <option value="{{ intervention_type }}">{{ intervention_type }}</option>
                                    {% endfor %}
                                </select>
                            </th>
                            <th>
                                <select id="intervention-nature-filter" class="filter-select">
                                    <option value="">اختر نوع التدخل أولاً</option>
                                </select>
                            </th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <!-- Detailed filter columns (hidden by default) -->
                            <th class="detailed-column" style="display: none;"></th>
                            <th class="detailed-column" style="display: none;"></th>
                            <th class="detailed-column" style="display: none;"></th>
                            <th class="detailed-column" style="display: none;"></th>
                            <th class="detailed-column" style="display: none;"></th>
                            <th class="detailed-column" style="display: none;"></th>
                            <th class="detailed-column" style="display: none;"></th>
                            <th class="detailed-column" style="display: none;"></th>
                            <th>
                                <button id="reset-filters" class="reset-filters-btn">
                                    <i class="fas fa-undo"></i> إعادة تعيين
                                </button>
                            </th>
                        </tr>
                    </thead>
                    <tbody id="data-table-body">
                        {% for item in medical_evacuation_data %}
                        <tr>
                            <td>{{ item.formatted_date }}</td>
                            {% if is_admin %}
                            <td>{{ item.wilaya_name }}</td>
                            {% endif %}
                            <td>{{ item.unit }}</td>
                            <td>{{ item.intervention_type }}</td>
                            <td>{{ item.intervention_nature }}</td>
                            <td>{% if item.location == 'داخل المنزل' %}1{% else %}0{% endif %}</td>
                            <td>{% if item.location == 'خارج المنزل' %}1{% else %}0{% endif %}</td>
                            <td>{{ item.operations_count }}</td>
                            <td>{{ item.interventions_count }}</td>
                            <!-- Detailed data columns for all intervention types (hidden by default) -->
                            <td class="detailed-column" style="display: none;">{{ item.paramedics_children_count }}</td>
                            <td class="detailed-column" style="display: none;">{{ item.paramedics_women_count }}</td>
                            <td class="detailed-column" style="display: none;">{{ item.paramedics_men_count }}</td>
                            <td class="detailed-column" style="display: none;">{{ item.total_paramedics_detailed_count }}</td>
                            <td class="detailed-column" style="display: none;">{{ item.deaths_children_count }}</td>
                            <td class="detailed-column" style="display: none;">{{ item.deaths_women_count }}</td>
                            <td class="detailed-column" style="display: none;">{{ item.deaths_men_count }}</td>
                            <td class="detailed-column" style="display: none;">{{ item.total_deaths_detailed_count }}</td>
                            <td class="actions-cell">
                                <a href="#" class="btn-edit" title="تعديل" data-id="{{ item.id }}">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="#" class="btn-delete" title="حذف" data-id="{{ item.id }}">
                                    <i class="fas fa-trash-alt"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="empty-data">
                <i class="fas fa-info-circle" style="font-size: 24px; margin-bottom: 10px;"></i>
                <p>لا توجد بيانات متاحة حاليًا.</p>
            </div>
            {% endif %}

            <!-- الأزرار العائمة -->
            <div class="floating-buttons">
                <a href="{% url 'medical_evacuation' %}" class="floating-btn add-btn" title="إضافة جديد">
                    <i class="fas fa-plus"></i>
                </a>
                <a href="#" id="floating-export-btn" class="floating-btn export-btn" title="تصدير إلى Excel">
                    <i class="fas fa-file-excel"></i>
                </a>
                <a href="#" id="back-to-top" class="floating-btn top-btn" title="العودة إلى الأعلى">
                    <i class="fas fa-arrow-up"></i>
                </a>
            </div>

            <!-- أزرار التنقل -->
            <div class="navigation-buttons">
                <a href="{% url 'tables_dashboard' %}" class="btn btn-blue">
                    <i class="fas fa-th-large"></i> لوحة الجداول
                </a>
                <a href="{% url 'home' %}" class="btn btn-gray">
                    <i class="fas fa-home"></i> الصفحة الرئيسية
                </a>
            </div>
        </div>
    </main>

    <footer>
        <p>حقوق النشر © 2025 جميع الحقوق محفوظة - تم التطوير بواسطة عبد الرزاق مختاري</p>
    </footer>

    <script src="{% static 'js/sidebar.js' %}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Back to top button
            const backToTopButton = document.getElementById('back-to-top');
            backToTopButton.addEventListener('click', function(e) {
                e.preventDefault();
                window.scrollTo({ top: 0, behavior: 'smooth' });
            });

            // Export to Excel button
            const exportExcelBtn = document.getElementById('export-excel-btn');
            const floatingExportBtn = document.getElementById('floating-export-btn');

            function exportToExcel() {
                // Get filter values
                const monthFilter = document.getElementById('month-filter').value;
                const yearFilter = document.getElementById('year-filter').value;
                const unitFilter = document.getElementById('unit-filter').value;
                const interventionTypeFilter = document.getElementById('intervention-type-filter')?.value || '';
                const interventionNatureFilter = document.getElementById('intervention-nature-filter')?.value || '';
                // Indoor/outdoor filters removed as requested

                // Build query string with filters
                let queryParams = new URLSearchParams();
                if (monthFilter) queryParams.append('month', monthFilter);
                if (yearFilter) queryParams.append('year', yearFilter);
                if (unitFilter) queryParams.append('unit', unitFilter);
                if (interventionTypeFilter) queryParams.append('intervention_type', interventionTypeFilter);
                if (interventionNatureFilter) queryParams.append('intervention_nature', interventionNatureFilter);
                // Indoor/outdoor filter parameters removed as requested

                {% if is_admin %}
                const wilayaFilter = document.getElementById('wilaya-filter').value;
                if (wilayaFilter) queryParams.append('wilaya', wilayaFilter);
                {% endif %}

                // Use Django URL pattern for export
                const baseUrl = "{% url 'export_table_to_excel' 'medical_evacuation' %}";
                const url = `${baseUrl}?${queryParams.toString()}`;

                // Redirect to export URL with filters
                window.location.href = url;
            }

            exportExcelBtn.addEventListener('click', function(e) {
                e.preventDefault();
                exportToExcel();
            });

            floatingExportBtn.addEventListener('click', function(e) {
                e.preventDefault();
                exportToExcel();
            });

            // Filter functionality
            const monthFilter = document.getElementById('month-filter');
            const yearFilter = document.getElementById('year-filter');
            const unitFilter = document.getElementById('unit-filter');
            const interventionTypeFilter = document.getElementById('intervention-type-filter');
            const interventionNatureFilter = document.getElementById('intervention-nature-filter');
            // Indoor/outdoor filters removed as requested
            const resetFiltersBtn = document.getElementById('reset-filters');
            const tableBody = document.getElementById('data-table-body');

            // Initialize intervention nature filter as disabled
            interventionNatureFilter.disabled = true;

            // Apply filters function
            function applyFilters() {
                const rows = tableBody.querySelectorAll('tr');
                const adminOffset = {{ is_admin|yesno:"1,0" }}; // Column offset for admin view

                rows.forEach(row => {
                    let showRow = true;

                    // Date filtering
                    if (monthFilter.value || yearFilter.value) {
                        const dateCell = row.cells[0].textContent;
                        const dateParts = dateCell.split(' ');

                        // Month filtering
                        if (monthFilter.value) {
                            // Get month name from the date cell
                            const monthName = dateParts[1];
                            // Find the month code for this month name
                            let matchingMonth = false;
                            {% for month_code, month_name in months %}
                            if ('{{ month_name }}' === monthName && '{{ month_code }}' === monthFilter.value) {
                                matchingMonth = true;
                            }
                            {% endfor %}

                            if (!matchingMonth) {
                                showRow = false;
                            }
                        }

                        // Year filtering
                        if (yearFilter.value && showRow) {
                            const year = dateParts[2];
                            if (year !== yearFilter.value) {
                                showRow = false;
                            }
                        }
                    }

                    // Unit filtering
                    if (unitFilter.value && showRow) {
                        const unitCell = row.cells[1 + adminOffset].textContent;
                        if (unitCell !== unitFilter.value) {
                            showRow = false;
                        }
                    }

                    // Intervention type filtering
                    if (interventionTypeFilter.value && showRow) {
                        const interventionTypeCell = row.cells[2 + adminOffset].textContent;
                        if (interventionTypeCell !== interventionTypeFilter.value) {
                            showRow = false;
                        }
                    }

                    // Intervention nature filtering
                    if (interventionNatureFilter.value && showRow) {
                        const interventionNatureCell = row.cells[3 + adminOffset].textContent;
                        if (interventionNatureCell !== interventionNatureFilter.value) {
                            showRow = false;
                        }
                    }

                    // Indoor/outdoor filtering removed as requested

                    // Wilaya filtering (admin only)
                    {% if is_admin %}
                    if (document.getElementById('wilaya-filter').value && showRow) {
                        const wilayaCell = row.cells[1].textContent;
                        const selectedWilayaName = document.getElementById('wilaya-filter').options[document.getElementById('wilaya-filter').selectedIndex].text;
                        if (wilayaCell !== selectedWilayaName) {
                            showRow = false;
                        }
                    }
                    {% endif %}

                    // Show or hide row
                    row.style.display = showRow ? '' : 'none';
                });
            }

            // Add event listeners to filters
            monthFilter.addEventListener('change', applyFilters);
            yearFilter.addEventListener('change', applyFilters);
            unitFilter.addEventListener('change', applyFilters);
            interventionTypeFilter.addEventListener('change', applyFilters);
            interventionNatureFilter.addEventListener('change', applyFilters);
            // Indoor/outdoor filter event listeners removed as requested

            {% if is_admin %}
            document.getElementById('wilaya-filter').addEventListener('change', applyFilters);
            {% endif %}

            // Reset filters
            resetFiltersBtn.addEventListener('click', function() {
                monthFilter.value = '';
                yearFilter.value = '';
                unitFilter.value = '';
                interventionTypeFilter.value = '';

                // Reset and disable intervention nature filter
                interventionNatureFilter.innerHTML = '<option value="">اختر نوع التدخل أولاً</option>';
                interventionNatureFilter.value = '';
                interventionNatureFilter.disabled = true;

                // Indoor/outdoor filter reset removed as requested

                {% if is_admin %}
                document.getElementById('wilaya-filter').value = '';
                {% endif %}

                // Show all rows
                tableBody.querySelectorAll('tr').forEach(row => {
                    row.style.display = '';
                });

                // Show all detailed columns when filters are reset
                const detailedColumns = document.querySelectorAll('.detailed-column');
                detailedColumns.forEach(col => col.style.display = '');
            });

            // Dynamic filtering for intervention nature based on intervention type
            interventionTypeFilter.addEventListener('change', function() {
                const selectedType = this.value;
                const natureFilter = document.getElementById('intervention-nature-filter');

                // Clear current options
                natureFilter.innerHTML = '';

                // Add default option
                const defaultOption = document.createElement('option');
                defaultOption.value = '';

                if (!selectedType) {
                    defaultOption.textContent = 'اختر نوع التدخل أولاً';
                    natureFilter.appendChild(defaultOption);
                    natureFilter.disabled = true;
                } else {
                    defaultOption.textContent = 'الكل';
                    natureFilter.appendChild(defaultOption);
                    natureFilter.disabled = false;

                    // Add specific natures based on the selected intervention type
                    if (selectedType === 'الاختناق') {
                        addFilterOption(natureFilter, 'بالغاز الطبيعي أو البوتان');
                        addFilterOption(natureFilter, 'بغاز أحادي أكسيد الكربون');
                        addFilterOption(natureFilter, 'بإنسداد المجاري التنفسية');
                        addFilterOption(natureFilter, 'بالأماكن المغلقة');
                    } else if (selectedType === 'التسممات') {
                        addFilterOption(natureFilter, 'بمواد غذائية');
                        addFilterOption(natureFilter, 'بالأدوية');
                        addFilterOption(natureFilter, 'بمواد التنظيف');
                        addFilterOption(natureFilter, 'بلسعات/عضات حيوانات');
                        addFilterOption(natureFilter, 'أخرى');
                    } else if (selectedType === 'الحروق') {
                        addFilterOption(natureFilter, 'ألسنة النار');
                        addFilterOption(natureFilter, 'مواد سائلة ساخنة');
                        addFilterOption(natureFilter, 'مواد كيميائية/مشعة');
                        addFilterOption(natureFilter, 'صعقات كهربائية');
                    } else if (selectedType === 'الانفجارات') {
                        addFilterOption(natureFilter, 'غاز البوتان/البروبان');
                        addFilterOption(natureFilter, 'الغاز الطبيعي');
                        addFilterOption(natureFilter, 'الأجهزة الكهرومنزلية');
                        addFilterOption(natureFilter, 'أجهزة التدفئة');
                        addFilterOption(natureFilter, 'أخرى');
                    } else if (selectedType === 'إجلاء المرضى') {
                        addFilterOption(natureFilter, 'إجلاء الجرحى');
                        addFilterOption(natureFilter, 'إجلاء الإختناقات');
                        addFilterOption(natureFilter, 'إجلاء التسممات');
                        addFilterOption(natureFilter, 'إجلاء الحروق');
                        addFilterOption(natureFilter, 'إجلاء الإنفجارات');
                        addFilterOption(natureFilter, 'إجلاء السقوط');
                        addFilterOption(natureFilter, 'إجلاء الشنق');
                        addFilterOption(natureFilter, 'إجلاء المرضى');
                    } else if (selectedType === 'الغرقى') {
                        addFilterOption(natureFilter, 'الغرق في المسطحات المائية');
                        addFilterOption(natureFilter, 'الغرق في السدود');
                        addFilterOption(natureFilter, 'الغرق في الأودية');
                        addFilterOption(natureFilter, 'الغرق في الشواطئ');
                        addFilterOption(natureFilter, 'الغرق في أماكن أخرى');
                    }
                }

                // Show/hide detailed columns based on selected intervention type
                const detailedColumns = document.querySelectorAll('.detailed-column');
                const detailedInterventionTypes = ['الاختناق', 'التسممات', 'الحروق', 'الانفجارات', 'إجلاء المرضى', 'الغرقى'];

                // Show detailed columns if a detailed intervention type is selected OR if "الكل" (All) is selected
                if (detailedInterventionTypes.includes(selectedType) || selectedType === '') {
                    detailedColumns.forEach(col => col.style.display = '');
                } else {
                    detailedColumns.forEach(col => col.style.display = 'none');
                }

                // Reset the nature filter value
                natureFilter.value = '';

                // Apply filters
                applyFilters();
            });

            // Helper function to add options to the filter
            function addFilterOption(selectElement, value) {
                const option = document.createElement('option');
                option.value = value;
                option.textContent = value;
                selectElement.appendChild(option);
            }

            // Delete button functionality
            const deleteButtons = document.querySelectorAll('.btn-delete');
            deleteButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const id = this.getAttribute('data-id');
                    if (confirm('هل أنت متأكد من حذف هذا السجل؟')) {
                        // Send delete request
                        fetch(`/delete-medical-evacuation/${id}/`, {
                            method: 'POST',
                            headers: {
                                'X-CSRFToken': '{{ csrf_token }}',
                                'Content-Type': 'application/json'
                            }
                        })
                        .then(response => {
                            if (response.ok) {
                                // Reload page after successful deletion
                                window.location.reload();
                            } else {
                                alert('حدث خطأ أثناء الحذف');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('حدث خطأ أثناء الحذف');
                        });
                    }
                });
            });

            // Edit button functionality
            const editButtons = document.querySelectorAll('.btn-edit');
            editButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const id = this.getAttribute('data-id');
                    // Redirect to edit page
                    window.location.href = `/medical-evacuation/edit/${id}/`;
                });
            });
        });
    </script>
</body>
</html>
