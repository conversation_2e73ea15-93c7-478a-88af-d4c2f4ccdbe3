{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحماية المدنية الجزائرية - جدول حوادث المرور</title>
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/home.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="{% static 'css/button-override.css' %}?v=1.0">
    <style>
        .table-container {
            max-width: 100%;
            margin: 0 auto;
            padding: 20px;
        }

        .table-wrapper {
            overflow-x: auto;
            margin-top: 20px;
            max-height: 70vh;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: relative;
            width: 100%;
            max-width: 100%;
        }

        /* إضافة تلميح للمستخدم بإمكانية التمرير */
        .table-wrapper::after {
            content: "← قم بالتمرير للمزيد →";
            position: absolute;
            bottom: 10px;
            right: 50%;
            transform: translateX(50%);
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            opacity: 0.8;
            pointer-events: none; /* لا يمنع التفاعل مع الجدول */
            animation: fadeOut 3s forwards 3s; /* اختفاء بعد 6 ثوان */
        }

        @keyframes fadeOut {
            from { opacity: 0.8; }
            to { opacity: 0; }
        }

        h2.page-title {
            text-align: center;
            margin-bottom: 30px;
            color: #0d47a1;
            border-bottom: 2px solid #0d47a1;
            padding-bottom: 10px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 80px; /* زيادة المساحة أسفل الجدول للأزرار العائمة */
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            overflow: hidden;
            font-size: 12px; /* Smaller font size for more columns */
        }

        .data-table th,
        .data-table td {
            padding: 6px 4px; /* Smaller padding for more columns */
            text-align: center;
            border: 1px solid #ddd;
            white-space: nowrap;
            min-width: 60px; /* Minimum width for columns */
        }

        /* تنسيق خاص لعمود التاريخ */
        .data-table td:first-child {
            min-width: 150px; /* عرض أكبر لعمود التاريخ */
            font-weight: bold; /* خط أكثر سمكًا */
            font-size: 15px; /* حجم خط أكبر */
            padding: 10px; /* تباعد داخلي أكبر */
            background-color: #f8f9fa; /* خلفية مميزة */
        }

        .data-table th:first-child {
            min-width: 150px; /* عرض أكبر لعمود التاريخ */
            font-weight: bold; /* خط أكثر سمكًا */
            font-size: 15px; /* حجم خط أكبر */
            padding: 10px; /* تباعد داخلي أكبر */
            background-color: #0d47a1; /* خلفية زرقاء */
            color: white; /* لون النص أبيض */
        }

        .data-table th {
            background-color: #0d47a1;
            position: sticky;
            top: 0;
            z-index: 10;
            font-weight: bold;
            color: white;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        /* تثبيت عمود الإجراءات على اليسار */
        .data-table th:last-child,
        .data-table td:last-child {
            position: sticky;
            left: 0;
            background-color: #f8f9fa;
            z-index: 5;
            box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
            min-width: 80px;
            text-align: center;
        }

        /* تغيير خلفية خلية الإجراءات عند التحويم */
        .data-table tr:hover td:last-child {
            background-color: #e9f5ff;
        }

        /* تغيير خلفية خلية الإجراءات للصفوف الزوجية */
        .data-table tr:nth-child(even) td:last-child {
            background-color: #f9f9f9;
        }

        /* تنسيق خاص لخلية الإجراءات */
        .actions-cell {
            padding: 5px !important;
            white-space: nowrap;
            width: 80px;
        }

        /* تنسيق صف الفلاتر */
        .filter-row th {
            padding: 5px;
            background-color: #e6f0ff;
            color: #0d47a1;
            position: sticky;
            top: 40px; /* يجب أن تكون أكبر من ارتفاع الصف الأول */
            z-index: 9;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .filter-select {
            width: 100%;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: white;
            font-size: 12px;
            text-align: center;
        }

        .date-filter {
            display: flex;
            gap: 5px;
        }

        .date-filter .filter-select {
            width: 50%;
        }

        .reset-filters-btn {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
            width: 100%;
        }

        .reset-filters-btn:hover {
            background-color: #e9ecef;
            border-color: #ced4da;
        }

        .data-table tr:hover {
            background-color: #f5f5f5;
        }

        .data-table tr:last-child td {
            border-bottom: none;
        }

        .table-actions {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .btn-add {
            background-color: #28a745;
            color: white;
            padding: 12px 20px;
            border-radius: 50px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .btn-add:hover {
            background-color: #218838;
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
        }

        .btn-export {
            background-color: #6c757d;
            color: white;
            padding: 12px 20px;
            border-radius: 50px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .btn-export:hover {
            background-color: #5a6268;
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
        }

        .btn-edit {
            color: #0d6efd;
            text-decoration: none;
            margin-right: 10px;
            display: inline-block;
            width: 30px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            border-radius: 50%;
            transition: background-color 0.3s;
        }

        .btn-edit:hover {
            background-color: rgba(13, 110, 253, 0.1);
        }

        .btn-delete {
            color: #dc3545;
            text-decoration: none;
            display: inline-block;
            width: 30px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            border-radius: 50%;
            transition: background-color 0.3s;
        }

        .btn-delete:hover {
            background-color: rgba(220, 53, 69, 0.1);
        }

        .empty-data {
            padding: 30px;
            text-align: center;
            color: #6c757d;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .navigation-buttons {
            display: flex;
            justify-content: center;
            gap: 10px;
            position: fixed;
            bottom: 20px;
            left: 20px;
            z-index: 100;
        }

        .btn {
            padding: 10px 15px;
            border-radius: 50px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
        }

        .btn-blue {
            background-color: #0d47a1;
            color: white;
        }

        .btn-gray {
            background-color: #6c757d;
            color: white;
        }

        /* تنسيق للأزرار العائمة */
        .floating-buttons {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .floating-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            transition: all 0.3s;
        }

        .floating-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
        }

        .add-btn {
            background-color: #28a745;
        }

        .add-btn:hover {
            background-color: #218838;
        }

        .export-btn {
            background-color: #6c757d;
        }

        .export-btn:hover {
            background-color: #5a6268;
        }

        .top-btn {
            background-color: #0d6efd;
        }

        .top-btn:hover {
            background-color: #0a58ca;
        }

        /* Modal styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.4);
            animation: fadeIn 0.3s;
        }

        .modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            width: 80%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            position: relative;
            animation: slideIn 0.3s;
        }

        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s;
        }

        .close:hover,
        .close:focus {
            color: #555;
            text-decoration: none;
        }

        .btn-view-all {
            color: #17a2b8;
            text-decoration: none;
            display: inline-block;
            width: 30px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            border-radius: 50%;
            transition: background-color 0.3s;
        }

        .btn-view-all:hover {
            background-color: rgba(23, 162, 184, 0.1);
        }

        #modal-data {
            margin-top: 20px;
        }

        #modal-data table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        #modal-data table th,
        #modal-data table td {
            padding: 8px;
            text-align: right;
            border: 1px solid #ddd;
        }

        #modal-data table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        @media (max-width: 768px) {
            .data-table {
                display: block;
                overflow-x: auto;
                white-space: nowrap;
            }

            .navigation-buttons {
                left: 10px;
                bottom: 70px;
            }

            .floating-buttons {
                bottom: 10px;
                right: 10px;
            }

            .floating-btn {
                width: 40px;
                height: 40px;
                font-size: 14px;
            }

            .btn-add, .btn-export, .btn {
                padding: 8px 12px;
                font-size: 12px;
            }

            .btn-add i, .btn-export i, .btn i {
                font-size: 14px;
            }

            .modal-content {
                width: 95%;
                margin: 10% auto;
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle" id="sidebar-toggle" style="cursor: pointer;">
            </div>
        </div>
    </header>

    {% include 'includes/sidebar.html' %}

    <main>
        <div class="table-container">
            <h2 class="page-title">جدول حوادث المرور</h2>

            <div class="table-actions">
                <a href="{% url 'traffic_accidents' %}" class="btn-add">
                    <i class="fas fa-plus"></i> إضافة جديد
                </a>
                <a href="#" id="export-excel-btn" class="btn-export">
                    <i class="fas fa-file-excel"></i> تصدير إلى Excel
                </a>
            </div>

            {% if traffic_accidents_data %}
            <div class="table-wrapper">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            {% if is_admin %}
                            <th>الولاية</th>
                            {% endif %}
                            <th>الوحدة المتدخلة</th>
                            <th>نوع الحادث</th>
                            <th>طبيعة الحادث</th>
                            <th>عدد الحوادث</th>
                            <th>عدد العمليات</th>
                            <th>نوع الطريق</th>
                            <th>فئة السائقين</th>
                            <th>عدد الجرحى رجال</th>
                            <th>عدد الجرحى نساء</th>
                            <th>عدد الجرحى أطفال</th>
                            <th>مجموع الجرحى</th>
                            <th>عدد الوفيات رجال</th>
                            <th>عدد الوفيات نساء</th>
                            <th>عدد الوفيات أطفال</th>
                            <th>مجموع الوفيات</th>
                            <th>سيارات وقود</th>
                            <th>سيارات غاز مميع</th>
                            <th>شاحنات</th>
                            <th>حافلات</th>
                            <th>دراجات</th>
                            <th>جرارات</th>
                            <th>النقل موجه</th>
                            <th>أخرى</th>
                            <th>06:00 - 09:00</th>
                            <th>09:00 - 12:00</th>
                            <th>12:00 - 14:00</th>
                            <th>14:00 - 16:00</th>
                            <th>16:00 - 20:00</th>
                            <th>20:00 - 00:00</th>
                            <th>00:00 - 06:00</th>
                            <th>الأحد</th>
                            <th>الإثنين</th>
                            <th>الثلاثاء</th>
                            <th>الأربعاء</th>
                            <th>الخميس</th>
                            <th>الجمعة</th>
                            <th>السبت</th>
                            <th>الإجراءات</th>
                        </tr>
                        <tr class="filter-row">
                            <th>
                                <div class="date-filter">
                                    <select id="month-filter" class="filter-select">
                                        <option value="">الشهر</option>
                                        {% for month_code, month_name in months %}
                                        <option value="{{ month_code }}">{{ month_name }}</option>
                                        {% endfor %}
                                    </select>
                                    <select id="year-filter" class="filter-select">
                                        <option value="">السنة</option>
                                        {% for year in years %}
                                        <option value="{{ year }}">{{ year }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </th>
                            {% if is_admin %}
                            <th>
                                <select id="wilaya-filter" class="filter-select">
                                    <option value="">الكل</option>
                                    {% for code, name in wilaya_choices %}
                                    <option value="{{ code }}">{{ name }}</option>
                                    {% endfor %}
                                </select>
                            </th>
                            {% endif %}
                            <th>
                                <select id="unit-filter" class="filter-select">
                                    <option value="">الكل</option>
                                    {% for unit in user_units %}
                                    <option value="{{ unit.name }}">{{ unit.name }}</option>
                                    {% endfor %}
                                </select>
                            </th>
                            <th>
                                <select id="accident-type-filter" class="filter-select">
                                    <option value="">الكل</option>
                                    {% for accident_type in accident_types %}
                                    <option value="{{ accident_type }}">{{ accident_type }}</option>
                                    {% endfor %}
                                </select>
                            </th>
                            <th>
                                <select id="accident-nature-filter" class="filter-select" disabled>
                                    <option value="">اختر نوع الحادث أولاً</option>
                                </select>
                            </th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th>
                                <button id="reset-filters" class="reset-filters-btn">
                                    <i class="fas fa-undo"></i> إعادة تعيين
                                </button>
                            </th>
                        </tr>
                    </thead>
                    <tbody id="data-table-body">
                        {% for item in traffic_accidents_data %}
                        <tr>
                            <td>{{ item.formatted_date }}</td>
                            {% if is_admin %}
                            <td>{{ item.wilaya_name }}</td>
                            {% endif %}
                            <td>{{ item.unit }}</td>
                            <td>{{ item.accident_type }}</td>
                            <td>{{ item.accident_nature }}</td>
                            <td>{{ item.accidents_count }}</td>
                            <td>{{ item.operations_count }}</td>
                            <td>{{ item.road_type }}</td>
                            <td>{{ item.driver_age }}</td>
                            <td>{{ item.casualties_men }}</td>
                            <td>{{ item.casualties_women }}</td>
                            <td>{{ item.casualties_children }}</td>
                            <td>{{ item.total_casualties }}</td>
                            <td>{{ item.fatalities_men }}</td>
                            <td>{{ item.fatalities_women }}</td>
                            <td>{{ item.fatalities_children }}</td>
                            <td>{{ item.total_fatalities }}</td>
                            <td>{{ item.fuel_cars }}</td>
                            <td>{{ item.lpg_cars }}</td>
                            <td>{{ item.trucks }}</td>
                            <td>{{ item.buses }}</td>
                            <td>{{ item.motorcycles }}</td>
                            <td>{{ item.tractors }}</td>
                            <td>{{ item.directed_transport }}</td>
                            <td>{{ item.other_vehicles }}</td>
                            <td>{{ item.time_06_09 }}</td>
                            <td>{{ item.time_09_12 }}</td>
                            <td>{{ item.time_12_14 }}</td>
                            <td>{{ item.time_14_16 }}</td>
                            <td>{{ item.time_16_20 }}</td>
                            <td>{{ item.time_20_00 }}</td>
                            <td>{{ item.time_00_06 }}</td>
                            <td>{{ item.sunday }}</td>
                            <td>{{ item.monday }}</td>
                            <td>{{ item.tuesday }}</td>
                            <td>{{ item.wednesday }}</td>
                            <td>{{ item.thursday }}</td>
                            <td>{{ item.friday }}</td>
                            <td>{{ item.saturday }}</td>
                            <td class="actions-cell">
                                <a href="#" class="btn-edit" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="#" class="btn-delete" title="حذف">
                                    <i class="fas fa-trash-alt"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="empty-data">
                <i class="fas fa-info-circle" style="font-size: 24px; margin-bottom: 10px;"></i>
                <p>لا توجد بيانات متاحة حاليًا.</p>
            </div>
            {% endif %}

            <!-- الأزرار العائمة -->
            <div class="floating-buttons">
                <a href="{% url 'traffic_accidents' %}" class="floating-btn add-btn" title="إضافة جديد">
                    <i class="fas fa-plus"></i>
                </a>
                <a href="#" id="floating-export-btn" class="floating-btn export-btn" title="تصدير إلى Excel">
                    <i class="fas fa-file-excel"></i>
                </a>
                <a href="#" id="back-to-top" class="floating-btn top-btn" title="العودة إلى الأعلى">
                    <i class="fas fa-arrow-up"></i>
                </a>
            </div>

            <!-- أزرار التنقل -->
            <div class="navigation-buttons">
                <a href="{% url 'tables_dashboard' %}" class="btn btn-blue">
                    <i class="fas fa-th-large"></i> لوحة الجداول
                </a>
                <a href="{% url 'home' %}" class="btn btn-gray">
                    <i class="fas fa-home"></i> الصفحة الرئيسية
                </a>
            </div>
        </div>
    </main>

    <!-- Modal for displaying all data -->
    <div id="data-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>كل البيانات</h2>
            <div id="modal-data"></div>
        </div>
    </div>

    <footer>
        <p>حقوق النشر © 2025 جميع الحقوق محفوظة - تم التطوير بواسطة عبد الرزاق مختاري</p>
    </footer>

    <script src="{% static 'js/sidebar.js' %}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Back to top button
            const backToTopButton = document.getElementById('back-to-top');
            backToTopButton.addEventListener('click', function(e) {
                e.preventDefault();
                window.scrollTo({ top: 0, behavior: 'smooth' });
            });

            // Export to Excel button
            const exportExcelBtn = document.getElementById('export-excel-btn');
            const floatingExportBtn = document.getElementById('floating-export-btn');

            function exportToExcel() {
                // Get filter values
                const monthFilter = document.getElementById('month-filter').value;
                const yearFilter = document.getElementById('year-filter').value;
                const unitFilter = document.getElementById('unit-filter').value;
                const accidentTypeFilter = document.getElementById('accident-type-filter')?.value || '';
                const accidentNatureFilter = document.getElementById('accident-nature-filter')?.value || '';

                // Build query string with filters
                let queryParams = new URLSearchParams();
                if (monthFilter) queryParams.append('month', monthFilter);
                if (yearFilter) queryParams.append('year', yearFilter);
                if (unitFilter) queryParams.append('unit', unitFilter);
                if (accidentTypeFilter) queryParams.append('accident_type', accidentTypeFilter);
                if (accidentNatureFilter) queryParams.append('accident_nature', accidentNatureFilter);

                // Use Django URL pattern for export
                const baseUrl = "{% url 'export_table_to_excel' 'traffic_accidents' %}";
                const url = `${baseUrl}?${queryParams.toString()}`;

                // Redirect to export URL with filters
                window.location.href = url;
            }

            exportExcelBtn.addEventListener('click', function(e) {
                e.preventDefault();
                exportToExcel();
            });

            floatingExportBtn.addEventListener('click', function(e) {
                e.preventDefault();
                exportToExcel();
            });

            // Filter functionality
            const monthFilter = document.getElementById('month-filter');
            const yearFilter = document.getElementById('year-filter');
            const unitFilter = document.getElementById('unit-filter');
            const accidentTypeFilter = document.getElementById('accident-type-filter');
            const accidentNatureFilter = document.getElementById('accident-nature-filter');
            const resetFiltersBtn = document.getElementById('reset-filters');
            const tableBody = document.getElementById('data-table-body');

            // Initialize accident nature filter as disabled
            accidentNatureFilter.disabled = true;

            // Apply filters function
            function applyFilters() {
                const rows = tableBody.querySelectorAll('tr');
                const adminOffset = {{ is_admin|yesno:"1,0" }}; // Column offset for admin view

                rows.forEach(row => {
                    let showRow = true;

                    // Date filtering
                    if (monthFilter.value || yearFilter.value) {
                        const dateCell = row.cells[0].textContent;
                        const dateParts = dateCell.split(' ');

                        // Month filtering
                        if (monthFilter.value) {
                            // Get month name from the date cell
                            const monthName = dateParts[1];
                            // Find the month code for this month name
                            let matchingMonth = false;
                            {% for month_code, month_name in months %}
                            if ('{{ month_name }}' === monthName && '{{ month_code }}' === monthFilter.value) {
                                matchingMonth = true;
                            }
                            {% endfor %}

                            if (!matchingMonth) {
                                showRow = false;
                            }
                        }

                        // Year filtering
                        if (yearFilter.value && showRow) {
                            const year = dateParts[2];
                            if (year !== yearFilter.value) {
                                showRow = false;
                            }
                        }
                    }

                    // Unit filtering
                    if (unitFilter.value && showRow) {
                        const unitCell = row.cells[1 + adminOffset].textContent;
                        if (unitCell !== unitFilter.value) {
                            showRow = false;
                        }
                    }

                    // Accident type filtering
                    if (accidentTypeFilter.value && showRow) {
                        const accidentTypeCell = row.cells[2 + adminOffset].textContent;
                        if (accidentTypeCell !== accidentTypeFilter.value) {
                            showRow = false;
                        }
                    }

                    // Accident nature filtering
                    if (accidentNatureFilter.value && showRow) {
                        const accidentNatureCell = row.cells[3 + adminOffset].textContent;
                        if (accidentNatureCell !== accidentNatureFilter.value) {
                            showRow = false;
                        }
                    }

                    // Add additional filters for road type and driver age if needed
                    // This can be expanded later if needed

                    // Wilaya filtering (admin only)
                    {% if is_admin %}
                    if (document.getElementById('wilaya-filter').value && showRow) {
                        const wilayaCell = row.cells[1].textContent;
                        const selectedWilayaName = document.getElementById('wilaya-filter').options[document.getElementById('wilaya-filter').selectedIndex].text;
                        if (wilayaCell !== selectedWilayaName) {
                            showRow = false;
                        }
                    }
                    {% endif %}

                    // Show or hide row
                    row.style.display = showRow ? '' : 'none';
                });
            }

            // Add event listeners to filters
            monthFilter.addEventListener('change', applyFilters);
            yearFilter.addEventListener('change', applyFilters);
            unitFilter.addEventListener('change', applyFilters);
            accidentTypeFilter.addEventListener('change', applyFilters);
            accidentNatureFilter.addEventListener('change', applyFilters);

            {% if is_admin %}
            document.getElementById('wilaya-filter').addEventListener('change', applyFilters);
            {% endif %}

            // Reset filters
            resetFiltersBtn.addEventListener('click', function() {
                monthFilter.value = '';
                yearFilter.value = '';
                unitFilter.value = '';
                accidentTypeFilter.value = '';

                // Reset and disable accident nature filter
                accidentNatureFilter.innerHTML = '<option value="">اختر نوع الحادث أولاً</option>';
                accidentNatureFilter.value = '';
                accidentNatureFilter.disabled = true;

                {% if is_admin %}
                document.getElementById('wilaya-filter').value = '';
                {% endif %}

                // Show all rows
                tableBody.querySelectorAll('tr').forEach(row => {
                    row.style.display = '';
                });
            });

            // Dynamic filtering for accident nature based on accident type
            accidentTypeFilter.addEventListener('change', function() {
                const selectedType = this.value;
                const natureFilter = document.getElementById('accident-nature-filter');

                // Clear current options
                natureFilter.innerHTML = '';

                // Add default option
                const defaultOption = document.createElement('option');
                defaultOption.value = '';

                if (!selectedType) {
                    defaultOption.textContent = 'اختر نوع الحادث أولاً';
                    natureFilter.appendChild(defaultOption);
                    natureFilter.disabled = true;
                } else {
                    defaultOption.textContent = 'الكل';
                    natureFilter.appendChild(defaultOption);
                    natureFilter.disabled = false;

                    // Add specific natures based on the selected accident type
                    if (selectedType === 'ضحايا مصدومة بالمركبات') {
                        addFilterOption(natureFilter, 'سيارة');
                        addFilterOption(natureFilter, 'شاحنة');
                        addFilterOption(natureFilter, 'حافلة');
                        addFilterOption(natureFilter, 'دراجة نارية');
                        addFilterOption(natureFilter, 'أخرى');
                    } else if (selectedType === 'ضحايا تصادم المركبات') {
                        addFilterOption(natureFilter, 'سيارة بسيارة');
                        addFilterOption(natureFilter, 'سيارة بشاحنة');
                        addFilterOption(natureFilter, 'سيارة بحافلة');
                        addFilterOption(natureFilter, 'سيارة بدراجة نارية');
                        addFilterOption(natureFilter, 'أخرى');
                    } else if (selectedType === 'ضحايا إنقلاب المركبات') {
                        addFilterOption(natureFilter, 'سيارة');
                        addFilterOption(natureFilter, 'شاحنة');
                        addFilterOption(natureFilter, 'حافلة');
                        addFilterOption(natureFilter, 'دراجة نارية');
                        addFilterOption(natureFilter, 'أخرى');
                    } else if (selectedType === 'ضحايا مصدومة بالقطار') {
                        addFilterOption(natureFilter, 'سيارة');
                        addFilterOption(natureFilter, 'شاحنة');
                        addFilterOption(natureFilter, 'حافلة');
                        addFilterOption(natureFilter, 'شخص');
                        addFilterOption(natureFilter, 'أخرى');
                    } else if (selectedType === 'ضحايا حوادث أخرى') {
                        addFilterOption(natureFilter, 'سقوط من مركبة');
                        addFilterOption(natureFilter, 'حريق مركبة');
                        addFilterOption(natureFilter, 'إنفجار مركبة');
                        addFilterOption(natureFilter, 'أخرى');
                    }
                }

                // Reset the nature filter value
                natureFilter.value = '';

                // Apply filters
                applyFilters();
            });

            // Helper function to add options to the filter
            function addFilterOption(selectElement, value) {
                const option = document.createElement('option');
                option.value = value;
                option.textContent = value;
                selectElement.appendChild(option);
            }

            // Modal functionality
            const modal = document.getElementById('data-modal');
            const modalData = document.getElementById('modal-data');
            const closeBtn = document.getElementsByClassName('close')[0];
            const viewAllButtons = document.querySelectorAll('.btn-view-all');

            // Close modal when clicking the X
            closeBtn.onclick = function() {
                modal.style.display = 'none';
            }

            // Close modal when clicking outside of it
            window.onclick = function(event) {
                if (event.target == modal) {
                    modal.style.display = 'none';
                }
            }

            // View all data button click
            viewAllButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const id = this.getAttribute('data-id');
                    fetchAllData(id);
                });
            });

            // Fetch all data for a specific record
            function fetchAllData(id) {
                // Show loading indicator
                modalData.innerHTML = '<div style="text-align: center; padding: 20px;"><i class="fas fa-spinner fa-spin" style="font-size: 24px;"></i><p>جاري تحميل البيانات...</p></div>';
                modal.style.display = 'block';

                // Fetch data from server
                fetch(`/get-traffic-accident-data/${id}/`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        displayAllData(data);
                    })
                    .catch(error => {
                        modalData.innerHTML = `<div style="color: red; text-align: center; padding: 20px;"><i class="fas fa-exclamation-triangle"></i><p>حدث خطأ أثناء تحميل البيانات: ${error.message}</p></div>`;
                    });
            }

            // Display all data in the modal
            function displayAllData(data) {
                let html = '<table>';

                // Basic information
                html += '<tr><th colspan="2">معلومات أساسية</th></tr>';
                html += `<tr><th>التاريخ</th><td>${data.formatted_date}</td></tr>`;
                html += `<tr><th>الوحدة المتدخلة</th><td>${data.unit}</td></tr>`;
                html += `<tr><th>نوع الحادث</th><td>${data.accident_type}</td></tr>`;
                html += `<tr><th>طبيعة الحادث</th><td>${data.accident_nature}</td></tr>`;
                html += `<tr><th>عدد الحوادث</th><td>${data.accidents_count}</td></tr>`;
                html += `<tr><th>عدد العمليات</th><td>${data.operations_count}</td></tr>`;
                html += `<tr><th>نوع الطريق</th><td>${data.road_type}</td></tr>`;
                html += `<tr><th>فئة السائقين</th><td>${data.driver_age}</td></tr>`;

                // Human losses
                html += '<tr><th colspan="2">الخسائر البشرية</th></tr>';
                html += `<tr><th>الجرحى رجال</th><td>${data.casualties_men}</td></tr>`;
                html += `<tr><th>الجرحى نساء</th><td>${data.casualties_women}</td></tr>`;
                html += `<tr><th>الجرحى أطفال</th><td>${data.casualties_children}</td></tr>`;
                html += `<tr><th>مجموع الجرحى</th><td>${data.total_casualties}</td></tr>`;
                html += `<tr><th>الوفيات رجال</th><td>${data.fatalities_men}</td></tr>`;
                html += `<tr><th>الوفيات نساء</th><td>${data.fatalities_women}</td></tr>`;
                html += `<tr><th>الوفيات أطفال</th><td>${data.fatalities_children}</td></tr>`;
                html += `<tr><th>مجموع الوفيات</th><td>${data.total_fatalities}</td></tr>`;

                // Vehicles
                html += '<tr><th colspan="2">المركبات</th></tr>';
                html += `<tr><th>سيارات وقود</th><td>${data.fuel_cars}</td></tr>`;
                html += `<tr><th>سيارات غاز مميع</th><td>${data.lpg_cars}</td></tr>`;
                html += `<tr><th>شاحنات</th><td>${data.trucks}</td></tr>`;
                html += `<tr><th>حافلات</th><td>${data.buses}</td></tr>`;
                html += `<tr><th>دراجات</th><td>${data.motorcycles}</td></tr>`;
                html += `<tr><th>جرارات</th><td>${data.tractors}</td></tr>`;
                html += `<tr><th>النقل موجه</th><td>${data.directed_transport}</td></tr>`;
                html += `<tr><th>أخرى</th><td>${data.other_vehicles}</td></tr>`;

                // Time slots
                html += '<tr><th colspan="2">الفترات الزمنية</th></tr>';
                html += `<tr><th>06:00 - 09:00</th><td>${data.time_06_09}</td></tr>`;
                html += `<tr><th>09:00 - 12:00</th><td>${data.time_09_12}</td></tr>`;
                html += `<tr><th>12:00 - 14:00</th><td>${data.time_12_14}</td></tr>`;
                html += `<tr><th>14:00 - 16:00</th><td>${data.time_14_16}</td></tr>`;
                html += `<tr><th>16:00 - 20:00</th><td>${data.time_16_20}</td></tr>`;
                html += `<tr><th>20:00 - 00:00</th><td>${data.time_20_00}</td></tr>`;
                html += `<tr><th>00:00 - 06:00</th><td>${data.time_00_06}</td></tr>`;

                // Days of week
                html += '<tr><th colspan="2">أيام الأسبوع</th></tr>';
                html += `<tr><th>الأحد</th><td>${data.sunday}</td></tr>`;
                html += `<tr><th>الإثنين</th><td>${data.monday}</td></tr>`;
                html += `<tr><th>الثلاثاء</th><td>${data.tuesday}</td></tr>`;
                html += `<tr><th>الأربعاء</th><td>${data.wednesday}</td></tr>`;
                html += `<tr><th>الخميس</th><td>${data.thursday}</td></tr>`;
                html += `<tr><th>الجمعة</th><td>${data.friday}</td></tr>`;
                html += `<tr><th>السبت</th><td>${data.saturday}</td></tr>`;

                html += '</table>';

                modalData.innerHTML = html;
            }
        });
    </script>
</body>
</html>
