{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحماية المدنية الجزائرية - الجدول العام للحرائق</title>
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/home.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="{% static 'css/button-override.css' %}?v=1.0">
    <style>
        .table-container {
            max-width: 100%;
            margin: 0 auto;
            padding: 20px;
        }

        .table-wrapper {
            overflow-x: auto;
            margin-top: 20px;
            max-height: 70vh;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        /* إضافة تلميح للمستخدم بإمكانية التمرير */
        .table-wrapper::after {
            content: "← قم بالتمرير للمزيد →";
            position: absolute;
            bottom: 10px;
            right: 50%;
            transform: translateX(50%);
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            opacity: 0.8;
            pointer-events: none; /* لا يمنع التفاعل مع الجدول */
            animation: fadeOut 3s forwards 3s; /* اختفاء بعد 6 ثوان */
        }

        @keyframes fadeOut {
            from { opacity: 0.8; }
            to { opacity: 0; }
        }

        h2.page-title {
            text-align: center;
            margin-bottom: 30px;
            color: #0d47a1;
            border-bottom: 2px solid #0d47a1;
            padding-bottom: 10px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 80px; /* زيادة المساحة أسفل الجدول للأزرار العائمة */
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            overflow: hidden;
            font-size: 14px;
        }

        .data-table th,
        .data-table td {
            padding: 8px;
            text-align: center;
            border: 1px solid #ddd;
            white-space: nowrap;
        }

        /* تنسيق خاص لعمود التاريخ */
        .data-table td:first-child {
            min-width: 150px; /* عرض أكبر لعمود التاريخ */
            font-weight: bold; /* خط أكثر سمكًا */
            font-size: 15px; /* حجم خط أكبر */
            padding: 10px; /* تباعد داخلي أكبر */
            background-color: #f8f9fa; /* خلفية مميزة */
        }

        .data-table th:first-child {
            min-width: 150px; /* عرض أكبر لعمود التاريخ */
            font-weight: bold; /* خط أكثر سمكًا */
            font-size: 15px; /* حجم خط أكبر */
            padding: 10px; /* تباعد داخلي أكبر */
            background-color: #0d47a1; /* خلفية زرقاء */
            color: white; /* لون النص أبيض */
        }

        .data-table th {
            background-color: #0d47a1;
            position: sticky;
            top: 0;
            z-index: 10;
            font-weight: bold;
            color: white;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        /* تثبيت عمود الإجراءات على اليسار */
        .data-table th:last-child,
        .data-table td:last-child {
            position: sticky;
            left: 0;
            background-color: #f8f9fa;
            z-index: 5;
            box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
            min-width: 80px;
            text-align: center;
        }

        /* تغيير خلفية خلية الإجراءات عند التحويم */
        .data-table tr:hover td:last-child {
            background-color: #e9f5ff;
        }

        /* تغيير خلفية خلية الإجراءات للصفوف الزوجية */
        .data-table tr:nth-child(even) td:last-child {
            background-color: #f9f9f9;
        }

        /* تنسيق خاص لخلية الإجراءات */
        .actions-cell {
            padding: 5px !important;
            white-space: nowrap;
            width: 80px;
        }

        /* تنسيق صف الفلاتر */
        .filter-row th {
            padding: 5px;
            background-color: #e6f0ff;
            color: #0d47a1;
            position: sticky;
            top: 40px; /* يجب أن تكون أكبر من ارتفاع الصف الأول */
            z-index: 9;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .filter-select {
            width: 100%;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: white;
            font-size: 12px;
            text-align: center;
        }

        .date-filter {
            display: flex;
            gap: 5px;
        }

        .date-filter .filter-select {
            width: 50%;
        }

        .reset-filters-btn {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
            width: 100%;
        }

        .reset-filters-btn:hover {
            background-color: #e9ecef;
            border-color: #ced4da;
        }

        .data-table tr:hover {
            background-color: #f5f5f5;
        }

        .data-table tr:last-child td {
            border-bottom: none;
        }

        .table-actions {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .btn-add {
            background-color: #28a745;
            color: white;
            padding: 12px 20px;
            border-radius: 50px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .btn-add:hover {
            background-color: #218838;
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
        }

        .btn-export {
            background-color: #6c757d;
            color: white;
            padding: 12px 20px;
            border-radius: 50px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .btn-export:hover {
            background-color: #5a6268;
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
        }

        .btn-edit {
            color: #0d6efd;
            text-decoration: none;
            margin-right: 10px;
            display: inline-block;
            width: 30px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            border-radius: 50%;
            transition: background-color 0.3s;
        }

        .btn-edit:hover {
            background-color: rgba(13, 110, 253, 0.1);
        }

        .btn-delete {
            color: #dc3545;
            text-decoration: none;
            display: inline-block;
            width: 30px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            border-radius: 50%;
            transition: background-color 0.3s;
        }

        .btn-delete:hover {
            background-color: rgba(220, 53, 69, 0.1);
        }

        .empty-data {
            padding: 30px;
            text-align: center;
            color: #6c757d;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .navigation-buttons {
            display: flex;
            justify-content: center;
            gap: 10px;
            position: fixed;
            bottom: 20px;
            left: 20px;
            z-index: 100;
        }

        .btn {
            padding: 10px 15px;
            border-radius: 50px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
        }

        .btn-blue {
            background-color: #0d47a1;
            color: white;
        }

        .btn-gray {
            background-color: #6c757d;
            color: white;
        }

        /* تنسيق للأزرار العائمة */
        .floating-buttons {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .floating-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            transition: all 0.3s;
        }

        .floating-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
        }

        .add-btn {
            background-color: #28a745;
        }

        .add-btn:hover {
            background-color: #218838;
        }

        .export-btn {
            background-color: #6c757d;
        }

        .export-btn:hover {
            background-color: #5a6268;
        }

        .top-btn {
            background-color: #0d6efd;
        }

        .top-btn:hover {
            background-color: #0a58ca;
        }

        @media (max-width: 768px) {
            .data-table {
                display: block;
                overflow-x: auto;
                white-space: nowrap;
            }

            .navigation-buttons {
                left: 10px;
                bottom: 70px;
            }

            .floating-buttons {
                bottom: 10px;
                right: 10px;
            }

            .floating-btn {
                width: 40px;
                height: 40px;
                font-size: 14px;
            }

            .btn-add, .btn-export, .btn {
                padding: 8px 12px;
                font-size: 12px;
            }

            .btn-add i, .btn-export i, .btn i {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle" id="sidebar-toggle" style="cursor: pointer;">
            </div>
        </div>
    </header>

    {% include 'includes/sidebar.html' %}

    <main>
        <div class="table-container">
            <h2 class="page-title">الجدول العام للحرائق</h2>

            <div class="table-actions">
                <a href="{% url 'general_fire_table' %}" class="btn-add">
                    <i class="fas fa-plus"></i> إضافة جديد
                </a>
                <a href="#" id="export-excel-btn" class="btn-export">
                    <i class="fas fa-file-excel"></i> تصدير إلى Excel
                </a>
            </div>

            {% if general_fire_data %}
            <div class="table-wrapper">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            {% if is_admin %}
                            <th>الولاية</th>
                            {% endif %}
                            <th>الوحدة المتدخلة</th>
                            <th>نوع الحريق</th>
                            <th>عدد الحرائق</th>
                            <th>عدد التدخلات</th>
                            <th>عدد الجرحى</th>
                            <th>عدد الوفيات</th>
                            <th>الإجراءات</th>
                        </tr>
                        <tr class="filter-row">
                            <th>
                                <div class="date-filter">
                                    <select id="month-filter" class="filter-select">
                                        <option value="">الشهر</option>
                                        {% for month_code, month_name in months %}
                                        <option value="{{ month_code }}">{{ month_name }}</option>
                                        {% endfor %}
                                    </select>
                                    <select id="year-filter" class="filter-select">
                                        <option value="">السنة</option>
                                        {% for year in years %}
                                        <option value="{{ year }}">{{ year }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </th>
                            {% if is_admin %}
                            <th>
                                <select id="wilaya-filter" class="filter-select">
                                    <option value="">الكل</option>
                                    {% for code, name in wilaya_choices %}
                                    <option value="{{ code }}">{{ name }}</option>
                                    {% endfor %}
                                </select>
                            </th>
                            {% endif %}
                            <th>
                                <select id="unit-filter" class="filter-select">
                                    <option value="">الكل</option>
                                    {% for unit in user_units %}
                                    <option value="{{ unit.name }}">{{ unit.name }}</option>
                                    {% endfor %}
                                </select>
                            </th>
                            <th>
                                <select id="fire-type-filter" class="filter-select">
                                    <option value="">الكل</option>
                                    {% for fire_type in fire_types %}
                                    <option value="{{ fire_type }}">{{ fire_type }}</option>
                                    {% endfor %}
                                </select>
                            </th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th>
                                <button id="reset-filters" class="reset-filters-btn">
                                    <i class="fas fa-undo"></i> إعادة تعيين
                                </button>
                            </th>
                        </tr>
                    </thead>
                <tbody>
                    {% for item in general_fire_data %}
                    <tr data-id="{{ item.id }}" {% if is_admin %}data-wilaya="{{ item.wilaya_code }}"{% endif %}>
                        <td data-iso-date="{{ item.date|date:'Y-m-d' }}">{{ item.formatted_date }}</td>
                        {% if is_admin %}
                        <td>{{ item.wilaya_name }}</td>
                        {% endif %}
                        <td>{{ item.intervening_unit }}</td>
                        <td>{{ item.fire_type }}</td>
                        <td>{{ item.number_of_fires }}</td>
                        <td>{{ item.number_of_interventions }}</td>
                        <td>{{ item.number_of_injured }}</td>
                        <td>{{ item.number_of_deaths }}</td>
                        <td class="actions-cell">
                            <a href="#" class="btn-edit" title="تعديل" data-id="{{ item.id }}"><i class="fas fa-edit"></i></a>
                            <a href="#" class="btn-delete" title="حذف" data-id="{{ item.id }}"><i class="fas fa-trash-alt"></i></a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                </table>
            </div>
            {% else %}
            <div class="empty-data">
                <p>لا توجد بيانات متاحة</p>
            </div>
            {% endif %}

            <!-- أزرار التنقل الثابتة -->
            <div class="navigation-buttons">
                <a href="/tables-dashboard/" class="btn btn-blue">
                    <i class="fas fa-arrow-right"></i> العودة إلى لوحة الجداول
                </a>
                <a href="/home/" class="btn btn-gray">
                    <i class="fas fa-home"></i> الصفحة الرئيسية
                </a>
            </div>

            <!-- الأزرار العائمة -->
            <div class="floating-buttons">
                <a href="/fires/general-fire-table/" class="floating-btn add-btn" title="إضافة جديد">
                    <i class="fas fa-plus"></i>
                </a>
                <a href="#" id="export-excel-btn" class="floating-btn export-btn" title="تصدير إلى Excel">
                    <i class="fas fa-file-excel"></i>
                </a>
                <a href="#" id="scroll-top-btn" class="floating-btn top-btn" title="العودة إلى الأعلى">
                    <i class="fas fa-arrow-up"></i>
                </a>
            </div>
        </div>
    </main>

    <footer>
        <p>حقوق النشر © 2025 جميع الحقوق محفوظة - تم التطوير بواسطة عبد الرزاق مختاري</p>
    </footer>

    <script src="{% static 'js/sidebar.js' %}"></script>
    <script src="{% static 'js/arabic-date-utils.js' %}"></script>

    <script>

        // إضافة تلميح للمستخدم عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            const tableWrapper = document.querySelector('.table-wrapper');
            if (!tableWrapper) return;

            // إضافة فئة لإظهار التلميح
            tableWrapper.classList.add('show-scroll-hint');

            // إزالة التلميح بعد أول تمرير
            tableWrapper.addEventListener('scroll', function() {
                tableWrapper.classList.remove('show-scroll-hint');
            }, { once: true });

            // توحيد أسماء الأشهر في جميع خلايا الجدول باستخدام الدالة المساعدة
            const dateCells = document.querySelectorAll('.data-table tbody tr td:first-child');
            dateCells.forEach(cell => {
                const originalText = cell.textContent;
                const standardizedText = standardizeArabicDateInText(originalText);

                // إذا كان هناك تغيير في النص، قم بتحديث الخلية
                if (originalText !== standardizedText) {
                    cell.textContent = standardizedText;
                    console.log(`تم توحيد التاريخ من "${originalText}" إلى "${standardizedText}"`);
                }
            });

            // تحديث الفلاتر بعد تغيير النص
            function updateFilters() {
                // إنشاء قائمة بجميع الوحدات المتدخلة الموجودة في الجدول
                const unitCells = document.querySelectorAll('.data-table tbody tr td:nth-child(2)');
                const uniqueUnits = new Set();
                unitCells.forEach(cell => {
                    if (cell.textContent.trim()) {
                        uniqueUnits.add(cell.textContent.trim());
                    }
                });

                // إنشاء قائمة بجميع أنواع الحرائق الموجودة في الجدول
                const fireTypeCells = document.querySelectorAll('.data-table tbody tr td:nth-child(3)');
                const uniqueFireTypes = new Set();
                fireTypeCells.forEach(cell => {
                    if (cell.textContent.trim()) {
                        uniqueFireTypes.add(cell.textContent.trim());
                    }
                });

                console.log('الوحدات المتدخلة الموجودة في الجدول:', Array.from(uniqueUnits));
                console.log('أنواع الحرائق الموجودة في الجدول:', Array.from(uniqueFireTypes));
            }

            // تشغيل وظيفة تحديث الفلاتر
            updateFilters();

            // تفعيل الفلاتر
            const monthFilter = document.getElementById('month-filter');
            const yearFilter = document.getElementById('year-filter');
            const unitFilter = document.getElementById('unit-filter');
            const fireTypeFilter = document.getElementById('fire-type-filter');
            const wilayaFilter = document.getElementById('wilaya-filter');
            const tableRows = document.querySelectorAll('.data-table tbody tr');

            // دالة لتطبيق الفلاتر
            function applyFilters() {
                const monthValue = monthFilter.value;
                const yearValue = yearFilter.value;
                const unitValue = unitFilter.value;
                const fireTypeValue = fireTypeFilter.value;
                const wilayaValue = wilayaFilter ? wilayaFilter.value : '';

                console.log("تطبيق الفلاتر:", { شهر: monthValue, سنة: yearValue, ولاية: wilayaValue, وحدة: unitValue, نوع: fireTypeValue });

                // تتبع عدد الصفوف المعروضة
                let visibleRowsCount = 0;

                tableRows.forEach(row => {
                    const dateCell = row.cells[0];
                    const dateCellText = dateCell.textContent.trim();
                    const isoDate = dateCell.getAttribute('data-iso-date');

                    // تحديد مؤشرات الخلايا بناءً على وجود عمود الولاية
                    const isAdminView = row.hasAttribute('data-wilaya');
                    const unitCellIndex = isAdminView ? 2 : 1;
                    const fireTypeCellIndex = isAdminView ? 3 : 2;

                    const unitCell = row.cells[unitCellIndex].textContent.trim();
                    const fireTypeCell = row.cells[fireTypeCellIndex].textContent.trim();

                    // الحصول على قيمة الولاية إذا كان المستخدم مسؤولاً
                    const wilayaCode = row.getAttribute('data-wilaya');

                    // استخراج الشهر والسنة من التاريخ ISO إذا كان متاحًا
                    let rowMonth, rowYear;

                    if (isoDate) {
                        // استخراج الشهر والسنة من التاريخ ISO (YYYY-MM-DD)
                        const isoDateParts = isoDate.split('-');
                        if (isoDateParts.length === 3) {
                            rowYear = isoDateParts[0];
                            rowMonth = isoDateParts[1];
                        }
                    } else {
                        // استخدام الدالة المساعدة لاستخراج معلومات التاريخ من النص
                        const dateInfo = extractDateInfo(dateCellText);
                        rowMonth = dateInfo.month;
                        rowYear = dateInfo.year;
                    }

                    console.log("معلومات التاريخ المستخرجة:", {
                        نص_التاريخ: dateCellText,
                        تاريخ_ISO: isoDate,
                        الشهر: rowMonth,
                        السنة: rowYear,
                        الولاية: wilayaCode
                    });

                    // تطبيق الفلاتر
                    const matchesMonth = !monthValue || (rowMonth && rowMonth === monthValue);
                    const matchesYear = !yearValue || (rowYear && rowYear === yearValue);
                    const matchesUnit = !unitValue || (unitCell && unitCell === unitValue);
                    const matchesFireType = !fireTypeValue || (fireTypeCell && fireTypeCell === fireTypeValue);
                    const matchesWilaya = !wilayaValue || !wilayaFilter || (wilayaCode && wilayaCode === wilayaValue);

                    console.log("نتائج المطابقة:", {
                        شهر: matchesMonth,
                        سنة: matchesYear,
                        ولاية: matchesWilaya,
                        وحدة: matchesUnit,
                        نوع: matchesFireType
                    });

                    // إظهار أو إخفاء الصف بناءً على نتيجة الفلاتر
                    if (matchesMonth && matchesYear && matchesWilaya && matchesUnit && matchesFireType) {
                        row.style.display = '';
                        visibleRowsCount++;
                    } else {
                        row.style.display = 'none';
                    }
                });
            }

            // إضافة مستمعي الأحداث للفلاتر
            monthFilter.addEventListener('change', applyFilters);
            yearFilter.addEventListener('change', applyFilters);
            unitFilter.addEventListener('change', applyFilters);
            fireTypeFilter.addEventListener('change', applyFilters);
            if (wilayaFilter) {
                wilayaFilter.addEventListener('change', applyFilters);
            }

            // إضافة مستمع الحدث لزر إعادة تعيين الفلاتر
            const resetFiltersBtn = document.getElementById('reset-filters');
            if (resetFiltersBtn) {
                resetFiltersBtn.addEventListener('click', function() {
                    // إعادة تعيين جميع الفلاتر
                    monthFilter.value = '';
                    yearFilter.value = '';
                    unitFilter.value = '';
                    fireTypeFilter.value = '';
                    if (wilayaFilter) {
                        wilayaFilter.value = '';
                    }

                    // إظهار جميع الصفوف
                    tableRows.forEach(row => {
                        row.style.display = '';
                    });

                    console.log("تم إعادة تعيين الفلاتر");
                });
            }

            // تفعيل زر تصدير Excel
            const exportExcelBtn = document.getElementById('export-excel-btn');
            if (exportExcelBtn) {
                exportExcelBtn.addEventListener('click', function(e) {
                    e.preventDefault();

                    // توحيد أسماء الأشهر في جميع الخلايا قبل التصدير
                    const dateCells = document.querySelectorAll('.data-table tbody tr td:first-child');
                    dateCells.forEach(cell => {
                        const originalText = cell.textContent;
                        const standardizedText = standardizeArabicDateInText(originalText);
                        if (originalText !== standardizedText) {
                            cell.textContent = standardizedText;
                        }
                    });

                    // جمع معرفات الصفوف المرئية فقط (بعد تطبيق الفلاتر)
                    const visibleRowIds = [];
                    tableRows.forEach(row => {
                        if (row.style.display !== 'none') {
                            const rowId = row.dataset.id;
                            if (rowId) {
                                visibleRowIds.push(rowId);
                            }
                        }
                    });

                    // إذا لم تكن هناك صفوف مرئية، نعرض رسالة للمستخدم
                    if (visibleRowIds.length === 0) {
                        alert('لا توجد بيانات للتصدير. الرجاء تغيير الفلاتر لعرض بعض البيانات أولاً.');
                        return;
                    }

                    // إنشاء عنوان URL مع معرفات الصفوف المرئية
                    const baseUrl = "{% url 'export_table_to_excel' 'general-fire' %}";
                    const url = `${baseUrl}?ids=${visibleRowIds.join(',')}`;

                    // فتح عنوان URL في نافذة جديدة أو تنزيل الملف
                    window.location.href = url;
                });
            }

            // تفعيل زر التمرير إلى الأعلى
            const scrollTopBtn = document.getElementById('scroll-top-btn');
            if (scrollTopBtn) {
                scrollTopBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    window.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                    });
                });
            }

            // تفعيل أزرار التعديل والحذف
            const editButtons = document.querySelectorAll('.btn-edit');
            const deleteButtons = document.querySelectorAll('.btn-delete');

            editButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const row = this.closest('tr');
                    const id = row.dataset.id || 'unknown';
                    alert('تعديل العنصر رقم: ' + id);
                    // هنا يمكن إضافة كود لفتح نموذج التعديل
                });
            });

            deleteButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const row = this.closest('tr');
                    const id = row.dataset.id || 'unknown';
                    if (confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
                        alert('تم حذف العنصر رقم: ' + id);
                        // هنا يمكن إضافة كود لحذف العنصر من قاعدة البيانات
                    }
                });
            });
        });
    </script>
</body>
</html>
