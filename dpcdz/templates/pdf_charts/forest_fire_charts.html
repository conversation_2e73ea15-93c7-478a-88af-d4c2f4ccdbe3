<!-- Forest Agricultural Fire Charts for PDF -->
{% load custom_filters %}

<!-- Chart 1: Fire Types and Chart 2: Loss Types -->
{% if data.fire_types or data.loss_types %}
<div class="charts-grid {% if data.fire_types and not data.loss_types %}single-item{% elif data.loss_types and not data.fire_types %}single-item{% endif %}">
    {% if data.fire_types %}
    <div class="chart-item">
        <div class="chart-title">عدد التدخلات</div>
        <div class="circle-chart color-1">
            {{ data.fire_types|dict_total }}
        </div>
        <div class="chart-legend">
            {% for key, value in data.fire_types.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    {% if data.loss_types %}
    <div class="chart-item">
        <div class="chart-title">أنواع الخسائر</div>
        <div class="circle-chart color-2">
            {{ data.loss_types|dict_total }}
        </div>
        <div class="chart-legend">
            {% for key, value in data.loss_types.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endif %}

<!-- Detailed Losses Display -->
{% if data.losses_detailed %}
<div class="charts-section">
    <div class="section-title">الخسائر المسجلة</div>
    <div class="losses-display">
        <div class="losses-grid">
            {% for key, value in data.losses_detailed.items %}
            <div class="loss-item">
                <div class="loss-value">{{ value }}</div>
                <div class="loss-label">{{ key }}</div>
            </div>
            {% endfor %}
        </div>
        
        {% if data.conversion_note %}
        <div class="conversion-note">
            {{ data.conversion_note }}
        </div>
        {% endif %}
    </div>
</div>
{% endif %}

<!-- Chart 3: Forest Area Affected and Chart 4: Agricultural Area Affected -->
{% if data.forest_area_affected or data.agricultural_area_affected %}
<div class="charts-grid {% if data.forest_area_affected and not data.agricultural_area_affected %}single-item{% elif data.agricultural_area_affected and not data.forest_area_affected %}single-item{% endif %}">
    {% if data.forest_area_affected %}
    <div class="chart-item">
        <div class="chart-title">المساحة الغابية المتضررة</div>
        <div class="circle-chart color-3">
            {{ data.forest_area_affected|dict_total }}
        </div>
        <div class="chart-legend">
            {% for key, value in data.forest_area_affected.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    {% if data.agricultural_area_affected %}
    <div class="chart-item">
        <div class="chart-title">المساحة الزراعية المتضررة</div>
        <div class="circle-chart color-4">
            {{ data.agricultural_area_affected|dict_total }}
        </div>
        <div class="chart-legend">
            {% for key, value in data.agricultural_area_affected.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endif %}

<!-- Chart 5: Fire Causes and Chart 6: Weather Conditions -->
{% if data.fire_causes or data.weather_conditions %}
<div class="charts-grid {% if data.fire_causes and not data.weather_conditions %}single-item{% elif data.weather_conditions and not data.fire_causes %}single-item{% endif %}">
    {% if data.fire_causes %}
    <div class="chart-item">
        <div class="chart-title">أسباب الحرائق</div>
        <div class="circle-chart color-5">
            {{ data.fire_causes|dict_total }}
        </div>
        <div class="chart-legend">
            {% for key, value in data.fire_causes.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    {% if data.weather_conditions %}
    <div class="chart-item">
        <div class="chart-title">الظروف الجوية</div>
        <div class="circle-chart color-6">
            {{ data.weather_conditions|dict_total }}
        </div>
        <div class="chart-legend">
            {% for key, value in data.weather_conditions.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endif %}

<!-- Additional Forest Fire Specific Charts -->
{% comment %}
Add any additional charts specific to forest fire data
{% endcomment %}

<!-- Response Time Analysis (if available) -->
{% if data.response_time %}
<div class="charts-grid single-item">
    <div class="chart-item">
        <div class="chart-title">تحليل وقت الاستجابة</div>
        <div class="bar-chart">
            <div>متوسط الوقت: {{ data.response_time|dict_total }} دقيقة</div>
        </div>
        <div class="chart-legend">
            {% for key, value in data.response_time.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

<!-- Seasonal Distribution (if available) -->
{% if data.seasonal_distribution %}
<div class="charts-grid single-item">
    <div class="chart-item">
        <div class="chart-title">التوزيع الموسمي للحرائق</div>
        <div class="circle-chart color-7">
            {{ data.seasonal_distribution|dict_total }}
        </div>
        <div class="chart-legend">
            {% for key, value in data.seasonal_distribution.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}
