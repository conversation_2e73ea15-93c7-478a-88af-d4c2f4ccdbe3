<!-- Operations Charts for PDF (Misc Operations, Security Device, Exceptional Operations, Interventions Without Work) -->
{% load custom_filters %}

<!-- Chart 1: Operation Types and Chart 2: Operations Distribution -->
{% if data.operations or data.operation_types %}
<div class="charts-grid {% if data.operations and not data.operation_types %}single-item{% elif data.operation_types and not data.operations %}single-item{% endif %}">
    {% if data.operations %}
    <div class="chart-item">
        <div class="chart-title">العمليات</div>
        <div class="circle-chart color-1">
            {{ data.operations|dict_total }}
        </div>
        <div class="chart-legend">
            {% for key, value in data.operations.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    {% if data.operation_types %}
    <div class="chart-item">
        <div class="chart-title">أنواع العمليات</div>
        <div class="circle-chart color-2">
            {{ data.operation_types|dict_total }}
        </div>
        <div class="chart-legend">
            {% for key, value in data.operation_types.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endif %}

<!-- Chart 3: Intervention Types and Chart 4: Intervention Natures -->
{% if data.intervention_types or data.intervention_natures %}
<div class="charts-grid {% if data.intervention_types and not data.intervention_natures %}single-item{% elif data.intervention_natures and not data.intervention_types %}single-item{% endif %}">
    {% if data.intervention_types %}
    <div class="chart-item">
        <div class="chart-title">أنواع التدخلات</div>
        <div class="circle-chart color-3">
            {{ data.intervention_types|dict_total }}
        </div>
        <div class="chart-legend">
            {% for key, value in data.intervention_types.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    {% if data.intervention_natures %}
    <div class="chart-item">
        <div class="chart-title">طبيعة التدخلات</div>
        <div class="circle-chart color-4">
            {{ data.intervention_natures|dict_total }}
        </div>
        <div class="chart-legend">
            {% for key, value in data.intervention_natures.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endif %}

<!-- Chart 5: Security Device Types (for security device operations) -->
{% if data.security_device_types %}
<div class="charts-grid single-item">
    <div class="chart-item">
        <div class="chart-title">أنواع الأجهزة الأمنية</div>
        <div class="circle-chart color-5">
            {{ data.security_device_types|dict_total }}
        </div>
        <div class="chart-legend">
            {% for key, value in data.security_device_types.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

<!-- Chart 6: Exceptional Operation Types (for exceptional operations) -->
{% if data.exceptional_operation_types %}
<div class="charts-grid single-item">
    <div class="chart-item">
        <div class="chart-title">أنواع العمليات الاستثنائية</div>
        <div class="circle-chart color-6">
            {{ data.exceptional_operation_types|dict_total }}
        </div>
        <div class="chart-legend">
            {% for key, value in data.exceptional_operation_types.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

<!-- Chart 7: Resource Utilization and Chart 8: Response Time -->
{% if data.resource_utilization or data.response_time %}
<div class="charts-grid {% if data.resource_utilization and not data.response_time %}single-item{% elif data.response_time and not data.resource_utilization %}single-item{% endif %}">
    {% if data.resource_utilization %}
    <div class="chart-item">
        <div class="chart-title">استخدام الموارد</div>
        <div class="bar-chart">
            <div>إجمالي: {{ data.resource_utilization|dict_total }}</div>
        </div>
        <div class="chart-legend">
            {% for key, value in data.resource_utilization.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    {% if data.response_time %}
    <div class="chart-item">
        <div class="chart-title">وقت الاستجابة</div>
        <div class="bar-chart">
            <div>متوسط: {{ data.response_time|dict_total }} دقيقة</div>
        </div>
        <div class="chart-legend">
            {% for key, value in data.response_time.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endif %}

<!-- Additional Operations Specific Charts -->
{% comment %}
Add any additional charts specific to operations data
{% endcomment %}

<!-- Success Rate (if available) -->
{% if data.success_rate %}
<div class="charts-grid single-item">
    <div class="chart-item">
        <div class="chart-title">معدل النجاح</div>
        <div class="circle-chart color-7">
            {{ data.success_rate|dict_total }}%
        </div>
        <div class="chart-legend">
            {% for key, value in data.success_rate.items %}
                <span class="legend-item">{{ key }}: {{ value }}%</span>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

<!-- Personnel Involved (if available) -->
{% if data.personnel_involved %}
<div class="charts-grid single-item">
    <div class="chart-item">
        <div class="chart-title">الأفراد المشاركون</div>
        <div class="circle-chart color-8">
            {{ data.personnel_involved|dict_total }}
        </div>
        <div class="chart-legend">
            {% for key, value in data.personnel_involved.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}
