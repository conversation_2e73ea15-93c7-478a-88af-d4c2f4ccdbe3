<!-- Traffic Accidents Charts for PDF -->
{% load custom_filters %}

<!-- Chart 1: Accident Types and Chart 2: Accident Natures -->
{% if data.accident_types or data.accident_natures %}
<div class="charts-grid {% if data.accident_types and not data.accident_natures %}single-item{% elif data.accident_natures and not data.accident_types %}single-item{% endif %}">
    {% if data.accident_types %}
    <div class="chart-item">
        <div class="chart-title">أنواع الحوادث</div>
        <div class="circle-chart color-1">
            {{ data.accident_types|dict_total }}
        </div>
        <div class="chart-legend">
            {% for key, value in data.accident_types.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    {% if data.accident_natures %}
    <div class="chart-item">
        <div class="chart-title">طبيعة الحوادث</div>
        <div class="circle-chart color-2">
            {{ data.accident_natures|dict_total }}
        </div>
        <div class="chart-legend">
            {% for key, value in data.accident_natures.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endif %}

<!-- Chart 3: Human Losses and Chart 4: Material Losses -->
{% if data.human_losses or data.material_losses %}
<div class="charts-grid {% if data.human_losses and not data.material_losses %}single-item{% elif data.material_losses and not data.human_losses %}single-item{% endif %}">
    {% if data.human_losses %}
    <div class="chart-item">
        <div class="chart-title">الخسائر البشرية</div>
        <div class="bar-chart">
            <div>إجمالي: {{ data.human_losses|dict_total }}</div>
        </div>
        <div class="chart-legend">
            {% for key, value in data.human_losses.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    {% if data.material_losses %}
    <div class="chart-item">
        <div class="chart-title">الخسائر المادية حسب نوع المركبة</div>
        <div class="bar-chart">
            <div>إجمالي: {{ data.material_losses|dict_total }}</div>
        </div>
        <div class="chart-legend">
            {% for key, value in data.material_losses.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endif %}

<!-- Additional Traffic Accident Specific Charts -->
{% comment %}
Add any additional charts specific to traffic accident data
{% endcomment %}

<!-- Detailed Human Losses by Demographics (if available) -->
{% if data.injuries_by_demographics or data.deaths_by_demographics %}
<div class="charts-grid {% if data.injuries_by_demographics and not data.deaths_by_demographics %}single-item{% elif data.deaths_by_demographics and not data.injuries_by_demographics %}single-item{% endif %}">
    {% if data.injuries_by_demographics %}
    <div class="chart-item">
        <div class="chart-title">توزيع الجرحى حسب الفئة</div>
        <div class="circle-chart color-3">
            {{ data.injuries_by_demographics|dict_total }}
        </div>
        <div class="chart-legend">
            {% for key, value in data.injuries_by_demographics.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    {% if data.deaths_by_demographics %}
    <div class="chart-item">
        <div class="chart-title">توزيع الوفيات حسب الفئة</div>
        <div class="circle-chart color-4">
            {{ data.deaths_by_demographics|dict_total }}
        </div>
        <div class="chart-legend">
            {% for key, value in data.deaths_by_demographics.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endif %}

<!-- Accident Severity Distribution (if available) -->
{% if data.accident_severity %}
<div class="charts-grid single-item">
    <div class="chart-item">
        <div class="chart-title">توزيع الحوادث حسب الخطورة</div>
        <div class="circle-chart color-5">
            {{ data.accident_severity|dict_total }}
        </div>
        <div class="chart-legend">
            {% for key, value in data.accident_severity.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}
