<!-- Fire Charts for PDF (General, Residential, Institutional, Public Area) -->
{% load custom_filters %}

<!-- Chart 1: Fire Types and Chart 2: Locations -->
{% if data.fire_types or data.locations %}
<div class="charts-grid {% if data.fire_types and not data.locations %}single-item{% elif data.locations and not data.fire_types %}single-item{% endif %}">
    {% if data.fire_types %}
    <div class="chart-item">
        <div class="chart-title">أنواع الحرائق</div>
        <div class="circle-chart color-1">
            {{ data.fire_types|dict_total }}
        </div>
        <div class="chart-legend">
            {% for key, value in data.fire_types.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    {% if data.locations %}
    <div class="chart-item">
        <div class="chart-title">مكان الحريق</div>
        <div class="circle-chart color-2">
            {{ data.locations|dict_total }}
        </div>
        <div class="chart-legend">
            {% for key, value in data.locations.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endif %}

<!-- Chart 3: Fire Distribution and Chart 4: Intervention Count -->
{% if data.fire_distribution or data.intervention_count %}
<div class="charts-grid {% if data.fire_distribution and not data.intervention_count %}single-item{% elif data.intervention_count and not data.fire_distribution %}single-item{% endif %}">
    {% if data.fire_distribution %}
    <div class="chart-item">
        <div class="chart-title">عدد التدخلات</div>
        <div class="circle-chart color-3">
            {{ data.fire_distribution|dict_total }}
        </div>
        <div class="chart-legend">
            {% for key, value in data.fire_distribution.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    {% if data.intervention_count %}
    <div class="chart-item">
        <div class="chart-title">إجمالي التدخلات</div>
        <div class="bar-chart">
            <div>{{ data.intervention_count|dict_total }}</div>
        </div>
        <div class="chart-legend">
            {% for key, value in data.intervention_count.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endif %}

<!-- Chart 5: Human Casualties and Chart 6: Material Damage -->
{% if data.human_casualties or data.material_damage %}
<div class="charts-grid {% if data.human_casualties and not data.material_damage %}single-item{% elif data.material_damage and not data.human_casualties %}single-item{% endif %}">
    {% if data.human_casualties %}
    <div class="chart-item">
        <div class="chart-title">الخسائر البشرية</div>
        <div class="bar-chart">
            <div>إجمالي: {{ data.human_casualties|dict_total }}</div>
        </div>
        <div class="chart-legend">
            {% for key, value in data.human_casualties.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    {% if data.material_damage %}
    <div class="chart-item">
        <div class="chart-title">الأضرار المادية</div>
        <div class="bar-chart">
            <div>إجمالي: {{ data.material_damage|dict_total }}</div>
        </div>
        <div class="chart-legend">
            {% for key, value in data.material_damage.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endif %}

<!-- Additional Fire Specific Charts -->
{% comment %}
Add any additional charts specific to fire data
{% endcomment %}

<!-- Fire Causes (if available) -->
{% if data.fire_causes %}
<div class="charts-grid single-item">
    <div class="chart-item">
        <div class="chart-title">أسباب الحرائق</div>
        <div class="circle-chart color-4">
            {{ data.fire_causes|dict_total }}
        </div>
        <div class="chart-legend">
            {% for key, value in data.fire_causes.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

<!-- Fire Severity (if available) -->
{% if data.fire_severity %}
<div class="charts-grid single-item">
    <div class="chart-item">
        <div class="chart-title">شدة الحرائق</div>
        <div class="circle-chart color-5">
            {{ data.fire_severity|dict_total }}
        </div>
        <div class="chart-legend">
            {% for key, value in data.fire_severity.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}
