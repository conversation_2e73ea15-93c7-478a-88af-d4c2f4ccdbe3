<!-- Medical Evacuation Charts for PDF -->
{% load custom_filters %}

<!-- Collect all charts data -->
{% comment %}
Create a list of all available charts for medical evacuation
{% endcomment %}

<!-- Chart 1: Intervention Types -->
{% if data.intervention_types %}
<div class="charts-grid {% if data.intervention_types|length == 1 and not data.intervention_natures and not data.locations and not data.paramedics_breakdown and not data.deaths_breakdown %}single-item{% endif %}">
    <div class="chart-item">
        <div class="chart-title">أنواع التدخلات</div>
        <div class="circle-chart color-1">
            {{ data.intervention_types|dict_total }}
        </div>
        <div class="chart-legend">
            {% for key, value in data.intervention_types.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
    
    <!-- Chart 2: Intervention Natures (if available) -->
    {% if data.intervention_natures %}
    <div class="chart-item">
        <div class="chart-title">طبيعة التدخلات</div>
        <div class="circle-chart color-2">
            {{ data.intervention_natures|dict_total }}
        </div>
        <div class="chart-legend">
            {% for key, value in data.intervention_natures.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endif %}

<!-- Chart 3: Locations and Chart 4: Paramedics Breakdown -->
{% if data.locations or data.paramedics_breakdown %}
<div class="charts-grid {% if data.locations and not data.paramedics_breakdown %}single-item{% elif data.paramedics_breakdown and not data.locations %}single-item{% endif %}">
    {% if data.locations %}
    <div class="chart-item">
        <div class="chart-title">مكان التدخل</div>
        <div class="circle-chart color-3">
            {{ data.locations|dict_total }}
        </div>
        <div class="chart-legend">
            {% for key, value in data.locations.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    {% if data.paramedics_breakdown %}
    <div class="chart-item">
        <div class="chart-title">توزيع المسعفين حسب الفئة</div>
        <div class="circle-chart color-4">
            {{ data.paramedics_breakdown|dict_total }}
        </div>
        <div class="chart-legend">
            {% for key, value in data.paramedics_breakdown.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endif %}

<!-- Chart 5: Deaths Breakdown (if available, center if alone) -->
{% if data.deaths_breakdown %}
<div class="charts-grid single-item">
    <div class="chart-item">
        <div class="chart-title">توزيع الوفيات حسب الفئة</div>
        <div class="circle-chart color-5">
            {{ data.deaths_breakdown|dict_total }}
        </div>
        <div class="chart-legend">
            {% for key, value in data.deaths_breakdown.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

<!-- Additional Medical Evacuation Specific Charts -->
{% comment %}
Add any additional charts specific to medical evacuation data
{% endcomment %}

<!-- Deaths by Nature Chart (if available) -->
{% if data.deaths_by_nature %}
<div class="charts-grid single-item">
    <div class="chart-item">
        <div class="chart-title">توزيع الوفيات حسب طبيعة التدخل</div>
        <div class="bar-chart">
            <div>إجمالي: {{ data.deaths_by_nature|dict_total }}</div>
        </div>
        <div class="chart-legend">
            {% for key, value in data.deaths_by_nature.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

<!-- Paramedics by Nature Chart (if available) -->
{% if data.paramedics_by_nature %}
<div class="charts-grid single-item">
    <div class="chart-item">
        <div class="chart-title">توزيع المسعفين حسب طبيعة التدخل</div>
        <div class="bar-chart">
            <div>إجمالي: {{ data.paramedics_by_nature|dict_total }}</div>
        </div>
        <div class="chart-legend">
            {% for key, value in data.paramedics_by_nature.items %}
                <span class="legend-item">{{ key }}: {{ value }}</span>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}
