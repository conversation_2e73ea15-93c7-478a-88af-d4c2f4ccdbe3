<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>إحصاء البيانات</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Arial', sans-serif;
            direction: rtl;
            text-align: right;
            background: white;
            color: #333;
            line-height: 1.6;
            font-size: 14px;
        }

        .pdf-container {
            max-width: 100%;
            margin: 0;
            padding: 20px;
        }

        .pdf-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }

        .pdf-header h1 {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .pdf-header .subtitle {
            font-size: 16px;
            opacity: 0.9;
        }

        .main-stats {
            margin-bottom: 30px;
        }

        .stats-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }

        .stats-table th {
            background: #0d47a1;
            color: white;
            padding: 12px;
            font-weight: 600;
            text-align: center;
        }

        .stats-table td {
            padding: 10px 12px;
            text-align: center;
            border-bottom: 1px solid #e0e0e0;
        }

        .stats-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .stats-table tr:hover {
            background: #e3f2fd;
        }

        .charts-section {
            margin-top: 30px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #0d47a1;
            margin-bottom: 20px;
            text-align: center;
            padding: 10px;
            background: #f0f4ff;
            border-radius: 8px;
        }

        .charts-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .charts-grid.single-item {
            grid-template-columns: 1fr;
            justify-items: stretch;
        }

        .charts-grid .chart-item:last-child:nth-child(odd) {
            grid-column: 1 / -1;
            justify-self: center;
            max-width: 50%;
        }

        .charts-grid.single-item .chart-item {
            max-width: 100%;
            width: 100%;
        }

        .chart-item {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border: 1px solid #e0e0e0;
            text-align: center;
        }

        .chart-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
        }

        .circle-chart {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            font-weight: 700;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            position: relative;
        }

        .bar-chart {
            width: 100%;
            height: 200px;
            background: #f8f9fa;
            border-radius: 8px;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: 700;
            color: #333;
            border: 2px solid #e0e0e0;
        }

        /* Single chart takes full width */
        .charts-grid.single-item .circle-chart {
            width: 300px;
            height: 300px;
            font-size: 48px;
        }

        .charts-grid.single-item .bar-chart {
            height: 250px;
            font-size: 32px;
        }

        .chart-legend {
            margin-top: 10px;
            font-size: 12px;
        }

        .legend-item {
            display: inline-block;
            margin: 2px 5px;
            padding: 2px 8px;
            background: #f0f0f0;
            border-radius: 12px;
            font-size: 11px;
        }

        .losses-display {
            background: #fff3e0;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }

        .losses-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
        }

        .loss-item {
            background: white;
            padding: 10px;
            border-radius: 6px;
            text-align: center;
            border: 1px solid #ddd;
        }

        .loss-value {
            font-size: 18px;
            font-weight: 700;
            color: #d32f2f;
        }

        .loss-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        .conversion-note {
            font-size: 12px;
            color: #666;
            text-align: center;
            margin-top: 15px;
            font-style: italic;
            background: #f5f5f5;
            padding: 8px;
            border-radius: 4px;
        }

        .no-data {
            text-align: center;
            padding: 40px;
            color: #666;
            font-size: 16px;
        }

        /* Color schemes for different chart types */
        .color-1 { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .color-2 { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .color-3 { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .color-4 { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        .color-5 { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
        .color-6 { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
        .color-7 { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); }
        .color-8 { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); }

        @media print {
            body {
                print-color-adjust: exact;
                -webkit-print-color-adjust: exact;
                margin: 0;
                padding: 0;
            }
            .pdf-container {
                margin: 0;
                padding: 15px;
                max-width: 100%;
            }
            .chart-item {
                break-inside: avoid;
                page-break-inside: avoid;
            }
            .charts-grid {
                break-inside: avoid;
                page-break-inside: avoid;
            }
            .section-title {
                break-after: avoid;
                page-break-after: avoid;
            }
            .stats-table {
                break-inside: avoid;
                page-break-inside: avoid;
            }
            /* Hide any elements that shouldn't appear in print */
            .no-print {
                display: none !important;
            }
        }

        /* Print button styles */
        .print-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: white;
            padding: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .print-btn {
            background: #0d47a1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
        }

        .print-btn:hover {
            background: #0a3880;
        }

        @media print {
            .print-controls {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <!-- Print Controls -->
    <div class="print-controls no-print">
        <button class="print-btn" onclick="window.print()">
            <i class="fas fa-print"></i> طباعة PDF
        </button>
        <button class="print-btn" onclick="window.close()" style="background: #666; margin-right: 10px;">
            <i class="fas fa-times"></i> إغلاق
        </button>
    </div>

    <div class="pdf-container">
        <!-- Header -->
        <div class="pdf-header">
            <h1>إحصائيات البيانات - الحماية المدنية الجزائرية</h1>
            <div class="subtitle">
                {% if filters.table_type and table_names %}
                    {% for key, value in table_names.items %}
                        {% if key == filters.table_type %}{{ value }}{% endif %}
                    {% endfor %}
                {% endif %}
                {% if filters.year %} - {{ filters.year }}{% endif %}
                {% if month_name %} - {{ month_name }}{% endif %}
            </div>
        </div>

        {% load custom_filters %}
        {% if dynamic_data and filters.table_type %}
            {% with data=dynamic_data|get_item:filters.table_type %}
                <!-- Main Statistics Table -->
                {% if data %}
                <div class="main-stats">
                    <div class="section-title">الإحصائيات الرئيسية</div>
                    <table class="stats-table">
                        <thead>
                            <tr>
                                <th>المؤشر الرئيسي</th>
                                <th>القيمة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if data.total_operations %}
                            <tr><td>إجمالي العمليات</td><td>{{ data.total_operations }}</td></tr>
                            {% endif %}
                            {% if data.total_interventions %}
                            <tr><td>إجمالي التدخلات</td><td>{{ data.total_interventions }}</td></tr>
                            {% endif %}
                            {% if data.total_fires %}
                            <tr><td>إجمالي الحرائق</td><td>{{ data.total_fires }}</td></tr>
                            {% endif %}
                            {% if data.total_accidents %}
                            <tr><td>إجمالي الحوادث</td><td>{{ data.total_accidents }}</td></tr>
                            {% endif %}
                            {% if data.total_paramedics %}
                            <tr><td>إجمالي المسعفين</td><td>{{ data.total_paramedics }}</td></tr>
                            {% endif %}
                            {% if data.total_injuries %}
                            <tr><td>إجمالي الجرحى</td><td>{{ data.total_injuries }}</td></tr>
                            {% endif %}
                            {% if data.total_deaths %}
                            <tr><td>إجمالي الوفيات</td><td>{{ data.total_deaths }}</td></tr>
                            {% endif %}
                            {% if data.total_records %}
                            <tr><td>عدد العمليات</td><td>{{ data.total_records }}</td></tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
                {% endif %}

                <!-- Charts and Circles Section -->
                {% if data %}
                <div class="charts-section">
                    <div class="section-title">الرسوم البيانية والإحصائيات التفصيلية</div>

                    <!-- Collect all chart data -->
                    {% comment %}
                    This section will dynamically generate charts based on available data
                    {% endcomment %}

                    <!-- Generate Charts Based on Available Data -->
                    {% if data %}
                        <!-- Collect all charts for proper 2-per-row layout -->
                        {% comment %}
                        Create a comprehensive list of all available charts
                        {% endcomment %}

                        <!-- Medical Evacuation Charts -->
                        {% if filters.table_type == 'medical_evacuation' %}
                            <div class="charts-grid">
                                {% if data.intervention_types %}
                                <div class="chart-item">
                                    <div class="chart-title">أنواع التدخلات</div>
                                    <div class="circle-chart color-1">
                                        {{ data.intervention_types|dict_total }}
                                    </div>
                                    <div class="chart-legend">
                                        {% for key, value in data.intervention_types.items %}
                                            <span class="legend-item">{{ key }}: {{ value }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}

                                {% if data.intervention_natures %}
                                <div class="chart-item">
                                    <div class="chart-title">طبيعة التدخلات</div>
                                    <div class="circle-chart color-2">
                                        {{ data.intervention_natures|dict_total }}
                                    </div>
                                    <div class="chart-legend">
                                        {% for key, value in data.intervention_natures.items %}
                                            <span class="legend-item">{{ key }}: {{ value }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}

                                {% if data.locations %}
                                <div class="chart-item">
                                    <div class="chart-title">مكان التدخل</div>
                                    <div class="circle-chart color-3">
                                        {{ data.locations|dict_total }}
                                    </div>
                                    <div class="chart-legend">
                                        {% for key, value in data.locations.items %}
                                            <span class="legend-item">{{ key }}: {{ value }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}

                                {% if data.paramedics_breakdown %}
                                <div class="chart-item">
                                    <div class="chart-title">توزيع المسعفين حسب الفئة</div>
                                    <div class="circle-chart color-4">
                                        {{ data.paramedics_breakdown|dict_total }}
                                    </div>
                                    <div class="chart-legend">
                                        {% for key, value in data.paramedics_breakdown.items %}
                                            <span class="legend-item">{{ key }}: {{ value }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}

                                {% if data.deaths_breakdown %}
                                <div class="chart-item">
                                    <div class="chart-title">توزيع الوفيات حسب الفئة</div>
                                    <div class="circle-chart color-5">
                                        {{ data.deaths_breakdown|dict_total }}
                                    </div>
                                    <div class="chart-legend">
                                        {% for key, value in data.deaths_breakdown.items %}
                                            <span class="legend-item">{{ key }}: {{ value }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        {% endif %}

                        <!-- Traffic Accidents Charts -->
                        {% if filters.table_type == 'traffic_accidents' %}
                            <div class="charts-grid">
                                {% if data.accident_types %}
                                <div class="chart-item">
                                    <div class="chart-title">أنواع الحوادث</div>
                                    <div class="circle-chart color-1">
                                        {{ data.accident_types|dict_total }}
                                    </div>
                                    <div class="chart-legend">
                                        {% for key, value in data.accident_types.items %}
                                            <span class="legend-item">{{ key }}: {{ value }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}

                                {% if data.accident_natures %}
                                <div class="chart-item">
                                    <div class="chart-title">طبيعة الحوادث</div>
                                    <div class="circle-chart color-2">
                                        {{ data.accident_natures|dict_total }}
                                    </div>
                                    <div class="chart-legend">
                                        {% for key, value in data.accident_natures.items %}
                                            <span class="legend-item">{{ key }}: {{ value }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}

                                {% if data.time_periods %}
                                <div class="chart-item">
                                    <div class="chart-title">عدد الحوادث حسب التوقيت الزمني</div>
                                    <div class="bar-chart">
                                        <div>إجمالي: {{ data.time_periods|dict_total }}</div>
                                    </div>
                                    <div class="chart-legend">
                                        {% for key, value in data.time_periods.items %}
                                            <span class="legend-item">{{ key }}: {{ value }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}

                                {% if data.days_of_week %}
                                <div class="chart-item">
                                    <div class="chart-title">عدد الحوادث حسب أيام الأسبوع</div>
                                    <div class="circle-chart color-3">
                                        {{ data.days_of_week|dict_total }}
                                    </div>
                                    <div class="chart-legend">
                                        {% for key, value in data.days_of_week.items %}
                                            <span class="legend-item">{{ key }}: {{ value }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}

                                {% if data.driver_categories %}
                                <div class="chart-item">
                                    <div class="chart-title">عدد الحوادث حسب فئة السائقين</div>
                                    <div class="circle-chart color-4">
                                        {{ data.driver_categories|dict_total }}
                                    </div>
                                    <div class="chart-legend">
                                        {% for key, value in data.driver_categories.items %}
                                            <span class="legend-item">{{ key }}: {{ value }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}

                                {% if data.road_types %}
                                <div class="chart-item">
                                    <div class="chart-title">عدد الحوادث حسب نوع الطريق</div>
                                    <div class="circle-chart color-5">
                                        {{ data.road_types|dict_total }}
                                    </div>
                                    <div class="chart-legend">
                                        {% for key, value in data.road_types.items %}
                                            <span class="legend-item">{{ key }}: {{ value }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}

                                {% if data.human_losses %}
                                <div class="chart-item">
                                    <div class="chart-title">الخسائر البشرية</div>
                                    <div class="bar-chart">
                                        <div>إجمالي: {{ data.human_losses|dict_total }}</div>
                                    </div>
                                    <div class="chart-legend">
                                        {% for key, value in data.human_losses.items %}
                                            <span class="legend-item">{{ key }}: {{ value }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}

                                {% if data.material_losses %}
                                <div class="chart-item">
                                    <div class="chart-title">الخسائر المادية</div>
                                    <div class="bar-chart">
                                        <div>إجمالي: {{ data.material_losses|dict_total }}</div>
                                    </div>
                                    <div class="chart-legend">
                                        {% for key, value in data.material_losses.items %}
                                            <span class="legend-item">{{ key }}: {{ value }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        {% endif %}

                        <!-- Fire Charts -->
                        {% if filters.table_type == 'general_fire' or filters.table_type == 'residential_fires' or filters.table_type == 'institutional_fires' or filters.table_type == 'public_area_fires' %}
                            <div class="charts-grid">
                                {% if data.fire_types %}
                                <div class="chart-item">
                                    <div class="chart-title">أنواع الحرائق</div>
                                    <div class="circle-chart color-1">
                                        {{ data.fire_types|dict_total }}
                                    </div>
                                    <div class="chart-legend">
                                        {% for key, value in data.fire_types.items %}
                                            <span class="legend-item">{{ key }}: {{ value }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}

                                {% if data.fire_interventions %}
                                <div class="chart-item">
                                    <div class="chart-title">عدد التدخلات حسب النوع</div>
                                    <div class="circle-chart color-2">
                                        {{ data.fire_interventions|dict_total }}
                                    </div>
                                    <div class="chart-legend">
                                        {% for key, value in data.fire_interventions.items %}
                                            <span class="legend-item">{{ key }}: {{ value }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}

                                {% if data.casualties %}
                                <div class="chart-item">
                                    <div class="chart-title">الخسائر البشرية</div>
                                    <div class="bar-chart">
                                        <div>إجمالي: {{ data.casualties|dict_total }}</div>
                                    </div>
                                    <div class="chart-legend">
                                        {% for key, value in data.casualties.items %}
                                            <span class="legend-item">{{ key }}: {{ value }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        {% endif %}

                        <!-- Forest Fire Charts -->
                        {% if filters.table_type == 'forest_agricultural_fires' %}
                            <div class="charts-grid">
                                {% if data.fire_types %}
                                <div class="chart-item">
                                    <div class="chart-title">عدد التدخلات</div>
                                    <div class="circle-chart color-1">
                                        {{ data.fire_types|dict_total }}
                                    </div>
                                    <div class="chart-legend">
                                        {% for key, value in data.fire_types.items %}
                                            <span class="legend-item">{{ key }}: {{ value }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}

                                {% if data.loss_types %}
                                <div class="chart-item">
                                    <div class="chart-title">أنواع الخسائر</div>
                                    <div class="circle-chart color-2">
                                        {{ data.loss_types|dict_total }}
                                    </div>
                                    <div class="chart-legend">
                                        {% for key, value in data.loss_types.items %}
                                            <span class="legend-item">{{ key }}: {{ value }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}
                            </div>

                            {% if data.losses_detailed %}
                                <div class="section-title">الخسائر المسجلة</div>
                                <div class="losses-display">
                                    <div class="losses-grid">
                                        {% for key, value in data.losses_detailed.items %}
                                        <div class="loss-item">
                                            <div class="loss-value">{{ value }}</div>
                                            <div class="loss-label">{{ key }}</div>
                                        </div>
                                        {% endfor %}
                                    </div>

                                    {% if data.conversion_note %}
                                    <div class="conversion-note">
                                        {{ data.conversion_note }}
                                    </div>
                                    {% endif %}
                                </div>
                            {% endif %}
                        {% endif %}

                        <!-- Operations Charts - Only 2 charts and 2 circles for each operation type -->

                        <!-- Misc Operations -->
                        {% if filters.table_type == 'misc_operations' %}
                            <div class="charts-grid">
                                {% if data.operation_types %}
                                <div class="chart-item">
                                    <div class="chart-title">أنواع العمليات المختلفة</div>
                                    <div class="circle-chart color-1">
                                        {{ data.operation_types|dict_total }}
                                    </div>
                                    <div class="chart-legend">
                                        {% for key, value in data.operation_types.items %}
                                            <span class="legend-item">{{ key }}: {{ value }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}

                                {% if data.monthly_distribution %}
                                <div class="chart-item">
                                    <div class="chart-title">التوزيع الشهري</div>
                                    <div class="circle-chart color-2">
                                        {{ data.monthly_distribution|dict_total }}
                                    </div>
                                    <div class="chart-legend">
                                        {% for key, value in data.monthly_distribution.items %}
                                            <span class="legend-item">{{ key }}: {{ value }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}

                                <div class="chart-item">
                                    <div class="chart-title">ملخص العمليات</div>
                                    <div class="bar-chart">
                                        <div>العمليات: {{ data.total_operations|default:0 }} | التدخلات: {{ data.total_interventions|default:0 }}</div>
                                    </div>
                                </div>

                                <div class="chart-item">
                                    <div class="chart-title">الخسائر البشرية</div>
                                    <div class="circle-chart color-4">
                                        {{ data.total_rescuers|default:0|add:data.total_deaths|default:0 }}
                                    </div>
                                    <div class="chart-legend">
                                        <span class="legend-item">المسعفين: {{ data.total_rescuers|default:0 }}</span>
                                        <span class="legend-item">الوفيات: {{ data.total_deaths|default:0 }}</span>
                                    </div>
                                </div>
                            </div>
                        {% endif %}

                        <!-- Security Device -->
                        {% if filters.table_type == 'security_device' %}
                            <div class="charts-grid">
                                {% if data.security_device_types %}
                                <div class="chart-item">
                                    <div class="chart-title">أنواع الأجهزة الأمنية</div>
                                    <div class="circle-chart color-1">
                                        {{ data.security_device_types|dict_total }}
                                    </div>
                                    <div class="chart-legend">
                                        {% for key, value in data.security_device_types.items %}
                                            <span class="legend-item">{{ key }}: {{ value }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}

                                {% if data.monthly_distribution %}
                                <div class="chart-item">
                                    <div class="chart-title">التوزيع الشهري</div>
                                    <div class="circle-chart color-2">
                                        {{ data.monthly_distribution|dict_total }}
                                    </div>
                                    <div class="chart-legend">
                                        {% for key, value in data.monthly_distribution.items %}
                                            <span class="legend-item">{{ key }}: {{ value }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}

                                <div class="chart-item">
                                    <div class="chart-title">ملخص الأمن</div>
                                    <div class="bar-chart">
                                        <div>العمليات: {{ data.total_operations|default:0 }} | التدخلات: {{ data.total_interventions|default:0 }}</div>
                                    </div>
                                </div>

                                <div class="chart-item">
                                    <div class="chart-title">الأفراد</div>
                                    <div class="circle-chart color-4">
                                        {{ data.total_rescuers|default:0|add:data.total_deaths|default:0 }}
                                    </div>
                                    <div class="chart-legend">
                                        <span class="legend-item">المسعفين: {{ data.total_rescuers|default:0 }}</span>
                                        <span class="legend-item">الوفيات: {{ data.total_deaths|default:0 }}</span>
                                    </div>
                                </div>
                            </div>
                        {% endif %}

                        <!-- Exceptional Operations -->
                        {% if filters.table_type == 'exceptional_operations' %}
                            <div class="charts-grid">
                                {% if data.exceptional_operation_types %}
                                <div class="chart-item">
                                    <div class="chart-title">أنواع العمليات الاستثنائية</div>
                                    <div class="circle-chart color-1">
                                        {{ data.exceptional_operation_types|dict_total }}
                                    </div>
                                    <div class="chart-legend">
                                        {% for key, value in data.exceptional_operation_types.items %}
                                            <span class="legend-item">{{ key }}: {{ value }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}

                                {% if data.resource_distribution %}
                                <div class="chart-item">
                                    <div class="chart-title">توزيع الموارد</div>
                                    <div class="circle-chart color-2">
                                        {{ data.resource_distribution|dict_total }}
                                    </div>
                                    <div class="chart-legend">
                                        {% for key, value in data.resource_distribution.items %}
                                            <span class="legend-item">{{ key }}: {{ value }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}

                                <div class="chart-item">
                                    <div class="chart-title">ملخص العمليات الاستثنائية</div>
                                    <div class="bar-chart">
                                        <div>العمليات: {{ data.total_operations|default:0 }} | التدخلات: {{ data.total_interventions|default:0 }}</div>
                                    </div>
                                </div>

                                <div class="chart-item">
                                    <div class="chart-title">إجمالي الموارد</div>
                                    <div class="circle-chart color-4">
                                        {{ data.resource_distribution|dict_total }}
                                    </div>
                                    <div class="chart-legend">
                                        <span class="legend-item">الموارد المستخدمة: {{ data.resource_distribution|dict_total }}</span>
                                    </div>
                                </div>
                            </div>
                        {% endif %}

                        <!-- Interventions Without Work -->
                        {% if filters.table_type == 'interventions_without_work' %}
                            <div class="charts-grid">
                                {% if data.intervention_types %}
                                <div class="chart-item">
                                    <div class="chart-title">أنواع التدخلات بدون عمل</div>
                                    <div class="circle-chart color-1">
                                        {{ data.intervention_types|dict_total }}
                                    </div>
                                    <div class="chart-legend">
                                        {% for key, value in data.intervention_types.items %}
                                            <span class="legend-item">{{ key }}: {{ value }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}

                                {% if data.vehicle_distribution %}
                                <div class="chart-item">
                                    <div class="chart-title">توزيع المركبات</div>
                                    <div class="circle-chart color-2">
                                        {{ data.vehicle_distribution|dict_total }}
                                    </div>
                                    <div class="chart-legend">
                                        {% for key, value in data.vehicle_distribution.items %}
                                            <span class="legend-item">{{ key }}: {{ value }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}

                                <div class="chart-item">
                                    <div class="chart-title">ملخص التدخلات</div>
                                    <div class="bar-chart">
                                        <div>التدخلات بدون عمل: {{ data.total_operations|default:0 }}</div>
                                    </div>
                                </div>

                                <div class="chart-item">
                                    <div class="chart-title">إجمالي المركبات</div>
                                    <div class="circle-chart color-4">
                                        {{ data.vehicle_distribution|dict_total }}
                                    </div>
                                    <div class="chart-legend">
                                        <span class="legend-item">المركبات المستخدمة: {{ data.vehicle_distribution|dict_total }}</span>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    {% endif %}
                </div>
                {% endif %}
            {% endwith %}
        {% else %}
            <div class="no-data">
                لا توجد بيانات متاحة للتصدير
            </div>
        {% endif %}
    </div>

    <script>
        // Auto-focus for better printing experience
        document.addEventListener('DOMContentLoaded', function() {
            // Add print functionality
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.key === 'p') {
                    e.preventDefault();
                    window.print();
                }
            });

            // Handle single chart layout
            const chartsGrids = document.querySelectorAll('.charts-grid');
            chartsGrids.forEach(grid => {
                const chartItems = grid.querySelectorAll('.chart-item');
                if (chartItems.length === 1) {
                    grid.classList.add('single-item');
                }
            });

            // Optional: Auto-print when page loads (uncomment if needed)
            // setTimeout(function() {
            //     window.print();
            // }, 1000);
        });

        // Print function
        function printPDF() {
            window.print();
        }

        // Close function
        function closePDF() {
            window.close();
        }
    </script>
</body>
</html>
