{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحماية المدنية الجزائرية - لوحة تحكم التحقق الصباحي</title>
    <!-- نظام CSS الموحد الجديد -->
    <link rel="stylesheet" href="{% static 'css/dpc-unified.css' %}">
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/home.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .dashboard-container {
            max-width: 1800px;
            width: 100%;
            padding: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            margin: 20px auto;
        }

        /* تصميم العنوان المحسن لصفحة لوحة التحكم */
        .header-section-enhanced {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 2.5rem;
            border-radius: 20px;
            margin-bottom: 2rem;
            box-shadow: 0 8px 25px rgba(44, 62, 80, 0.3);
            position: relative;
            overflow: hidden;
        }

        .header-section-enhanced::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.1;
        }

        .header-content {
            display: flex;
            align-items: center;
            gap: 2rem;
            position: relative;
            z-index: 1;
        }

        .header-icon {
            font-size: 4rem;
            opacity: 0.9;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header-text {
            flex: 1;
            text-align: center;
        }

        .header-title {
            font-size: 2.2rem;
            font-weight: 700;
            margin: 0 0 0.5rem 0;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin: 0 0 1rem 0;
            line-height: 1.4;
        }

        .header-badge .badge {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.9rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .controls-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
        }

        .controls-row {
            display: flex;
            gap: 20px;
            align-items: end;
            flex-wrap: wrap;
            justify-content: center;
        }

        .control-group {
            flex: 1;
            min-width: 200px;
        }

        .control-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
            font-size: 0.95rem;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            outline: none;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #0056b3, #004085);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.08);
            border-left: 5px solid;
            transition: transform 0.3s ease;
            text-align: center;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card.primary {
            border-left-color: #007bff;
        }

        .stat-card.success {
            border-left-color: #28a745;
        }

        .stat-card.warning {
            border-left-color: #ffc107;
        }

        .stat-card.danger {
            border-left-color: #dc3545;
        }

        .stat-card .icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .stat-card.primary .icon {
            color: #007bff;
        }

        .stat-card.success .icon {
            color: #28a745;
        }

        .stat-card.warning .icon {
            color: #ffc107;
        }

        .stat-card.danger .icon {
            color: #dc3545;
        }

        .stat-card .value {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .stat-card.primary .value {
            color: #007bff;
        }

        .stat-card.success .value {
            color: #28a745;
        }

        .stat-card.warning .value {
            color: #ffc107;
        }

        .stat-card.danger .value {
            color: #dc3545;
        }

        .stat-card .label {
            color: #6c757d;
            font-size: 1rem;
            font-weight: 600;
        }

        .charts-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .chart-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.08);
        }

        .chart-card h3 {
            text-align: center;
            margin-bottom: 20px;
            color: #2c3e50;
            font-size: 1.3rem;
            font-weight: 600;
        }

        .chart-container {
            position: relative;
            height: 300px;
        }

        .alerts-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .alerts-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .alerts-header h3 {
            color: #2c3e50;
            font-size: 1.3rem;
            font-weight: 600;
            margin: 0;
        }

        .alert-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .alert-item.critical {
            border-left-color: #dc3545;
        }

        .alert-item.high {
            border-left-color: #fd7e14;
        }

        .alert-item.medium {
            border-left-color: #ffc107;
        }

        .alert-item.low {
            border-left-color: #17a2b8;
        }

        .alert-content h4 {
            margin: 0 0 5px 0;
            font-size: 1rem;
            font-weight: 600;
        }

        .alert-content p {
            margin: 0;
            color: #6c757d;
            font-size: 0.9rem;
        }

        .alert-actions {
            display: flex;
            gap: 10px;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.875rem;
        }

        .units-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 15px rgba(0,0,0,0.08);
        }

        .units-table th {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 15px 12px;
            text-align: center;
            font-weight: 600;
            font-size: 0.95rem;
        }

        .units-table td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #e9ecef;
            vertical-align: middle;
        }

        .units-table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .readiness-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            position: relative;
        }

        .readiness-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s ease;
        }

        .readiness-fill.excellent {
            background: linear-gradient(90deg, #28a745, #20c997);
        }

        .readiness-fill.good {
            background: linear-gradient(90deg, #ffc107, #fd7e14);
        }

        .readiness-fill.poor {
            background: linear-gradient(90deg, #dc3545, #e74c3c);
        }

        .readiness-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 0.8rem;
            font-weight: 600;
            color: #2c3e50;
        }

        /* أدوات التحكم المحسنة */
        .controls-section-enhanced {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid #e3f2fd;
        }

        .controls-header {
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #f5f5f5;
        }

        .controls-header h3 {
            color: #2c3e50;
            font-size: 1.4rem;
            font-weight: 700;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .controls-content {
            display: flex;
            gap: 2rem;
            align-items: end;
            flex-wrap: wrap;
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 12px;
            border: 1px solid #e9ecef;
        }

        .control-group-enhanced {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            min-width: 200px;
        }

        .control-group-enhanced label {
            font-weight: 600;
            color: #495057;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-control-enhanced {
            padding: 0.75rem 1rem;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-control-enhanced:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .action-buttons-enhanced {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .action-btn-enhanced {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 20px;
            border: none;
            border-radius: 10px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            min-width: 150px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .action-btn-enhanced::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .action-btn-enhanced:hover::before {
            left: 100%;
        }

        .action-btn-enhanced:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
            color: white;
            text-decoration: none;
        }

        .btn-morning-check {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }

        .btn-coordination {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        }

        .btn-refresh {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        }

        .action-btn-enhanced .btn-icon {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .action-btn-enhanced .btn-text {
            font-weight: 600;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .controls-row {
                flex-direction: column;
            }

            .controls-content {
                flex-direction: column;
                gap: 1rem;
            }

            .control-group-enhanced {
                min-width: 100%;
            }

            .action-buttons-enhanced {
                justify-content: center;
                width: 100%;
            }

            .action-btn-enhanced {
                min-width: 120px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .charts-section {
                grid-template-columns: 1fr;
            }

            .alerts-header {
                flex-direction: column;
                gap: 15px;
            }

            .header-content {
                flex-direction: column;
                text-align: center;
                gap: 1rem;
            }
        }

        /* الأزرار العائمة */
        .floating-buttons {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .floating-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            transition: all 0.3s;
            text-decoration: none;
            font-size: 18px;
            border: none;
            cursor: pointer;
        }

        .floating-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
            color: white;
            text-decoration: none;
        }

        .coordination-btn {
            background-color: #28a745;
        }

        .unified-btn {
            background-color: #ffc107;
        }

        .assignment-btn {
            background-color: #17a2b8;
        }

        .interventions-btn {
            background-color: #e74c3c;
        }

        .home-btn {
            background-color: #0d47a1;
        }

        .top-btn {
            background-color: #0d6efd;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle clickable" id="sidebar-toggle">
            </div>
        </div>
    </header>

    {% include 'includes/sidebar.html' %}

    <main>
        <div class="dashboard-container">
            <!-- العنوان المحسن -->
            <div class="header-section-enhanced">
                <div class="header-content">
                    <div class="header-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="header-text">
                        <h1 class="header-title">لوحة تحكم التحقق الصباحي</h1>
                        <p class="header-subtitle">نظرة شاملة على جاهزية جميع الوحدات والإحصائيات التفصيلية</p>
                        <div class="header-badge">
                            <span class="badge badge-info">
                                <i class="fas fa-calendar"></i> {{ selected_date|date:"l, d F Y" }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أدوات التحكم المحسنة -->
            <div class="controls-section-enhanced">
                <div class="controls-header">
                    <h3><i class="fas fa-cogs"></i> أدوات التحكم والتنقل</h3>
                </div>

                <div class="controls-content">
                    <form method="GET" id="controlsForm" class="controls-form">
                        <div class="control-group-enhanced">
                            <label for="date">
                                <i class="fas fa-calendar"></i>
                                التاريخ
                            </label>
                            <input type="date" name="date" id="date" class="form-control-enhanced"
                                   value="{{ selected_date|date:'Y-m-d' }}" onchange="this.form.submit()">
                        </div>
                    </form>

                    <div class="action-buttons-enhanced">
                        <a href="{% url 'morning_check_system' %}" class="action-btn-enhanced btn-morning-check">
                            <div class="btn-icon">
                                <i class="fas fa-sun"></i>
                            </div>
                            <div class="btn-text">
                                التحقق الصباحي
                            </div>
                        </a>

                        <a href="{% url 'coordination_center' %}" class="action-btn-enhanced btn-coordination">
                            <div class="btn-icon">
                                <i class="fas fa-sitemap"></i>
                            </div>
                            <div class="btn-text">
                                مركز التنسيق
                            </div>
                        </a>

                        <button class="action-btn-enhanced btn-refresh" onclick="location.reload()">
                            <div class="btn-icon">
                                <i class="fas fa-sync-alt"></i>
                            </div>
                            <div class="btn-text">
                                تحديث البيانات
                            </div>
                        </button>
                    </div>
                </div>
            </div>

            <!-- الإحصائيات العامة -->
            <div class="stats-grid">
                <div class="stat-card primary">
                    <div class="icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="value">{{ units_with_summaries }}/{{ total_units }}</div>
                    <div class="label">الوحدات المفعلة</div>
                </div>
                
                <div class="stat-card success">
                    <div class="icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="value">{{ units_fully_ready }}</div>
                    <div class="label">وحدات جاهزة بالكامل</div>
                </div>
                
                <div class="stat-card warning">
                    <div class="icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="value">{{ avg_readiness }}%</div>
                    <div class="label">متوسط الجاهزية</div>
                </div>
                
                <div class="stat-card danger">
                    <div class="icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="value">{{ active_alerts.count }}</div>
                    <div class="label">تنبيهات نشطة</div>
                </div>
            </div>

            <!-- الرسوم البيانية -->
            <div class="charts-section">
                <div class="chart-card">
                    <h3>توزيع حالة الأعوان</h3>
                    <div class="chart-container">
                        <canvas id="personnelChart"></canvas>
                    </div>
                </div>
                
                <div class="chart-card">
                    <h3>توزيع حالة الوسائل</h3>
                    <div class="chart-container">
                        <canvas id="vehicleChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- التنبيهات النشطة -->
            {% if active_alerts %}
            <div class="alerts-section">
                <div class="alerts-header">
                    <h3><i class="fas fa-bell"></i> التنبيهات النشطة</h3>
                    <div>
                        <span class="badge badge-danger">حرج: {{ critical_alerts }}</span>
                        <span class="badge badge-warning">عالي: {{ high_alerts }}</span>
                        <span class="badge badge-info">متوسط: {{ medium_alerts }}</span>
                        <span class="badge badge-secondary">منخفض: {{ low_alerts }}</span>
                    </div>
                </div>
                
                {% for alert in active_alerts|slice:":10" %}
                <div class="alert-item {{ alert.priority }}">
                    <div class="alert-content">
                        <h4>{{ alert.title }}</h4>
                        <p>{{ alert.unit.name }} - {{ alert.get_priority_display }} - {{ alert.date }}</p>
                    </div>
                    <div class="alert-actions">
                        <button class="btn btn-sm btn-primary" onclick="acknowledgeAlert({{ alert.id }})">
                            <i class="fas fa-eye"></i> اطلاع
                        </button>
                        <button class="btn btn-sm btn-success" onclick="resolveAlert({{ alert.id }})">
                            <i class="fas fa-check"></i> حل
                        </button>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% endif %}

            <!-- جدول الوحدات -->
            <div style="margin-bottom: 30px;">
                <h3 style="margin-bottom: 20px; color: #2c3e50;">
                    <i class="fas fa-list"></i> تفاصيل الوحدات
                </h3>
                
                <table class="units-table">
                    <thead>
                        <tr>
                            <th>الوحدة</th>
                            <th>الفرقة العاملة</th>
                            <th>الأعوان</th>
                            <th>الوسائل</th>
                            <th>نسبة الجاهزية</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for summary in morning_summaries %}
                        <tr>
                            <td>{{ summary.unit.name }}</td>
                            <td>
                                {% if summary.active_shift %}
                                    {{ summary.active_shift.get_name_display }}
                                {% else %}
                                    <span style="color: #dc3545;">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>{{ summary.present_personnel }}/{{ summary.total_personnel }}</td>
                            <td>{{ summary.ready_vehicles }}/{{ summary.total_vehicles }}</td>
                            <td>
                                <div class="readiness-bar">
                                    <div class="readiness-fill {% if summary.overall_readiness_score >= 80 %}excellent{% elif summary.overall_readiness_score >= 60 %}good{% else %}poor{% endif %}" 
                                         style="width: {{ summary.overall_readiness_score }}%"></div>
                                    <div class="readiness-text">{{ summary.overall_readiness_score }}%</div>
                                </div>
                            </td>
                            <td>
                                {% if summary.is_fully_ready %}
                                    <span style="color: #28a745; font-weight: 600;">
                                        <i class="fas fa-check-circle"></i> جاهز
                                    </span>
                                {% else %}
                                    <span style="color: #dc3545; font-weight: 600;">
                                        <i class="fas fa-exclamation-circle"></i> غير جاهز
                                    </span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{% url 'morning_check_system' %}?unit_id={{ summary.unit.id }}&date={{ selected_date|date:'Y-m-d' }}" 
                                   class="btn btn-sm btn-primary">
                                    <i class="fas fa-eye"></i> عرض
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- الأزرار العائمة -->
            <div class="floating-buttons">
                <a href="{% url 'coordination_center' %}" class="floating-btn coordination-btn" title="مركز التنسيق">
                    <i class="fas fa-building"></i>
                </a>
                <a href="{% url 'unified_morning_check' %}" class="floating-btn unified-btn" title="الصفحة الموحدة">
                    <i class="fas fa-sun"></i>
                </a>
                <a href="{% url 'vehicle_crew_assignment' %}" class="floating-btn assignment-btn" title="توزيع الأعوان">
                    <i class="fas fa-users"></i>
                </a>
                <a href="{% url 'daily_interventions' %}" class="floating-btn interventions-btn" title="التدخلات اليومية">
                    <i class="fas fa-ambulance"></i>
                </a>
                <a href="{% url 'home' %}" class="floating-btn home-btn" title="الصفحة الرئيسية">
                    <i class="fas fa-home"></i>
                </a>
                <a href="#" id="back-to-top" class="floating-btn top-btn" title="العودة إلى الأعلى">
                    <i class="fas fa-arrow-up"></i>
                </a>
            </div>
        </div>
    </main>

    <script>
        // بيانات الرسوم البيانية
        const chartData = {{ chart_data|safe }};

        // رسم بياني لحالة الأعوان
        const personnelCtx = document.getElementById('personnelChart').getContext('2d');
        new Chart(personnelCtx, {
            type: 'doughnut',
            data: {
                labels: ['حاضر', 'غائب', 'في مهمة'],
                datasets: [{
                    data: [
                        chartData.personnel_status.present,
                        chartData.personnel_status.absent,
                        chartData.personnel_status.on_mission
                    ],
                    backgroundColor: ['#28a745', '#dc3545', '#ffc107'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // رسم بياني لحالة الوسائل
        const vehicleCtx = document.getElementById('vehicleChart').getContext('2d');
        new Chart(vehicleCtx, {
            type: 'doughnut',
            data: {
                labels: ['جاهز', 'غير جاهز', 'صيانة'],
                datasets: [{
                    data: [
                        chartData.vehicle_status.ready,
                        chartData.vehicle_status.not_ready,
                        chartData.vehicle_status.maintenance
                    ],
                    backgroundColor: ['#28a745', '#dc3545', '#ffc107'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // دوال إدارة التنبيهات
        function acknowledgeAlert(alertId) {
            if (confirm('هل تريد تأكيد الاطلاع على هذا التنبيه؟')) {
                fetch('{% url "readiness_alerts" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify({
                        action: 'acknowledge_alert',
                        alert_id: alertId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        location.reload();
                    } else {
                        alert('حدث خطأ: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('حدث خطأ في الاتصال');
                });
            }
        }

        function resolveAlert(alertId) {
            const notes = prompt('ملاحظات الحل (اختياري):');
            if (notes !== null) {
                fetch('{% url "readiness_alerts" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify({
                        action: 'resolve_alert',
                        alert_id: alertId,
                        resolution_notes: notes
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        location.reload();
                    } else {
                        alert('حدث خطأ: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('حدث خطأ في الاتصال');
                });
            }
        }

        // دالة للحصول على CSRF token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // وظيفة العودة إلى الأعلى
        document.getElementById('back-to-top').addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    </script>
</body>
</html>
