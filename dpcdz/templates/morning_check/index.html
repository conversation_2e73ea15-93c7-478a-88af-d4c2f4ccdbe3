{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحماية المدنية الجزائرية - نظام التحقق الصباحي المتقدم</title>
    <link rel="stylesheet" href="{% static 'css/dpc-unified.css' %}">
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/home.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle clickable" id="sidebar-toggle">
            </div>
        </div>
    </header>

    {% include 'includes/sidebar.html' %}

    <main>
        <div class="morning-check-container">
            {% csrf_token %}
            
            <!-- العنوان الرئيسي -->
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-sun"></i>
                    نظام التحقق الصباحي المتقدم
                </h1>
                <p class="page-subtitle">نظام شامل لإدارة التعداد الصباحي وجاهزية الوحدات</p>
            </div>

            <!-- أدوات التحكم -->
            <div class="controls-section">
                <form method="GET" class="controls-form">
                    <div class="controls-row">
                        <div class="control-group">
                            <label for="unit_id">الوحدة</label>
                            <select name="unit_id" id="unit_id" class="form-control" onchange="this.form.submit()">
                                <option value="">-- اختر الوحدة --</option>
                                {% for unit in units %}
                                    <option value="{{ unit.id }}" {% if selected_unit and selected_unit.id == unit.id %}selected{% endif %}>
                                        {{ unit.name }} - {{ unit.get_wilaya_display }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="control-group">
                            <label for="date">التاريخ</label>
                            <input type="date" name="date" id="date" class="form-control" 
                                   value="{{ selected_date|date:'Y-m-d' }}" onchange="this.form.submit()">
                        </div>
                        
                        <div class="control-group">
                            <a href="{% url 'morning_check_dashboard' %}" class="btn btn-primary">
                                <i class="fas fa-chart-line"></i> لوحة التحكم
                            </a>
                        </div>
                    </div>
                </form>
            </div>

            {% if not selected_unit %}
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle"></i>
                    يرجى اختيار الوحدة للمتابعة
                </div>
            {% else %}

            <!-- بطاقات الملخص -->
            <div class="summary-cards">
                <!-- بطاقة الأعوان -->
                <div class="summary-card">
                    <div class="card-header">
                        <h3><i class="fas fa-users"></i> الأعوان</h3>
                    </div>
                    <div class="card-body">
                        <div class="value">{{ present_count }}/{{ unit_personnel.count }}</div>
                        <div class="label">حاضر من إجمالي</div>
                    </div>
                </div>
                
                <!-- بطاقة الوسائل -->
                <div class="summary-card">
                    <div class="card-header">
                        <h3><i class="fas fa-truck"></i> الوسائل</h3>
                    </div>
                    <div class="card-body">
                        <div class="value">{{ operational_count }}/{{ unit_equipment.count }}</div>
                        <div class="label">جاهز من إجمالي</div>
                    </div>
                </div>
                
                <!-- بطاقة نسبة الجاهزية -->
                <div class="summary-card">
                    <div class="card-header">
                        <h3><i class="fas fa-percentage"></i> نسبة الجاهزية</h3>
                    </div>
                    <div class="card-body">
                        <div class="value readiness-score" data-score="{{ readiness_score }}">{{ readiness_score }}%</div>
                        <div class="label">الجاهزية العامة</div>
                    </div>
                </div>
                
                <!-- بطاقة الفرقة العاملة -->
                <div class="summary-card">
                    <div class="card-header">
                        <h3><i class="fas fa-clock"></i> الفرقة العاملة</h3>
                    </div>
                    <div class="card-body">
                        <div class="value">
                            {% if daily_schedule and daily_schedule.active_shift %}
                                {{ daily_schedule.active_shift.get_name_display }}
                            {% else %}
                                غير محدد
                            {% endif %}
                        </div>
                        <div class="label">اليوم</div>
                    </div>
                </div>
            </div>

            <!-- التنبيهات النشطة -->
            {% if active_alerts %}
            <div class="alert alert-warning">
                <h4><i class="fas fa-exclamation-triangle"></i> تنبيهات نشطة ({{ active_alerts.count }})</h4>
                <ul class="alerts-list">
                    {% for alert in active_alerts %}
                    <li class="alert-item priority-{{ alert.priority }}">
                        <strong>{{ alert.title }}</strong> - {{ alert.get_priority_display }}
                        {% if alert.message %}
                            <br><small>{{ alert.message }}</small>
                        {% endif %}
                    </li>
                    {% endfor %}
                </ul>
            </div>
            {% endif %}

            <!-- نظام التبويبات -->
            <div class="tabs-container">
                <div class="nav-tabs">
                    <button class="nav-tab active" onclick="showTab('personnel')">
                        <i class="fas fa-users"></i> الأعوان
                    </button>
                    <button class="nav-tab" onclick="showTab('equipment')">
                        <i class="fas fa-truck"></i> الوسائل
                    </button>
                    <button class="nav-tab" onclick="showTab('shifts')">
                        <i class="fas fa-clock"></i> الفرق
                    </button>
                    <button class="nav-tab" onclick="showTab('eight-hours')">
                        <i class="fas fa-business-time"></i> نظام 8 ساعات
                    </button>
                </div>

                <!-- تبويب الأعوان -->
                <div id="personnel" class="tab-content active">
                    <!-- إحصائيات الفرق الثلاث -->
                    <div class="shift-stats-container">
                        <h4><i class="fas fa-users-cog"></i> إحصائيات الفرق</h4>
                        <div class="shift-cards">
                            {% for shift_key, shift_data in shift_stats.items %}
                            <div class="shift-card" data-shift="{{ shift_key }}">
                                <div class="shift-header">
                                    <h5>{{ shift_data.name }}</h5>
                                    <span class="shift-count">{{ shift_data.present_count }}/{{ shift_data.total_count }}</span>
                                </div>
                                <div class="shift-progress">
                                    <div class="progress-bar" style="width: {% if shift_data.total_count > 0 %}{{ shift_data.present_count|floatformat:0|mul:100|div:shift_data.total_count }}{% else %}0{% endif %}%"></div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <div class="action-buttons">
                        <button class="btn btn-success" onclick="addPersonnel()">
                            <i class="fas fa-user-plus"></i> إضافة عون جديد
                        </button>
                        <button class="btn btn-info" onclick="exportPersonnelData()">
                            <i class="fas fa-file-excel"></i> تصدير البيانات
                        </button>
                    </div>

                    <div class="table-responsive">
                        <table class="data-table" id="personnelTable">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-hashtag"></i> رقم التسجيل</th>
                                    <th><i class="fas fa-id-card"></i> رقم القيد</th>
                                    <th><i class="fas fa-user"></i> الاسم الكامل</th>
                                    <th><i class="fas fa-venus-mars"></i> الجنس</th>
                                    <th><i class="fas fa-birthday-cake"></i> العمر</th>
                                    <th><i class="fas fa-phone"></i> الهاتف</th>
                                    <th><i class="fas fa-star"></i> الرتبة</th>
                                    <th><i class="fas fa-briefcase"></i> المنصب</th>
                                    <th><i class="fas fa-clock"></i> نظام العمل</th>
                                    <th><i class="fas fa-users"></i> الفرقة</th>
                                    <th><i class="fas fa-check-circle"></i> الحالة</th>
                                    <th><i class="fas fa-cogs"></i> الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for personnel in unit_personnel %}
                                <tr data-status="{{ personnel.daily_status.status }}" data-shift="{{ personnel.assigned_shift }}">
                                    <td><strong>{{ personnel.personnel_registration_number }}</strong></td>
                                    <td>{{ personnel.registration_number|default:"-" }}</td>
                                    <td>{{ personnel.full_name }}</td>
                                    <td>
                                        {% if personnel.gender == 'male' %}
                                            <i class="fas fa-mars text-primary"></i> ذكر
                                        {% elif personnel.gender == 'female' %}
                                            <i class="fas fa-venus text-danger"></i> أنثى
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>{{ personnel.age|default:"-" }}</td>
                                    <td>
                                        {% if personnel.phone_number %}
                                            <a href="tel:{{ personnel.phone_number }}">{{ personnel.phone_number }}</a>
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>{{ personnel.rank|default:"-" }}</td>
                                    <td>{{ personnel.position|default:"-" }}</td>
                                    <td>
                                        <span class="work-system-badge work-system-{{ personnel.work_system }}">
                                            {{ personnel.get_work_system_display }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if personnel.assigned_shift %}
                                            <span class="shift-badge shift-{{ personnel.assigned_shift }}">
                                                {{ personnel.get_shift_display_arabic }}
                                            </span>
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="status-badge status-{{ personnel.daily_status.status }}">
                                            {{ personnel.daily_status.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="action-buttons-group">
                                            <button class="btn btn-primary btn-sm" onclick="editPersonnel({{ personnel.id }})" title="تعديل البيانات">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            {% if personnel.work_system == '24_hours' %}
                                            <button class="btn btn-warning btn-sm" onclick="transferPersonnel({{ personnel.id }})" title="تحويل إلى فرقة أخرى">
                                                <i class="fas fa-exchange-alt"></i>
                                            </button>
                                            {% endif %}
                                            <button class="btn btn-danger btn-sm" onclick="deletePersonnel({{ personnel.id }})" title="حذف العون">
                                                <i class="fas fa-user-minus"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="12" class="text-center">لا توجد بيانات أعوان</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- تبويب الوسائل -->
                <div id="equipment" class="tab-content">
                    <div class="action-buttons">
                        <button class="btn btn-success" onclick="addEquipment()">
                            <i class="fas fa-plus-circle"></i> إضافة وسيلة جديدة
                        </button>
                        <button class="btn btn-info" onclick="exportEquipmentData()">
                            <i class="fas fa-file-excel"></i> تصدير البيانات
                        </button>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="data-table" id="equipmentTable">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-hashtag"></i> الرقم التسلسلي</th>
                                    <th><i class="fas fa-truck"></i> نوع الوسيلة</th>
                                    <th><i class="fas fa-broadcast-tower"></i> رقم الراديو</th>
                                    <th><i class="fas fa-check-circle"></i> الحالة</th>
                                    <th><i class="fas fa-sticky-note"></i> ملاحظات</th>
                                    <th><i class="fas fa-cogs"></i> الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for equipment in unit_equipment %}
                                <tr data-status="{{ equipment.daily_status.status }}">
                                    <td>{{ equipment.serial_number }}</td>
                                    <td>{{ equipment.equipment_type }}</td>
                                    <td>{{ equipment.radio_number|default:"-" }}</td>
                                    <td>
                                        <span class="status-badge status-{{ equipment.daily_status.status }}">
                                            {{ equipment.daily_status.get_status_display }}
                                        </span>
                                    </td>
                                    <td>{{ equipment.daily_status.notes|default:"-" }}</td>
                                    <td>
                                        <div class="action-buttons-group">
                                            <button class="btn btn-primary btn-sm" onclick="editEquipmentStatus({{ equipment.id }})" title="تعديل الحالة">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <a href="{% url 'vehicle_crew_assignment' %}?unit={{ selected_unit.id }}&vehicle={{ equipment.id }}"
                                               class="btn btn-warning btn-sm" title="توزيع الأعوان">
                                                <i class="fas fa-users"></i>
                                            </a>
                                            <button class="btn btn-danger btn-sm" onclick="deleteEquipment({{ equipment.id }})" title="حذف الوسيلة">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center">لا توجد بيانات وسائل</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- تبويب الفرق -->
                <div id="shifts" class="tab-content">
                    <div class="action-buttons">
                        <button class="btn btn-success" onclick="createShift()">
                            <i class="fas fa-plus"></i> إنشاء فرقة
                        </button>
                        <button class="btn btn-primary" onclick="setActiveShift()">
                            <i class="fas fa-clock"></i> تعيين فرقة عاملة
                        </button>
                    </div>

                    <div class="alert alert-info">
                        <strong>الفرقة العاملة اليوم:</strong>
                        {% if daily_schedule and daily_schedule.active_shift %}
                            {{ daily_schedule.active_shift.get_name_display }} ({{ daily_schedule.active_shift.get_shift_type_display }})
                        {% else %}
                            لم يتم تحديد فرقة عاملة لهذا اليوم
                        {% endif %}
                    </div>

                    <div class="table-responsive">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>اسم الفرقة</th>
                                    <th>نوع النظام</th>
                                    <th>عدد الأعوان</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for shift in available_shifts %}
                                <tr>
                                    <td>{{ shift.get_name_display }}</td>
                                    <td>{{ shift.get_shift_type_display }}</td>
                                    <td>{{ shift.personnel.count }}</td>
                                    <td>
                                        {% if daily_schedule and daily_schedule.active_shift and daily_schedule.active_shift.id == shift.id %}
                                            <span class="status-badge status-operational">نشط</span>
                                        {% else %}
                                            <span class="status-badge status-maintenance">غير نشط</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <button class="btn btn-primary btn-sm" onclick="manageShiftPersonnel({{ shift.id }})">
                                            <i class="fas fa-users"></i> إدارة الأعوان
                                        </button>
                                        {% if not daily_schedule or not daily_schedule.active_shift or daily_schedule.active_shift.id != shift.id %}
                                        <button class="btn btn-warning btn-sm" onclick="activateShift({{ shift.id }})">
                                            <i class="fas fa-play"></i> تفعيل
                                        </button>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center">لا توجد فرق مسجلة</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- تبويب نظام 8 ساعات -->
                <div id="eight-hours" class="tab-content">
                    <div class="action-buttons">
                        <button class="btn btn-success" onclick="addEightHourPersonnel()">
                            <i class="fas fa-clock"></i> إضافة عون نظام 8 ساعات
                        </button>
                        <button class="btn btn-info" onclick="exportEightHourData()">
                            <i class="fas fa-file-excel"></i> تصدير البيانات
                        </button>
                    </div>

                    <div class="table-responsive">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-hashtag"></i> رقم التسجيل</th>
                                    <th><i class="fas fa-user"></i> الاسم الكامل</th>
                                    <th><i class="fas fa-venus-mars"></i> الجنس</th>
                                    <th><i class="fas fa-birthday-cake"></i> العمر</th>
                                    <th><i class="fas fa-phone"></i> الهاتف</th>
                                    <th><i class="fas fa-clock"></i> فترة العمل</th>
                                    <th><i class="fas fa-tasks"></i> نوع المهمة</th>
                                    <th><i class="fas fa-play"></i> وقت البداية</th>
                                    <th><i class="fas fa-stop"></i> وقت النهاية</th>
                                    <th><i class="fas fa-check-circle"></i> الحضور</th>
                                    <th><i class="fas fa-cogs"></i> الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in eight_hour_personnel %}
                                <tr>
                                    <td><strong>{{ record.personnel_registration_number }}</strong></td>
                                    <td>{{ record.full_name }}</td>
                                    <td>
                                        {% if record.gender == 'male' %}
                                            <i class="fas fa-mars text-primary"></i> ذكر
                                        {% elif record.gender == 'female' %}
                                            <i class="fas fa-venus text-danger"></i> أنثى
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>{{ record.age }}</td>
                                    <td>
                                        {% if record.phone_number %}
                                            <a href="tel:{{ record.phone_number }}">{{ record.phone_number }}</a>
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>{{ record.get_work_period_display }}</td>
                                    <td>{{ record.get_task_type_display }}</td>
                                    <td>{{ record.start_time }}</td>
                                    <td>{{ record.end_time }}</td>
                                    <td>
                                        {% if record.is_present %}
                                            <span class="status-badge status-present">حاضر</span>
                                        {% else %}
                                            <span class="status-badge status-absent">غائب</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="action-buttons-group">
                                            <button class="btn btn-primary btn-sm" onclick="editEightHourPersonnel({{ record.id }})" title="تعديل البيانات">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-danger btn-sm" onclick="deleteEightHourPersonnel({{ record.id }})" title="حذف العون">
                                                <i class="fas fa-user-times"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="11" class="text-center">لا توجد بيانات نظام 8 ساعات</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            {% endif %}

            <!-- الأزرار العائمة -->
            <div class="floating-buttons">
                <a href="{% url 'coordination_center' %}" class="floating-btn home-btn" title="مركز التنسيق">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <a href="#" id="back-to-top" class="floating-btn top-btn" title="العودة إلى الأعلى">
                    <i class="fas fa-arrow-up"></i>
                </a>
            </div>
        </div>
    </main>

    <footer>
        <p>حقوق النشر © 2025 جميع الحقوق محفوظة - تم التطوير بواسطة عبد الرزاق مختاري</p>
    </footer>

    <!-- JavaScript -->
    <script>
        // Tab functionality
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all nav tabs
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab content
            const selectedTab = document.getElementById(tabName);
            if (selectedTab) {
                selectedTab.classList.add('active');
            }

            // Add active class to clicked nav tab
            const clickedTab = event.target.closest('.nav-tab');
            if (clickedTab) {
                clickedTab.classList.add('active');
            }
        }

        // Personnel functions
        function addPersonnel() {
            // إنشاء نموذج إضافة عون جديد
            const modalHtml = `
                <div class="modal-overlay" id="addPersonnelModal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3><i class="fas fa-user-plus"></i> إضافة عون جديد</h3>
                            <button class="close-btn" onclick="closeModal('addPersonnelModal')">&times;</button>
                        </div>
                        <form id="addPersonnelForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="personnel_registration_number">رقم التسجيل *</label>
                                    <input type="text" id="personnel_registration_number" name="personnel_registration_number" required>
                                </div>
                                <div class="form-group">
                                    <label for="registration_number">رقم القيد</label>
                                    <input type="text" id="registration_number" name="registration_number">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="full_name">الاسم الكامل *</label>
                                    <input type="text" id="full_name" name="full_name" required>
                                </div>
                                <div class="form-group">
                                    <label for="gender">الجنس *</label>
                                    <select id="gender" name="gender" required>
                                        <option value="">اختر الجنس</option>
                                        <option value="male">ذكر</option>
                                        <option value="female">أنثى</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="age">العمر *</label>
                                    <input type="number" id="age" name="age" min="18" max="65" required>
                                </div>
                                <div class="form-group">
                                    <label for="phone_number">رقم الهاتف</label>
                                    <input type="tel" id="phone_number" name="phone_number">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="rank">الرتبة</label>
                                    <input type="text" id="rank" name="rank">
                                </div>
                                <div class="form-group">
                                    <label for="position">المنصب</label>
                                    <input type="text" id="position" name="position">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="work_system">نظام العمل *</label>
                                    <select id="work_system" name="work_system" required onchange="toggleShiftField()">
                                        <option value="">اختر نظام العمل</option>
                                        <option value="24_hours">نظام 24 ساعة</option>
                                        <option value="8_hours">نظام 8 ساعات</option>
                                    </select>
                                </div>
                                <div class="form-group" id="shift_group" style="display: none;">
                                    <label for="assigned_shift">الفرقة المخصصة</label>
                                    <select id="assigned_shift" name="assigned_shift">
                                        <option value="">اختر الفرقة</option>
                                        <option value="shift_1">الفرقة الأولى</option>
                                        <option value="shift_2">الفرقة الثانية</option>
                                        <option value="shift_3">الفرقة الثالثة</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-actions">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save"></i> حفظ
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="closeModal('addPersonnelModal')">
                                    <i class="fas fa-times"></i> إلغاء
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // إضافة مستمع للنموذج
            document.getElementById('addPersonnelForm').addEventListener('submit', function(e) {
                e.preventDefault();
                submitAddPersonnel();
            });
        }

        function toggleShiftField() {
            const workSystem = document.getElementById('work_system').value;
            const shiftGroup = document.getElementById('shift_group');

            if (workSystem === '24_hours') {
                shiftGroup.style.display = 'block';
                document.getElementById('assigned_shift').required = true;
            } else {
                shiftGroup.style.display = 'none';
                document.getElementById('assigned_shift').required = false;
                document.getElementById('assigned_shift').value = '';
            }
        }

        function submitAddPersonnel() {
            const form = document.getElementById('addPersonnelForm');
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);
            data.unit_id = {{ selected_unit.id }};

            fetch('{% url "add_personnel_ajax" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم إضافة العون بنجاح');
                    closeModal('addPersonnelModal');
                    location.reload();
                } else {
                    alert('خطأ: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في الاتصال');
            });
        }

        function editPersonnel(personnelId) {
            alert('تعديل بيانات العون رقم: ' + personnelId + ' - سيتم تطوير هذه الوظيفة قريباً');
        }

        function deletePersonnel(personnelId) {
            if (confirm('هل أنت متأكد من حذف هذا العون؟')) {
                fetch('{% url "delete_personnel_ajax" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                    },
                    body: JSON.stringify({personnel_id: personnelId})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        location.reload();
                    } else {
                        alert('خطأ: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ في الاتصال');
                });
            }
        }

        function transferPersonnel(personnelId) {
            // إنشاء نموذج تحويل العون
            const modalHtml = `
                <div class="modal-overlay" id="transferPersonnelModal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3><i class="fas fa-exchange-alt"></i> تحويل عون بين الفرق</h3>
                            <button class="close-btn" onclick="closeModal('transferPersonnelModal')">&times;</button>
                        </div>
                        <form id="transferPersonnelForm">
                            <div class="form-group">
                                <label for="target_shift">الفرقة المستهدفة *</label>
                                <select id="target_shift" name="target_shift" required>
                                    <option value="">اختر الفرقة المستهدفة</option>
                                    <option value="shift_1">الفرقة الأولى</option>
                                    <option value="shift_2">الفرقة الثانية</option>
                                    <option value="shift_3">الفرقة الثالثة</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="transfer_reason">سبب التحويل *</label>
                                <textarea id="transfer_reason" name="transfer_reason" rows="3" required></textarea>
                            </div>
                            <div class="form-actions">
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-exchange-alt"></i> تحويل
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="closeModal('transferPersonnelModal')">
                                    <i class="fas fa-times"></i> إلغاء
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // إضافة مستمع للنموذج
            document.getElementById('transferPersonnelForm').addEventListener('submit', function(e) {
                e.preventDefault();
                submitTransferPersonnel(personnelId);
            });
        }

        function submitTransferPersonnel(personnelId) {
            const form = document.getElementById('transferPersonnelForm');
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);
            data.personnel_id = personnelId;

            fetch('{% url "transfer_personnel_ajax" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    closeModal('transferPersonnelModal');
                    location.reload();
                } else {
                    alert('خطأ: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في الاتصال');
            });
        }

        function exportPersonnelData() {
            alert('تصدير بيانات الأعوان - سيتم تطوير هذه الوظيفة قريباً');
        }

        // Equipment functions
        function addEquipment() {
            // إنشاء نموذج إضافة وسيلة جديدة
            const modalHtml = `
                <div class="modal-overlay" id="addEquipmentModal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3><i class="fas fa-plus-circle"></i> إضافة وسيلة جديدة</h3>
                            <button class="close-btn" onclick="closeModal('addEquipmentModal')">&times;</button>
                        </div>
                        <form id="addEquipmentForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="serial_number">الرقم التسلسلي *</label>
                                    <input type="text" id="serial_number" name="serial_number" required>
                                </div>
                                <div class="form-group">
                                    <label for="equipment_type">نوع الوسيلة *</label>
                                    <input type="text" id="equipment_type" name="equipment_type" required>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="radio_number">رقم الراديو</label>
                                    <input type="text" id="radio_number" name="radio_number">
                                </div>
                            </div>
                            <div class="form-actions">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save"></i> حفظ
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="closeModal('addEquipmentModal')">
                                    <i class="fas fa-times"></i> إلغاء
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // إضافة مستمع للنموذج
            document.getElementById('addEquipmentForm').addEventListener('submit', function(e) {
                e.preventDefault();
                submitAddEquipment();
            });
        }

        function submitAddEquipment() {
            const form = document.getElementById('addEquipmentForm');
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);
            data.unit_id = {{ selected_unit.id }};

            fetch('{% url "add_equipment_ajax" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم إضافة الوسيلة بنجاح');
                    closeModal('addEquipmentModal');
                    location.reload();
                } else {
                    alert('خطأ: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في الاتصال');
            });
        }

        function deleteEquipment(equipmentId) {
            if (confirm('هل أنت متأكد من حذف هذه الوسيلة؟')) {
                fetch('{% url "delete_equipment_ajax" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                    },
                    body: JSON.stringify({equipment_id: equipmentId})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        location.reload();
                    } else {
                        alert('خطأ: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ في الاتصال');
                });
            }
        }

        function editEquipmentStatus(equipmentId) {
            alert('تعديل حالة الوسيلة رقم: ' + equipmentId + ' - سيتم تطوير هذه الوظيفة قريباً');
        }

        function exportEquipmentData() {
            alert('تصدير بيانات الوسائل - سيتم تطوير هذه الوظيفة قريباً');
        }

        // Shift functions
        function createShift() {
            alert('إنشاء فرقة جديدة - سيتم تطوير هذه الوظيفة قريباً');
        }

        function setActiveShift() {
            alert('تعيين فرقة عاملة - سيتم تطوير هذه الوظيفة قريباً');
        }

        function manageShiftPersonnel(shiftId) {
            alert('إدارة أعوان الفرقة رقم: ' + shiftId + ' - سيتم تطوير هذه الوظيفة قريباً');
        }

        function activateShift(shiftId) {
            alert('تفعيل الفرقة رقم: ' + shiftId + ' - سيتم تطوير هذه الوظيفة قريباً');
        }

        // Eight hour personnel functions
        function addEightHourPersonnel() {
            // إنشاء نموذج إضافة عون نظام 8 ساعات
            const modalHtml = `
                <div class="modal-overlay" id="addEightHourPersonnelModal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3><i class="fas fa-clock"></i> إضافة عون نظام 8 ساعات</h3>
                            <button class="close-btn" onclick="closeModal('addEightHourPersonnelModal')">&times;</button>
                        </div>
                        <form id="addEightHourPersonnelForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="eight_personnel_registration_number">رقم التسجيل *</label>
                                    <input type="text" id="eight_personnel_registration_number" name="personnel_registration_number" required>
                                </div>
                                <div class="form-group">
                                    <label for="eight_full_name">الاسم الكامل *</label>
                                    <input type="text" id="eight_full_name" name="full_name" required>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="eight_gender">الجنس *</label>
                                    <select id="eight_gender" name="gender" required>
                                        <option value="">اختر الجنس</option>
                                        <option value="male">ذكر</option>
                                        <option value="female">أنثى</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="eight_age">العمر *</label>
                                    <input type="number" id="eight_age" name="age" min="18" max="65" required>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="eight_phone_number">رقم الهاتف</label>
                                    <input type="tel" id="eight_phone_number" name="phone_number">
                                </div>
                                <div class="form-group">
                                    <label for="work_period">فترة العمل *</label>
                                    <select id="work_period" name="work_period" required>
                                        <option value="">اختر فترة العمل</option>
                                        <option value="morning">فترة صباحية (08:00 - 16:00)</option>
                                        <option value="evening">فترة مسائية (16:00 - 00:00)</option>
                                        <option value="night">فترة ليلية (00:00 - 08:00)</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="task_type">نوع المهمة *</label>
                                    <select id="task_type" name="task_type" required>
                                        <option value="">اختر نوع المهمة</option>
                                        <option value="administrative">عمل إداري</option>
                                        <option value="maintenance">صيانة</option>
                                        <option value="communications">اتصالات</option>
                                        <option value="storage">مخزن</option>
                                        <option value="security">أمن</option>
                                        <option value="support">دعم</option>
                                        <option value="technical">تقني</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="task_description">وصف المهمة</label>
                                <textarea id="task_description" name="task_description" rows="3"></textarea>
                            </div>
                            <div class="form-group">
                                <label for="eight_notes">ملاحظات</label>
                                <textarea id="eight_notes" name="notes" rows="2"></textarea>
                            </div>
                            <div class="form-actions">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save"></i> حفظ
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="closeModal('addEightHourPersonnelModal')">
                                    <i class="fas fa-times"></i> إلغاء
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // إضافة مستمع للنموذج
            document.getElementById('addEightHourPersonnelForm').addEventListener('submit', function(e) {
                e.preventDefault();
                submitAddEightHourPersonnel();
            });
        }

        function submitAddEightHourPersonnel() {
            const form = document.getElementById('addEightHourPersonnelForm');
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);
            data.unit_id = {{ selected_unit.id }};

            fetch('{% url "add_eight_hour_personnel_ajax" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم إضافة عون نظام 8 ساعات بنجاح');
                    closeModal('addEightHourPersonnelModal');
                    location.reload();
                } else {
                    alert('خطأ: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في الاتصال');
            });
        }

        function editEightHourPersonnel(personnelId) {
            alert('تعديل بيانات عون نظام 8 ساعات رقم: ' + personnelId + ' - سيتم تطوير هذه الوظيفة قريباً');
        }

        function deleteEightHourPersonnel(personnelId) {
            if (confirm('هل أنت متأكد من حذف عون نظام 8 ساعات؟')) {
                fetch('{% url "delete_eight_hour_personnel_ajax" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                    },
                    body: JSON.stringify({personnel_id: personnelId})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        location.reload();
                    } else {
                        alert('خطأ: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ في الاتصال');
                });
            }
        }

        function exportEightHourData() {
            alert('تصدير بيانات نظام 8 ساعات - سيتم تطوير هذه الوظيفة قريباً');
        }

        // Modal functions
        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.remove();
            }
        }

        // إغلاق النوافذ المنبثقة عند النقر خارجها
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal-overlay')) {
                e.target.remove();
            }
        });

        // Back to top functionality
        document.addEventListener('DOMContentLoaded', function() {
            const backToTopBtn = document.getElementById('back-to-top');

            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    backToTopBtn.style.display = 'block';
                } else {
                    backToTopBtn.style.display = 'none';
                }
            });

            backToTopBtn.addEventListener('click', function(e) {
                e.preventDefault();
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        });

        // Sidebar functionality
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const sidebar = document.querySelector('.sidebar');

            if (sidebarToggle && sidebar) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('active');
                });
            }
        });

        // Readiness score color coding
        document.addEventListener('DOMContentLoaded', function() {
            const readinessScore = document.querySelector('.readiness-score');
            if (readinessScore) {
                const score = parseInt(readinessScore.getAttribute('data-score'));
                if (score >= 80) {
                    readinessScore.style.color = '#28a745'; // Green
                } else if (score >= 60) {
                    readinessScore.style.color = '#ffc107'; // Yellow
                } else {
                    readinessScore.style.color = '#dc3545'; // Red
                }
            }
        });
    </script>

    <style>
        /* إضافات CSS للنظام المحدث */

        /* إحصائيات الفرق */
        .shift-stats-container {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .shift-cards {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .shift-card {
            flex: 1;
            min-width: 200px;
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #007bff;
        }

        .shift-card[data-shift="shift_1"] {
            border-left-color: #007bff;
        }

        .shift-card[data-shift="shift_2"] {
            border-left-color: #28a745;
        }

        .shift-card[data-shift="shift_3"] {
            border-left-color: #fd7e14;
        }

        .shift-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .shift-header h5 {
            margin: 0;
            color: #333;
        }

        .shift-count {
            font-weight: bold;
            color: #666;
        }

        .shift-progress {
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            background: #007bff;
            transition: width 0.3s ease;
        }

        /* شارات الحالة */
        .work-system-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .work-system-24_hours {
            background: #e3f2fd;
            color: #1976d2;
        }

        .work-system-8_hours {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        .shift-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .shift-shift_1 {
            background: #e3f2fd;
            color: #1976d2;
        }

        .shift-shift_2 {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .shift-shift_3 {
            background: #fff3e0;
            color: #f57c00;
        }

        /* مجموعة أزرار الإجراءات */
        .action-buttons-group {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .action-buttons-group .btn {
            padding: 4px 8px;
            font-size: 12px;
        }

        /* النوافذ المنبثقة */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            border-radius: 8px;
            padding: 0;
            max-width: 600px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            background: #007bff;
            color: white;
            padding: 15px 20px;
            border-radius: 8px 8px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 18px;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
        }

        .modal-content form {
            padding: 20px;
        }

        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-group {
            flex: 1;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .form-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }

        /* تحسينات الجدول */
        .data-table th {
            font-size: 13px;
            padding: 8px 6px;
        }

        .data-table td {
            font-size: 13px;
            padding: 8px 6px;
        }

        /* تحسينات متجاوبة */
        @media (max-width: 768px) {
            .shift-cards {
                flex-direction: column;
            }

            .form-row {
                flex-direction: column;
            }

            .action-buttons-group {
                justify-content: center;
            }

            .modal-content {
                width: 95%;
                margin: 10px;
            }
        }
    </style>
</body>
</html>
