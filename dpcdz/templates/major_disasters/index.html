{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحماية المدنية الجزائرية - الكوارث الكبرى</title>
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/home.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- Leaflet CSS for interactive maps -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
</head>
<body>
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle" id="sidebar-toggle" style="cursor: pointer;">
            </div>
        </div>
    </header>

    {% include 'includes/sidebar.html' %}

    <main>
        <div class="home-container">
            <div class="page-header">
                <div class="page-icon">
                    <i class="fas fa-exclamation-circle"></i>
                </div>
                <h2>الكوارث الكبرى</h2>
                <p class="page-description">منصة القيادة والتنسيق لإدارة الكوارث الكبرى</p>
            </div>

            <!-- الأزرار الرئيسية -->
            <div class="menu-grid">
                <a href="{% url 'field_agent' %}" class="menu-item disaster-item">
                    <div class="menu-icon">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <div class="menu-title">رئيس العدد</div>
                    <div class="menu-description">
                        واجهة العون الميداني لتحديث معلومات التدخل والإبلاغ عن الكوارث
                    </div>
                </a>

                <a href="{% url 'unit_leader' %}" class="menu-item disaster-item">
                    <div class="menu-icon">
                        <i class="fas fa-user-cog"></i>
                    </div>
                    <div class="menu-title">قائد الوحدة</div>
                    <div class="menu-description">
                        واجهة قائد الوحدة لإدارة الدعم والتنسيق بين الوحدات
                    </div>
                </a>
            </div>

            <!-- خريطة تفاعلية -->
            <div class="map-section">
                <div class="map-header">
                    <h3><i class="fas fa-map-marked-alt"></i> خريطة الكوارث التفاعلية</h3>
                    <div class="map-controls">
                        <button class="btn btn-primary" id="refresh-map">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                        <button class="btn btn-secondary" id="fullscreen-map">
                            <i class="fas fa-expand"></i> ملء الشاشة
                        </button>
                    </div>
                </div>
                <div id="disaster-map" class="disaster-map"></div>
            </div>

            <!-- لوحة معلومات الكوارث النشطة -->
            <div class="active-disasters">
                <h3><i class="fas fa-fire"></i> الكوارث النشطة</h3>
                <div class="disasters-grid">
                    <div class="disaster-card fire">
                        <div class="disaster-icon">
                            <i class="fas fa-fire"></i>
                        </div>
                        <div class="disaster-info">
                            <h4>حريق غابة - جبل بوكرع</h4>
                            <p><i class="fas fa-map-marker-alt"></i> دائرة المشروحة، ولاية سوق أهراس</p>
                            <p><i class="fas fa-clock"></i> 2025/07/15 - 14:22</p>
                            <span class="severity high">خطورة مرتفعة</span>
                        </div>
                        <div class="disaster-actions">
                            <button class="btn btn-sm btn-primary">تفاصيل</button>
                            <button class="btn btn-sm btn-warning">تحديث</button>
                        </div>
                    </div>

                    <div class="disaster-card flood">
                        <div class="disaster-icon">
                            <i class="fas fa-water"></i>
                        </div>
                        <div class="disaster-info">
                            <h4>فيضان - وادي سيبوس</h4>
                            <p><i class="fas fa-map-marker-alt"></i> بلدية سوق أهراس</p>
                            <p><i class="fas fa-clock"></i> 2025/07/15 - 16:45</p>
                            <span class="severity medium">خطورة متوسطة</span>
                        </div>
                        <div class="disaster-actions">
                            <button class="btn btn-sm btn-primary">تفاصيل</button>
                            <button class="btn btn-sm btn-warning">تحديث</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الأزرار العائمة -->
            <div class="floating-buttons">
                <a href="{% url 'home' %}" class="floating-btn home-btn" title="الصفحة الرئيسية">
                    <i class="fas fa-home"></i>
                </a>
                <a href="{% url 'coordination_center' %}" class="floating-btn coordination-btn" title="مركز التنسيق">
                    <i class="fas fa-building"></i>
                </a>
                <a href="#" id="back-to-top" class="floating-btn top-btn" title="العودة إلى الأعلى">
                    <i class="fas fa-arrow-up"></i>
                </a>
            </div>
        </div>
    </main>

    <footer>
        <p>حقوق النشر © 2025 جميع الحقوق محفوظة - تم التطوير بواسطة عبد الرزاق مختاري</p>
    </footer>

    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="{% static 'js/sidebar.js' %}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize the map
            const map = L.map('disaster-map').setView([36.2861, 7.5431], 10); // Souk Ahras coordinates

            // Add OpenStreetMap tiles
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);

            // Add sample disaster markers
            const fireIcon = L.divIcon({
                html: '<i class="fas fa-fire" style="color: #ff4444; font-size: 20px;"></i>',
                iconSize: [30, 30],
                className: 'disaster-marker'
            });

            const floodIcon = L.divIcon({
                html: '<i class="fas fa-water" style="color: #4444ff; font-size: 20px;"></i>',
                iconSize: [30, 30],
                className: 'disaster-marker'
            });

            // Sample fire marker
            L.marker([36.3, 7.6], {icon: fireIcon})
                .addTo(map)
                .bindPopup('<b>حريق غابة - جبل بوكرع</b><br>خطورة مرتفعة<br>3 وحدات متدخلة');

            // Sample flood marker
            L.marker([36.27, 7.53], {icon: floodIcon})
                .addTo(map)
                .bindPopup('<b>فيضان - وادي سيبوس</b><br>خطورة متوسطة<br>2 وحدات متدخلة');

            // Map controls
            document.getElementById('refresh-map').addEventListener('click', function() {
                map.invalidateSize();
                // Here you would typically reload disaster data
            });

            document.getElementById('fullscreen-map').addEventListener('click', function() {
                const mapContainer = document.getElementById('disaster-map');
                if (mapContainer.requestFullscreen) {
                    mapContainer.requestFullscreen();
                }
            });

            // Back to top button
            const backToTopButton = document.getElementById('back-to-top');
            if (backToTopButton) {
                backToTopButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                });
            }
        });
    </script>

    <style>
        /* Page header styles */
        .page-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #dc3545, #fd7e14);
            border-radius: 10px;
            color: white;
        }

        .page-icon {
            font-size: 48px;
            margin-bottom: 10px;
        }

        .page-description {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }

        /* Disaster menu items */
        .disaster-item {
            border: 2px solid #dc3545;
            transition: all 0.3s ease;
        }

        .disaster-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
            border-color: #c82333;
        }

        /* Map section */
        .map-section {
            margin: 30px 0;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .map-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .map-controls {
            display: flex;
            gap: 10px;
        }

        .disaster-map {
            height: 400px;
            width: 100%;
        }

        /* Active disasters section */
        .active-disasters {
            margin: 30px 0;
        }

        .disasters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .disaster-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 5px solid;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .disaster-card.fire {
            border-left-color: #dc3545;
        }

        .disaster-card.flood {
            border-left-color: #007bff;
        }

        .disaster-icon {
            font-size: 32px;
            width: 60px;
            text-align: center;
        }

        .disaster-card.fire .disaster-icon {
            color: #dc3545;
        }

        .disaster-card.flood .disaster-icon {
            color: #007bff;
        }

        .disaster-info {
            flex: 1;
        }

        .disaster-info h4 {
            margin: 0 0 10px 0;
            color: #333;
        }

        .disaster-info p {
            margin: 5px 0;
            color: #666;
            font-size: 14px;
        }

        .severity {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .severity.high {
            background: #dc3545;
            color: white;
        }

        .severity.medium {
            background: #ffc107;
            color: #333;
        }

        .disaster-actions {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        /* Floating buttons */
        .floating-buttons {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .floating-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            transition: all 0.3s;
            text-decoration: none;
            font-size: 18px;
            border: none;
            cursor: pointer;
        }

        .floating-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
        }

        .home-btn {
            background-color: #0d47a1;
        }

        .coordination-btn {
            background-color: #28a745;
        }

        .top-btn {
            background-color: #0d6efd;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .map-header {
                flex-direction: column;
                gap: 10px;
            }

            .disasters-grid {
                grid-template-columns: 1fr;
            }

            .disaster-card {
                flex-direction: column;
                text-align: center;
            }
        }
    </style>
</body>
</html>
