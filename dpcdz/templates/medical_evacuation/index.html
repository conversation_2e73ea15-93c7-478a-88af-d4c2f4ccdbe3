{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإجلاء الصحي - الحماية المدنية الجزائرية</title>
    <link rel="stylesheet" href="{% static 'css/styles.css' %}?v=1.1">
    <link rel="stylesheet" href="{% static 'css/medical_evacuation.css' %}?v=1.1">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}?v=1.1">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/themes/material_blue.css">
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/ar.js"></script>
    <style>
        /* Override button styles */
        .save-btn {
            background-color: #28a745 !important;
            color: white !important;
        }
        .save-btn:hover {
            background-color: #218838 !important;
        }
        .home-btn {
            background-color: #0d47a1 !important;
            color: white !important;
        }
        .home-btn:hover {
            background-color: #0a3880 !important;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle" id="sidebar-toggle" style="cursor: pointer;">
            </div>
        </div>
    </header>

    {% include 'includes/sidebar.html' %}

    <main>
        <div class="form-container">
            <h2 class="form-title">نموذج الإجلاء الصحي</h2>

            {% if messages %}
                <div class="messages">
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}

            <form id="medicalEvacuationForm" method="post" action="{% url 'medical_evacuation' %}">
                {% csrf_token %}

                <!-- Header Row -->
                <div class="form-row">
                    <div class="form-group">
                        <label for="date">التاريخ:</label>
                        <div class="date-picker-container">
                            <input type="text" id="date" name="date" class="form-control date-picker date-picker-input" placeholder="اختر التاريخ" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="unit">الوحدة المتدخلة:</label>
                        <select id="unit" name="unit" class="form-select" required>
                            <option value="" disabled selected>اختر الوحدة</option>
                            {% for unit in units %}
                            <option value="{{ unit.name }}">{{ unit.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="interventionType">نوع التدخل:</label>
                        <select id="interventionType" name="intervention_type" class="form-select" required>
                            <option value="" disabled selected>اختر نوع التدخل</option>
                            <option value="الاختناق">الاختناق (Choking)</option>
                            <option value="التسممات">التسممات (Poisoning)</option>
                            <option value="الحروق">الحروق (Burns)</option>
                            <option value="الانفجارات">الانفجارات (Explosions)</option>
                            <option value="إجلاء المرضى">إجلاء المرضى (Patient Evacuation)</option>
                            <option value="الغرقى">الغرقى (Drowning)</option>
                        </select>
                    </div>
                </div>

                <!-- Second Row -->
                <div class="form-row">
                    <div class="form-group">
                        <label for="interventionNature">طبيعة التدخل:</label>
                        <select id="interventionNature" name="intervention_nature" class="form-select" required disabled>
                            <option value="" disabled selected>اختر نوع التدخل أولاً</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>الموقع:</label>
                        <div class="radio-group">
                            <div class="radio-option">
                                <input type="radio" id="locationIndoor" name="location" value="indoor" required>
                                <label for="locationIndoor">داخل المنزل</label>
                            </div>
                            <div class="radio-option">
                                <input type="radio" id="locationOutdoor" name="location" value="outdoor">
                                <label for="locationOutdoor">خارج المنزل</label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Counts Row -->
                <div class="form-row">
                    <div class="form-group count-field">
                        <label for="operationsCount">عدد العمليات:</label>
                        <input type="number" id="operationsCount" name="operations_count" min="0" value="0" class="form-control" required>
                    </div>

                    <div class="form-group count-field">
                        <label for="interventionsCount">عدد التدخلات:</label>
                        <input type="number" id="interventionsCount" name="interventions_count" min="0" value="0" class="form-control" required>
                    </div>
                </div>

                <!-- Detailed fields for all intervention types (hidden by default) -->
                <div id="detailedFields" style="display: none;">
                    <!-- Operations and Interventions Row -->
                    <div class="form-row">
                        <div class="form-group count-field">
                            <label for="detailedOperationsCount">عدد العمليات:</label>
                            <input type="number" id="detailedOperationsCount" name="detailed_operations_count" min="0" value="0" class="form-control">
                        </div>

                        <div class="form-group count-field">
                            <label for="detailedInterventionsCount">عدد التدخلات:</label>
                            <input type="number" id="detailedInterventionsCount" name="detailed_interventions_count" min="0" value="0" class="form-control">
                        </div>
                    </div>

                    <!-- Paramedics Row -->
                    <div class="form-row">
                        <div class="form-group count-field">
                            <label for="paramedicsChildrenCount">عدد المسعفين أطفال:</label>
                            <input type="number" id="paramedicsChildrenCount" name="paramedics_children_count" min="0" value="0" class="form-control paramedics-input">
                        </div>

                        <div class="form-group count-field">
                            <label for="paramedicsWomenCount">عدد المسعفين نساء:</label>
                            <input type="number" id="paramedicsWomenCount" name="paramedics_women_count" min="0" value="0" class="form-control paramedics-input">
                        </div>

                        <div class="form-group count-field">
                            <label for="paramedicsMenCount">عدد المسعفين رجال:</label>
                            <input type="number" id="paramedicsMenCount" name="paramedics_men_count" min="0" value="0" class="form-control paramedics-input">
                        </div>

                        <div class="form-group count-field">
                            <label for="totalParamedicsCount">مجموع المسعفين:</label>
                            <input type="number" id="totalParamedicsCount" name="total_paramedics_count" min="0" value="0" class="form-control" readonly style="background-color: #f8f9fa;">
                        </div>
                    </div>

                    <!-- Deaths Row -->
                    <div class="form-row">
                        <div class="form-group count-field">
                            <label for="deathsChildrenCount">عدد الوفيات أطفال:</label>
                            <input type="number" id="deathsChildrenCount" name="deaths_children_count" min="0" value="0" class="form-control deaths-input">
                        </div>

                        <div class="form-group count-field">
                            <label for="deathsWomenCount">عدد الوفيات نساء:</label>
                            <input type="number" id="deathsWomenCount" name="deaths_women_count" min="0" value="0" class="form-control deaths-input">
                        </div>

                        <div class="form-group count-field">
                            <label for="deathsMenCount">عدد الوفيات رجال:</label>
                            <input type="number" id="deathsMenCount" name="deaths_men_count" min="0" value="0" class="form-control deaths-input">
                        </div>

                        <div class="form-group count-field">
                            <label for="totalDeathsDetailedCount">مجموع الوفيات:</label>
                            <input type="number" id="totalDeathsDetailedCount" name="total_deaths_detailed_count" min="0" value="0" class="form-control" readonly style="background-color: #f8f9fa;">
                        </div>
                    </div>
                </div>

                <!-- Action Buttons Row -->
                <div class="main-buttons-container">
                    <div class="main-buttons">
                        <a href="{% url 'home' %}" class="main-btn home-btn" style="background-color: #0d47a1 !important; color: white !important;">
                            <i class="fas fa-home"></i> الصفحة الرئيسية
                        </a>
                        <button type="button" id="saveToBoth" class="main-btn save-btn" style="background-color: #28a745 !important; color: white !important;">
                            <i class="fas fa-save"></i> حفظ
                        </button>
                    </div>
                </div>
            </form>

            <!-- الأزرار العائمة -->
            <div class="floating-buttons">
                <a href="{% url 'table_medical_evacuation' %}" class="floating-btn tables-btn" title="لوحة الجداول">
                    <i class="fas fa-th"></i>
                </a>
                <a href="{% url 'home' %}" class="floating-btn home-btn" title="الصفحة الرئيسية" style="background-color: #0d47a1 !important;">
                    <i class="fas fa-home"></i>
                </a>
                <button type="button" id="floatingSaveBtn" class="floating-btn save-btn" title="حفظ" style="background-color: #28a745 !important;">
                    <i class="fas fa-save"></i>
                </button>
                <a href="#" id="back-to-top" class="floating-btn top-btn" title="العودة إلى الأعلى">
                    <i class="fas fa-arrow-up"></i>
                </a>
            </div>
        </div>
    </main>

    <footer>
        <p>حقوق النشر © 2025 جميع الحقوق محفوظة - تم التطوير بواسطة عبد الرزاق مختاري</p>
    </footer>

    <!-- Modal for confirmation -->
    <div id="confirmModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>تأكيد العملية</h3>
            <p>هل أنت متأكد من إرسال البيانات؟</p>
            <div class="modal-buttons">
                <button id="confirmYes" class="btn btn-green">نعم</button>
                <button id="confirmNo" class="btn btn-gray">لا</button>
            </div>
        </div>
    </div>

    <script src="{% static 'js/sidebar.js' %}"></script>
    <script src="{% static 'js/common-buttons.js' %}"></script>
    <script src="{% static 'js/medical_evacuation.js' %}"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
</body>
</html>
