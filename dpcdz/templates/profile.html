{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحماية المدنية الجزائرية - الملف الشخصي</title>
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .profile-container {
            max-width: 800px;
            margin: 50px auto;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }

        .profile-header {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }

        .profile-avatar {
            width: 100px;
            height: 100px;
            background-color: #0d47a1;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 40px;
            margin-left: 20px;
        }

        .profile-title h1 {
            font-size: 24px;
            color: #333;
            margin-bottom: 5px;
        }

        .profile-title p {
            font-size: 16px;
            color: #666;
            margin: 0;
        }

        .profile-info {
            margin-bottom: 30px;
        }

        .info-group {
            margin-bottom: 20px;
        }

        .info-group h2 {
            font-size: 18px;
            color: #0d47a1;
            margin-bottom: 15px;
            border-bottom: 2px solid #0d47a1;
            padding-bottom: 5px;
            display: inline-block;
        }

        .info-row {
            display: flex;
            margin-bottom: 10px;
        }

        .info-label {
            width: 150px;
            font-weight: bold;
            color: #555;
        }

        .info-value {
            flex: 1;
            color: #333;
        }

        .profile-actions {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
        }

        .profile-actions a {
            padding: 8px 15px;
            border-radius: 4px;
            text-decoration: none;
            font-weight: bold;
            margin-right: 10px;
            transition: background-color 0.3s;
        }

        .btn-edit {
            background-color: #0d47a1;
            color: white;
        }

        .btn-edit:hover {
            background-color: #083378;
        }

        .btn-back {
            background-color: #6c757d;
            color: white;
        }

        .btn-back:hover {
            background-color: #5a6268;
        }

        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }

        .user-actions {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            gap: 10px;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle" id="sidebar-toggle" style="cursor: pointer;">
            </div>
        </div>
    </header>

    <!-- Sidebar -->
    <div id="sidebar-overlay" class="sidebar-overlay"></div>
    <div id="sidebar" class="sidebar">
        <span id="sidebar-close" class="sidebar-close">
            <i class="fas fa-times"></i>
        </span>

        <div class="sidebar-header">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-name">{{ request.user.username }}</div>
                <div class="user-role">
                    {% if request.user.is_superuser %}
                        مدير النظام
                    {% elif request.user.is_staff %}
                        مشرف
                    {% else %}
                        مستخدم عادي
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="sidebar-content">
            <ul class="sidebar-menu">
                <li>
                    <a href="{% url 'profile' %}">
                        <i class="fas fa-user-circle"></i>
                        الملف الشخصي
                    </a>
                </li>
                <li>
                    <a href="{% url 'home' %}">
                        <i class="fas fa-home"></i>
                        الصفحة الرئيسية
                    </a>
                </li>


                <li>
                    <a href="{% url 'fires' %}">
                        <i class="fas fa-fire"></i>
                        الحرائق
                    </a>
                </li>
                <li>
                    <a href="{% url 'misc_operations' %}">
                        <i class="fas fa-tools"></i>
                        عمليات مختلفة
                    </a>
                </li>
                <li>
                    <a href="{% url 'settings' %}">
                        <i class="fas fa-cog"></i>
                        الإعدادات
                    </a>
                </li>
                <li>
                    <a href="{% url 'logout' %}">
                        <i class="fas fa-sign-out-alt"></i>
                        تسجيل الخروج
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <main>
        <div class="profile-container">
            <div class="profile-header">
                <div class="profile-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="profile-title">
                    <h1>{{ request.user.get_full_name|default:request.user.username }}</h1>
                    <p>{{ request.user.email }}</p>
                </div>
            </div>

            <div class="profile-info">
                <div class="info-group">
                    <h2>معلومات المستخدم</h2>
                    <div class="info-row">
                        <div class="info-label">اسم المستخدم:</div>
                        <div class="info-value">{{ request.user.username }}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">الاسم الكامل:</div>
                        <div class="info-value">{{ request.user.get_full_name|default:"غير محدد" }}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">البريد الإلكتروني:</div>
                        <div class="info-value">{{ request.user.email|default:"غير محدد" }}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">آخر تسجيل دخول:</div>
                        <div class="info-value">{{ request.user.last_login|date:"d/m/Y H:i" }}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">تاريخ الانضمام:</div>
                        <div class="info-value">{{ request.user.date_joined|date:"d/m/Y" }}</div>
                    </div>
                </div>

                <div class="info-group">
                    <h2>الصلاحيات</h2>
                    <div class="info-row">
                        <div class="info-label">نوع الحساب:</div>
                        <div class="info-value">
                            {% if request.user.is_superuser %}
                                مدير النظام
                            {% elif request.user.is_staff %}
                                مشرف
                            {% else %}
                                مستخدم عادي
                            {% endif %}
                        </div>
                    </div>
                    {% if request.user.is_staff %}
                    <div class="info-row">
                        <div class="info-label">صلاحيات إضافية:</div>
                        <div class="info-value">
                            <ul>
                                {% if request.user.is_superuser %}
                                <li>الوصول الكامل للنظام</li>
                                {% endif %}
                                <li>الوصول إلى لوحة الإدارة</li>
                            </ul>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <div class="profile-actions">
                <a href="{% url 'home' %}" class="btn-back">العودة للرئيسية</a>
            </div>
        </div>
    </main>

    <footer>
        <p>حقوق النشر © 2025 جميع الحقوق محفوظة - تم التطوير بواسطة عبد الرزاق مختاري</p>
    </footer>

    <script src="{% static 'js/sidebar.js' %}"></script>
</body>
</html>
