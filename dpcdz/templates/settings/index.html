{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container mt-5">
    <h2 class="mb-4 text-center">إدارة النظام</h2>
    
    <!-- User Management Section -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h4>إدارة المستخدمين</h4>
        </div>
        <div class="card-body">
            <form method="post" class="mb-4">
                {% csrf_token %}
                <div class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">اسم المستخدم</label>
                        {{ user_form.username }}
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">كلمة المرور</label>
                        {{ user_form.password1 }}
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">تأكيد كلمة المرور</label>
                        {{ user_form.password2 }}
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">الولاية</label>
                        {{ user_form.wilaya }}
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">الدور</label>
                        {{ user_form.role }}
                    </div>
                    <div class="col-12">
                        <button type="submit" name="create_user" class="btn btn-success">
                            <i class="fas fa-user-plus"></i> إنشاء مستخدم
                        </button>
                    </div>
                </div>
            </form>

            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>اسم المستخدم</th>
                            <th>الولاية</th>
                            <th>الدور</th>
                            <th>الوحدات المسؤول عنها</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr>
                            <td>{{ user.username }}</td>
                            <td>{{ user.userprofile.get_wilaya_display }}</td>
                            <td>{{ user.userprofile.get_role_display }}</td>
                            <td>
                                {% for unit in user.userprofile.intervention_units.all %}
                                <span class="badge bg-secondary">{{ unit.name }}</span>
                                {% endfor %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Intervention Units Management Section -->
    <div class="card">
        <div class="card-header bg-info text-white">
            <h4>إدارة الوحدات المتدخلة</h4>
        </div>
        <div class="card-body">
            <!-- Wilaya Selection -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <label class="form-label">اختر الولاية</label>
                    <select id="wilayaSelect" class="form-select" name="wilaya">
                        {% for code, name in wilaya_choices %}
                        <option value="{{ code }}">{{ name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>

            <!-- Add New Unit Form -->
            <form method="post" class="mb-4" id="unitForm">
                {% csrf_token %}
                <div class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">كود الوحدة</label>
                        {{ unit_form.code }}
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">اسم الوحدة</label>
                        {{ unit_form.name }}
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">الولاية</label>
                        {{ unit_form.wilaya }}
                    </div>
                    <div class="col-12">
                        <button type="submit" name="create_unit" class="btn btn-primary">
                            <i class="fas fa-plus-circle"></i> إضافة وحدة
                        </button>
                    </div>
                </div>
            </form>

            <!-- Units Table -->
            <div class="table-responsive">
                <table class="table table-striped" id="unitsTable">
                    <thead>
                        <tr>
                            <th>الكود</th>
                            <th>الاسم</th>
                            <th>الولاية</th>
                            <th>النوع</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for unit in units %}
                        <tr data-wilaya="{{ unit.wilaya }}">
                            <td>{{ unit.code }}</td>
                            <td>{{ unit.name }}</td>
                            <td>{{ unit.get_wilaya_display }}</td>
                            <td>
                                {% if unit.is_default %}
                                <span class="badge bg-info">وحدة افتراضية</span>
                                {% else %}
                                <span class="badge bg-secondary">وحدة مخصصة</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if not unit.is_default %}
                                <button class="btn btn-danger btn-sm delete-unit" data-unit-id="{{ unit.id }}">
                                    <i class="fas fa-trash"></i> حذف
                                </button>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const wilayaSelect = document.getElementById('wilayaSelect');
    const unitsTable = document.getElementById('unitsTable');
    const unitForm = document.getElementById('unitForm');
    const wilayaInput = unitForm.querySelector('[name="wilaya"]');

    // Filter units based on selected wilaya
    function filterUnits() {
        const selectedWilaya = wilayaSelect.value;
        const rows = unitsTable.querySelectorAll('tbody tr');
        
        rows.forEach(row => {
            if (row.dataset.wilaya === selectedWilaya) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });

        // Update the form's wilaya input
        wilayaInput.value = selectedWilaya;
    }

    // Initialize with current selection
    filterUnits();

    // Listen for wilaya selection changes
    wilayaSelect.addEventListener('change', filterUnits);

    // Handle unit deletion
    document.querySelectorAll('.delete-unit').forEach(button => {
        button.addEventListener('click', async function(e) {
            if (confirm('هل أنت متأكد من حذف هذه الوحدة؟')) {
                const unitId = this.dataset.unitId;
                try {
                    const response = await fetch(`/delete-unit/${unitId}/`, {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        },
                    });
                    
                    if (response.ok) {
                        // Remove the row from the table
                        this.closest('tr').remove();
                    } else {
                        alert('حدث خطأ أثناء حذف الوحدة');
                    }
                } catch (error) {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء حذف الوحدة');
                }
            }
        });
    });

    // Auto-generate code when name is entered
    const nameInput = document.querySelector('[name="name"]');
    const codeInput = document.querySelector('[name="code"]');
    
    nameInput.addEventListener('input', function() {
        const wilayaCode = wilayaSelect.value;
        // Generate a code based on the wilaya and unit name
        const nameWords = this.value.trim().split(/\s+/);
        let code = `W${wilayaCode}-`;
        
        if (nameWords.length > 0) {
            // Take first letter of each word, up to 3 letters
            code += nameWords.slice(0, 3).map(word => word.charAt(0)).join('');
        }
        
        codeInput.value = code.toUpperCase();
    });
});
</script>
{% endblock %}
