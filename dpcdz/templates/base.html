{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحماية المدنية الجزائرية - نظام إدخال البيانات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="{% static 'css/button-override.css' %}?v=1.0">
    <style>
        /* إصلاح عام للأيقونات في جميع الصفحات */
        .fas, .far, .fab, .fal, .fad {
            font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "Font Awesome 6 Brands" !important;
            font-weight: 900 !important;
            display: inline-block !important;
            font-style: normal !important;
            font-variant: normal !important;
            text-rendering: auto !important;
            line-height: 1 !important;
        }

        .far {
            font-weight: 400 !important;
        }

        .fab {
            font-family: "Font Awesome 6 Brands" !important;
            font-weight: 400 !important;
        }

        /* تحسين عرض الأيقونات في القوائم */
        .sidebar-menu i, .nav i, .btn i {
            margin-left: 8px;
            font-size: 1.1rem;
        }

        /* إصلاح مشكلة عدم ظهور الأيقونات */
        i[class*="fa-"]:before {
            font-family: "Font Awesome 6 Free" !important;
            font-weight: 900 !important;
        }
        body {
            font-family: 'Amiri', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }

        .header {
            background-color: #fff;
            padding: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .header img {
            height: 60px;
        }

        .header h1 {
            font-size: 22px;
            font-weight: bold;
            margin: 0;
            color: #343a40;
        }

        .main-content {
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 30px;
        }

        .btn-home {
            font-size: 18px;
            margin-bottom: 15px;
            padding: 15px;
            text-align: center;
            width: 100%;
        }

        .footer {
            background-color: #343a40;
            color: #fff;
            padding: 15px 0;
            text-align: center;
            font-size: 14px;
        }

        label {
            font-size: 16px;
            font-weight: bold;
        }

        .form-title {
            font-size: 22px;
            margin-bottom: 20px;
            color: #343a40;
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 10px;
        }

        .action-buttons {
            margin-top: 30px;
        }

        .action-buttons .btn {
            margin-left: 10px;
            padding: 8px 20px;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <img src="https://www.interieur.gov.dz/images/protection-civile.png" alt="علم الجزائر" class="img-fluid">
                </div>
                <div class="col-md-6 text-center">
                    <h1>المديرية العامة للحماية المدنية</h1>
                </div>
                <div class="col-md-3 text-left">
                    <img src="https://www.interieur.gov.dz/images/protection-civile.png" alt="شعار الحماية المدنية" class="img-fluid sidebar-toggle" id="sidebar-toggle" style="cursor: pointer;">
                </div>
            </div>
        </div>
    </header>

    <!-- Sidebar -->
    <div id="sidebar-overlay" class="sidebar-overlay"></div>
    <div id="sidebar" class="sidebar">
        <span id="sidebar-close" class="sidebar-close">
            <i class="fas fa-times"></i>
        </span>

        <div class="sidebar-header">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-name">{{ request.user.username }}</div>
                <div class="user-role">
                    {% if request.user.is_superuser %}
                        مدير النظام
                    {% elif request.user.is_staff %}
                        مشرف
                    {% else %}
                        مستخدم عادي
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="sidebar-content">
            <ul class="sidebar-menu">
                <li>
                    <a href="{% url 'profile' %}">
                        <i class="fas fa-user-circle"></i>
                        الملف الشخصي
                    </a>
                </li>
                <li>
                    <a href="{% url 'home' %}">
                        <i class="fas fa-home"></i>
                        الصفحة الرئيسية
                    </a>
                </li>


                <li>
                    <a href="{% url 'fires' %}">
                        <i class="fas fa-fire"></i>
                        الحرائق
                    </a>
                </li>
                <li>
                    <a href="{% url 'misc_operations' %}">
                        <i class="fas fa-tools"></i>
                        عمليات مختلفة
                    </a>
                </li>
                <li>
                    <a href="{% url 'settings' %}">
                        <i class="fas fa-cog"></i>
                        الإعدادات
                    </a>
                </li>
                <li>
                    <a href="{% url 'logout' %}">
                        <i class="fas fa-sign-out-alt"></i>
                        تسجيل الخروج
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <div class="container">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}

        {% block content %}{% endblock %}
    </div>

    <footer class="footer mt-5">
        <div class="container">
            <p>حقوق النشر © 2025 جميع الحقوق محفوظة - تم التطوير بواسطة عبد الرزاق مختاري</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="{% static 'js/sidebar.js' %}"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
