{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحماية المدنية الجزائرية - لوحة تحكم جاهزية الوسائل</title>
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/home.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    
    <style>
        /* تصميم العنوان المحسن لصفحة جاهزية الوسائل */
        .header-section-enhanced {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 2.5rem;
            border-radius: 20px;
            margin-bottom: 2rem;
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
            position: relative;
            overflow: hidden;
        }

        .header-section-enhanced::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.1;
        }

        .header-content {
            display: flex;
            align-items: center;
            gap: 2rem;
            position: relative;
            z-index: 1;
        }

        .header-icon {
            font-size: 4rem;
            opacity: 0.9;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header-text {
            flex: 1;
        }

        .header-title {
            font-size: 2.2rem;
            font-weight: 700;
            margin: 0 0 0.5rem 0;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin: 0 0 1rem 0;
            line-height: 1.4;
        }

        .header-badge .badge {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.9rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .section-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid #e3f2fd;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #f5f5f5;
        }

        .section-header h3 {
            color: #0d47a1;
            font-size: 1.4rem;
            font-weight: 700;
            margin: 0;
        }

        .section-header h3 i {
            margin-left: 0.5rem;
            color: #1976d2;
        }

        /* كروت الإحصائيات المحسنة - في صف واحد */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 1rem;
            margin-bottom: 2rem;
        }

        @media (max-width: 1200px) {
            .stats-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 480px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid #e3f2fd;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #007bff 0%, #6610f2 50%, #e83e8c 100%);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            opacity: 0.8;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .stat-label {
            color: #666;
            font-size: 1rem;
            font-weight: 500;
        }

        /* ألوان مخصصة للكروت */
        .stat-card:nth-child(1)::before {
            background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);
        }

        .stat-card:nth-child(2)::before {
            background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
        }

        .stat-card:nth-child(3)::before {
            background: linear-gradient(90deg, #ffc107 0%, #fd7e14 100%);
        }

        .stat-card:nth-child(4)::before {
            background: linear-gradient(90deg, #dc3545 0%, #c82333 100%);
        }

        .stat-card:nth-child(5)::before {
            background: linear-gradient(90deg, #6f42c1 0%, #5a32a3 100%);
        }

        .readiness-progress {
            background: #f5f5f5;
            border-radius: 10px;
            height: 20px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .progress-bar {
            height: 100%;
            border-radius: 10px;
            transition: width 0.5s ease;
        }

        .unit-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border: 1px solid #e9ecef;
        }

        .unit-name {
            font-weight: 600;
            color: #0d47a1;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .unit-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .unit-percentage {
            font-size: 1.5rem;
            font-weight: 700;
        }

        /* أزرار التحكم المحسنة */
        .control-buttons-enhanced {
            display: flex;
            justify-content: center;
            gap: 1.5rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .action-btn-enhanced {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 15px 25px;
            border: none;
            border-radius: 12px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            min-width: 200px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .action-btn-enhanced::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .action-btn-enhanced:hover::before {
            left: 100%;
        }

        .action-btn-enhanced:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            color: white;
            text-decoration: none;
        }

        .btn-crew-assignment {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .btn-unit-count {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        }

        .btn-interventions {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        }

        .action-btn-enhanced .btn-icon {
            font-size: 1.3rem;
            opacity: 0.9;
        }

        .action-btn-enhanced .btn-text {
            font-weight: 600;
            font-size: 0.95rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .btn-success {
            background-color: #28a745;
            color: white;
        }

        .btn-success:hover {
            background-color: #218838;
            transform: translateY(-2px);
        }

        .btn-info {
            background-color: #17a2b8;
            color: white;
        }

        .btn-info:hover {
            background-color: #138496;
            transform: translateY(-2px);
        }

        .btn-primary {
            background-color: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
        }

        .btn-outline-primary {
            background-color: transparent;
            color: #007bff;
            border: 2px solid #007bff;
        }

        .btn-outline-primary:hover {
            background-color: #007bff;
            color: white;
        }

        .btn-outline-secondary {
            background-color: transparent;
            color: #6c757d;
            border: 2px solid #6c757d;
        }

        .btn-outline-secondary:hover {
            background-color: #6c757d;
            color: white;
        }

        .text-success { color: #28a745 !important; }
        .text-warning { color: #ffc107 !important; }
        .text-danger { color: #dc3545 !important; }
        .text-primary { color: #007bff !important; }

        .bg-success { background-color: #28a745 !important; }
        .bg-warning { background-color: #ffc107 !important; }
        .bg-danger { background-color: #dc3545 !important; }
        .bg-primary { background-color: #007bff !important; }

        .alert {
            padding: 1rem 1.5rem;
            border-radius: 8px;
            border: none;
            margin-bottom: 1rem;
        }

        .alert-info {
            background-color: #d1ecf1;
            color: #0c5460;
        }

        /* الأزرار العائمة */
        .floating-buttons {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .floating-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            transition: all 0.3s;
            text-decoration: none;
            font-size: 18px;
            border: none;
            cursor: pointer;
        }

        .floating-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
            color: white;
            text-decoration: none;
        }

        .coordination-btn {
            background-color: #28a745;
        }

        .unified-btn {
            background-color: #ffc107;
        }

        .assignment-btn {
            background-color: #17a2b8;
        }

        .interventions-btn {
            background-color: #e74c3c;
        }

        .home-btn {
            background-color: #0d47a1;
        }

        .top-btn {
            background-color: #0d6efd;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle" id="sidebar-toggle" style="cursor: pointer;">
            </div>
        </div>
    </header>

    {% include 'includes/sidebar.html' %}

    <main>
        <div class="home-container">
            {% csrf_token %}
            <!-- العنوان الرئيسي المحسن -->
            <div class="header-section-enhanced">
                <div class="header-content">
                    <div class="header-icon">
                        <i class="fas fa-truck"></i>
                    </div>
                    <div class="header-text">
                        <h1 class="header-title">لوحة تحكم جاهزية الوسائل</h1>
                        <p class="header-subtitle">مراقبة وإدارة جاهزية الوسائل والعتاد في الوقت الفعلي</p>
                        <div class="header-badge">
                            <span class="badge badge-info">
                                <i class="fas fa-calendar"></i> {{ today|date:"l, d F Y" }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أزرار التحكم المحسنة -->
            <div class="control-buttons-enhanced">
                <a href="{% url 'vehicle_crew_assignment' %}" class="action-btn-enhanced btn-crew-assignment">
                    <div class="btn-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="btn-text">
                        توزيع الأعوان على الوسائل
                    </div>
                </a>

                <a href="{% url 'daily_unit_count' %}" class="action-btn-enhanced btn-unit-count">
                    <div class="btn-icon">
                        <i class="fas fa-clipboard-list"></i>
                    </div>
                    <div class="btn-text">
                        التعداد الصباحي للوحدة
                    </div>
                </a>

                <a href="{% url 'daily_interventions' %}" class="action-btn-enhanced btn-interventions">
                    <div class="btn-icon">
                        <i class="fas fa-ambulance"></i>
                    </div>
                    <div class="btn-text">
                        التدخلات اليومية
                    </div>
                </a>
            </div>

            <!-- إحصائيات الجاهزية -->
            <div class="section-card">
                <div class="section-header">
                    <h3><i class="fas fa-chart-pie"></i> إحصائيات الجاهزية</h3>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon text-primary">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div class="stat-number text-primary">{{ total_vehicles }}</div>
                        <div class="stat-label">إجمالي الوسائل</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon text-success">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-number text-success">{{ ready_vehicles }}</div>
                        <div class="stat-label">وسائل جاهزة</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon text-warning">
                            <i class="fas fa-hand-paper"></i>
                        </div>
                        <div class="stat-number text-warning">{{ manually_confirmed }}</div>
                        <div class="stat-label">مؤكدة يدوياً</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon text-danger">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="stat-number text-danger">{{ not_ready_vehicles }}</div>
                        <div class="stat-label">غير جاهزة</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon text-info">
                            <i class="fas fa-ambulance"></i>
                        </div>
                        <div class="stat-number text-info" id="intervention-vehicles-count">0</div>
                        <div class="stat-label">في تدخل</div>
                    </div>
                </div>

                <!-- نسبة الجاهزية الإجمالية -->
                <div style="margin-top: 2rem;">
                    <h4 style="margin-bottom: 1rem; color: #0d47a1;">
                        <i class="fas fa-chart-pie" style="margin-left: 0.5rem;"></i>
                        نسبة الجاهزية الإجمالية
                    </h4>
                    <div style="display: flex; align-items: center; gap: 1rem;">
                        <div style="flex-grow: 1;">
                            <div class="readiness-progress">
                                <div class="progress-bar
                                    {% if readiness_percentage >= 80 %}bg-success
                                    {% elif readiness_percentage >= 60 %}bg-warning
                                    {% else %}bg-danger{% endif %}"
                                    style="width: {{ readiness_percentage }}%">
                                </div>
                            </div>
                        </div>
                        <div>
                            <span style="font-size: 2rem; font-weight: 700;
                                {% if readiness_percentage >= 80 %}color: #28a745;
                                {% elif readiness_percentage >= 60 %}color: #ffc107;
                                {% else %}color: #dc3545;{% endif %}">
                                {{ readiness_percentage }}%
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جاهزية الوحدات -->
            <div class="section-card">
                <div class="section-header">
                    <h3><i class="fas fa-building"></i> جاهزية الوحدات</h3>
                </div>

                {% for unit_data in units_readiness %}
                <div class="unit-card">
                    <div class="unit-name">{{ unit_data.unit.name }}</div>
                    <div class="unit-stats">
                        <div>
                            <small style="color: #666;">{{ unit_data.ready_vehicles }} من {{ unit_data.total_vehicles }} وسيلة جاهزة</small>
                        </div>
                        <div class="unit-percentage
                            {% if unit_data.percentage >= 80 %}text-success
                            {% elif unit_data.percentage >= 60 %}text-warning
                            {% else %}text-danger{% endif %}">
                            {{ unit_data.percentage }}%
                        </div>
                    </div>
                    <div class="readiness-progress">
                        <div class="progress-bar
                            {% if unit_data.percentage >= 80 %}bg-success
                            {% elif unit_data.percentage >= 60 %}bg-warning
                            {% else %}bg-danger{% endif %}"
                            style="width: {{ unit_data.percentage }}%">
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle" style="margin-left: 0.5rem;"></i>
                    لا توجد بيانات وحدات متاحة لليوم الحالي
                </div>
                {% endfor %}
            </div>

            <!-- الأزرار العائمة -->
            <div class="floating-buttons">
                <a href="{% url 'coordination_center' %}" class="floating-btn coordination-btn" title="مركز التنسيق">
                    <i class="fas fa-building"></i>
                </a>
                <a href="{% url 'unified_morning_check' %}" class="floating-btn unified-btn" title="الصفحة الموحدة">
                    <i class="fas fa-sun"></i>
                </a>
                <a href="{% url 'vehicle_crew_assignment' %}" class="floating-btn assignment-btn" title="توزيع الأعوان">
                    <i class="fas fa-users"></i>
                </a>
                <a href="{% url 'daily_interventions' %}" class="floating-btn interventions-btn" title="التدخلات اليومية">
                    <i class="fas fa-ambulance"></i>
                </a>
                <a href="{% url 'home' %}" class="floating-btn home-btn" title="الصفحة الرئيسية">
                    <i class="fas fa-home"></i>
                </a>
                <a href="#" id="back-to-top" class="floating-btn top-btn" title="العودة إلى الأعلى">
                    <i class="fas fa-arrow-up"></i>
                </a>
            </div>
        </div>
    </main>

    <script src="{% static 'js/sidebar.js' %}"></script>
    <script>
        // Auto refresh every 5 minutes
        setTimeout(function() {
            location.reload();
        }, 300000);

        // Add loading animation to buttons
        document.querySelectorAll('.btn').forEach(function(btn) {
            btn.addEventListener('click', function() {
                if (this.href) {
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
                    this.style.pointerEvents = 'none';

                    // Re-enable after 3 seconds if still on page
                    setTimeout(function() {
                        btn.innerHTML = originalText;
                        btn.style.pointerEvents = 'auto';
                    }, 3000);
                }
            });
        });

        // ========================================
        // التزامن مع صفحة التدخلات اليومية
        // ========================================

        // استقبال رسائل التزامن من صفحة التدخلات
        window.addEventListener('message', function(event) {
            console.log('تم استقبال رسالة تزامن في صفحة الجاهزية:', event.data);

            if (event.data.type === 'vehicle_in_intervention') {
                // تحديث حالة الوسيلة إلى "في تدخل"
                const vehicleId = event.data.vehicleId;
                const interventionId = event.data.interventionId;

                updateVehicleInterventionDisplay(vehicleId, 'في تدخل', interventionId);
                incrementInterventionCount();

            } else if (event.data.type === 'intervention_completed') {
                // تحديث حالة الوسيلة إلى "متاحة"
                const interventionId = event.data.interventionId;

                updateVehiclesFromCompletedIntervention(interventionId);
                decrementInterventionCount();

            } else if (event.data.type === 'add_intervention_container') {
                // إضافة حاوية جديدة للتدخل المكتمل
                addInterventionContainer(event.data.interventionData);
            }
        });

        function updateVehicleInterventionDisplay(vehicleId, status, interventionId) {
            // البحث عن الوسيلة في الصفحة وتحديث عرضها
            const vehicleElements = document.querySelectorAll(`[data-vehicle-id="${vehicleId}"]`);

            vehicleElements.forEach(element => {
                if (status === 'في تدخل') {
                    element.classList.add('intervention-active');
                    element.style.border = '2px solid #dc3545';
                    element.style.backgroundColor = '#fff5f5';

                    // إضافة شارة "في تدخل"
                    const badge = document.createElement('div');
                    badge.className = 'intervention-badge';
                    badge.innerHTML = '<i class="fas fa-ambulance"></i> في تدخل';
                    badge.style.cssText = `
                        position: absolute;
                        top: 10px;
                        right: 10px;
                        background: #dc3545;
                        color: white;
                        padding: 5px 10px;
                        border-radius: 15px;
                        font-size: 0.8rem;
                        z-index: 10;
                    `;
                    element.style.position = 'relative';
                    element.appendChild(badge);
                } else {
                    element.classList.remove('intervention-active');
                    element.style.border = '';
                    element.style.backgroundColor = '';

                    // إزالة شارة "في تدخل"
                    const badge = element.querySelector('.intervention-badge');
                    if (badge) {
                        badge.remove();
                    }
                }
            });
        }

        function incrementInterventionCount() {
            const countElement = document.getElementById('intervention-vehicles-count');
            if (countElement) {
                const currentCount = parseInt(countElement.textContent) || 0;
                countElement.textContent = currentCount + 1;
            }
        }

        function decrementInterventionCount() {
            const countElement = document.getElementById('intervention-vehicles-count');
            if (countElement) {
                const currentCount = parseInt(countElement.textContent) || 0;
                countElement.textContent = Math.max(0, currentCount - 1);
            }
        }

        function updateVehiclesFromCompletedIntervention(interventionId) {
            // البحث عن جميع الوسائل في هذا التدخل وتحديث حالتها
            document.querySelectorAll('.intervention-active').forEach(element => {
                updateVehicleInterventionDisplay(element.dataset.vehicleId, 'متاحة', null);
            });
        }

        function addInterventionContainer(interventionData) {
            // إضافة حاوية جديدة تُظهر التدخل المكتمل
            const container = document.createElement('div');
            container.className = 'intervention-completed-container';
            container.innerHTML = `
                <div class="alert alert-success" style="margin: 1rem 0;">
                    <h5><i class="fas fa-check-circle"></i> تدخل مكتمل</h5>
                    <p><strong>رقم التدخل:</strong> ${interventionData.intervention_number}</p>
                    <p><strong>النوع:</strong> ${interventionData.intervention_type}</p>
                    <p><strong>الموقع:</strong> ${interventionData.location}</p>
                    <p><strong>الوقت:</strong> ${new Date().toLocaleTimeString('ar-DZ')}</p>
                </div>
            `;

            // إضافة الحاوية في أعلى الصفحة
            const mainContent = document.querySelector('main');
            if (mainContent) {
                mainContent.insertBefore(container, mainContent.firstChild);

                // إزالة الحاوية بعد 10 ثوانٍ
                setTimeout(() => {
                    container.remove();
                }, 10000);
            }
        }

        // تحميل عدد الوسائل في التدخل عند تحميل الصفحة
        function loadInterventionVehiclesCount() {
            // يمكن إضافة استدعاء API للحصول على العدد الحالي
            fetch('/api/interventions/get-intervention-vehicles-count/')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('intervention-vehicles-count').textContent = data.count;
                    }
                })
                .catch(error => {
                    console.error('خطأ في تحميل عدد الوسائل في التدخل:', error);
                });
        }

        // تحميل العدد عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadInterventionVehiclesCount();
        });

        // وظيفة العودة إلى الأعلى
        document.getElementById('back-to-top').addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    </script>
</body>
</html>
