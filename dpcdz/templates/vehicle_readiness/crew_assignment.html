{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحماية المدنية الجزائرية - توزيع الأعوان على الوسائل</title>
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/home.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    
    <style>
        /* تصميم العنوان المحسن */
        .header-section-enhanced {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2.5rem;
            border-radius: 20px;
            margin-bottom: 2rem;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .header-section-enhanced::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.1;
        }

        .header-content {
            display: flex;
            align-items: center;
            gap: 2rem;
            margin-bottom: 2rem;
            position: relative;
            z-index: 1;
        }

        .header-icon {
            font-size: 4rem;
            opacity: 0.9;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header-text {
            flex: 1;
        }

        .header-title {
            font-size: 2.2rem;
            font-weight: 700;
            margin: 0 0 0.5rem 0;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin: 0 0 1rem 0;
            line-height: 1.4;
        }

        .header-badge .badge {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.9rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .header-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            position: relative;
            z-index: 1;
        }

        .action-btn-enhanced {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 15px 20px;
            background: rgba(255, 255, 255, 0.15);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            min-width: 140px;
        }

        .action-btn-enhanced:hover {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
            color: white;
            text-decoration: none;
        }

        .action-btn-enhanced .btn-icon {
            font-size: 1.5rem;
            opacity: 0.9;
        }

        .action-btn-enhanced .btn-text span {
            font-weight: 600;
            font-size: 0.95rem;
        }

        .action-btn-enhanced .btn-text small {
            opacity: 0.8;
            font-size: 0.75rem;
            display: block;
            margin-top: 2px;
        }

        /* تصميم الفلاتر المحسن */
        .filters-section-enhanced {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid #e3f2fd;
        }

        .filters-header {
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #f5f5f5;
        }

        .filters-header h3 {
            color: #0d47a1;
            font-size: 1.4rem;
            font-weight: 700;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .filters-form-enhanced {
            display: flex;
            gap: 2rem;
            align-items: end;
            flex-wrap: wrap;
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 12px;
            border: 1px solid #e9ecef;
        }

        .filter-group-enhanced {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            min-width: 200px;
            flex: 1;
        }

        .filter-group-enhanced label {
            font-weight: 600;
            color: #495057;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-control-enhanced {
            padding: 0.75rem 1rem;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-control-enhanced:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .filter-actions-enhanced {
            display: flex;
            gap: 0.5rem;
            align-items: end;
        }

        .btn-filter-apply {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-filter-apply:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }

        .btn-filter-reset {
            background: white;
            color: #6c757d;
            border: 2px solid #dee2e6;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-filter-reset:hover {
            border-color: #dc3545;
            color: #dc3545;
            transform: translateY(-1px);
        }

        /* تصميم كروت الوسائل المحسن */
        .vehicle-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid #e3f2fd;
            transition: all 0.3s ease;
            height: fit-content;
            position: relative;
            overflow: hidden;
        }

        .vehicle-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background: linear-gradient(180deg, #007bff 0%, #0056b3 100%);
            transition: width 0.3s ease;
        }

        .vehicle-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .vehicle-card:hover::before {
            width: 8px;
        }

        /* ألوان مميزة لأنواع الوسائل مع تدرجات */
        .vehicle-card[data-vehicle-type*="ambulance"]::before {
            background: linear-gradient(180deg, #dc3545 0%, #c82333 100%);
        }

        .vehicle-card[data-vehicle-type*="camion"]::before {
            background: linear-gradient(180deg, #fd7e14 0%, #e8690b 100%);
        }

        .vehicle-card[data-vehicle-type*="fourgon"]::before {
            background: linear-gradient(180deg, #6f42c1 0%, #5a32a3 100%);
        }

        .vehicle-card[data-vehicle-type*="vehicule"]::before {
            background: linear-gradient(180deg, #20c997 0%, #1aa179 100%);
        }

        /* حالات الجاهزية مع ألوان مميزة */
        .vehicle-card.ready {
            background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
            border-color: #28a745;
        }

        .vehicle-card.manually-confirmed {
            background: linear-gradient(135deg, #fff8e1 0%, #f3e5ab 100%);
            border-color: #ffc107;
        }

        .vehicle-card.not-ready {
            background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
            border-color: #dc3545;
        }

        .text-purple {
            color: #6f42c1 !important;
        }

        .vehicle-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid #f8f9fa;
        }

        /* تخطيط شبكي للوسائل مع حاوية قابلة للتمرير محسنة */
        .vehicles-container {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 1.5rem;
            max-height: 750px;
            overflow-y: auto;
            border: 2px solid #dee2e6;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .vehicles-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #007bff 0%, #6610f2 50%, #e83e8c 100%);
            border-radius: 15px 15px 0 0;
        }

        .vehicles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 1.5rem;
            margin-top: 1rem;
        }

        /* تخطيط رئيسي محسن - الأعوان خلف الوسائل */
        .main-layout {
            display: grid;
            grid-template-columns: 380px 1fr;
            gap: 2rem;
            align-items: start;
            min-height: 600px;
        }

        /* تحسين حاوية الأعوان - أكبر للسحب والإفلات */
        .personnel-container {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 1.5rem;
            border: 2px solid #dee2e6;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
            height: 750px;
            display: flex;
            flex-direction: column;
            position: sticky;
            top: 20px;
            position: relative;
        }

        .personnel-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #28a745 0%, #20c997 50%, #17a2b8 100%);
            border-radius: 15px 15px 0 0;
        }

        .personnel-list {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid #e3f2fd;
            max-height: 550px;
            overflow-y: auto;
            flex: 1;
            margin-top: 1rem;
        }

        /* تحسين شريط التمرير */
        .vehicles-container::-webkit-scrollbar,
        .personnel-list::-webkit-scrollbar {
            width: 8px;
        }

        .vehicles-container::-webkit-scrollbar-track,
        .personnel-list::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .vehicles-container::-webkit-scrollbar-thumb,
        .personnel-list::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .vehicles-container::-webkit-scrollbar-thumb:hover,
        .personnel-list::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* تحسين حجم الحاوية الرئيسية للأجهزة الكبيرة */
        .home-container {
            max-width: 1600px !important;
            margin: 0 auto;
            padding: 20px;
        }

        /* للشاشات الكبيرة (أجهزة سطح المكتب) */
        @media (min-width: 1400px) {
            .home-container {
                max-width: 1800px !important;
                padding: 30px;
            }
            .main-layout {
                grid-template-columns: 400px 1fr;
                gap: 2rem;
            }
            .vehicles-container,
            .personnel-container {
                max-height: 800px;
                height: 800px;
            }
        }

        /* للأجهزة اللوحية والشاشات المتوسطة */
        @media (min-width: 768px) and (max-width: 1399px) {
            .home-container {
                max-width: 1400px !important;
                padding: 25px;
            }
            .main-layout {
                grid-template-columns: 380px 1fr;
                gap: 1.5rem;
            }
            .vehicles-container,
            .personnel-container {
                max-height: 750px;
                height: 750px;
            }
        }

        /* تصميم متجاوب محسن */
        @media (max-width: 1200px) {
            .main-layout {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .vehicles-grid {
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            }

            .vehicles-container,
            .personnel-container {
                max-height: 500px;
                height: auto;
            }

            .header-content {
                flex-direction: column;
                text-align: center;
                gap: 1rem;
            }

            .header-actions {
                flex-direction: column;
                align-items: center;
            }

            .action-btn-enhanced {
                min-width: 200px;
            }
        }

        @media (max-width: 768px) {
            .vehicles-grid {
                grid-template-columns: 1fr;
            }

            .main-layout {
                gap: 1rem;
            }

            .vehicles-container,
            .personnel-container {
                max-height: 400px;
            }

            .home-container {
                max-width: 100% !important;
                padding: 15px;
            }

            .header-section-enhanced {
                padding: 1.5rem;
            }

            .header-title {
                font-size: 1.8rem;
            }

            .header-subtitle {
                font-size: 1rem;
            }

            .filters-form-enhanced {
                flex-direction: column;
                gap: 1rem;
            }

            .filter-group-enhanced {
                min-width: 100%;
            }

            .filter-actions-enhanced {
                justify-content: center;
                width: 100%;
            }

            .filter-actions-enhanced button {
                flex: 1;
                max-width: 150px;
            }
        }

        .vehicle-title {
            font-weight: 600;
            color: #0d47a1;
            margin-bottom: 0;
            font-size: 1rem;
        }

        .readiness-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-weight: 500;
            font-size: 0.8rem;
        }

        .readiness-ready {
            background-color: #d4edda;
            color: #155724;
        }

        .readiness-manual {
            background-color: #fff3cd;
            color: #856404;
        }

        .readiness-not-ready {
            background-color: #f8d7da;
            color: #721c24;
        }

        .crew-slot {
            background: #f8f9fa;
            border: 1px dashed #dee2e6;
            border-radius: 6px;
            padding: 0.5rem;
            margin-bottom: 0.25rem;
            min-height: 50px;
            display: flex;
            flex-direction: column;
            align-items: stretch;
            justify-content: flex-start;
            transition: all 0.2s ease;
        }

        .crew-slot.occupied {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-style: solid;
        }

        .crew-slot.drop-zone {
            background: #e8f5e8;
            border-color: #4caf50;
        }

        .crew-member {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 0.5rem;
            width: 100%;
            cursor: grab;
            transition: all 0.2s ease;
            margin-bottom: 0.25rem;
        }

        .crew-member:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
        }

        .crew-member.dragging {
            opacity: 0.5;
            transform: rotate(5deg);
        }

        /* ترتيب الوسائل: غير الجاهزة في الأعلى، الجاهزة في الأسفل */
        .vehicle-card.not-ready {
            order: 1;
            border-left: 6px solid #dc3545;
            background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);
            position: relative;
        }

        .vehicle-card.not-ready::before {
            content: "⚠️ غير جاهز";
            position: absolute;
            top: 10px;
            right: 10px;
            background: #dc3545;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: bold;
            z-index: 10;
        }

        .vehicle-card.ready {
            order: 2;
            border-left: 6px solid #28a745;
            background: linear-gradient(135deg, #f0fff4 0%, #ffffff 100%);
            position: relative;
        }

        .vehicle-card.ready::before {
            content: "✅ جاهز";
            position: absolute;
            top: 10px;
            right: 10px;
            background: #28a745;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: bold;
            z-index: 10;
        }

        .vehicle-card.manually-confirmed {
            order: 2;
            border-left: 6px solid #ffc107;
            background: linear-gradient(135deg, #fffbf0 0%, #ffffff 100%);
            position: relative;
        }

        .vehicle-card.manually-confirmed::before {
            content: "👋 مؤكد يدوياً";
            position: absolute;
            top: 10px;
            right: 10px;
            background: #ffc107;
            color: #212529;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: bold;
            z-index: 10;
        }

        /* تحسين مظهر بطاقات الوسائل */
        .vehicle-card {
            transition: all 0.3s ease;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .vehicle-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .personnel-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            cursor: grab;
            transition: all 0.2s ease;
        }

        .personnel-item:hover {
            background: #e9ecef;
            transform: translateX(-5px);
        }

        .personnel-item.dragging {
            opacity: 0.5;
        }

        .role-label {
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            margin-bottom: 0.25rem;
            display: block;
            color: #495057;
        }

        .role-driver { color: #dc3545; }
        .role-crew-chief { color: #fd7e14; }
        .role-agent { color: #198754; }
        .role-specialist { color: #6f42c1; }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .btn-primary {
            background-color: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
        }

        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }

        .btn-warning:hover {
            background-color: #e0a800;
            transform: translateY(-2px);
        }

        .btn-outline-danger {
            background-color: transparent;
            color: #dc3545;
            border: 1px solid #dc3545;
        }

        .btn-outline-danger:hover {
            background-color: #dc3545;
            color: white;
        }

        .missing-roles {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 0.75rem;
            margin-top: 1rem;
        }

        .missing-roles ul {
            margin-bottom: 0;
            padding-right: 1rem;
        }

        .alert {
            padding: 1rem 1.5rem;
            border-radius: 8px;
            border: none;
            margin-bottom: 1rem;
        }

        .alert-info {
            background-color: #d1ecf1;
            color: #0c5460;
        }

        .badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .bg-secondary {
            background-color: #6c757d !important;
            color: white;
        }

        .text-muted {
            color: #6c757d !important;
        }

        .float-end {
            float: left !important;
        }

        .d-flex {
            display: flex;
        }

        .justify-content-between {
            justify-content: space-between;
        }

        .align-items-start {
            align-items: flex-start;
        }

        .grid-layout {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
        }

        @media (max-width: 768px) {
            .grid-layout {
                grid-template-columns: 1fr;
            }

            .filter-form {
                grid-template-columns: 1fr;
            }
        }

        /* رسائل التنبيه */
        .messages-container {
            margin-bottom: 1rem;
        }

        .alert {
            border-radius: 8px;
            border: 1px solid;
            padding: 12px 16px;
            margin-bottom: 10px;
        }

        .alert-warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }

        .alert-info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }

        .alert-link {
            color: inherit;
            font-weight: bold;
            text-decoration: none;
        }

        .alert-link:hover {
            text-decoration: underline;
        }

        /* تحسين منطقة الإفلات */
        .drop-hint {
            padding: 10px;
            border: 2px dashed #ddd;
            border-radius: 5px;
            text-align: center;
            margin-top: 5px;
            background-color: #f8f9fa;
        }

        .agents-area {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .crew-slot.drop-zone {
            border-color: #007bff !important;
            background-color: #e3f2fd !important;
        }

        /* أزرار التعيين السريع */
        .quick-assign-buttons {
            margin-left: 10px;
        }

        .btn-xs {
            padding: 0.125rem 0.25rem;
            font-size: 0.75rem;
            line-height: 1.2;
            border-radius: 0.2rem;
        }

        .quick-assign-section {
            border: 2px solid #28a745;
            border-radius: 8px;
        }

        .personnel-item {
            transition: all 0.2s ease;
        }

        .personnel-item:hover {
            background-color: #f8f9fa;
            transform: translateY(-1px);
        }

        /* رسوم متحركة للإشعارات */
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        .notification-toast {
            animation: slideInRight 0.3s ease;
        }

        /* تأثيرات إضافية للكروت */
        .vehicle-card {
            animation: fadeInUp 0.5s ease;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* تحسين أزرار الجاهزية */
        .readiness-badge {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .readiness-badge:hover {
            transform: scale(1.05);
        }

        /* تحسين عناصر الأعوان */
        .personnel-item {
            transition: all 0.3s ease;
            border-radius: 8px;
        }

        .personnel-item:hover {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        /* الأزرار العائمة */
        .floating-buttons {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .floating-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            transition: all 0.3s;
            text-decoration: none;
            font-size: 18px;
            border: none;
            cursor: pointer;
        }

        .floating-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
            color: white;
            text-decoration: none;
        }

        .coordination-btn {
            background-color: #28a745;
        }

        .unified-btn {
            background-color: #ffc107;
        }

        .readiness-btn {
            background-color: #dc3545;
        }

        .interventions-btn {
            background-color: #e74c3c;
        }

        .home-btn {
            background-color: #0d47a1;
        }

        .top-btn {
            background-color: #0d6efd;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle" id="sidebar-toggle" style="cursor: pointer;">
            </div>
        </div>
    </header>

    {% include 'includes/sidebar.html' %}

    <main>
        <div class="home-container">
            {% csrf_token %}
            <!-- العنوان الرئيسي المحسن -->
            <div class="header-section-enhanced">
                <div class="header-content">
                    <div class="header-icon">
                        <i class="fas fa-users-cog"></i>
                    </div>
                    <div class="header-text">
                        <h1 class="header-title">توزيع الأعوان على الوسائل</h1>
                        <p class="header-subtitle">إدارة وتوزيع الأعوان على الوسائل المختلفة - {{ assignment_date|date:"d/m/Y" }}</p>
                        <div class="header-badge">
                            <span class="badge badge-info">
                                <i class="fas fa-calendar"></i> {{ assignment_date|date:"l, d F Y" }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- أزرار التنقل المحسنة -->
                <div class="header-actions">
                    <button class="action-btn-enhanced btn-unified" onclick="openUnifiedPage()">
                        <div class="btn-icon">
                            <i class="fas fa-external-link-alt"></i>
                        </div>
                        <div class="btn-text">
                            <span>الصفحة الموحدة</span>
                            <small>إدارة شاملة</small>
                        </div>
                    </button>

                    <a href="{% url 'vehicle_readiness_dashboard' %}" class="action-btn-enhanced btn-dashboard">
                        <div class="btn-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <div class="btn-text">
                            <span>لوحة التحكم</span>
                            <small>جاهزية الوسائل</small>
                        </div>
                    </a>

                    <button class="action-btn-enhanced btn-refresh" onclick="location.reload()">
                        <div class="btn-icon">
                            <i class="fas fa-sync-alt"></i>
                        </div>
                        <div class="btn-text">
                            <span>تحديث</span>
                            <small>إعادة تحميل</small>
                        </div>
                    </button>
                </div>
            </div>

            <!-- فلاتر البحث المحسنة -->
            <div class="filters-section-enhanced">
                <div class="filters-header">
                    <h3><i class="fas fa-filter"></i> فلاتر البحث والتحكم</h3>
                </div>

                <form method="GET" class="filters-form-enhanced">
                    <div class="filter-group-enhanced">
                        <label for="unit">
                            <i class="fas fa-building"></i>
                            الوحدة
                        </label>
                        <select name="unit" id="unit" class="form-control-enhanced">
                            {% for unit in user_units %}
                            <option value="{{ unit.id }}" {% if unit.id == selected_unit.id %}selected{% endif %}>
                                {{ unit.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="filter-group-enhanced">
                        <label for="date">
                            <i class="fas fa-calendar"></i>
                            التاريخ
                        </label>
                        <input type="date" name="date" id="date" value="{{ assignment_date|date:'Y-m-d' }}" class="form-control-enhanced">
                    </div>

                    <div class="filter-actions-enhanced">
                        <button type="submit" class="btn-filter-apply">
                            <i class="fas fa-search"></i>
                            تطبيق الفلاتر
                        </button>
                        <button type="button" class="btn-filter-reset" onclick="resetFilters()">
                            <i class="fas fa-times"></i>
                            إعادة تعيين
                        </button>
                    </div>
                </form>
            </div>

            <!-- رسائل التنبيه -->
            {% if messages %}
            <div class="messages-container">
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {% if message.tags == 'error' %}
                        <i class="fas fa-exclamation-circle me-2"></i>
                    {% elif message.tags == 'warning' %}
                        <i class="fas fa-exclamation-triangle me-2"></i>
                    {% elif message.tags == 'success' %}
                        <i class="fas fa-check-circle me-2"></i>
                    {% else %}
                        <i class="fas fa-info-circle me-2"></i>
                    {% endif %}
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                {% endfor %}
            </div>
            {% endif %}

            <!-- المحتوى الرئيسي -->
            <div class="main-layout">
                <!-- قسم الأعوان المتاحين - خلف الوسائل للسحب والإفلات السهل -->
                <div class="personnel-container">
                    <div class="section-header" style="margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 2px solid #dee2e6;">
                        <h4 style="margin: 0; font-size: 1.2rem; color: #495057;">
                            <i class="fas fa-user-friends"></i> الأعوان المتاحون
                            <small class="text-muted" style="font-size: 0.8rem;">(اسحب وأفلت أو استخدم الأزرار)</small>
                        </h4>
                    </div>

                    <div class="personnel-list">
                        {% if not personnel %}
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>لا يوجد أعوان مسجلون!</strong>
                            <p class="mb-2">لم يتم العثور على أي أعوان في التعداد الصباحي لتاريخ {{ assignment_date|date:"d/m/Y" }}.</p>
                            <p class="mb-0">
                                يرجى إضافة الأعوان أولاً من
                                <a href="{% url 'daily_unit_count' %}?unit_id={{ selected_unit.id }}" class="alert-link">
                                    <i class="fas fa-external-link-alt"></i> صفحة التعداد الصباحي
                                </a>
                            </p>
                        </div>
                        {% endif %}

                        {% for person in unassigned_personnel %}
                        <div class="personnel-item"
                             draggable="true"
                             data-personnel-id="{{ person.id }}"
                             data-job-function="{{ person.job_function }}">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="flex-grow-1">
                                    <div class="fw-bold" style="font-size: 0.9rem;">{{ person.full_name }}</div>
                                    <small class="text-muted">{{ person.rank }}</small>
                                    <span class="badge bg-secondary" style="font-size: 0.7rem;">{{ person.get_job_function_display }}</span>
                                </div>
                                <div class="quick-assign-buttons">
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary btn-sm" onclick="showVehicleSelector({{ person.id }}, 'driver')" title="سائق">
                                            <i class="fas fa-car"></i>
                                        </button>
                                        <button class="btn btn-outline-success btn-sm" onclick="showVehicleSelector({{ person.id }}, 'crew_chief')" title="رئيس">
                                            <i class="fas fa-user-tie"></i>
                                        </button>
                                        <button class="btn btn-outline-secondary btn-sm" onclick="showVehicleSelector({{ person.id }}, 'agent')" title="عون">
                                            <i class="fas fa-user"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% empty %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle" style="margin-left: 0.5rem;"></i>
                            جميع الأعوان معينون على الوسائل
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- قسم الوسائل -->
                <div>
                    <div class="section-header">
                        <h3><i class="fas fa-truck"></i> الوسائل المتاحة</h3>
                    </div>

                {% if not vehicle_assignments %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>لا توجد وسائل جاهزة للتوزيع!</strong>
                    <p class="mb-2">لم يتم العثور على أي وسائل جاهزة (حالة: جاهز) لتاريخ {{ assignment_date|date:"d/m/Y" }}.</p>

                    {% if debug_info %}
                    <div class="mb-2">
                        <strong>تفاصيل الفلترة:</strong>
                        <ul class="mb-2">
                            <li>إجمالي الوسائل: {{ debug_info.total_vehicles }}</li>
                            <li>الوسائل الجاهزة: {{ debug_info.ready_vehicles }}</li>
                            <li>الوسائل غير الجاهزة: {{ debug_info.non_ready_vehicles }}</li>
                        </ul>
                        {% if debug_info.non_ready_details %}
                        <strong>الوسائل غير الجاهزة:</strong>
                        <ul class="mb-2">
                            {% for item in debug_info.non_ready_details %}
                            <li>{{ item.vehicle.equipment_type }} - {{ item.vehicle.serial_number }}: {{ item.status_display }}</li>
                            {% endfor %}
                        </ul>
                        {% endif %}
                    </div>
                    {% endif %}

                    <p class="mb-2">
                        <strong>الأسباب المحتملة:</strong>
                    </p>
                    <ul class="mb-2">
                        <li>لا توجد وسائل مسجلة في الوحدة</li>
                        <li>جميع الوسائل في حالة "معطل" أو "في الصيانة"</li>
                        <li>لم يتم تحديث حالة الوسائل لهذا التاريخ</li>
                    </ul>
                    <p class="mb-0">
                        يرجى مراجعة وتحديث حالة الوسائل من
                        <a href="{% url 'unified_morning_check' %}?unit_id={{ selected_unit.id }}&date={{ assignment_date|date:'Y-m-d' }}" class="alert-link">
                            <i class="fas fa-external-link-alt"></i> صفحة التعداد الصباحي الموحد
                        </a>
                    </p>
                </div>
                {% endif %}

                <!-- حاوية الوسائل المتاحة مع التمرير -->
                <div class="vehicles-container">
                    <div class="section-header" style="margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 2px solid #dee2e6;">
                        <h4 style="margin: 0; font-size: 1.2rem; color: #495057;">
                            <i class="fas fa-truck"></i> الوسائل المتاحة
                            <small class="text-muted" style="font-size: 0.8rem;">(غير الجاهزة في الأعلى، الجاهزة في الأسفل)</small>
                        </h4>
                    </div>

                    <div class="vehicles-grid">
                    {% for vehicle_id, vehicle_data in vehicle_assignments.items %}
                    <div class="vehicle-card
                        {% if vehicle_data.readiness.status == 'ready' and vehicle_data.readiness.readiness_score > 0 %}ready
                        {% elif vehicle_data.readiness.status == 'manually_confirmed' %}manually-confirmed
                        {% else %}not-ready{% endif %}"
                        data-vehicle-id="{{ vehicle_id }}"
                        data-vehicle-type="{{ vehicle_data.vehicle.equipment_type|lower }}"
                        data-readiness-status="{{ vehicle_data.readiness.status }}"
                        data-readiness-score="{{ vehicle_data.readiness.readiness_score }}">
                    <div class="vehicle-header">
                        <div>
                            <h5 class="vehicle-title">
                                {% if 'ambulance' in vehicle_data.vehicle.equipment_type|lower %}
                                    <i class="fas fa-ambulance text-danger"></i>
                                {% elif 'camion' in vehicle_data.vehicle.equipment_type|lower %}
                                    <i class="fas fa-fire-extinguisher text-warning"></i>
                                {% elif 'fourgon' in vehicle_data.vehicle.equipment_type|lower %}
                                    <i class="fas fa-truck text-purple"></i>
                                {% else %}
                                    <i class="fas fa-car text-info"></i>
                                {% endif %}
                                {{ vehicle_data.vehicle.equipment_type }}
                            </h5>
                            <small class="text-muted">{{ vehicle_data.vehicle.serial_number }}</small>
                        </div>
                        <div class="d-flex flex-column align-items-end">
                            <div class="mb-1">
                                {% if vehicle_data.readiness.status == 'ready' and vehicle_data.readiness.readiness_score > 0 %}
                                    <span class="readiness-badge readiness-ready">
                                        <i class="fas fa-check-circle me-1"></i>
                                        جاهز ({{ vehicle_data.readiness.readiness_score }}%)
                                    </span>
                                {% elif vehicle_data.readiness.status == 'manually_confirmed' %}
                                    <span class="readiness-badge readiness-manual">
                                        <i class="fas fa-hand-paper me-1"></i>
                                        مؤكد يدوياً
                                    </span>
                                {% else %}
                                    <span class="readiness-badge readiness-not-ready">
                                        <i class="fas fa-times-circle me-1"></i>
                                        {% if vehicle_data.readiness.readiness_score == 0 %}
                                            غير جاهز - فارغ
                                        {% else %}
                                            غير جاهز ({{ vehicle_data.readiness.readiness_score }}%)
                                        {% endif %}
                                    </span>
                                {% endif %}
                            </div>
                            <small class="text-muted">
                                <i class="fas fa-users"></i> {{ vehicle_data.assignments.count }} أعوان
                            </small>
                        </div>
                    </div>
                    
                    <!-- طريقة سريعة للتعيين -->
                    <div class="quick-assign-section" style="margin-bottom: 0.75rem; padding: 0.75rem; background-color: #f8f9fa; border-radius: 6px;">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <small class="fw-bold text-muted"><i class="fas fa-bolt"></i> تعيين سريع</small>
                            {% if vehicle_data.assignments.count > 0 %}
                                <button class="btn btn-warning btn-sm" onclick="markVehicleReady({{ vehicle_id }})">
                                    <i class="fas fa-clock"></i> غير جاهز - انقر للتأكيد
                                </button>
                            {% else %}
                                <button class="btn btn-outline-secondary btn-sm" disabled title="يجب تعيين أعوان أولاً">
                                    <i class="fas fa-exclamation-triangle"></i> فارغ
                                </button>
                            {% endif %}
                        </div>
                        <div class="row g-2">
                            <div class="col-4">
                                <select class="form-select form-select-sm" onchange="assignQuick(this, 'driver', {{ vehicle_id }})" title="سائق">
                                    <option value="">سائق</option>
                                    {% for person in unassigned_personnel %}
                                    <option value="{{ person.id }}">{{ person.full_name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-4">
                                <select class="form-select form-select-sm" onchange="assignQuick(this, 'crew_chief', {{ vehicle_id }})" title="رئيس عدد">
                                    <option value="">رئيس عدد</option>
                                    {% for person in unassigned_personnel %}
                                    <option value="{{ person.id }}">{{ person.full_name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-4">
                                <select class="form-select form-select-sm" onchange="assignQuick(this, 'agent', {{ vehicle_id }})" title="عون">
                                    <option value="">عون</option>
                                    {% for person in unassigned_personnel %}
                                    <option value="{{ person.id }}">{{ person.full_name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- الأعوان المعينين -->
                    <div class="assigned-crew" style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 0.5rem; margin-bottom: 0.75rem;">
                        <!-- Driver Slot -->
                        <div>
                            <label class="role-label role-driver" style="font-size: 0.8rem; margin-bottom: 0.25rem;">سائق</label>
                            <div class="crew-slot" data-role="driver" data-vehicle="{{ vehicle_id }}" style="min-height: 60px; padding: 0.5rem;">
                                {% for assignment in vehicle_data.assignments %}
                                    {% if assignment.role == 'driver' %}
                                        <div class="crew-member" data-assignment-id="{{ assignment.id }}" style="padding: 0.5rem; margin-bottom: 0.25rem;">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <small class="fw-bold">{{ assignment.personnel.full_name }}</small><br>
                                                    <small class="text-muted" style="font-size: 0.7rem;">{{ assignment.personnel.rank }}</small>
                                                </div>
                                                <button class="btn btn-outline-danger btn-sm" style="padding: 0.1rem 0.3rem; font-size: 0.7rem;" onclick="removePersonnel({{ assignment.id }})">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                    {% endif %}
                                {% endfor %}
                                {% if not vehicle_data.assignments|length or not vehicle_data.assignments %}
                                    <small class="text-muted">اسحب سائق هنا</small>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Crew Chief Slot -->
                        <div>
                            <label class="role-label role-crew-chief" style="font-size: 0.8rem; margin-bottom: 0.25rem;">رئيس عدد</label>
                            <div class="crew-slot" data-role="crew_chief" data-vehicle="{{ vehicle_id }}" style="min-height: 60px; padding: 0.5rem;">
                                {% for assignment in vehicle_data.assignments %}
                                    {% if assignment.role == 'crew_chief' %}
                                        <div class="crew-member" data-assignment-id="{{ assignment.id }}" style="padding: 0.5rem; margin-bottom: 0.25rem;">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <small class="fw-bold">{{ assignment.personnel.full_name }}</small><br>
                                                    <small class="text-muted" style="font-size: 0.7rem;">{{ assignment.personnel.rank }}</small>
                                                </div>
                                                <button class="btn btn-outline-danger btn-sm" style="padding: 0.1rem 0.3rem; font-size: 0.7rem;" onclick="removePersonnel({{ assignment.id }})">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                    {% endif %}
                                {% endfor %}
                                <small class="text-muted">اسحب رئيس عدد هنا</small>
                            </div>
                        </div>

                        <!-- Agents Slot -->
                        <div>
                            <label class="role-label role-agent" style="font-size: 0.8rem; margin-bottom: 0.25rem;">الأعوان</label>
                            <div class="crew-slot agents-area" data-role="agent" data-vehicle="{{ vehicle_id }}" style="min-height: 60px; padding: 0.5rem;">
                                {% for assignment in vehicle_data.assignments %}
                                    {% if assignment.role == 'agent' %}
                                        <div class="crew-member" data-assignment-id="{{ assignment.id }}" style="padding: 0.5rem; margin-bottom: 0.25rem;">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <small class="fw-bold">{{ assignment.personnel.full_name }}</small><br>
                                                    <small class="text-muted" style="font-size: 0.7rem;">{{ assignment.personnel.rank }}</small>
                                                </div>
                                                <button class="btn btn-outline-danger btn-sm" style="padding: 0.1rem 0.3rem; font-size: 0.7rem;" onclick="removePersonnel({{ assignment.id }})">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                    {% endif %}
                                {% endfor %}
                                <small class="text-muted">اسحب الأعوان هنا</small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Missing Roles Alert -->
                    {% if vehicle_data.readiness.missing_roles %}
                    <div class="missing-roles">
                        <strong><i class="fas fa-exclamation-triangle me-2"></i>أدوار مفقودة:</strong>
                        <ul>
                            {% for role in vehicle_data.readiness.missing_roles %}
                            <li>{{ role }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}
                    
                            <!-- Manual Confirmation Button -->
                            {% if vehicle_data.readiness.status == 'not_ready' and user_profile.role in 'wilaya_manager,unit_manager' %}
                            <div style="margin-top: 1rem;">
                                <button class="btn btn-warning" onclick="confirmManually({{ vehicle_id }})">
                                    <i class="fas fa-hand-paper"></i>
                                    تأكيد الجاهزية يدوياً
                                </button>
                            </div>
                            {% endif %}
                        </div>
                        {% empty %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle" style="margin-left: 0.5rem;"></i>
                            لا توجد وسائل متاحة للوحدة المحددة في هذا التاريخ
                        </div>
                        {% endfor %}
                    </div>
                </div>


            </div>
            <!-- الأزرار العائمة -->
            <div class="floating-buttons">
                <a href="{% url 'coordination_center' %}" class="floating-btn coordination-btn" title="مركز التنسيق">
                    <i class="fas fa-building"></i>
                </a>
                <a href="{% url 'unified_morning_check' %}" class="floating-btn unified-btn" title="الصفحة الموحدة">
                    <i class="fas fa-sun"></i>
                </a>
                <a href="{% url 'vehicle_readiness_dashboard' %}" class="floating-btn readiness-btn" title="جاهزية الوسائل">
                    <i class="fas fa-truck"></i>
                </a>
                <a href="{% url 'daily_interventions' %}" class="floating-btn interventions-btn" title="التدخلات اليومية">
                    <i class="fas fa-ambulance"></i>
                </a>
                <a href="{% url 'home' %}" class="floating-btn home-btn" title="الصفحة الرئيسية">
                    <i class="fas fa-home"></i>
                </a>
                <a href="#" id="back-to-top" class="floating-btn top-btn" title="العودة إلى الأعلى">
                    <i class="fas fa-arrow-up"></i>
                </a>
            </div>
        </div>
    </main>

    <script src="{% static 'js/sidebar.js' %}"></script>
    
    <script>
        // CSRF Token
        const csrfToken = '{{ csrf_token }}';

        // متغير لتتبع نافذة الصفحة الموحدة
        let unifiedWindow = null;

        // دالة ترتيب الوسائل حسب الجاهزية
        function sortVehiclesByReadiness() {
            const vehiclesGrid = document.querySelector('.vehicles-grid');
            if (!vehiclesGrid) return;

            const vehicleCards = Array.from(vehiclesGrid.querySelectorAll('.vehicle-card'));

            // ترتيب الوسائل: غير الجاهزة أولاً، ثم الجاهزة
            vehicleCards.sort((a, b) => {
                const aStatus = a.dataset.readinessStatus;
                const aScore = parseInt(a.dataset.readinessScore) || 0;
                const bStatus = b.dataset.readinessStatus;
                const bScore = parseInt(b.dataset.readinessScore) || 0;

                // غير الجاهزة في الأعلى (أولوية 1)
                if ((aStatus === 'not_ready' || aScore === 0) && (bStatus === 'ready' || bStatus === 'manually_confirmed')) {
                    return -1;
                }
                if ((bStatus === 'not_ready' || bScore === 0) && (aStatus === 'ready' || aStatus === 'manually_confirmed')) {
                    return 1;
                }

                // ترتيب ثانوي حسب نوع الوسيلة
                const aType = a.dataset.vehicleType;
                const bType = b.dataset.vehicleType;
                return aType.localeCompare(bType);
            });

            // إعادة ترتيب العناصر في DOM
            vehicleCards.forEach(card => {
                vehiclesGrid.appendChild(card);
            });
        }

        // دالة فتح الصفحة الموحدة مع التتبع
        function openUnifiedPage() {
            const unitId = {{ selected_unit.id }};
            const date = '{{ assignment_date|date:"Y-m-d" }}';
            const unifiedUrl = `/coordination-center/unified-morning-check/?unit_id=${unitId}&date=${date}`;

            // فتح النافذة أو التركيز عليها إذا كانت مفتوحة
            if (unifiedWindow && !unifiedWindow.closed) {
                unifiedWindow.focus();
            } else {
                unifiedWindow = window.open(unifiedUrl, 'unified_window', 'width=1400,height=900,scrollbars=yes,resizable=yes');
                window.unifiedWindow = unifiedWindow; // حفظ مرجع عام
            }
        }

        // Drag and Drop functionality
        let draggedElement = null;

        // Add event listeners for drag and drop
        document.addEventListener('DOMContentLoaded', function() {
            initializeDragAndDrop();
            initializeReadinessButtons();
            // ترتيب الوسائل حسب الجاهزية عند تحميل الصفحة
            setTimeout(sortVehiclesByReadiness, 100);
        });

        function initializeReadinessButtons() {
            // تحديث جميع أزرار الجاهزية عند تحميل الصفحة
            const vehicleCards = document.querySelectorAll('[data-vehicle-id]');
            vehicleCards.forEach(card => {
                const vehicleId = card.dataset.vehicleId;
                updateReadinessButton(vehicleId);
            });
        }

        function initializeDragAndDrop() {
            // Personnel items - draggable
            const personnelItems = document.querySelectorAll('.personnel-item');
            personnelItems.forEach(item => {
                item.addEventListener('dragstart', handleDragStart);
                item.addEventListener('dragend', handleDragEnd);
            });

            // Crew slots - drop zones
            const crewSlots = document.querySelectorAll('.crew-slot');
            crewSlots.forEach(slot => {
                slot.addEventListener('dragover', handleDragOver);
                slot.addEventListener('drop', handleDrop);
                slot.addEventListener('dragenter', handleDragEnter);
                slot.addEventListener('dragleave', handleDragLeave);
            });
        }

        function handleDragStart(e) {
            draggedElement = this;
            this.classList.add('dragging');
            e.dataTransfer.effectAllowed = 'move';
        }

        function handleDragEnd(e) {
            this.classList.remove('dragging');
            draggedElement = null;
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';
        }

        function handleDragEnter(e) {
            e.preventDefault();
            this.classList.add('drop-zone');
        }

        function handleDragLeave(e) {
            this.classList.remove('drop-zone');
        }

        function handleDrop(e) {
            e.preventDefault();
            this.classList.remove('drop-zone');

            if (draggedElement) {
                const personnelId = draggedElement.dataset.personnelId;
                const jobFunction = draggedElement.dataset.jobFunction;
                const vehicleId = this.dataset.vehicle;
                const role = this.dataset.role;

                // إزالة جميع القيود - السماح بأي عون في أي دور
                // لا فحص للتأهيل أو التوافق

                // السماح بتعيين أكثر من عون في نفس المنصب
                // إزالة فحص المنصب المحجوز

                // Assign personnel to vehicle
                assignPersonnelToVehicle(personnelId, vehicleId, role);
            }
        }

        // تم إزالة دالة isRoleCompatible - لا قيود على التوافق
        // أي عون يمكن تعيينه في أي دور بدون قيود

        // Quick Assignment Functions
        function assignQuick(selectElement, role, vehicleId) {
            const personnelId = selectElement.value;
            if (personnelId) {
                assignPersonnelToVehicle(personnelId, vehicleId, role);
                selectElement.value = ''; // إعادة تعيين القائمة
            }
        }

        function markVehicleReady(vehicleId) {
            // تحديث الزر فوراً لإظهار الحالة الجديدة
            updateReadinessButtonToReady(vehicleId);

            fetch('/api/mark-vehicle-ready/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify({
                    vehicle_id: vehicleId,
                    date: '{{ assignment_date|date:"Y-m-d" }}'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('نجح', 'تم تأكيد جاهزية الوسيلة', 'success');
                    // تحديث الجاهزية مع التأكيد اليدوي
                    updateVehicleReadinessManual(vehicleId);
                    // إعادة تحميل الصفحة بعد تأخير قصير
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showAlert('خطأ', data.message || 'حدث خطأ', 'error');
                    // إعادة الزر لحالته السابقة في حالة الخطأ
                    updateReadinessButton(vehicleId);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('خطأ', 'حدث خطأ في الاتصال', 'error');
                // إعادة الزر لحالته السابقة في حالة الخطأ
                updateReadinessButton(vehicleId);
            });
        }

        function updateReadinessButtonToReady(vehicleId) {
            // تحديث الزر لإظهار "جاهز (100%)" فوراً
            const vehicleCard = document.querySelector(`[data-vehicle-id="${vehicleId}"]`);
            if (!vehicleCard) return;

            const readinessButton = vehicleCard.querySelector('button[onclick*="markVehicleReady"]');
            if (readinessButton) {
                readinessButton.disabled = true; // تعطيل الزر مؤقتاً
                readinessButton.className = 'btn btn-success btn-sm w-100';
                readinessButton.innerHTML = '<i class="fas fa-check-circle"></i> جاهز (100%)';
                readinessButton.title = 'تم تأكيد الجاهزية';
            }
        }

        function updateVehicleReadinessManual(vehicleId) {
            // تحديث الجاهزية مع التأكيد اليدوي
            fetch('/api/update-readiness-from-assignment/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify({
                    vehicle_id: vehicleId,
                    date: '{{ assignment_date|date:"Y-m-d" }}',
                    action: 'manual_confirm'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('تم تأكيد الجاهزية يدوياً:', data);

                    // إرسال رسالة تزامن للصفحة الموحدة
                    if (window.unifiedWindow && !window.unifiedWindow.closed) {
                        window.unifiedWindow.postMessage({
                            type: 'readiness_update',
                            vehicle_id: vehicleId,
                            readiness_score: data.readiness_score,
                            readiness_status: data.readiness_status,
                            crew_count: data.crew_count,
                            manual_confirm: true,
                            timestamp: new Date().toISOString()
                        }, '*');
                    }

                    // حفظ في localStorage للتزامن
                    localStorage.setItem('last_readiness_update', JSON.stringify({
                        vehicle_id: vehicleId,
                        readiness_score: data.readiness_score,
                        readiness_status: data.readiness_status,
                        crew_count: data.crew_count,
                        manual_confirm: true,
                        timestamp: new Date().toISOString()
                    }));
                } else {
                    console.error('خطأ في تأكيد الجاهزية:', data.message);
                }
            })
            .catch(error => {
                console.error('خطأ في الاتصال:', error);
            });
        }

        function showVehicleSelector(personnelId, role) {
            const vehicles = [
                {% for vehicle_id, vehicle_data in vehicle_assignments.items %}
                {
                    id: {{ vehicle_id }},
                    name: '{{ vehicle_data.vehicle.equipment_type }} - {{ vehicle_data.vehicle.serial_number }}',
                    status: 'operational' // فقط الوسائل الجاهزة تظهر هنا
                },
                {% endfor %}
            ];

            let options = '<option value="">اختر الوسيلة</option>';
            vehicles.forEach(vehicle => {
                if (vehicle.status === 'operational') {
                    options += `<option value="${vehicle.id}">${vehicle.name}</option>`;
                }
            });

            const roleNames = {
                'driver': 'سائق',
                'crew_chief': 'رئيس عدد',
                'agent': 'عون'
            };

            const html = `
                <div class="mb-3">
                    <label class="form-label">اختر الوسيلة لتعيين العون كـ ${roleNames[role]}:</label>
                    <select class="form-select" id="vehicleSelector">
                        ${options}
                    </select>
                </div>
            `;

            if (confirm(`هل تريد تعيين هذا العون كـ ${roleNames[role]}؟`)) {
                const vehicleId = prompt('أدخل رقم الوسيلة أو اختر من القائمة:');
                if (vehicleId && vehicles.find(v => v.id == vehicleId)) {
                    assignPersonnelToVehicle(personnelId, vehicleId, role);
                } else if (vehicleId) {
                    showAlert('خطأ', 'رقم الوسيلة غير صحيح', 'error');
                }
            }
        }

        // API Functions
        function assignPersonnelToVehicle(personnelId, vehicleId, role) {
            const data = {
                personnel_id: personnelId,
                vehicle_id: vehicleId,
                role: role,
                assignment_date: '{{ assignment_date|date:"Y-m-d" }}'
            };

            fetch('{% url "assign_personnel_to_vehicle" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('نجح', data.message, 'success');
                    // تحديث زر الجاهزية فقط (بدون تحديث الجاهزية تلقائياً)
                    updateReadinessButton(vehicleId);
                    // إزالة التحديث التلقائي للجاهزية - يجب النقر على زر "جاهز" يدوياً
                    // updateVehicleReadiness(vehicleId); // تم إزالة هذا السطر
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showAlert('خطأ', data.message, 'error');
                }
            })
            .catch(error => {
                showAlert('خطأ', 'حدث خطأ في الاتصال', 'error');
                console.error('Error:', error);
            });
        }

        function updateReadinessButton(vehicleId) {
            // فحص عدد الأعوان المعينين على الوسيلة
            const vehicleCard = document.querySelector(`[data-vehicle-id="${vehicleId}"]`);
            if (!vehicleCard) return;

            const crewMembers = vehicleCard.querySelectorAll('.crew-member');
            const readinessButton = vehicleCard.querySelector('button[onclick*="markVehicleReady"]');

            if (readinessButton) {
                if (crewMembers.length > 0) {
                    // إذا كان هناك أعوان معينين، فعل الزر ولكن اجعله "غير جاهز" حتى يتم النقر عليه
                    readinessButton.disabled = false;
                    readinessButton.className = 'btn btn-warning btn-sm w-100';
                    readinessButton.innerHTML = '<i class="fas fa-clock"></i> غير جاهز - انقر للتأكيد';
                    readinessButton.title = 'انقر لتأكيد جاهزية الوسيلة';
                } else {
                    // إذا لم يكن هناك أعوان، عطل الزر
                    readinessButton.disabled = true;
                    readinessButton.className = 'btn btn-outline-secondary btn-sm w-100';
                    readinessButton.innerHTML = '<i class="fas fa-exclamation-triangle"></i> فارغ';
                    readinessButton.title = 'يجب تعيين أعوان أولاً';
                }
            }
        }

        function updateVehicleReadiness(vehicleId) {
            // تحديث الجاهزية في الخلفية وإرسال رسالة تزامن للصفحة الموحدة
            fetch('/api/update-readiness-from-assignment/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify({
                    vehicle_id: vehicleId,
                    date: '{{ assignment_date|date:"Y-m-d" }}',
                    action: 'calculate'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('تم تحديث الجاهزية:', data);

                    // إرسال رسالة تزامن للصفحة الموحدة
                    if (window.unifiedWindow && !window.unifiedWindow.closed) {
                        window.unifiedWindow.postMessage({
                            type: 'readiness_update',
                            vehicle_id: vehicleId,
                            readiness_score: data.readiness_score,
                            readiness_status: data.readiness_status,
                            crew_count: data.crew_count,
                            timestamp: new Date().toISOString()
                        }, '*');
                    }

                    // حفظ في localStorage للتزامن
                    localStorage.setItem('last_readiness_update', JSON.stringify({
                        vehicle_id: vehicleId,
                        readiness_score: data.readiness_score,
                        readiness_status: data.readiness_status,
                        crew_count: data.crew_count,
                        timestamp: new Date().toISOString()
                    }));
                } else {
                    console.error('خطأ في تحديث الجاهزية:', data.message);
                }
            })
            .catch(error => {
                console.error('خطأ في الاتصال:', error);
            });
        }

        function removePersonnel(assignmentId) {
            if (!confirm('هل أنت متأكد من إزالة هذا العون؟')) {
                return;
            }

            const data = {
                assignment_id: assignmentId
            };

            fetch('{% url "remove_personnel_from_vehicle" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('نجح', data.message, 'success');
                    // الحصول على vehicleId من العنصر المحذوف
                    const assignmentElement = document.querySelector(`[data-assignment-id="${assignmentId}"]`);
                    if (assignmentElement) {
                        const vehicleCard = assignmentElement.closest('[data-vehicle-id]');
                        if (vehicleCard) {
                            const vehicleId = vehicleCard.dataset.vehicleId;
                            updateReadinessButton(vehicleId);
                            // إزالة التحديث التلقائي للجاهزية - يجب النقر على زر "جاهز" يدوياً
                            // updateVehicleReadiness(vehicleId); // تم إزالة هذا السطر
                        }
                    }
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showAlert('خطأ', data.message, 'error');
                }
            })
            .catch(error => {
                showAlert('خطأ', 'حدث خطأ في الاتصال', 'error');
                console.error('Error:', error);
            });
        }

        function confirmManually(vehicleId) {
            const reason = prompt('يرجى إدخال سبب التأكيد اليدوي:');
            if (!reason) {
                return;
            }

            const data = {
                vehicle_id: vehicleId,
                date: '{{ assignment_date|date:"Y-m-d" }}',
                reason: reason
            };

            fetch('{% url "confirm_vehicle_readiness_manually" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('نجح', data.message, 'success');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showAlert('خطأ', data.message, 'error');
                }
            })
            .catch(error => {
                showAlert('خطأ', 'حدث خطأ في الاتصال', 'error');
                console.error('Error:', error);
            });
        }

        function showAlert(title, message, type) {
            const alertClass = type === 'success' ? 'alert-success' :
                              type === 'warning' ? 'alert-warning' : 'alert-danger';

            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show position-fixed"
                     style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                    <strong>${title}:</strong> ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', alertHtml);

            // Auto remove after 5 seconds
            setTimeout(() => {
                const alert = document.querySelector('.alert:last-of-type');
                if (alert) {
                    alert.remove();
                }
            }, 5000);
        }

        // دالة إعادة تعيين الفلاتر
        function resetFilters() {
            // إعادة تعيين قيم الفلاتر للقيم الافتراضية
            const unitSelect = document.getElementById('unit');
            const dateInput = document.getElementById('date');

            if (unitSelect && unitSelect.options.length > 0) {
                unitSelect.selectedIndex = 0;
            }

            if (dateInput) {
                const today = new Date();
                const formattedDate = today.toISOString().split('T')[0];
                dateInput.value = formattedDate;
            }

            // إشعار المستخدم
            showNotification('تم إعادة تعيين الفلاتر', 'info');
        }

        // دالة إشعار محسنة
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} notification-toast`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                border-radius: 12px;
                box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                animation: slideInRight 0.3s ease;
                backdrop-filter: blur(10px);
            `;

            const iconMap = {
                'success': 'fas fa-check-circle',
                'info': 'fas fa-info-circle',
                'warning': 'fas fa-exclamation-triangle',
                'error': 'fas fa-times-circle'
            };

            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 12px;">
                    <i class="${iconMap[type] || iconMap.info}" style="font-size: 1.2rem;"></i>
                    <span style="flex: 1;">${message}</span>
                    <button type="button" class="close" style="margin-left: auto; background: none; border: none; font-size: 1.5rem; opacity: 0.7; cursor: pointer;">
                        <span>&times;</span>
                    </button>
                </div>
            `;

            document.body.appendChild(notification);

            // إزالة تلقائية بعد 4 ثوان
            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 4000);

            // إزالة عند النقر على X
            notification.querySelector('.close').addEventListener('click', () => {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            });
        }

        // استقبال رسائل من النوافذ الأخرى (نظام التزامن)
        window.addEventListener('message', function(event) {
            console.log('تم استقبال رسالة تزامن:', event.data);

            if (event.data.type === 'vehicle_readiness_update' || event.data.type === 'vehicle_status_changed') {
                const vehicleId = event.data.vehicle_id;
                const status = event.data.status;
                const statusDisplay = event.data.status_display || status;

                console.log(`تحديث حالة الوسيلة ${vehicleId} إلى ${status}`);

                // إذا تم تغيير حالة الوسيلة إلى غير جاهز، أعد تحميل الصفحة
                if (status === 'broken' || status === 'maintenance') {
                    showAlert('تحديث الوسائل',
                        `تم تغيير حالة الوسيلة إلى "${statusDisplay}". ستختفي من قائمة الوسائل المتاحة وسيتم تحرير جميع الأعوان المعينين عليها تلقائياً. سيتم تحديث الصفحة...`,
                        'warning'
                    );
                    setTimeout(() => {
                        console.log('إعادة تحميل الصفحة بسبب تغيير حالة الوسيلة إلى غير جاهز');
                        location.reload();
                    }, 3000);
                } else if (status === 'operational') {
                    // إذا تم تغيير الحالة إلى جاهز، أعد تحميل الصفحة لإظهار الوسيلة
                    showAlert('تحديث الوسائل',
                        `تم تغيير حالة الوسيلة إلى "${statusDisplay}". ستظهر في قائمة الوسائل المتاحة. سيتم تحديث الصفحة...`,
                        'success'
                    );
                    setTimeout(() => {
                        console.log('إعادة تحميل الصفحة بسبب تغيير حالة الوسيلة إلى جاهز');
                        location.reload();
                    }, 2000);
                }
            }
        });

        // وظيفة العودة إلى الأعلى
        document.getElementById('back-to-top').addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    </script>
</body>
</html>
