{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحماية المدنية الجزائرية - الحرائق في الأماكن المستقبِلة للجمهور</title>
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/home.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="{% static 'css/public_area_fires.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="{% static 'css/button-override.css' %}?v=1.0">
</head>
<body>
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle" id="sidebar-toggle" style="cursor: pointer;">
            </div>
        </div>
    </header>

    {% include 'includes/sidebar.html' %}

    <main>
        <div class="home-container">
            <div class="fire-details">
                <div class="fire-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="fire-title">الحرائق في الأماكن المستقبِلة للجمهور</div>

                <div id="alert-container" class="hidden"></div>

                <div class="fire-form">
                    <form id="publicAreaFireForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="date">التاريخ</label>
                                <input type="date" class="form-control" id="date" name="date" required lang="ar">
                            </div>

                            <div class="form-group">
                                <label for="intervening_unit">الوحدات المتدخلة</label>
                                <select class="form-control" id="intervening_unit" name="intervening_unit" required>
                                    <option value="">اختر الوحدة المتدخلة</option>
                                    {% for unit in units %}
                                    <option value="{{ unit.name }}">{{ unit.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="number_of_fires">عدد الحرائق</label>
                                <input type="number" class="form-control" id="number_of_fires" name="number_of_fires" min="0" value="0" required>
                            </div>

                            <div class="form-group">
                                <label for="number_of_interventions">عدد التدخلات</label>
                                <input type="number" class="form-control" id="number_of_interventions" name="number_of_interventions" min="0" value="0" required>
                            </div>

                            <div class="form-group">
                                <label for="number_of_injured">عدد الجرحى</label>
                                <input type="number" class="form-control" id="number_of_injured" name="number_of_injured" min="0" value="0" required>
                            </div>

                            <div class="form-group">
                                <label for="number_of_deaths">عدد الوفيات</label>
                                <input type="number" class="form-control" id="number_of_deaths" name="number_of_deaths" min="0" value="0" required>
                            </div>
                        </div>

                        <div class="section-title">معلومات حول المؤسسة</div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="institution_name">إسم المؤسسة</label>
                                <input type="text" class="form-control" id="institution_name" name="institution_name" required>
                            </div>

                            <div class="form-group">
                                <label for="institution_type">النوع</label>
                                <input type="text" class="form-control" id="institution_type" name="institution_type" required>
                            </div>

                            <div class="form-group">
                                <label for="institution_category">الصنف</label>
                                <input type="text" class="form-control" id="institution_category" name="institution_category" required>
                            </div>
                        </div>

                        <div class="section-title">الوسائل المستعملة</div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="ambulances">سيارة إسعاف</label>
                                <input type="number" class="form-control" id="ambulances" name="ambulances" min="0" value="0" required>
                            </div>

                            <div class="form-group">
                                <label for="fire_trucks">شاحنة إطفاء</label>
                                <input type="number" class="form-control" id="fire_trucks" name="fire_trucks" min="0" value="0" required>
                            </div>

                            <div class="form-group">
                                <label for="mechanical_ladders">السلم الميكانيكي</label>
                                <input type="number" class="form-control" id="mechanical_ladders" name="mechanical_ladders" min="0" value="0" required>
                            </div>

                            <div class="form-group">
                                <label for="other_resources">وسائل أخرى</label>
                                <input type="number" class="form-control" id="other_resources" name="other_resources" min="0" value="0" required>
                            </div>
                        </div>

                        <div class="section-title">مدة التدخل</div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="hours">عدد الساعات</label>
                                <input type="number" class="form-control" id="hours" name="hours" min="0" max="23" value="0" step="1" required>
                            </div>

                            <div class="form-group">
                                <label for="minutes">عدد الدقائق</label>
                                <input type="number" class="form-control" id="minutes" name="minutes" min="0" max="59" value="0" step="1" required>
                            </div>
                        </div>

                        <div class="main-buttons-container">
                            <div class="main-buttons">
                                <a href="{% url 'home' %}" class="main-btn home-btn">
                                    <i class="fas fa-home"></i> الصفحة الرئيسية
                                </a>
                                <a href="{% url 'fires' %}" class="main-btn back-btn">
                                    <i class="fas fa-arrow-right"></i> صفحة الحرائق
                                </a>
                                <button type="button" id="saveButton" class="main-btn save-btn">
                                    <i class="fas fa-save"></i> حفظ
                                </button>
                            </div>
                        </div>

                        <!-- الأزرار العائمة -->
                        <div class="floating-buttons">
                            <a href="{% url 'table_public_area_fires' %}" class="floating-btn tables-btn" title="لوحة الجداول">
                                <i class="fas fa-th"></i>
                            </a>
                            <a href="{% url 'home' %}" class="floating-btn home-btn" title="الصفحة الرئيسية">
                                <i class="fas fa-home"></i>
                            </a>
                            <button type="button" id="floatingSaveBtn" class="floating-btn save-btn" title="حفظ">
                                <i class="fas fa-save"></i>
                            </button>
                            <a href="#" id="back-to-top" class="floating-btn top-btn" title="العودة إلى الأعلى">
                                <i class="fas fa-arrow-up"></i>
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <p>حقوق النشر © 2025 جميع الحقوق محفوظة - تم التطوير بواسطة عبد الرزاق مختاري</p>
    </footer>

    <script src="{% static 'js/common-buttons.js' %}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Button functionality is now handled by common-buttons.js

            const form = document.getElementById('publicAreaFireForm');
            const saveButton = document.getElementById('saveButton');
            const alertContainer = document.getElementById('alert-container');
            const dateInput = document.getElementById('date');
            const hoursInput = document.getElementById('hours');
            const minutesInput = document.getElementById('minutes');

            // Set up Arabic month names
            const arabicMonths = {
                '01': 'جانفي',
                '02': 'فيفري',
                '03': 'مارس',
                '04': 'أفريل',
                '05': 'ماي',
                '06': 'جوان',
                '07': 'جويلية',
                '08': 'أوت',
                '09': 'سبتمبر',
                '10': 'أكتوبر',
                '11': 'نوفمبر',
                '12': 'ديسمبر'
            };

            // Format date display in Arabic
            dateInput.addEventListener('change', function() {
                if (this.value) {
                    const dateParts = this.value.split('-');
                    const year = dateParts[0];
                    const month = dateParts[1];
                    const day = dateParts[2];

                    // Store the formatted date as a data attribute
                    this.setAttribute('data-formatted-date', `${day} ${arabicMonths[month]} ${year}`);
                }
            });

            // Ensure hours and minutes are valid
            hoursInput.addEventListener('input', function() {
                let value = parseInt(this.value);
                if (isNaN(value) || value < 0) {
                    this.value = 0;
                } else if (value > 23) {
                    this.value = 23;
                }
            });

            minutesInput.addEventListener('input', function() {
                let value = parseInt(this.value);
                if (isNaN(value) || value < 0) {
                    this.value = 0;
                } else if (value > 59) {
                    this.value = 59;
                }
            });

            // Function to show alert
            function showAlert(message, type) {
                alertContainer.className = `alert alert-${type}`;
                alertContainer.textContent = message;
                alertContainer.classList.remove('hidden');

                // Auto hide after 5 seconds
                setTimeout(() => {
                    alertContainer.classList.add('hidden');
                }, 5000);
            }

            // Function to validate form
            function validateForm() {
                const date = document.getElementById('date').value;
                const interveningUnit = document.getElementById('intervening_unit').value;
                const numberOfFires = document.getElementById('number_of_fires').value;
                const numberOfInterventions = document.getElementById('number_of_interventions').value;
                const numberOfInjured = document.getElementById('number_of_injured').value;
                const numberOfDeaths = document.getElementById('number_of_deaths').value;
                const institutionName = document.getElementById('institution_name').value;
                const institutionType = document.getElementById('institution_type').value;
                const institutionCategory = document.getElementById('institution_category').value;
                const ambulances = document.getElementById('ambulances').value;
                const fireTrucks = document.getElementById('fire_trucks').value;
                const mechanicalLadders = document.getElementById('mechanical_ladders').value;
                const otherResources = document.getElementById('other_resources').value;
                const hours = document.getElementById('hours').value;
                const minutes = document.getElementById('minutes').value;

                const missingFields = [];

                if (!date) missingFields.push('التاريخ');
                if (!interveningUnit) missingFields.push('الوحدات المتدخلة');
                if (numberOfFires === '') missingFields.push('عدد الحرائق');
                if (numberOfInterventions === '') missingFields.push('عدد التدخلات');
                if (numberOfInjured === '') missingFields.push('عدد الجرحى');
                if (numberOfDeaths === '') missingFields.push('عدد الوفيات');
                if (!institutionName) missingFields.push('إسم المؤسسة');
                if (!institutionType) missingFields.push('النوع');
                if (!institutionCategory) missingFields.push('الصنف');
                if (ambulances === '') missingFields.push('سيارة إسعاف');
                if (fireTrucks === '') missingFields.push('شاحنة إطفاء');
                if (mechanicalLadders === '') missingFields.push('السلم الميكانيكي');
                if (otherResources === '') missingFields.push('وسائل أخرى');
                if (hours === '' || minutes === '') missingFields.push('مدة التدخل');

                if (missingFields.length > 0) {
                    showAlert('يرجى ملء الحقول التالية: ' + missingFields.join('، '), 'danger');
                    return false;
                }

                // Check for negative numbers
                if (parseInt(numberOfFires) < 0 ||
                    parseInt(numberOfInterventions) < 0 ||
                    parseInt(numberOfInjured) < 0 ||
                    parseInt(numberOfDeaths) < 0 ||
                    parseInt(ambulances) < 0 ||
                    parseInt(fireTrucks) < 0 ||
                    parseInt(mechanicalLadders) < 0 ||
                    parseInt(otherResources) < 0) {
                    showAlert('لا يمكن إدخال أرقام سالبة', 'danger');
                    return false;
                }

                return true;
            }

            // Save button click handler
            saveButton.addEventListener('click', function() {
                if (!validateForm()) {
                    return;
                }

                // Format intervention duration
                let hours = parseInt(document.getElementById('hours').value);
                let minutes = parseInt(document.getElementById('minutes').value);

                // Ensure valid values
                if (isNaN(hours) || hours < 0) hours = 0;
                // No upper limit for hours
                if (isNaN(minutes) || minutes < 0) minutes = 0;
                if (minutes > 59) minutes = 59;

                // Format without leading zeros for hours (unlimited hours)
                const hoursStr = hours.toString();
                const minutesStr = minutes < 10 ? '0' + minutes : minutes.toString();

                const interventionDuration = `${hoursStr} سا ${minutesStr} د`;

                // Prepare data for submission
                const dateInput = document.getElementById('date');
                const formData = {
                    date: dateInput.value,
                    formatted_date: dateInput.getAttribute('data-formatted-date') || dateInput.value,
                    intervening_unit: document.getElementById('intervening_unit').value,
                    number_of_fires: parseInt(document.getElementById('number_of_fires').value),
                    number_of_interventions: parseInt(document.getElementById('number_of_interventions').value),
                    number_of_injured: parseInt(document.getElementById('number_of_injured').value),
                    number_of_deaths: parseInt(document.getElementById('number_of_deaths').value),
                    institution_name: document.getElementById('institution_name').value,
                    institution_type: document.getElementById('institution_type').value,
                    institution_category: document.getElementById('institution_category').value,
                    ambulances: parseInt(document.getElementById('ambulances').value),
                    fire_trucks: parseInt(document.getElementById('fire_trucks').value),
                    mechanical_ladders: parseInt(document.getElementById('mechanical_ladders').value),
                    other_resources: parseInt(document.getElementById('other_resources').value),
                    intervention_duration: interventionDuration
                };

                // Send data to server
                fetch(window.location.href, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        showAlert(data.message, 'success');
                        // Reset form
                        form.reset();
                        // Reset hours and minutes to "00"
                        document.getElementById('hours').value = "0";
                        document.getElementById('minutes').value = "0";
                    } else {
                        showAlert(data.message, 'danger');
                    }
                })
                .catch(error => {
                    showAlert('حدث خطأ أثناء حفظ البيانات: ' + error, 'danger');
                });
            });

            // Function to get CSRF token
            function getCookie(name) {
                let cookieValue = null;
                if (document.cookie && document.cookie !== '') {
                    const cookies = document.cookie.split(';');
                    for (let i = 0; i < cookies.length; i++) {
                        const cookie = cookies[i].trim();
                        if (cookie.substring(0, name.length + 1) === (name + '=')) {
                            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                            break;
                        }
                    }
                }
                return cookieValue;
            }
        });
    </script>
    <script src="{% static 'js/sidebar.js' %}"></script>
</body>
</html>
