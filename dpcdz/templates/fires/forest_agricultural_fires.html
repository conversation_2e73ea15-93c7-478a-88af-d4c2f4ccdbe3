{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحماية المدنية الجزائرية - حرائق الغابات و المحاصيل</title>
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/home.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="{% static 'css/forest_agricultural_fires.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="{% static 'css/button-override.css' %}?v=1.0">
</head>
<body>
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle" id="sidebar-toggle" style="cursor: pointer;">
            </div>
        </div>
    </header>

    {% include 'includes/sidebar.html' %}

    <main>
        <div class="home-container">
            <div class="fire-details">
                <div class="fire-icon">
                    <i class="fas fa-tree"></i>
                </div>
                <div class="fire-title">حرائق الغابات و المحاصيل</div>

                <div id="alert-container" class="hidden"></div>

                <div class="fire-form">
                    <form id="forestAgriculturalFireForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="date">التاريخ</label>
                                <input type="date" class="form-control" id="date" name="date" required lang="ar">
                            </div>

                            <div class="form-group">
                                <label for="municipality">البلدية</label>
                                <input type="text" class="form-control" id="municipality" name="municipality" required placeholder="أدخل اسم البلدية">
                            </div>

                            <div class="form-group">
                                <label for="intervening_unit">الوحدات المتدخلة</label>
                                <select class="form-control" id="intervening_unit" name="intervening_unit" required>
                                    <option value="">اختر الوحدة المتدخلة</option>
                                    {% for unit in units %}
                                    <option value="{{ unit.name }}">{{ unit.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="fire_type">نوع الحريق</label>
                                <select class="form-control" id="fire_type" name="fire_type" required>
                                    <option value="">اختر نوع الحريق</option>
                                    <option value="حرائق الغابات و الأدغال">حرائق الغابات و الأدغال</option>
                                    <option value="حرائق المحاصيل">حرائق المحاصيل</option>
                                    <option value="حرائق الأعشاب">حرائق الأعشاب</option>
                                    <option value="حرائق الأعلاف">حرائق الأعلاف</option>
                                    <option value="حرائق النخيل">حرائق النخيل</option>
                                    <option value="حرائق الأشجار المثمرة">حرائق الأشجار المثمرة</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="number_of_fires">عدد الحرائق</label>
                                <input type="number" class="form-control" id="number_of_fires" name="number_of_fires" min="0" value="0" required>
                            </div>

                            <div class="form-group">
                                <label for="number_of_interventions">عدد التدخلات</label>
                                <input type="number" class="form-control" id="number_of_interventions" name="number_of_interventions" min="0" value="0" required>
                            </div>
                        </div>

                        <div class="section-title">الخسائر المسجلة</div>

                        <div id="dynamic-losses-fields" class="dynamic-fields">
                            <!-- Dynamic fields will be inserted here based on fire type -->
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="loss_type">نوع الخسائر</label>
                                <input type="text" class="form-control" id="loss_type" name="loss_type" required>
                            </div>
                        </div>

                        <div class="main-buttons-container">
                            <div class="main-buttons">
                                <a href="{% url 'home' %}" class="main-btn home-btn">
                                    <i class="fas fa-home"></i> الصفحة الرئيسية
                                </a>
                                <a href="{% url 'fires' %}" class="main-btn back-btn">
                                    <i class="fas fa-arrow-right"></i> صفحة الحرائق
                                </a>
                                <button type="button" id="saveButton" class="main-btn save-btn">
                                    <i class="fas fa-save"></i> حفظ
                                </button>
                            </div>
                        </div>

                        <!-- الأزرار العائمة -->
                        <div class="floating-buttons">
                            <a href="{% url 'table_forest_agricultural_fires' %}" class="floating-btn tables-btn" title="لوحة الجداول">
                                <i class="fas fa-th"></i>
                            </a>
                            <a href="{% url 'home' %}" class="floating-btn home-btn" title="الصفحة الرئيسية">
                                <i class="fas fa-home"></i>
                            </a>
                            <button type="button" id="floatingSaveBtn" class="floating-btn save-btn" title="حفظ">
                                <i class="fas fa-save"></i>
                            </button>
                            <a href="#" id="back-to-top" class="floating-btn top-btn" title="العودة إلى الأعلى">
                                <i class="fas fa-arrow-up"></i>
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <p>حقوق النشر © 2025 جميع الحقوق محفوظة - تم التطوير بواسطة عبد الرزاق مختاري</p>
    </footer>

    <script src="{% static 'js/common-buttons.js' %}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Button functionality is now handled by common-buttons.js

            const form = document.getElementById('forestAgriculturalFireForm');
            const saveButton = document.getElementById('saveButton');
            const alertContainer = document.getElementById('alert-container');
            const dateInput = document.getElementById('date');
            const fireTypeSelect = document.getElementById('fire_type');
            const dynamicLossesFields = document.getElementById('dynamic-losses-fields');

            // Set up Arabic month names
            const arabicMonths = {
                '01': 'جانفي',
                '02': 'فيفري',
                '03': 'مارس',
                '04': 'أفريل',
                '05': 'ماي',
                '06': 'جوان',
                '07': 'جويلية',
                '08': 'أوت',
                '09': 'سبتمبر',
                '10': 'أكتوبر',
                '11': 'نوفمبر',
                '12': 'ديسمبر'
            };

            // Format date display in Arabic
            dateInput.addEventListener('change', function() {
                if (this.value) {
                    const dateParts = this.value.split('-');
                    const year = dateParts[0];
                    const month = dateParts[1];
                    const day = dateParts[2];

                    // Store the formatted date as a data attribute
                    this.setAttribute('data-formatted-date', `${day} ${arabicMonths[month]} ${year}`);
                }
            });

            // Function to update dynamic fields based on fire type
            fireTypeSelect.addEventListener('change', function() {
                const fireType = this.value;
                dynamicLossesFields.innerHTML = '';

                if (fireType === 'حرائق الغابات و الأدغال' || fireType === 'حرائق المحاصيل' || fireType === 'حرائق الأعشاب') {
                    // Area-based losses fields
                    dynamicLossesFields.innerHTML = `
                        <div class="form-row">
                            <div class="form-group">
                                <label for="losses_hectare">الخسائر بالهكتار</label>
                                <input type="number" class="form-control" id="losses_hectare" name="losses_hectare" min="0" value="0" required>
                            </div>

                            <div class="form-group">
                                <label for="losses_are">الخسائر بالآر</label>
                                <input type="number" class="form-control" id="losses_are" name="losses_are" min="0" value="0" required>
                            </div>

                            <div class="form-group">
                                <label for="losses_square_meter">الخسائر بالمتر مربع</label>
                                <input type="number" class="form-control" id="losses_square_meter" name="losses_square_meter" min="0" value="0" required>
                            </div>
                        </div>
                    `;
                } else if (fireType === 'حرائق الأعلاف' || fireType === 'حرائق النخيل' || fireType === 'حرائق الأشجار المثمرة') {
                    // Count-based losses field
                    dynamicLossesFields.innerHTML = `
                        <div class="form-row">
                            <div class="form-group">
                                <label for="losses_count">عدد الخسائر</label>
                                <input type="number" class="form-control" id="losses_count" name="losses_count" min="0" value="0" required>
                            </div>
                        </div>
                    `;
                }
            });

            // Function to show alert
            function showAlert(message, type) {
                alertContainer.className = `alert alert-${type}`;
                alertContainer.textContent = message;
                alertContainer.classList.remove('hidden');

                // Auto hide after 5 seconds
                setTimeout(() => {
                    alertContainer.classList.add('hidden');
                }, 5000);
            }

            // Function to validate form
            function validateForm() {
                const date = document.getElementById('date').value;
                const municipality = document.getElementById('municipality').value;
                const interveningUnit = document.getElementById('intervening_unit').value;
                const fireType = document.getElementById('fire_type').value;
                const numberOfFires = document.getElementById('number_of_fires').value;
                const numberOfInterventions = document.getElementById('number_of_interventions').value;
                const lossType = document.getElementById('loss_type').value;

                const missingFields = [];

                if (!date) missingFields.push('التاريخ');
                if (!municipality) missingFields.push('البلدية');
                if (!interveningUnit) missingFields.push('الوحدات المتدخلة');
                if (!fireType) missingFields.push('نوع الحريق');
                if (numberOfFires === '') missingFields.push('عدد الحرائق');
                if (numberOfInterventions === '') missingFields.push('عدد التدخلات');
                if (!lossType) missingFields.push('نوع الخسائر');

                // Validate losses based on fire type
                if (fireType === 'حرائق الغابات و الأدغال' || fireType === 'حرائق المحاصيل' || fireType === 'حرائق الأعشاب') {
                    const lossesHectare = document.getElementById('losses_hectare').value;
                    const lossesAre = document.getElementById('losses_are').value;
                    const lossesSquareMeter = document.getElementById('losses_square_meter').value;

                    if (lossesHectare === '') missingFields.push('الخسائر بالهكتار');
                    if (lossesAre === '') missingFields.push('الخسائر بالآر');
                    if (lossesSquareMeter === '') missingFields.push('الخسائر بالمتر مربع');
                } else if (fireType === 'حرائق الأعلاف' || fireType === 'حرائق النخيل' || fireType === 'حرائق الأشجار المثمرة') {
                    const lossesCount = document.getElementById('losses_count').value;

                    if (lossesCount === '') missingFields.push('عدد الخسائر');
                }

                if (missingFields.length > 0) {
                    showAlert('يرجى ملء الحقول التالية: ' + missingFields.join('، '), 'danger');
                    return false;
                }

                // Check for negative numbers
                if (parseInt(numberOfFires) < 0 || parseInt(numberOfInterventions) < 0) {
                    showAlert('لا يمكن إدخال أرقام سالبة', 'danger');
                    return false;
                }

                return true;
            }

            // Save button click handler
            saveButton.addEventListener('click', function() {
                if (!validateForm()) {
                    return;
                }

                // Prepare data for submission
                const dateInput = document.getElementById('date');
                const fireType = document.getElementById('fire_type').value;

                const formData = {
                    date: dateInput.value,
                    formatted_date: dateInput.getAttribute('data-formatted-date') || dateInput.value,
                    municipality: document.getElementById('municipality').value,
                    intervening_unit: document.getElementById('intervening_unit').value,
                    fire_type: fireType,
                    number_of_fires: parseInt(document.getElementById('number_of_fires').value),
                    number_of_interventions: parseInt(document.getElementById('number_of_interventions').value),
                    loss_type: document.getElementById('loss_type').value
                };

                // Add losses based on fire type
                if (fireType === 'حرائق الغابات و الأدغال' || fireType === 'حرائق المحاصيل' || fireType === 'حرائق الأعشاب') {
                    formData.losses_hectare = parseInt(document.getElementById('losses_hectare').value);
                    formData.losses_are = parseInt(document.getElementById('losses_are').value);
                    formData.losses_square_meter = parseInt(document.getElementById('losses_square_meter').value);
                } else if (fireType === 'حرائق الأعلاف' || fireType === 'حرائق النخيل' || fireType === 'حرائق الأشجار المثمرة') {
                    formData.losses_count = parseInt(document.getElementById('losses_count').value);
                }

                // Send data to server
                fetch(window.location.href, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        showAlert(data.message, 'success');
                        // Reset form
                        form.reset();
                        // Clear dynamic fields
                        dynamicLossesFields.innerHTML = '';
                    } else {
                        showAlert(data.message, 'danger');
                    }
                })
                .catch(error => {
                    showAlert('حدث خطأ أثناء حفظ البيانات: ' + error, 'danger');
                });
            });

            // Function to get CSRF token
            function getCookie(name) {
                let cookieValue = null;
                if (document.cookie && document.cookie !== '') {
                    const cookies = document.cookie.split(';');
                    for (let i = 0; i < cookies.length; i++) {
                        const cookie = cookies[i].trim();
                        if (cookie.substring(0, name.length + 1) === (name + '=')) {
                            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                            break;
                        }
                    }
                }
                return cookieValue;
            }
        });
    </script>
    <script src="{% static 'js/sidebar.js' %}"></script>
</body>
</html>
