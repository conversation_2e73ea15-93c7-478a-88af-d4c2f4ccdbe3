{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحماية المدنية الجزائرية - رئيس العدد</title>
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/home.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle" id="sidebar-toggle" style="cursor: pointer;">
            </div>
        </div>
    </header>

    {% include 'includes/sidebar.html' %}

    <main>
        <div class="home-container">
            <div class="page-header">
                <div class="page-icon">
                    <i class="fas fa-user-shield"></i>
                </div>
                <h2>رئيس العدد - العون الميداني</h2>
                <p class="page-description">واجهة العون الميداني لتحديث معلومات التدخل والإبلاغ عن الكوارث</p>
            </div>

            <!-- الأزرار الرئيسية -->
            <div class="action-buttons">
                <button class="action-btn intervention-btn" id="during-intervention">
                    <div class="btn-icon">
                        <i class="fas fa-tools"></i>
                    </div>
                    <div class="btn-content">
                        <h3>أثناء التدخل</h3>
                        <p>تحديث معلومات التدخل الميداني</p>
                    </div>
                </button>

                <button class="action-btn complete-btn" id="complete-intervention">
                    <div class="btn-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="btn-content">
                        <h3>إنهاء التدخل</h3>
                        <p>تسجيل التقرير النهائي للتدخل</p>
                    </div>
                </button>

                <button class="action-btn disaster-btn" id="report-disaster">
                    <div class="btn-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="btn-content">
                        <h3>بلّغ كارثة كبرى</h3>
                        <p>إرسال بلاغ فوري عن كارثة كبيرة</p>
                    </div>
                </button>
            </div>

            <!-- جدول التقارير السابقة -->
            <div class="reports-section">
                <h3><i class="fas fa-history"></i> التقارير السابقة</h3>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>رقم</th>
                                <th>نوع التدخل</th>
                                <th>التاريخ</th>
                                <th>الموقع</th>
                                <th>الحالة</th>
                                <th>إجراء</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>001</td>
                                <td>حريق منزل</td>
                                <td>2025/07/15 - 14:30</td>
                                <td>حي النصر، سوق أهراس</td>
                                <td><span class="status completed">مكتمل</span></td>
                                <td>
                                    <button class="btn btn-sm btn-primary">عرض</button>
                                </td>
                            </tr>
                            <tr>
                                <td>002</td>
                                <td>حادث مرور</td>
                                <td>2025/07/15 - 16:45</td>
                                <td>الطريق الوطني رقم 16</td>
                                <td><span class="status in-progress">قيد التنفيذ</span></td>
                                <td>
                                    <button class="btn btn-sm btn-warning">تحديث</button>
                                </td>
                            </tr>
                            <tr>
                                <td>003</td>
                                <td>إجلاء صحي</td>
                                <td>2025/07/15 - 18:20</td>
                                <td>بلدية المشروحة</td>
                                <td><span class="status pending">في الانتظار</span></td>
                                <td>
                                    <button class="btn btn-sm btn-success">بدء</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- الأزرار العائمة -->
            <div class="floating-buttons">
                <a href="{% url 'major_disasters' %}" class="floating-btn disaster-btn" title="الكوارث الكبرى">
                    <i class="fas fa-exclamation-circle"></i>
                </a>
                <a href="{% url 'coordination_center' %}" class="floating-btn coordination-btn" title="مركز التنسيق">
                    <i class="fas fa-building"></i>
                </a>
                <a href="{% url 'home' %}" class="floating-btn home-btn" title="الصفحة الرئيسية">
                    <i class="fas fa-home"></i>
                </a>
                <a href="#" id="back-to-top" class="floating-btn top-btn" title="العودة إلى الأعلى">
                    <i class="fas fa-arrow-up"></i>
                </a>
            </div>
        </div>
    </main>

    <!-- Modal for During Intervention -->
    <div class="modal fade" id="interventionModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تحديث معلومات التدخل</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="interventionForm">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">وقت الوصول</label>
                                <input type="time" class="form-control" id="arrival-time">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">عدد الحاضرين</label>
                                <input type="number" class="form-control" id="present-count">
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">الموقع GPS</label>
                                <button type="button" class="btn btn-outline-primary" id="get-location">
                                    <i class="fas fa-map-marker-alt"></i> تحديد الموقع
                                </button>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">عدد الضحايا</label>
                                <input type="number" class="form-control" id="casualties-count">
                            </div>
                        </div>
                        <div class="mt-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" rows="3" id="intervention-notes"></textarea>
                        </div>
                        <div class="mt-3">
                            <label class="form-label">إرفاق صور</label>
                            <input type="file" class="form-control" multiple accept="image/*" id="intervention-images">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="save-intervention">حفظ التحديث</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Disaster Report -->
    <div class="modal fade" id="disasterModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">بلاغ كارثة كبرى</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="disasterForm">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">نوع الكارثة</label>
                                <select class="form-control" id="disaster-type">
                                    <option value="">اختر نوع الكارثة</option>
                                    <option value="fire">🔥 حريق</option>
                                    <option value="flood">🌊 فيضان</option>
                                    <option value="earthquake">🌍 زلزال</option>
                                    <option value="landslide">⛰️ انهيار أرضي</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">درجة الخطورة</label>
                                <select class="form-control" id="disaster-severity">
                                    <option value="">اختر درجة الخطورة</option>
                                    <option value="normal">عادي</option>
                                    <option value="medium">متوسط</option>
                                    <option value="high">مرتفع</option>
                                    <option value="critical">حرج</option>
                                </select>
                            </div>
                        </div>
                        <div class="mt-3">
                            <label class="form-label">الموقع</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="disaster-location" placeholder="أدخل الموقع">
                                <button type="button" class="btn btn-outline-primary" id="get-disaster-location">
                                    <i class="fas fa-crosshairs"></i> GPS
                                </button>
                            </div>
                        </div>
                        <div class="mt-3">
                            <label class="form-label">وصف الكارثة</label>
                            <textarea class="form-control" rows="4" id="disaster-description" placeholder="اكتب وصفاً مفصلاً للكارثة..."></textarea>
                        </div>
                        <div class="mt-3">
                            <label class="form-label">مرفقات</label>
                            <input type="file" class="form-control" multiple accept="image/*,audio/*" id="disaster-attachments">
                            <small class="text-muted">يمكنك إرفاق صور أو تسجيلات صوتية</small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-danger" id="send-disaster-report">
                        <i class="fas fa-paper-plane"></i> إرسال البلاغ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <footer>
        <p>حقوق النشر © 2025 جميع الحقوق محفوظة - تم التطوير بواسطة عبد الرزاق مختاري</p>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{% static 'js/sidebar.js' %}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Action button handlers
            document.getElementById('during-intervention').addEventListener('click', function() {
                const modal = new bootstrap.Modal(document.getElementById('interventionModal'));
                modal.show();
            });

            document.getElementById('report-disaster').addEventListener('click', function() {
                const modal = new bootstrap.Modal(document.getElementById('disasterModal'));
                modal.show();
            });

            // GPS location handlers
            document.getElementById('get-location').addEventListener('click', function() {
                if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition(function(position) {
                        alert(`الموقع: ${position.coords.latitude}, ${position.coords.longitude}`);
                    });
                } else {
                    alert('GPS غير متاح في هذا المتصفح');
                }
            });

            document.getElementById('get-disaster-location').addEventListener('click', function() {
                if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition(function(position) {
                        document.getElementById('disaster-location').value = 
                            `${position.coords.latitude}, ${position.coords.longitude}`;
                    });
                } else {
                    alert('GPS غير متاح في هذا المتصفح');
                }
            });

            // Save handlers
            document.getElementById('save-intervention').addEventListener('click', function() {
                alert('تم حفظ تحديث التدخل بنجاح');
                bootstrap.Modal.getInstance(document.getElementById('interventionModal')).hide();
            });

            document.getElementById('send-disaster-report').addEventListener('click', function() {
                const disasterType = document.getElementById('disaster-type').value;
                const severity = document.getElementById('disaster-severity').value;
                
                if (!disasterType || !severity) {
                    alert('يرجى ملء جميع الحقول المطلوبة');
                    return;
                }
                
                alert('تم إرسال بلاغ الكارثة بنجاح! سيتم التواصل معك قريباً.');
                bootstrap.Modal.getInstance(document.getElementById('disasterModal')).hide();
            });

            // Back to top button
            const backToTopButton = document.getElementById('back-to-top');
            if (backToTopButton) {
                backToTopButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                });
            }
        });
    </script>

    <style>
        /* Page header styles */
        .page-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #28a745, #20c997);
            border-radius: 10px;
            color: white;
        }

        .page-icon {
            font-size: 48px;
            margin-bottom: 10px;
        }

        .page-description {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }

        /* Action buttons */
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .action-btn {
            display: flex;
            align-items: center;
            gap: 20px;
            padding: 25px;
            border: none;
            border-radius: 10px;
            background: white;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            text-align: right;
        }

        .action-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .intervention-btn {
            border-left: 5px solid #ffc107;
        }

        .intervention-btn:hover {
            background: #fff3cd;
        }

        .complete-btn {
            border-left: 5px solid #28a745;
        }

        .complete-btn:hover {
            background: #d4edda;
        }

        .disaster-btn {
            border-left: 5px solid #dc3545;
        }

        .disaster-btn:hover {
            background: #f8d7da;
        }

        .btn-icon {
            font-size: 32px;
            width: 60px;
            text-align: center;
        }

        .intervention-btn .btn-icon {
            color: #ffc107;
        }

        .complete-btn .btn-icon {
            color: #28a745;
        }

        .disaster-btn .btn-icon {
            color: #dc3545;
        }

        .btn-content h3 {
            margin: 0 0 5px 0;
            color: #333;
        }

        .btn-content p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }

        /* Reports section */
        .reports-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .reports-section h3 {
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f8f9fa;
        }

        /* Status badges */
        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .status.completed {
            background: #28a745;
            color: white;
        }

        .status.in-progress {
            background: #ffc107;
            color: #333;
        }

        .status.pending {
            background: #6c757d;
            color: white;
        }

        /* Floating buttons */
        .floating-buttons {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .floating-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            transition: all 0.3s;
            text-decoration: none;
            font-size: 18px;
            border: none;
            cursor: pointer;
        }

        .floating-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
        }

        .floating-btn.disaster-btn {
            background-color: #dc3545;
        }

        .floating-btn.coordination-btn {
            background-color: #28a745;
        }

        .floating-btn.home-btn {
            background-color: #0d47a1;
        }

        .floating-btn.top-btn {
            background-color: #0d6efd;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .action-buttons {
                grid-template-columns: 1fr;
            }

            .action-btn {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }
        }
    </style>
</body>
</html>
