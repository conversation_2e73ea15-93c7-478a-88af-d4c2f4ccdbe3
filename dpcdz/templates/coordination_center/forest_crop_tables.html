{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحماية المدنية الجزائرية - صفحة جداول</title>
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/home.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle" id="sidebar-toggle" style="cursor: pointer;">
            </div>
        </div>
    </header>

    {% include 'includes/sidebar.html' %}

    <main>
        <div class="home-container">
            <h2>صفحة جداول</h2>
            
            <div class="menu-grid">
                <a href="{% url 'forest_fires_table' %}" class="menu-item">
                    <div class="menu-icon">
                        <i class="fas fa-tree"></i>
                    </div>
                    <div class="menu-title">جدول حرائق الغابات</div>
                    <div class="menu-description">
                        عرض وإدارة جدول حرائق الغابات
                    </div>
                </a>

                <a href="{% url 'crop_fires_table' %}" class="menu-item">
                    <div class="menu-icon">
                        <i class="fas fa-seedling"></i>
                    </div>
                    <div class="menu-title">جدول حرائق المحاصيل الزراعية</div>
                    <div class="menu-description">
                        عرض وإدارة جدول حرائق المحاصيل الزراعية
                    </div>
                </a>
            </div>

            <div class="action-buttons">
                <a href="{% url 'forest_crop_fires' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i>
                    العودة إلى حرائق الغابات والمحاصيل
                </a>
                <a href="{% url 'coordination_center' %}" class="btn btn-primary">
                    <i class="fas fa-home"></i>
                    العودة إلى مركز التنسيق
                </a>
            </div>
        </div>
    </main>

    <footer>
        <p>حقوق النشر © 2025 جميع الحقوق محفوظة - تم التطوير بواسطة عبد الرزاق مختاري</p>
    </footer>

    <script src="{% static 'js/sidebar.js' %}"></script>

    <style>
        .menu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .menu-item {
            background-color: #f5f5f5;
            border-radius: 8px;
            padding: 25px;
            text-decoration: none;
            color: #333;
            transition: transform 0.3s, box-shadow 0.3s;
            display: flex;
            flex-direction: column;
            align-items: center;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .menu-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-decoration: none;
            color: #333;
        }

        .menu-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            color: #0d47a1;
        }

        .menu-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .menu-description {
            font-size: 0.9rem;
            color: #666;
            text-align: center;
        }

        .action-buttons {
            text-align: center;
            margin-top: 30px;
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            transition: background-color 0.3s;
        }

        .btn-primary {
            background-color: #0d47a1;
        }

        .btn-primary:hover {
            background-color: #083378;
            color: white;
        }

        .btn-secondary {
            background-color: #6c757d;
        }

        .btn-secondary:hover {
            background-color: #545b62;
            color: white;
        }

        .btn i {
            margin-left: 8px;
        }
    </style>
</body>
</html>
