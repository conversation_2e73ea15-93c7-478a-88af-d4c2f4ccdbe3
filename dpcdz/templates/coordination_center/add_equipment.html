{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة وسيلة جديدة - نظام الحماية المدنية</title>
    
    <!-- نظام CSS الموحد الجديد -->
    <link rel="stylesheet" href="{% static 'css/dpc-unified.css' %}">
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/home.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
</head>

<body>
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle clickable" id="sidebar-toggle">
            </div>
        </div>
    </header>

    {% include 'includes/sidebar.html' %}

    <main>
        <div class="home-container">
            <!-- العنوان الرئيسي -->
            <div class="unit-count-details">
                <div class="unit-count-icon">
                    <i class="fas fa-truck"></i>
                </div>
                <div class="unit-count-title">إضافة وسيلة جديدة</div>
                <div class="unit-count-subtitle">وحدة: {{ selected_unit.name }}</div>
            </div>

            <!-- عرض الرسائل -->
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-triangle{% else %}info-circle{% endif %}"></i>
                        {{ message }}
                        <button type="button" class="close" data-dismiss="alert">
                            <span>&times;</span>
                        </button>
                    </div>
                {% endfor %}
            {% endif %}

            <!-- نموذج إضافة الوسيلة -->
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-truck"></i> بيانات الوسيلة الجديدة</h3>
                </div>
                <div class="card-body">
                    <form method="post" id="addEquipmentForm">
                        {% csrf_token %}
                        
                        <div class="row">
                            <!-- نوع الوسيلة -->
                            <div class="col-12">
                                <div class="form-group">
                                    <label class="form-label required">
                                        <i class="fas fa-truck text-primary"></i> نوع الوسيلة
                                    </label>
                                    <select name="equipment_type" class="form-control" id="equipmentTypeSelect" required>
                                        <option value="">-- اختر نوع الوسيلة --</option>
                                        <optgroup label="وسائل الإطفاء">
                                            <option value="شاحنة إطفاء">شاحنة إطفاء</option>
                                            <option value="شاحنة مياه">شاحنة مياه</option>
                                            <option value="سيارة إطفاء خفيفة">سيارة إطفاء خفيفة</option>
                                        </optgroup>
                                        <optgroup label="وسائل الإسعاف">
                                            <option value="سيارة إسعاف">سيارة إسعاف</option>
                                            <option value="سيارة إسعاف متقدمة">سيارة إسعاف متقدمة</option>
                                        </optgroup>
                                        <optgroup label="وسائل النقل">
                                            <option value="سيارة خفيفة">سيارة خفيفة</option>
                                            <option value="حافلة نقل">حافلة نقل</option>
                                            <option value="دراجة نارية">دراجة نارية</option>
                                        </optgroup>
                                        <optgroup label="وسائل جوية">
                                            <option value="طائرة هليكوبتر">طائرة هليكوبتر</option>
                                            <option value="طائرة إطفاء">طائرة إطفاء</option>
                                        </optgroup>
                                        <optgroup label="وسائل بحرية">
                                            <option value="قارب إنقاذ">قارب إنقاذ</option>
                                            <option value="سفينة إطفاء">سفينة إطفاء</option>
                                        </optgroup>
                                        <optgroup label="معدات خاصة">
                                            <option value="رافعة">رافعة</option>
                                            <option value="جرافة">جرافة</option>
                                            <option value="مولد كهربائي">مولد كهربائي</option>
                                            <option value="ضاغط هواء">ضاغط هواء</option>
                                            <option value="أخرى">أخرى</option>
                                        </optgroup>
                                        {% if user_role == 'admin' or user_role == 'wilaya_manager' %}
                                        <optgroup label="إضافة جديد">
                                            <option value="add_new_type">إضافة نوع وسيلة جديد</option>
                                        </optgroup>
                                        {% endif %}
                                    </select>
                                    <small class="form-text text-muted">اختر نوع الوسيلة من القائمة</small>
                                </div>
                            </div>
                        </div>

                        <!-- حقل إضافة نوع جديد -->
                        <div class="row d-none" id="newTypeRow">
                            <div class="col-12">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-plus text-primary"></i> نوع الوسيلة الجديد
                                    </label>
                                    <input type="text" name="new_equipment_type" class="form-control" 
                                           placeholder="أدخل نوع الوسيلة الجديد">
                                    <small class="form-text text-muted">سيتم إضافة هذا النوع للقائمة</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- رقم الراديو -->
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-broadcast-tower text-primary"></i> رقم الراديو
                                    </label>
                                    <input type="text" name="radio_number" class="form-control" 
                                           placeholder="أدخل رقم الراديو (اختياري)">
                                    <small class="form-text text-muted">رقم الراديو الخاص بالوسيلة</small>
                                </div>
                            </div>

                            <!-- رقم اللوحة -->
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-id-card text-primary"></i> رقم اللوحة
                                    </label>
                                    <input type="text" name="license_plate" class="form-control" 
                                           placeholder="أدخل رقم اللوحة (اختياري)">
                                    <small class="form-text text-muted">رقم لوحة الوسيلة</small>
                                </div>
                            </div>
                        </div>

                        <!-- تنبيه مهم -->
                        <div class="alert alert-info" role="alert">
                            <i class="fas fa-info-circle"></i>
                            <strong>ملاحظة مهمة:</strong>
                            بعد إضافة الوسيلة، ستظهر في جدول التعداد الصباحي كل يوم. يمكنك تغيير حالتها (تعمل/معطلة/تحت الصيانة) من الجدول مباشرة.
                        </div>

                        <!-- أزرار العمل -->
                        <div class="action-buttons">
                            <a href="{% url 'daily_unit_count' %}?unit_id={{ selected_unit.id }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right"></i> العودة للتعداد الصباحي
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-truck"></i> إضافة الوسيلة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>

    <script src="{% static 'js/sidebar.js' %}"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            const equipmentTypeSelect = document.getElementById('equipmentTypeSelect');
            const newTypeRow = document.getElementById('newTypeRow');

            // إدارة إضافة نوع وسيلة جديد
            if (equipmentTypeSelect) {
                equipmentTypeSelect.addEventListener('change', function() {
                    if (this.value === 'add_new_type') {
                        newTypeRow.classList.remove('d-none');
                        const newTypeInput = document.querySelector('input[name="new_equipment_type"]');
                        if (newTypeInput) {
                            newTypeInput.required = true;
                            newTypeInput.focus();
                        }
                    } else {
                        newTypeRow.classList.add('d-none');
                        const newTypeInput = document.querySelector('input[name="new_equipment_type"]');
                        if (newTypeInput) {
                            newTypeInput.required = false;
                            newTypeInput.value = '';
                        }
                    }
                });
            }

            // التحقق من صحة النموذج
            const form = document.getElementById('addEquipmentForm');
            form.addEventListener('submit', function(e) {
                const equipmentType = document.querySelector('select[name="equipment_type"]').value;
                const newEquipmentType = document.querySelector('input[name="new_equipment_type"]').value.trim();

                if (!equipmentType) {
                    e.preventDefault();
                    alert('يرجى اختيار نوع الوسيلة');
                    return false;
                }

                if (equipmentType === 'add_new_type' && !newEquipmentType) {
                    e.preventDefault();
                    alert('يرجى إدخال نوع الوسيلة الجديد');
                    return false;
                }
            });

            // إخفاء الرسائل تلقائياً وإغلاق النافذة عند النجاح
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert');
                alerts.forEach(function(alert) {
                    if (alert.classList.contains('alert-success')) {
                        alert.style.opacity = '0';
                        setTimeout(function() {
                            alert.remove();
                            // إغلاق النافذة وتحديث النافذة الأصلية
                            if (window.opener) {
                                window.opener.focus();
                                // تحديث النافذة الأصلية بعد ثانية
                                setTimeout(function() {
                                    window.opener.location.reload();
                                }, 1000);
                            }
                            window.close();
                        }, 1000);
                    }
                });
            }, 2000);

            // إضافة زر إغلاق النافذة
            const closeButton = document.createElement('button');
            closeButton.innerHTML = '<i class="fas fa-times"></i> إغلاق النافذة';
            closeButton.className = 'btn btn-secondary';
            closeButton.style.position = 'fixed';
            closeButton.style.top = '20px';
            closeButton.style.left = '20px';
            closeButton.style.zIndex = '1000';
            closeButton.onclick = function() {
                window.close();
            };
            document.body.appendChild(closeButton);
        });
    </script>
</body>
</html>
