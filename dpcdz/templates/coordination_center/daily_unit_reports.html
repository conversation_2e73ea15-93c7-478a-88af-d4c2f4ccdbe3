{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقارير الحضور والمعدات</title>
    <!-- نظام CSS الموحد الجديد -->
    <link rel="stylesheet" href="{% static 'css/dpc-unified.css' %}">
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/home.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }



        .reports-container {
            max-width: 1400px;
            width: 100%;
            padding: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            margin: 20px auto;
        }

        .page-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #007bff;
        }

        .page-title {
            color: #2c3e50;
            font-size: 2.2rem;
            font-weight: 700;
            font-family: 'Cairo', sans-serif;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .page-title i {
            font-size: 1.8rem;
            color: #007bff;
        }

        .page-header p {
            color: #6c757d;
            font-size: 1.1rem;
            margin: 0;
            font-family: 'Cairo', sans-serif;
        }

        .filters-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.08);
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
        }

        .filter-row {
            display: flex;
            gap: 20px;
            align-items: end;
            flex-wrap: wrap;
            justify-content: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .filter-group {
            flex: 1;
            min-width: 200px;
        }

        .filter-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #495057;
            font-family: 'Cairo', sans-serif;
        }

        .filter-group input,
        .filter-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            font-family: 'Cairo', sans-serif;
        }

        .btn-group {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
            font-size: 14px;
        }

        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .btn-warning { background: #ffc107; color: black; }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .table-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
            margin: 0 auto 30px auto;
            overflow: hidden;
            max-width: 1300px;
            border: 1px solid #e9ecef;
        }

        .table-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-header h3 {
            margin: 0;
            font-size: 1.4rem;
            display: flex;
            align-items: center;
            gap: 10px;
            font-family: 'Cairo', sans-serif;
            font-weight: 700;
        }

        .table-responsive {
            max-height: 500px;
            overflow-y: auto;
            margin: 0;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
            background: white;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #dee2e6;
            font-family: 'Cairo', sans-serif;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 700;
            position: sticky;
            top: 0;
            z-index: 10;
            font-family: 'Cairo', sans-serif;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .status-present { color: #28a745; font-weight: bold; }
        .status-absent { color: #dc3545; font-weight: bold; }
        .status-mission { color: #ffc107; font-weight: bold; }
        .status-operational { color: #28a745; font-weight: bold; }
        .status-broken { color: #dc3545; font-weight: bold; }
        .status-maintenance { color: #ffc107; font-weight: bold; }

        .action-buttons {
            display: flex;
            gap: 5px;
            justify-content: center;
        }

        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            color: #6c757d;
            font-family: 'Cairo', sans-serif;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        .empty-state p {
            font-family: 'Cairo', sans-serif;
            font-size: 1.1rem;
        }

        @media (max-width: 768px) {
            .main-container {
                margin-left: 0;
                padding: 10px;
            }

            .reports-container {
                padding: 20px;
                margin: 10px;
            }

            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }

            .filter-group {
                min-width: auto;
            }

            .btn-group {
                flex-direction: column;
            }

            .table-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
                padding: 20px;
            }

            .table-section {
                margin: 0 0 20px 0;
            }

            .page-title {
                font-size: 1.8rem;
            }

            .page-title i {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle clickable" id="sidebar-toggle">
            </div>
        </div>
    </header>

    {% include 'includes/sidebar.html' %}

    <main>
        <div class="reports-container">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-chart-bar"></i>
                    تقارير الحضور والمعدات
                </h1>
                <p>نظام شامل لمتابعة حضور الأعوان وحالة المعدات</p>
            </div>

            <!-- فلاتر البحث -->
            <div class="filters-section">
                <form method="GET" id="filtersForm">
                    <div class="filter-row">
                        <div class="filter-group">
                            <label for="unit_id">الوحدة</label>
                            <select name="unit_id" id="unit_id">
                                <option value="">جميع الوحدات</option>
                                {% for unit in units %}
                                <option value="{{ unit.id }}" {% if unit.id|stringformat:"s" == selected_unit_id %}selected{% endif %}>
                                    {{ unit.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="date_from">من تاريخ</label>
                            <input type="date" name="date_from" id="date_from" value="{{ date_from }}">
                        </div>
                        
                        <div class="filter-group">
                            <label for="date_to">إلى تاريخ</label>
                            <input type="date" name="date_to" id="date_to" value="{{ date_to }}">
                        </div>
                        
                        <div class="filter-group">
                            <label for="report_type">نوع التقرير</label>
                            <select name="report_type" id="report_type">
                                <option value="daily" {% if report_type == 'daily' %}selected{% endif %}>يومي</option>
                                <option value="monthly" {% if report_type == 'monthly' %}selected{% endif %}>شهري</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <div class="btn-group">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="resetFilters()">
                                    <i class="fas fa-undo"></i> إعادة تعيين
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- جدول الأشخاص -->
            <div class="table-section">
                <div class="table-header">
                    <h3>
                        <i class="fas fa-users"></i>
                        تقرير حضور الأشخاص
                    </h3>
                    <div class="btn-group">
                        <button class="btn btn-success btn-sm" onclick="exportPersonnelExcel()">
                            <i class="fas fa-file-excel"></i> Excel
                        </button>
                        <button class="btn btn-info btn-sm" onclick="exportPersonnelPDF()">
                            <i class="fas fa-file-pdf"></i> PDF
                        </button>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>رقم القيد</th>
                                <th>الاسم الكامل</th>
                                <th>الرتبة</th>
                                <th>المنصب</th>
                                <th>الوحدة</th>
                                <th>أيام الحضور</th>
                                <th>أيام الغياب</th>
                                <th>أيام المهام</th>
                                <th>إجمالي الأيام</th>
                                <th>معدل الحضور</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for detail in personnel_details %}
                            <tr>
                                <td>{{ detail.personnel.registration_number }}</td>
                                <td>{{ detail.personnel.full_name }}</td>
                                <td>{{ detail.personnel.rank|default:"–" }}</td>
                                <td>{{ detail.personnel.position|default:"–" }}</td>
                                <td>{{ detail.unit.name }}</td>
                                <td class="status-present">{{ detail.present_days }}</td>
                                <td class="status-absent">{{ detail.absent_days }}</td>
                                <td class="status-mission">{{ detail.mission_days }}</td>
                                <td>{{ detail.total_days }}</td>
                                <td class="status-present">{{ detail.attendance_rate }}%</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-success btn-sm" onclick="exportPersonExcel({{ detail.personnel.id }})">
                                            <i class="fas fa-file-excel"></i>
                                        </button>
                                        <button class="btn btn-info btn-sm" onclick="exportPersonPDF({{ detail.personnel.id }})">
                                            <i class="fas fa-file-pdf"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="11">
                                    <div class="empty-state">
                                        <i class="fas fa-users"></i>
                                        <p>لا توجد بيانات أشخاص للفترة المحددة</p>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- جدول المعدات -->
            <div class="table-section">
                <div class="table-header">
                    <h3>
                        <i class="fas fa-truck"></i>
                        تقرير حالة المعدات
                    </h3>
                    <div class="btn-group">
                        <button class="btn btn-success btn-sm" onclick="exportEquipmentExcel()">
                            <i class="fas fa-file-excel"></i> Excel
                        </button>
                        <button class="btn btn-info btn-sm" onclick="exportEquipmentPDF()">
                            <i class="fas fa-file-pdf"></i> PDF
                        </button>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>الرقم التسلسلي</th>
                                <th>نوع الوسيلة</th>
                                <th>رقم الراديو</th>
                                <th>الوحدة</th>
                                <th>أيام التشغيل</th>
                                <th>أيام التعطل</th>
                                <th>أيام الصيانة</th>
                                <th>إجمالي الأيام</th>
                                <th>معدل التشغيل</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for equipment in equipment_details %}
                            <tr>
                                <td>{{ equipment.equipment.serial_number }}</td>
                                <td>{{ equipment.equipment.equipment_type }}</td>
                                <td>{{ equipment.equipment.radio_number|default:"–" }}</td>
                                <td>{{ equipment.unit.name }}</td>
                                <td class="status-operational">{{ equipment.operational_days }}</td>
                                <td class="status-broken">{{ equipment.broken_days }}</td>
                                <td class="status-maintenance">{{ equipment.maintenance_days }}</td>
                                <td>{{ equipment.total_days }}</td>
                                <td class="status-operational">{{ equipment.operational_rate }}%</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-success btn-sm" onclick="exportEquipmentExcel({{ equipment.equipment.id }})">
                                            <i class="fas fa-file-excel"></i>
                                        </button>
                                        <button class="btn btn-info btn-sm" onclick="exportEquipmentPDF({{ equipment.equipment.id }})">
                                            <i class="fas fa-file-pdf"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="10">
                                    <div class="empty-state">
                                        <i class="fas fa-truck"></i>
                                        <p>لا توجد بيانات معدات للفترة المحددة</p>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <script src="{% static 'js/sidebar.js' %}"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        // وظائف التصدير للأشخاص
        function exportPersonnelExcel() {
            const form = document.getElementById('filtersForm');
            const formData = new FormData(form);
            formData.append('export', 'personnel_excel');

            const params = new URLSearchParams(formData);
            const exportUrl = window.location.pathname + '?' + params.toString();
            window.open(exportUrl, '_blank');
        }

        function exportPersonnelPDF() {
            const form = document.getElementById('filtersForm');
            const formData = new FormData(form);
            formData.append('export', 'personnel_pdf');

            const params = new URLSearchParams(formData);
            const exportUrl = window.location.pathname + '?' + params.toString();
            window.open(exportUrl, '_blank');
        }

        function exportPersonExcel(personId) {
            const form = document.getElementById('filtersForm');
            const formData = new FormData(form);
            formData.append('export', 'person_excel');
            formData.append('person_id', personId);

            const params = new URLSearchParams(formData);
            const exportUrl = window.location.pathname + '?' + params.toString();
            window.open(exportUrl, '_blank');
        }

        function exportPersonPDF(personId) {
            const form = document.getElementById('filtersForm');
            const formData = new FormData(form);
            formData.append('export', 'person_pdf');
            formData.append('person_id', personId);

            const params = new URLSearchParams(formData);
            const exportUrl = window.location.pathname + '?' + params.toString();
            window.open(exportUrl, '_blank');
        }

        // وظائف التصدير للمعدات
        function exportEquipmentExcel() {
            const form = document.getElementById('filtersForm');
            const formData = new FormData(form);
            formData.append('export', 'equipment_excel');

            const params = new URLSearchParams(formData);
            const exportUrl = window.location.pathname + '?' + params.toString();
            window.open(exportUrl, '_blank');
        }

        function exportEquipmentPDF() {
            const form = document.getElementById('filtersForm');
            const formData = new FormData(form);
            formData.append('export', 'equipment_pdf');

            const params = new URLSearchParams(formData);
            const exportUrl = window.location.pathname + '?' + params.toString();
            window.open(exportUrl, '_blank');
        }

        function exportEquipmentExcel(equipmentId) {
            const form = document.getElementById('filtersForm');
            const formData = new FormData(form);
            formData.append('export', 'equipment_individual_excel');
            formData.append('equipment_id', equipmentId);

            const params = new URLSearchParams(formData);
            const exportUrl = window.location.pathname + '?' + params.toString();
            window.open(exportUrl, '_blank');
        }

        function exportEquipmentPDF(equipmentId) {
            const form = document.getElementById('filtersForm');
            const formData = new FormData(form);
            formData.append('export', 'equipment_individual_pdf');
            formData.append('equipment_id', equipmentId);

            const params = new URLSearchParams(formData);
            const exportUrl = window.location.pathname + '?' + params.toString();
            window.open(exportUrl, '_blank');
        }

        // وظائف أخرى
        function resetFilters() {
            document.getElementById('filtersForm').reset();
            window.location.href = window.location.pathname;
        }

        // تحديث تلقائي للتواريخ
        $(document).ready(function() {
            // تعيين التاريخ الحالي إذا لم يكن محدد
            if (!$('#date_from').val()) {
                const today = new Date();
                const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
                $('#date_from').val(firstDay.toISOString().split('T')[0]);
            }

            if (!$('#date_to').val()) {
                const today = new Date();
                $('#date_to').val(today.toISOString().split('T')[0]);
            }
        });
    </script>
