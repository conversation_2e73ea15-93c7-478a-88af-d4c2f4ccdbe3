{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>الحماية المدنية الجزائرية - نظام التحقق الصباحي الموحد</title>
    <!-- نظام CSS الموحد الجديد -->
    <link rel="stylesheet" href="{% static 'css/dpc-unified.css' %}">
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/home.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle" id="sidebar-toggle" style="cursor: pointer;">
            </div>
        </div>
    </header>

    {% include 'includes/sidebar.html' %}

    <main>
        <div class="unified-container">
            {% csrf_token %}
            
            <!-- العنوان والأدوات -->
            <div class="header-section">
                <div class="unit-count-icon">
                    <i class="fas fa-sun"></i>
                </div>
                <h1 class="unit-count-title">نظام التحقق الصباحي الموحد</h1>
                <p class="unit-count-subtitle">النظام الشامل الجديد لإدارة الأعوان والوسائل والتوزيع - {{ selected_date|date:"d/m/Y" }}</p>
                <div class="system-badge">
                    <span class="badge badge-success">
                        <i class="fas fa-check-circle"></i> النظام الموحد الجديد
                    </span>
                </div>
                
                <div class="controls-enhanced">
                    <!-- الصف الأول: التحكم الأساسي -->
                    <div class="control-row primary-controls">
                        <div class="control-item">
                            <label for="unitSelect">
                                <i class="fas fa-building"></i>
                                الوحدة
                            </label>
                            <select id="unitSelect" class="form-control enhanced-select" onchange="changeUnit()">
                                {% for unit in units %}
                                <option value="{{ unit.id }}" {% if unit.id == selected_unit.id %}selected{% endif %}>
                                    {{ unit.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="control-item">
                            <label for="dateSelect">
                                <i class="fas fa-calendar"></i>
                                التاريخ
                            </label>
                            <input type="date" id="dateSelect" class="form-control enhanced-input"
                                   value="{{ selected_date|date:'Y-m-d' }}" onchange="changeDate()">
                        </div>

                        <div class="control-item shift-info">
                            <label>
                                <i class="fas fa-users-cog"></i>
                                الفرقة العاملة
                            </label>
                            <div id="currentShiftInfo" class="current-shift-display">
                                <div class="shift-badge" id="shiftBadge">
                                    <i class="fas fa-users"></i>
                                    <span id="shiftName">جاري التحميل...</span>
                                </div>
                                <small id="shiftTime" class="text-muted d-block">--</small>
                            </div>
                        </div>
                    </div>

                    <!-- الصف الثاني: أزرار التنقل -->
                    <div class="control-row navigation-controls">
                        <a href="{% url 'coordination_center' %}" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-right"></i>
                            مركز التنسيق
                        </a>

                        <button class="btn btn-outline-info" onclick="createShiftSchedule()" title="إنشاء جدولة الفرق">
                            <i class="fas fa-calendar-plus"></i>
                            إنشاء جدولة
                        </button>

                        <a href="{% url 'daily_unit_count' %}?unit_id={{ selected_unit.id }}" class="btn btn-outline-secondary">
                            <i class="fas fa-list-alt"></i>
                            النظام التقليدي
                        </a>
                    </div>
                </div>
            </div>
            


            {% if error %}
            <div class="alert alert-warning text-center">
                <i class="fas fa-exclamation-triangle"></i> {{ error }}
            </div>
            {% else %}
            
            <!-- بطاقات الملخص السريع -->
            <div class="summary-cards-row">
                <div class="summary-card personnel-card">
                    <div class="card-header">
                        <h4><i class="fas fa-users"></i> الأعوان</h4>
                    </div>
                    <div class="card-body">
                        <div class="stats">
                            <div class="stat-item">
                                <span class="stat-value">{{ stats.total_personnel }}</span>
                                <span class="stat-label">إجمالي</span>
                            </div>
                            <div class="stat-item present">
                                <span class="stat-value">{{ stats.present_personnel }}</span>
                                <span class="stat-label">حاضر</span>
                            </div>
                            <div class="stat-item absent">
                                <span class="stat-value">{{ stats.absent_personnel }}</span>
                                <span class="stat-label">غائب</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="summary-card vehicles-card">
                    <div class="card-header">
                        <h4><i class="fas fa-truck"></i> الوسائل</h4>
                    </div>
                    <div class="card-body">
                        <div class="stats">
                            <div class="stat-item">
                                <span class="stat-value">{{ stats.total_vehicles }}</span>
                                <span class="stat-label">إجمالي</span>
                            </div>
                            <div class="stat-item ready">
                                <span class="stat-value">{{ stats.ready_vehicles }}</span>
                                <span class="stat-label">جاهز</span>
                            </div>
                            <div class="stat-item not-ready">
                                <span class="stat-value">{{ stats.not_ready_vehicles }}</span>
                                <span class="stat-label">غير جاهز</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="summary-card readiness-card">
                    <div class="card-header">
                        <h4><i class="fas fa-percentage"></i> الجاهزية العامة</h4>
                    </div>
                    <div class="card-body">
                        <div class="readiness-score" data-score="{{ stats.overall_readiness }}">
                            {{ stats.overall_readiness }}%
                        </div>
                        <div class="readiness-label">نسبة الجاهزية</div>
                    </div>
                </div>
            </div>
            
            <!-- الفرق الثلاث -->
            <div class="shifts-section">
                <h3><i class="fas fa-users-cog"></i> الفرق العاملة (نظام 24 ساعة)</h3>
                <div class="shifts-grid">
                    {% for shift_key, shift_data in stats.shift_stats.items %}
                    <div class="shift-card {{ shift_key }}">
                        <div class="shift-header">
                            <h5>{{ shift_data.name }}</h5>
                            <div class="shift-badge">
                                {% if shift_key == 'shift_1' %}
                                    <i class="fas fa-users" style="color: #007bff;"></i>
                                {% elif shift_key == 'shift_2' %}
                                    <i class="fas fa-user-friends" style="color: #28a745;"></i>
                                {% else %}
                                    <i class="fas fa-people-group" style="color: #fd7e14;"></i>
                                {% endif %}
                            </div>
                        </div>
                        <div class="shift-stats">
                            <div class="shift-count">{{ shift_data.total }} أعوان</div>
                            <div class="shift-present">{{ shift_data.present }} حاضر</div>
                        </div>
                        <div class="shift-progress">
                            {% if shift_data.total > 0 %}
                                {% widthratio shift_data.present shift_data.total 100 as percentage %}
                                <div class="progress-bar" style="width: {{ percentage }}%"></div>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            
            <!-- أزرار الإجراءات الرئيسية المحسنة -->
            <div class="main-actions-grid">
                <!-- الصف الأول: الإجراءات الأساسية -->
                <div class="action-row primary-actions">
                    <button class="action-btn-large btn-add-personnel" onclick="openAddPersonnelPage()">
                        <div class="btn-icon">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="btn-text">
                            <h4>إضافة عون</h4>
                            <small>إضافة عون جديد للوحدة</small>
                        </div>
                    </button>

                    <button class="action-btn-large btn-add-equipment" onclick="openAddEquipmentPage()">
                        <div class="btn-icon">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div class="btn-text">
                            <h4>إضافة وسيلة</h4>
                            <small>إضافة وسيلة جديدة</small>
                        </div>
                    </button>

                    <button class="action-btn-large btn-assignments" onclick="openAssignmentPage()">
                        <div class="btn-icon">
                            <i class="fas fa-users-cog"></i>
                        </div>
                        <div class="btn-text">
                            <h4>توزيع الأعوان</h4>
                            <small>توزيع الأعوان على الوسائل</small>
                        </div>
                    </button>

                    <button class="action-btn-large btn-schedule" onclick="openShiftSchedulePage()">
                        <div class="btn-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="btn-text">
                            <h4>جدولة الفرق</h4>
                            <small>إدارة جدولة الفرق العاملة</small>
                        </div>
                    </button>
                </div>

                <!-- الصف الثاني: الأدوات المساعدة -->
                <div class="action-row secondary-actions">
                    <button class="action-btn-small btn-interventions" onclick="openDailyInterventionsPage()">
                        <i class="fas fa-ambulance"></i>
                        <span>التدخلات اليومية</span>
                    </button>

                    <button class="action-btn-small btn-roles" onclick="openManageRolesPage()">
                        <i class="fas fa-user-cog"></i>
                        <span>إدارة الرتب</span>
                    </button>

                    <button class="action-btn-small btn-refresh" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i>
                        <span>تحديث البيانات</span>
                    </button>

                    <button class="action-btn-small btn-test" onclick="runFullSystemTest()">
                        <i class="fas fa-cogs"></i>
                        <span>اختبار شامل</span>
                    </button>
                </div>
            </div>
            
            <!-- الجدول الرئيسي الموحد -->
            <div class="main-table-section">
                <!-- الأزرار الرئيسية للتنقل -->
                <div class="navigation-actions">
                    <button class="action-btn personnel-btn active" id="personnel-btn" onclick="showSection('personnel')">
                        <div class="btn-content-inline">
                            <i class="fas fa-users"></i>
                            <h3>الأعوان</h3>
                        </div>
                    </button>

                    <button class="action-btn vehicles-btn" id="vehicles-btn" onclick="showSection('vehicles')">
                        <div class="btn-content-inline">
                            <i class="fas fa-truck"></i>
                            <h3>الوسائل</h3>
                        </div>
                    </button>

                    <button class="action-btn assignments-btn" id="assignments-btn" onclick="navigateToAssignments()">
                        <div class="btn-content-inline">
                            <i class="fas fa-users-cog"></i>
                            <h3>توزيع الوسائل</h3>
                        </div>
                    </button>

                    <button class="action-btn interventions-btn" id="interventions-btn" onclick="navigateToInterventions()">
                        <div class="btn-content-inline">
                            <i class="fas fa-ambulance"></i>
                            <h3>التدخلات اليومية</h3>
                        </div>
                    </button>
                </div>
                
                <!-- محتوى التبويبات -->
                <div class="tab-content">
                    <!-- تبويب الأعوان -->
                    <div class="tab-pane active" id="personnel-tab">
                        <div class="table-header">
                            <h4><i class="fas fa-users"></i> إدارة الأعوان</h4>
                            <div class="table-controls-enhanced">
                                <!-- فلاتر محسنة في صف واحد -->
                                <div class="filters-container">
                                    <div class="filter-group search-group">
                                        <label class="filter-label">
                                            <i class="fas fa-search"></i>
                                            البحث
                                        </label>
                                        <input type="text" id="personnelSearch" class="form-control filter-input"
                                               placeholder="ابحث في الأعوان...">
                                    </div>

                                    <div class="filter-group status-group">
                                        <label class="filter-label">
                                            <i class="fas fa-user-check"></i>
                                            الحالة
                                        </label>
                                        <select id="personnelFilter" class="form-control filter-select">
                                            <option value="">الكل</option>
                                            <option value="present">حاضر</option>
                                            <option value="absent">غائب</option>
                                            <option value="on_mission">في مهمة</option>
                                            <option value="standby">احتياطي</option>
                                        </select>
                                    </div>

                                    <div class="filter-group shift-group">
                                        <label class="filter-label">
                                            <i class="fas fa-users"></i>
                                            الفرقة
                                        </label>
                                        <select id="shiftFilter" class="form-control filter-select">
                                            <option value="">الكل</option>
                                            <option value="shift_1">الأولى (A)</option>
                                            <option value="shift_2">الثانية (B)</option>
                                            <option value="shift_3">الثالثة (C)</option>
                                        </select>
                                    </div>

                                    <div class="filter-group system-group">
                                        <label class="filter-label">
                                            <i class="fas fa-clock"></i>
                                            النظام
                                        </label>
                                        <select id="workSystemFilter" class="form-control filter-select">
                                            <option value="">الكل</option>
                                            <option value="24_hours">24 ساعة</option>
                                            <option value="8_hours">8 ساعات</option>
                                        </select>
                                    </div>

                                    <div class="filter-actions">
                                        <button class="btn btn-outline-secondary btn-sm" onclick="clearAllFilters()">
                                            <i class="fas fa-times"></i>
                                            مسح الفلاتر
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="table-responsive">
                            <table id="personnelTable" class="unified-table">
                                <thead>
                                    <tr>
                                        <th><i class="fas fa-id-card"></i> رقم التسجيل</th>
                                        <th><i class="fas fa-user"></i> الاسم الكامل</th>
                                        <th><i class="fas fa-venus-mars"></i> الجنس</th>
                                        <th><i class="fas fa-birthday-cake"></i> العمر</th>
                                        <th><i class="fas fa-calendar-plus"></i> تاريخ الالتحاق</th>
                                        <th><i class="fas fa-phone"></i> الهاتف</th>
                                        <th><i class="fas fa-clock"></i> نظام العمل</th>
                                        <th><i class="fas fa-users"></i> الفرقة</th>
                                        <th><i class="fas fa-check-circle"></i> الحالة</th>
                                        <th><i class="fas fa-business-time"></i> حالة العمل</th>
                                        <th><i class="fas fa-cogs"></i> إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for person in personnel %}
                                    <tr data-personnel-id="{{ person.id }}" data-status="{{ person.daily_status.status }}">
                                        <td>
                                            <strong>{{ person.registration_number|default:"غير محدد" }}</strong>
                                        </td>
                                        <td>
                                            <div class="personnel-info">
                                                <strong>{{ person.full_name }}</strong>
                                                {% if person.rank %}
                                                    <br><small class="text-muted">{{ person.rank }}</small>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            {% if person.gender %}
                                                <i class="{{ person.get_gender_icon }} 
                                                   {% if person.gender == 'male' %}text-primary{% else %}text-danger{% endif %}"></i>
                                                {{ person.get_gender_display }}
                                            {% else %}
                                                <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {{ person.age|default:"غير محدد" }}
                                        </td>
                                        <td>
                                            {% if person.joining_date %}
                                                <span class="text-primary">
                                                    <i class="fas fa-calendar-plus"></i>
                                                    {{ person.joining_date|date:"Y/m/d" }}
                                                </span>
                                                {% if person.years_of_service %}
                                                    <br><small class="text-muted">{{ person.years_of_service }} سنة خدمة</small>
                                                {% endif %}
                                            {% else %}
                                                <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if person.phone_number %}
                                                <a href="tel:{{ person.phone_number }}" class="phone-link">
                                                    {{ person.phone_number }}
                                                </a>
                                            {% else %}
                                                <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge {{ person.get_work_system_badge_class }}">
                                                {{ person.get_work_system_display }}
                                            </span>
                                        </td>
                                        <td>
                                            {% if person.assigned_shift %}
                                                <span class="badge {{ person.get_shift_badge_class }}">
                                                    {{ person.get_shift_display_arabic }}
                                                </span>
                                            {% else %}
                                                <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <select class="form-control status-select personnel-status-select" data-id="{{ person.id }}" data-type="personnel" onchange="updatePersonnelStatus(this)">
                                                <option value="present" {% if person.daily_status.status == 'present' %}selected{% endif %}>
                                                    <i class="fas fa-user-check text-success"></i> حاضر
                                                </option>
                                                <option value="absent" {% if person.daily_status.status == 'absent' %}selected{% endif %}>
                                                    <i class="fas fa-user-times text-danger"></i> غائب
                                                </option>
                                                <option value="on_mission" {% if person.daily_status.status == 'on_mission' %}selected{% endif %}>
                                                    <i class="fas fa-user-cog text-warning"></i> في مهمة
                                                </option>
                                                <option value="standby" {% if person.daily_status.status == 'standby' %}selected{% endif %}>
                                                    <i class="fas fa-user-shield text-warning"></i> احتياطي
                                                </option>
                                            </select>
                                        </td>
                                        <td>
                                            <span class="badge {{ person.get_work_status_badge_class }} work-status" data-personnel-id="{{ person.id }}">
                                                {% if person.get_work_status_today == 'working' %}
                                                    <i class="fas fa-briefcase"></i> {{ person.get_work_status_display }}
                                                {% elif person.get_work_status_today == 'resting' %}
                                                    <i class="fas fa-bed"></i> {{ person.get_work_status_display }}
                                                {% elif person.get_work_status_today == 'unassigned' %}
                                                    <i class="fas fa-question-circle"></i> {{ person.get_work_status_display }}
                                                {% else %}
                                                    <i class="fas fa-clock"></i> {{ person.get_work_status_display }}
                                                {% endif %}
                                            </span>
                                            <br>
                                            <small class="text-muted">
                                                {% if person.work_system == '24_hours' %}
                                                    24 ساعة - {{ person.get_shift_display_arabic }}
                                                {% else %}
                                                    8 ساعات (أحد-خميس)
                                                {% endif %}
                                            </small>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                {% if person.work_system == '24_hours' %}
                                                <button class="btn btn-sm btn-warning" onclick="transferPersonnel({{ person.id }})"
                                                        title="تحويل فرقة">
                                                    <i class="fas fa-exchange-alt"></i>
                                                </button>
                                                {% endif %}
                                                <button class="btn btn-sm btn-secondary" onclick="changeWorkSystem({{ person.id }}, '{{ person.work_system }}')"
                                                        title="تغيير نظام العمل">
                                                    <i class="fas fa-clock"></i>
                                                </button>
                                                <button class="btn btn-sm btn-info" onclick="editPersonnel({{ person.id }})"
                                                        title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-sm btn-danger" onclick="deletePersonnel({{ person.id }})"
                                                        title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="11" class="text-center text-muted">لا توجد أعوان مسجلين</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- تبويب الوسائل -->
                    <div class="tab-pane" id="vehicles-tab">
                        <div class="table-header">
                            <h4><i class="fas fa-truck"></i> إدارة الوسائل</h4>
                            <div class="table-info">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    الجدول قابل للتمرير - حد أقصى 6 صفوف
                                </small>
                            </div>
                        </div>
                        
                        <div class="table-responsive">
                            <table id="vehiclesTable" class="unified-table">
                                <thead>
                                    <tr>
                                        <th><i class="fas fa-hashtag"></i> الرقم التسلسلي</th>
                                        <th><i class="fas fa-truck"></i> نوع الوسيلة</th>
                                        <th><i class="fas fa-broadcast-tower"></i> رقم الراديو</th>
                                        <th><i class="fas fa-check-circle"></i> الحالة</th>
                                        <th><i class="fas fa-percentage"></i> الجاهزية</th>
                                        <th><i class="fas fa-ambulance"></i> في تدخل</th>
                                        <th><i class="fas fa-cogs"></i> إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for vehicle in equipment %}
                                    <tr data-vehicle-id="{{ vehicle.id }}">
                                        <td><strong>{{ vehicle.serial_number }}</strong></td>
                                        <td>{{ vehicle.equipment_type }}</td>
                                        <td>{{ vehicle.radio_number|default:"غير محدد" }}</td>
                                        <td>
                                            <div class="status-display-container">
                                                <!-- عرض الحالة الحالية مع الأيقونة -->
                                                <div class="current-status-display" id="status-display-{{ vehicle.id }}">
                                                    {% if vehicle.daily_status.status == 'operational' %}
                                                        <span class="badge badge-success">
                                                            <i class="fas fa-check-circle"></i> جاهز
                                                        </span>
                                                    {% elif vehicle.daily_status.status == 'broken' %}
                                                        <span class="badge badge-danger">
                                                            <i class="fas fa-times-circle"></i> معطل
                                                        </span>
                                                    {% elif vehicle.daily_status.status == 'maintenance' %}
                                                        <span class="badge badge-warning">
                                                            <i class="fas fa-tools"></i> صيانة
                                                        </span>
                                                    {% else %}
                                                        <span class="badge badge-secondary">
                                                            <i class="fas fa-question-circle"></i> غير محدد
                                                        </span>
                                                    {% endif %}
                                                </div>

                                                <!-- قائمة التحديث -->
                                                <select class="form-control status-select equipment-status-select mt-1"
                                                        data-id="{{ vehicle.id }}"
                                                        data-type="equipment"
                                                        onchange="updateEquipmentStatus(this)"
                                                        style="font-size: 12px;">
                                                    <option value="">-- تغيير الحالة --</option>
                                                    <option value="operational" {% if vehicle.daily_status.status == 'operational' %}selected{% endif %}>
                                                        جاهز
                                                    </option>
                                                    <option value="broken" {% if vehicle.daily_status.status == 'broken' %}selected{% endif %}>
                                                        معطل
                                                    </option>
                                                    <option value="maintenance" {% if vehicle.daily_status.status == 'maintenance' %}selected{% endif %}>
                                                        صيانة
                                                    </option>
                                                </select>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="readiness-indicator">
                                                {% if vehicle.readiness.status == 'ready' %}
                                                    <span class="readiness-score" data-score="{{ vehicle.readiness.readiness_score }}" style="color: #28a745;">
                                                        {{ vehicle.readiness.readiness_score }}%
                                                    </span>
                                                {% elif vehicle.readiness.status == 'manually_confirmed' %}
                                                    <span class="readiness-score" data-score="{{ vehicle.readiness.readiness_score }}" style="color: #ffc107;">
                                                        {{ vehicle.readiness.readiness_score }}%
                                                    </span>
                                                {% else %}
                                                    <span class="readiness-score" data-score="0" style="color: #dc3545;">
                                                        0%
                                                    </span>
                                                {% endif %}
                                                <span class="readiness-status {{ vehicle.readiness.status }}">
                                                    {{ vehicle.readiness.get_status_display }}
                                                </span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="intervention-status" id="intervention-status-{{ vehicle.id }}">
                                                <span class="badge badge-secondary">
                                                    <i class="fas fa-check-circle"></i> متاحة
                                                </span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn btn-sm btn-success" onclick="confirmVehicleReady({{ vehicle.id }})" 
                                                        title="تأكيد الجاهزية">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button class="btn btn-sm btn-info" onclick="editVehicle({{ vehicle.id }})" 
                                                        title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-sm btn-danger" onclick="deleteVehicle({{ vehicle.id }})" 
                                                        title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="7" class="text-center text-muted">لا توجد وسائل مسجلة</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- تبويب توزيع الوسائل -->
                    <div class="tab-pane" id="assignments-tab">
                        <div class="table-header">
                            <h4><i class="fas fa-users-cog"></i> توزيع الأعوان على الوسائل</h4>
                        </div>
                        
                        <div class="assignment-grid">
                            <p class="text-center text-muted">
                                <i class="fas fa-info-circle"></i>
                                سيتم إضافة واجهة توزيع الأعوان على الوسائل هنا
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            {% endif %}

            <!-- الأزرار العائمة -->
            <div class="floating-buttons">
                <a href="{% url 'coordination_center' %}" class="floating-btn coordination-btn" title="مركز التنسيق">
                    <i class="fas fa-building"></i>
                </a>
                <a href="{% url 'vehicle_crew_assignment' %}" class="floating-btn assignment-btn" title="توزيع الأعوان">
                    <i class="fas fa-users"></i>
                </a>
                <a href="{% url 'vehicle_readiness_dashboard' %}" class="floating-btn readiness-btn" title="جاهزية الوسائل">
                    <i class="fas fa-truck"></i>
                </a>
                <a href="{% url 'daily_interventions' %}" class="floating-btn interventions-btn" title="التدخلات اليومية">
                    <i class="fas fa-ambulance"></i>
                </a>
                <a href="{% url 'home' %}" class="floating-btn home-btn" title="الصفحة الرئيسية">
                    <i class="fas fa-home"></i>
                </a>
                <a href="#" id="back-to-top" class="floating-btn top-btn" title="العودة إلى الأعلى">
                    <i class="fas fa-arrow-up"></i>
                </a>
            </div>
        </div>
    </main>

    <!-- النوافذ المنبثقة -->
    <!-- نافذة إضافة عون -->
    <div id="addPersonnelModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-user-plus"></i> إضافة عون جديد</h3>
                <span class="close" onclick="closeModal('addPersonnelModal')">&times;</span>
            </div>
            <form id="addPersonnelForm">
                <div class="modal-body">
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="registration_number">رقم التسجيل *</label>
                            <input type="text" class="form-control" id="registration_number" name="registration_number" required>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="personnel_id">رقم القيد</label>
                            <input type="text" class="form-control" id="personnel_id" name="personnel_id">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group col-md-8">
                            <label for="full_name">الاسم الكامل *</label>
                            <input type="text" class="form-control" id="full_name" name="full_name" required>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="gender">الجنس *</label>
                            <select class="form-control" id="gender" name="gender" required>
                                <option value="">اختر الجنس</option>
                                <option value="male">ذكر</option>
                                <option value="female">أنثى</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group col-md-4">
                            <label for="age">العمر</label>
                            <input type="number" class="form-control" id="age" name="age" min="18" max="65">
                        </div>
                        <div class="form-group col-md-8">
                            <label for="phone_number">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="phone_number" name="phone_number">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group col-md-4">
                            <label for="rank">الرتبة</label>
                            <input type="text" class="form-control" id="rank" name="rank">
                        </div>
                        <div class="form-group col-md-8">
                            <label for="position">المنصب</label>
                            <input type="text" class="form-control" id="position" name="position">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="work_system">نظام العمل *</label>
                            <select class="form-control" id="work_system" name="work_system" required onchange="toggleShiftField()">
                                <option value="">اختر نظام العمل</option>
                                <option value="24_hours">نظام 24 ساعة</option>
                                <option value="8_hours">نظام 8 ساعات</option>
                            </select>
                        </div>
                        <div class="form-group col-md-6" id="shift_field" style="display: none;">
                            <label for="assigned_shift">الفرقة المخصصة</label>
                            <select class="form-control" id="assigned_shift" name="assigned_shift">
                                <option value="">اختر الفرقة</option>
                                <option value="shift_1">الفرقة الأولى</option>
                                <option value="shift_2">الفرقة الثانية</option>
                                <option value="shift_3">الفرقة الثالثة</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-success" onclick="savePersonnel()">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('addPersonnelModal')">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة تحويل عون -->
    <div id="transferModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-exchange-alt"></i> تحويل عون بين الفرق</h3>
                <span class="close" onclick="closeModal('transferModal')">&times;</span>
            </div>
            <form id="transferForm">
                <div class="modal-body">
                    <input type="hidden" id="transfer_personnel_id" name="personnel_id">

                    <div class="form-group">
                        <label>العون المحدد:</label>
                        <div id="selected_personnel_info" class="alert alert-info">
                            <!-- سيتم ملء معلومات العون هنا -->
                        </div>
                    </div>

                    <div class="form-group">
                        <label>الفرقة الحالية:</label>
                        <div id="current_shift_display" class="badge badge-primary">
                            <!-- سيتم عرض الفرقة الحالية هنا -->
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="target_shift">الفرقة الجديدة *</label>
                        <select class="form-control" id="target_shift" name="target_shift" required>
                            <option value="">اختر الفرقة الجديدة</option>
                            <option value="shift_1">الفرقة الأولى</option>
                            <option value="shift_2">الفرقة الثانية</option>
                            <option value="shift_3">الفرقة الثالثة</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="transfer_reason">سبب التحويل *</label>
                        <textarea class="form-control" id="transfer_reason" name="reason" rows="3" required placeholder="اكتب سبب التحويل..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-warning" onclick="saveTransfer()">
                        <i class="fas fa-exchange-alt"></i> تحويل
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('transferModal')">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // وظائف JavaScript أساسية
        function changeUnit() {
            const unitId = document.getElementById('unitSelect').value;
            const date = document.getElementById('dateSelect').value;
            window.location.href = `?unit_id=${unitId}&date=${date}`;
        }

        function changeDate() {
            const unitId = document.getElementById('unitSelect').value;
            const date = document.getElementById('dateSelect').value;
            window.location.href = `?unit_id=${unitId}&date=${date}`;
        }

        function showSection(sectionName) {
            console.log('Showing section:', sectionName);

            // إخفاء جميع الأقسام
            document.querySelectorAll('.tab-pane').forEach(tab => {
                tab.classList.remove('active');
                console.log('Hiding tab:', tab.id);
            });

            // إزالة الحالة النشطة من جميع الأزرار
            document.querySelectorAll('.action-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // إظهار القسم المحدد
            const targetTab = document.getElementById(sectionName + '-tab');
            const targetBtn = document.getElementById(sectionName + '-btn');

            if (targetTab) {
                targetTab.classList.add('active');
                console.log('Showing tab:', targetTab.id);
            } else {
                console.error('Tab not found:', sectionName + '-tab');
            }

            if (targetBtn) {
                targetBtn.classList.add('active');
                console.log('Activating button:', targetBtn.id);
            } else {
                console.error('Button not found:', sectionName + '-btn');
            }
        }

        function navigateToAssignments() {
            const unitId = document.getElementById('unitSelect').value;
            const date = document.getElementById('dateSelect').value;

            if (!unitId) {
                alert('يرجى اختيار الوحدة أولاً');
                return;
            }

            const url = `/vehicle-crew-assignment/?unit=${unitId}&date=${date}`;
            window.open(url, '_blank');
        }

        function navigateToInterventions() {
            const url = '/coordination-center/daily-interventions/';
            window.open(url, '_blank');
        }

        // الحفاظ على الدالة القديمة للتوافق مع الكود الموجود
        function showTab(tabName) {
            showSection(tabName);
        }

        function openAddPersonnelPage() {
            const unitId = document.getElementById('unitSelect').value;
            const url = `/coordination-center/add-personnel/?unit_id=${unitId}`;
            window.open(url, '_blank', 'width=1000,height=700,scrollbars=yes,resizable=yes');
        }

        function openAddEquipmentPage() {
            const unitId = document.getElementById('unitSelect').value;
            const url = `/coordination-center/add-equipment/?unit_id=${unitId}`;
            window.open(url, '_blank', 'width=1000,height=700,scrollbars=yes,resizable=yes');
        }

        function openManageRolesPage() {
            const url = `/coordination-center/manage-roles/`;
            window.open(url, '_blank', 'width=1000,height=700,scrollbars=yes,resizable=yes');
        }

        // الوظائف القديمة للنوافذ المنبثقة (احتياطية)
        function addPersonnel() {
            document.getElementById('addPersonnelModal').style.display = 'block';
        }

        function addVehicle() {
            alert('سيتم إضافة نموذج إضافة الوسيلة قريباً');
        }

        function showAssignmentView() {
            navigateToAssignments();
        }

        function openAssignmentPage() {
            const unitId = document.getElementById('unitSelect').value;
            const date = document.getElementById('dateSelect').value;

            if (!unitId || !date) {
                alert('يرجى اختيار الوحدة والتاريخ أولاً');
                return;
            }

            const url = `/vehicle-crew-assignment/?unit=${unitId}&date=${date}`;

            // فتح النافذة وتتبعها للتزامن
            window.assignmentWindow = window.open(url, 'assignmentWindow');

            // التحقق من إغلاق النافذة
            const checkClosed = setInterval(() => {
                if (window.assignmentWindow && window.assignmentWindow.closed) {
                    clearInterval(checkClosed);
                    window.assignmentWindow = null;
                }
            }, 1000);
        }

        function openAddPersonnelPage() {
            const unitId = document.getElementById('unitSelect').value;

            if (!unitId) {
                alert('يرجى اختيار الوحدة أولاً');
                return;
            }

            const url = `/coordination-center/add-personnel/?unit_id=${unitId}`;
            window.open(url, '_blank');
        }

        function openShiftSchedulePage() {
            const unitId = document.getElementById('unitSelect').value;

            if (!unitId) {
                alert('يرجى اختيار الوحدة أولاً');
                return;
            }

            const url = `/coordination-center/shift-schedule/?unit_id=${unitId}`;
            window.open(url, '_blank');
        }

        function refreshData() {
            location.reload();
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function toggleShiftField() {
            const workSystem = document.getElementById('work_system').value;
            const shiftField = document.getElementById('shift_field');

            if (workSystem === '24_hours') {
                shiftField.style.display = 'block';
                document.getElementById('assigned_shift').required = true;
            } else {
                shiftField.style.display = 'none';
                document.getElementById('assigned_shift').required = false;
                document.getElementById('assigned_shift').value = '';
            }
        }

        function savePersonnel() {
            const form = document.getElementById('addPersonnelForm');
            const formData = new FormData(form);

            // تحويل FormData إلى JSON
            const data = {};
            formData.forEach((value, key) => {
                data[key] = value;
            });

            // إضافة معرف الوحدة والتاريخ
            data.unit_id = document.getElementById('unitSelect').value;
            data.date = document.getElementById('dateSelect').value;

            fetch('/api/unified/add-personnel/', {
                method: 'POST',
                body: JSON.stringify(data),
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم إضافة العون بنجاح');
                    closeModal('addPersonnelModal');
                    location.reload();
                } else {
                    alert('خطأ: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ غير متوقع');
            });
        }

        function updatePersonnelStatus(selectElement) {
            const personnelId = selectElement.getAttribute('data-id');
            const newStatus = selectElement.value;
            const date = document.getElementById('dateSelect').value;

            // استخدام نظام التزامن إذا كان متاحاً
            if (typeof updatePersonnelStatusSync === 'function') {
                updatePersonnelStatusSync(personnelId, newStatus);
                return;
            }

            // الطريقة القديمة كاحتياطي
            fetch('/api/unified/update-personnel-status/', {
                method: 'POST',
                body: JSON.stringify({
                    personnel_id: personnelId,
                    status: newStatus,
                    date: date
                }),
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث الصف في الجدول فوراً
                    const row = selectElement.closest('tr');
                    row.setAttribute('data-status', newStatus);

                    // تحديث عرض الحالة
                    updatePersonnelStatusDisplay(personnelId, newStatus);

                    // إشعار بالنجاح
                    showNotification('success', 'تم تحديث حالة العون بنجاح');

                    // تحديث الإحصائيات بدون إعادة تحميل
                    updateStatsDisplay();
                } else {
                    alert('خطأ: ' + data.error);
                    // إعادة القيمة السابقة
                    selectElement.selectedIndex = 0;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ غير متوقع');
                // إعادة القيمة السابقة
                selectElement.selectedIndex = 0;
            });
        }

        function updateEquipmentStatus(selectElement) {
            const equipmentId = selectElement.getAttribute('data-id');
            const newStatus = selectElement.value;
            const date = document.getElementById('dateSelect').value;
            const oldValue = selectElement.getAttribute('data-old-value') || selectElement.options[0].value;

            if (!newStatus) return; // إذا لم يتم اختيار حالة

            // إضافة تسجيل للتتبع
            const csrfToken = getCookie('csrftoken');
            console.log('تحديث حالة الوسيلة:', {
                equipmentId: equipmentId,
                newStatus: newStatus,
                date: date,
                oldValue: oldValue,
                csrfToken: csrfToken ? 'موجود' : 'غير موجود'
            });

            // تحديث عرض الحالة فوراً
            updateEquipmentStatusDisplay(equipmentId, newStatus);

            // حفظ القيمة القديمة
            selectElement.setAttribute('data-old-value', selectElement.value);

            // إظهار مؤشر التحديث
            selectElement.disabled = true;
            selectElement.style.opacity = '0.6';

            fetch('/api/unified/update-equipment-status/', {
                method: 'POST',
                body: JSON.stringify({
                    equipment_id: equipmentId,
                    status: newStatus,
                    date: date
                }),
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                console.log('استجابة الخادم:', response.status, response.statusText);
                console.log('نوع المحتوى:', response.headers.get('content-type'));

                // التحقق من نوع المحتوى
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    throw new Error(`الخادم أرجع محتوى غير صحيح: ${contentType}`);
                }

                if (!response.ok) {
                    throw new Error(`خطأ HTTP: ${response.status} ${response.statusText}`);
                }

                return response.json();
            })
            .then(data => {
                console.log('بيانات الاستجابة:', data);
                if (data.success) {
                    // تحديث عرض الحالة فوراً
                    updateEquipmentStatusDisplay(equipmentId, newStatus);

                    // إشعار بالنجاح
                    showNotification('success', 'تم تحديث حالة الوسيلة بنجاح');

                    // إشعار خاص إذا تم تغيير الحالة إلى غير جاهز
                    if (newStatus === 'broken' || newStatus === 'maintenance') {
                        const statusText = newStatus === 'broken' ? 'معطل' : 'في الصيانة';
                        showNotification('warning',
                            `تم تغيير حالة الوسيلة إلى "${statusText}". ستختفي من صفحة توزيع الأعوان.`
                        );

                        // إشعار صفحة التوزيع إذا كانت مفتوحة
                        if (window.assignmentWindow && !window.assignmentWindow.closed) {
                            try {
                                window.assignmentWindow.postMessage({
                                    type: 'vehicle_status_changed',
                                    vehicle_id: equipmentId,
                                    status: newStatus,
                                    status_display: statusText,
                                    timestamp: new Date().toISOString()
                                }, '*');
                                console.log('تم إرسال رسالة التزامن لصفحة التوزيع');
                            } catch (error) {
                                console.error('خطأ في إرسال رسالة التزامن:', error);
                            }
                        }
                    } else if (newStatus === 'operational') {
                        showNotification('success',
                            'تم تغيير حالة الوسيلة إلى "جاهز". ستظهر في صفحة توزيع الأعوان.'
                        );

                        // إشعار صفحة التوزيع بالوسيلة الجديدة الجاهزة
                        if (window.assignmentWindow && !window.assignmentWindow.closed) {
                            try {
                                window.assignmentWindow.postMessage({
                                    type: 'vehicle_status_changed',
                                    vehicle_id: equipmentId,
                                    status: newStatus,
                                    status_display: 'جاهز',
                                    timestamp: new Date().toISOString()
                                }, '*');
                                console.log('تم إرسال رسالة التزامن لصفحة التوزيع');
                            } catch (error) {
                                console.error('خطأ في إرسال رسالة التزامن:', error);
                            }
                        }
                    }

                    // تحديث الإحصائيات بدون إعادة تحميل
                    updateStatsDisplay();
                } else {
                    console.error('خطأ من الخادم:', data.error);
                    alert('خطأ: ' + data.error);
                    // إعادة القيمة السابقة
                    selectElement.value = oldValue;
                }
            })
            .catch(error => {
                console.error('خطأ في الشبكة:', error);

                let errorMessage = 'حدث خطأ غير متوقع';
                if (error.message.includes('application/json')) {
                    errorMessage = 'خطأ في الخادم: تم إرجاع صفحة HTML بدلاً من JSON. قد تحتاج لتسجيل الدخول مرة أخرى.';
                } else if (error.message.includes('HTTP:')) {
                    errorMessage = 'خطأ في الخادم: ' + error.message;
                } else {
                    errorMessage = 'خطأ في الشبكة: ' + error.message;
                }

                alert(errorMessage);
                // إعادة القيمة السابقة
                selectElement.value = oldValue;
            })
            .finally(() => {
                // إعادة تفعيل القائمة المنسدلة
                selectElement.disabled = false;
                selectElement.style.opacity = '1';

                // تطبيق الألوان بعد التحديث
                applyEquipmentStatusColors();
            });
        }

        function transferPersonnel(personnelId) {
            // فتح نموذج التحويل في تبويب جديد
            const unitId = document.getElementById('unitSelect').value;
            const transferUrl = `/coordination-center/transfer-personnel/?unit_id=${unitId}&personnel_id=${personnelId}`;
            window.open(transferUrl, '_blank');
        }

        function transferPersonnelModal(personnelId) {
            // الدالة القديمة للنافذة المنبثقة (احتياطية)
            const row = document.querySelector(`tr[data-personnel-id="${personnelId}"]`);
            if (!row) {
                alert('لم يتم العثور على العون');
                return;
            }

            const personnelName = row.cells[1].querySelector('strong').textContent;
            const currentShift = row.cells[6].textContent.trim();

            // ملء النافذة المنبثقة
            document.getElementById('transfer_personnel_id').value = personnelId;
            document.getElementById('selected_personnel_info').innerHTML = `
                <strong>${personnelName}</strong><br>
                <small>معرف العون: ${personnelId}</small>
            `;
            document.getElementById('current_shift_display').textContent = currentShift;

            // إظهار النافذة
            document.getElementById('transferModal').style.display = 'block';
        }

        function saveTransfer() {
            const form = document.getElementById('transferForm');
            const formData = new FormData(form);

            const data = {};
            formData.forEach((value, key) => {
                data[key] = value;
            });

            // التحقق من البيانات المطلوبة
            if (!data.personnel_id || !data.target_shift || !data.reason) {
                alert('يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            console.log('بيانات التحويل:', data);

            fetch('/api/unified/transfer-personnel/', {
                method: 'POST',
                body: JSON.stringify(data),
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                }
            })
            .then(response => {
                console.log('استجابة الخادم:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('بيانات الاستجابة:', data);
                if (data.success) {
                    alert('تم تحويل العون بنجاح');
                    closeModal('transferModal');
                    location.reload();
                } else {
                    alert('خطأ: ' + (data.error || 'خطأ غير معروف'));
                }
            })
            .catch(error => {
                console.error('تفاصيل الخطأ:', error);
                alert('حدث خطأ في الاتصال: ' + error.message);
            });
        }

        function changeWorkSystem(personnelId, currentSystem) {
            const newSystem = currentSystem === '24_hours' ? '8_hours' : '24_hours';
            const systemNames = {
                '24_hours': 'نظام 24 ساعة',
                '8_hours': 'نظام 8 ساعات'
            };

            const confirmMessage = `هل تريد تغيير نظام العمل من ${systemNames[currentSystem]} إلى ${systemNames[newSystem]}؟`;

            if (confirm(confirmMessage)) {
                const data = {
                    personnel_id: personnelId,
                    new_work_system: newSystem
                };

                fetch('/api/unified/change-work-system/', {
                    method: 'POST',
                    body: JSON.stringify(data),
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        alert('تم تغيير نظام العمل بنجاح');
                        location.reload();
                    } else {
                        alert('خطأ: ' + (data.error || 'خطأ غير معروف'));
                    }
                })
                .catch(error => {
                    console.error('خطأ في تغيير نظام العمل:', error);
                    alert('حدث خطأ في الاتصال: ' + error.message);
                });
            }
        }

        function editPersonnel(personnelId) {
            alert('سيتم إضافة نموذج تعديل العون قريباً');
        }

        function deletePersonnel(personnelId) {
            if (confirm('هل أنت متأكد من حذف هذا العون؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
                fetch('/api/unified/delete-personnel/', {
                    method: 'POST',
                    body: JSON.stringify({personnel_id: personnelId}),
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('تم حذف العون بنجاح');
                        location.reload();
                    } else {
                        alert('خطأ: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ غير متوقع');
                });
            }
        }

        function confirmVehicleReady(vehicleId) {
            alert('سيتم إضافة وظيفة تأكيد الجاهزية قريباً');
        }

        function editVehicle(vehicleId) {
            alert('سيتم إضافة نموذج تعديل الوسيلة قريباً');
        }

        function deleteVehicle(vehicleId) {
            if (confirm('هل أنت متأكد من حذف هذه الوسيلة؟')) {
                alert('سيتم إضافة وظيفة الحذف قريباً');
            }
        }

        // وظيفة الحصول على CSRF token
        function getCookie(name) {
            // محاولة الحصول على التوكن من meta tag أولاً
            const metaToken = document.querySelector('meta[name="csrf-token"]');
            if (metaToken) {
                return metaToken.getAttribute('content');
            }

            // إذا لم يوجد في meta tag، استخدم الطريقة التقليدية من الكوكيز
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // تحديث ألوان نسبة الجاهزية وإصلاح العرض
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.readiness-score').forEach(function(element) {
                const score = parseInt(element.getAttribute('data-score'));

                // إصلاح العرض للوسائل غير الجاهزة
                const statusElement = element.parentElement.querySelector('.readiness-status');
                if (statusElement && statusElement.classList.contains('not_ready')) {
                    element.textContent = '0%';
                    element.setAttribute('data-score', '0');
                    element.style.color = '#dc3545';
                } else {
                    // تطبيق الألوان العادية
                    if (score >= 80) {
                        element.style.color = '#28a745';
                    } else if (score >= 60) {
                        element.style.color = '#ffc107';
                    } else {
                        element.style.color = '#dc3545';
                    }
                }
            });

            // إضافة مستمع للتحديث عند التركيز على النافذة (عند العودة من النوافذ الجديدة)
            window.addEventListener('focus', function() {
                // تحديث الصفحة بعد ثانيتين من العودة للنافذة
                setTimeout(function() {
                    if (document.hasFocus()) {
                        location.reload();
                    }
                }, 2000);
            });
        });

        // تحميل معلومات الفرقة العاملة
        function loadCurrentShiftInfo() {
            const unitId = document.getElementById('unitSelect').value;

            if (!unitId) {
                document.getElementById('shiftName').textContent = 'اختر الوحدة';
                document.getElementById('shiftTime').textContent = '--';
                return;
            }

            fetch(`/api/unified/current-shift/?unit_id=${unitId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const shiftBadge = document.getElementById('shiftBadge');
                        const shiftName = document.getElementById('shiftName');
                        const shiftTime = document.getElementById('shiftTime');

                        if (data.current_shift) {
                            shiftName.textContent = data.shift_display;
                            shiftTime.textContent = `من ${data.start_time} إلى ${data.end_time} (${data.remaining_hours} ساعة متبقية)`;

                            // تغيير لون الشارة حسب الفرقة
                            shiftBadge.className = 'shift-badge';
                            if (data.current_shift === 'shift_1') {
                                shiftBadge.classList.add('shift-a');
                            } else if (data.current_shift === 'shift_2') {
                                shiftBadge.classList.add('shift-b');
                            } else if (data.current_shift === 'shift_3') {
                                shiftBadge.classList.add('shift-c');
                            }
                        } else {
                            shiftName.textContent = 'لا توجد فرقة عاملة';
                            shiftTime.textContent = data.message || 'لم يتم تحديد جدولة';
                            shiftBadge.className = 'shift-badge shift-none';
                        }
                    } else {
                        document.getElementById('shiftName').textContent = 'خطأ في التحميل';
                        document.getElementById('shiftTime').textContent = data.error || 'خطأ غير معروف';
                    }
                })
                .catch(error => {
                    console.error('خطأ في تحميل معلومات الفرقة:', error);
                    document.getElementById('shiftName').textContent = 'خطأ في الاتصال';
                    document.getElementById('shiftTime').textContent = 'تعذر تحميل البيانات';
                });
        }

        // إنشاء جدولة الفرق
        function createShiftSchedule() {
            const unitId = document.getElementById('unitSelect').value;
            const date = document.getElementById('dateSelect').value;

            if (!unitId || !date) {
                alert('يرجى اختيار الوحدة والتاريخ أولاً');
                return;
            }

            if (confirm('هل تريد إنشاء جدولة جديدة للفرق؟ سيتم إنشاء دورة لمدة 21 يوم.')) {
                const data = {
                    unit_id: unitId,
                    start_date: date
                };

                fetch('/api/unified/create-shift-schedule/', {
                    method: 'POST',
                    body: JSON.stringify(data),
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('تم إنشاء جدولة الفرق بنجاح');
                        loadCurrentShiftInfo(); // إعادة تحميل معلومات الفرقة
                    } else {
                        alert('خطأ: ' + (data.error || 'خطأ غير معروف'));
                    }
                })
                .catch(error => {
                    console.error('خطأ في إنشاء الجدولة:', error);
                    alert('حدث خطأ في الاتصال');
                });
            }
        }

        // تهيئة نظام التزامن
        document.addEventListener('DOMContentLoaded', function() {
            const unitId = document.getElementById('unitSelect').value;
            const date = document.getElementById('dateSelect').value;

            if (unitId && date) {
                // تحميل نظام التزامن
                if (typeof initSyncSystem === 'function') {
                    initSyncSystem(unitId, date);
                }

                // تحميل معلومات الفرقة العاملة
                loadCurrentShiftInfo();

                // تحديث معلومات الفرقة كل دقيقة
                setInterval(loadCurrentShiftInfo, 60000);
            }

            // تطبيق الألوان على القوائم المنسدلة للوسائل
            applyEquipmentStatusColors();
        });

        // تحديث حالة العون مع التزامن
        function updatePersonnelStatusWithSync(personnelId, newStatus) {
            if (typeof updatePersonnelStatusSync === 'function') {
                updatePersonnelStatusSync(personnelId, newStatus);
            } else {
                // الطريقة القديمة كاحتياطي
                updatePersonnelStatus(personnelId, newStatus);
            }
        }

        // تحديث جاهزية الوسيلة مع التزامن
        function updateEquipmentStatusWithSync(equipmentId, newStatus) {
            if (typeof updateVehicleReadinessSync === 'function') {
                updateVehicleReadinessSync(equipmentId, newStatus);
            } else {
                // الطريقة القديمة كاحتياطي
                updateEquipmentStatus(equipmentId, newStatus);
            }
        }

        // تحديث عرض حالة العون في الواجهة
        function updatePersonnelStatusDisplay(personnelId, newStatus) {
            const row = document.querySelector(`tr[data-personnel-id="${personnelId}"]`);
            if (row) {
                // تحديث القائمة المنسدلة
                const statusSelect = row.querySelector('.status-select');
                if (statusSelect) {
                    statusSelect.value = newStatus;

                    // تطبيق الألوان حسب الحالة
                    statusSelect.className = 'form-control status-select personnel-status-select';
                    if (newStatus === 'present') {
                        statusSelect.classList.add('personnel-status-present');
                    } else if (newStatus === 'absent') {
                        statusSelect.classList.add('personnel-status-absent');
                    } else if (newStatus === 'on_mission') {
                        statusSelect.classList.add('personnel-status-mission');
                    } else if (newStatus === 'standby') {
                        statusSelect.classList.add('personnel-status-standby');
                    }
                }

                // تحديث حالة العمل بناءً على الحالة الجديدة
                const workStatusElement = row.querySelector('.work-status');
                if (workStatusElement) {
                    updateWorkStatusDisplay(workStatusElement, newStatus, personnelId);
                }

                // تحديث النص المرافق
                const statusText = document.getElementById(`personnel-status-text-${personnelId}`);
                if (statusText) {
                    let newText = '';
                    switch(newStatus) {
                        case 'present':
                            newText = '✅ حاضر';
                            break;
                        case 'absent':
                            newText = '❌ غائب';
                            break;
                        case 'on_mission':
                            newText = '🚀 في مهمة';
                            break;
                    }
                    statusText.textContent = newText;

                    // إضافة تأثير وميض للنص
                    statusText.style.animation = 'pulse 0.5s ease-in-out';
                    setTimeout(() => {
                        statusText.style.animation = '';
                    }, 500);
                }

                // تحديث خاصية البيانات
                row.setAttribute('data-status', newStatus);

                // إضافة تأثير بصري للتحديث
                row.classList.add('status-updated');
                setTimeout(() => {
                    row.classList.remove('status-updated');
                }, 2000);
            }
        }

        // تحديث عرض حالة الوسيلة في الواجهة
        function updateEquipmentStatusDisplay(equipmentId, newStatus) {
            const row = document.querySelector(`tr[data-vehicle-id="${equipmentId}"]`);
            if (row) {
                // تحديث عرض الحالة الحالية مع الأيقونة
                const statusDisplay = row.querySelector(`#status-display-${equipmentId}`);
                if (statusDisplay) {
                    let badgeClass = '';
                    let icon = '';
                    let text = '';

                    switch(newStatus) {
                        case 'operational':
                            badgeClass = 'badge-success';
                            icon = 'fas fa-check-circle';
                            text = 'جاهز';
                            break;
                        case 'broken':
                            badgeClass = 'badge-danger';
                            icon = 'fas fa-times-circle';
                            text = 'معطل';
                            break;
                        case 'maintenance':
                            badgeClass = 'badge-warning';
                            icon = 'fas fa-tools';
                            text = 'صيانة';
                            break;
                        default:
                            badgeClass = 'badge-secondary';
                            icon = 'fas fa-question-circle';
                            text = 'غير محدد';
                    }

                    statusDisplay.innerHTML = `
                        <span class="badge ${badgeClass}">
                            <i class="${icon}"></i> ${text}
                        </span>
                    `;
                }

                // تحديث القائمة المنسدلة
                const statusSelect = row.querySelector('.status-select');
                if (statusSelect) {
                    statusSelect.value = newStatus;
                }

                // تحديث الجاهزية تلقائياً
                const readinessContainer = row.querySelector('.readiness-indicator');
                if (readinessContainer && (newStatus === 'broken' || newStatus === 'maintenance')) {
                    // تحديث الجاهزية إلى "غير جاهز" للوسائل المعطلة أو في الصيانة
                    const readinessScore = readinessContainer.querySelector('.readiness-score');
                    const readinessStatus = readinessContainer.querySelector('.readiness-status');

                    if (readinessScore) {
                        readinessScore.textContent = '0%';
                        readinessScore.setAttribute('data-score', '0');
                        readinessScore.style.color = '#dc3545'; // أحمر
                    }

                    if (readinessStatus) {
                        readinessStatus.textContent = 'غير جاهز';
                        readinessStatus.className = 'readiness-status not_ready';
                    }
                } else if (readinessContainer && newStatus === 'operational') {
                    // إعادة تعيين الجاهزية للوسائل الجاهزة
                    const readinessScore = readinessContainer.querySelector('.readiness-score');
                    const readinessStatus = readinessContainer.querySelector('.readiness-status');

                    if (readinessScore) {
                        readinessScore.textContent = '100%';
                        readinessScore.setAttribute('data-score', '100');
                        readinessScore.style.color = '#28a745'; // أخضر
                    }

                    if (readinessStatus) {
                        readinessStatus.textContent = 'جاهز';
                        readinessStatus.className = 'readiness-status ready';
                    }
                }

                // تحديث النص داخل القائمة المنسدلة
                if (statusSelect) {
                    const selectedOption = statusSelect.querySelector(`option[value="${newStatus}"]`);
                    if (selectedOption) {
                        // إضافة تأثير وميض للقائمة المنسدلة
                        statusSelect.style.animation = 'pulse 0.5s ease-in-out';
                        setTimeout(() => {
                            statusSelect.style.animation = '';
                        }, 500);
                    }
                }

                // إضافة تأثير بصري للتحديث
                row.classList.add('status-updated');
                setTimeout(() => {
                    row.classList.remove('status-updated');
                }, 2000);
            }
        }

        // تطبيق الألوان على جميع القوائم المنسدلة عند تحميل الصفحة
        function applyEquipmentStatusColors() {
            const equipmentSelects = document.querySelectorAll('.equipment-status-select');
            equipmentSelects.forEach(select => {
                const status = select.value;
                select.className = 'form-control status-select equipment-status-select';
                if (status === 'operational') {
                    select.classList.add('equipment-status-operational');
                } else if (status === 'broken') {
                    select.classList.add('equipment-status-broken');
                } else if (status === 'maintenance') {
                    select.classList.add('equipment-status-maintenance');
                }
            });
        }

        // إظهار إشعار محسن
        function showNotification(type, message) {
            // إنشاء عنصر الإشعار
            const notification = document.createElement('div');
            let alertClass, iconClass;

            switch(type) {
                case 'success':
                    alertClass = 'alert-success';
                    iconClass = 'fa-check-circle';
                    break;
                case 'warning':
                    alertClass = 'alert-warning';
                    iconClass = 'fa-exclamation-triangle';
                    break;
                case 'error':
                    alertClass = 'alert-danger';
                    iconClass = 'fa-times-circle';
                    break;
                default:
                    alertClass = 'alert-info';
                    iconClass = 'fa-info-circle';
            }

            notification.className = `alert ${alertClass} notification-toast`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                max-width: 500px;
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                animation: slideInRight 0.3s ease-out;
                font-weight: bold;
                border: none;
            `;

            notification.innerHTML = `
                <i class="fas ${iconClass}" style="margin-left: 10px;"></i>
                ${message}
                <button type="button" class="close" onclick="this.parentElement.remove()" style="float: left; margin-right: 10px; background: none; border: none; font-size: 18px; cursor: pointer;">×</button>
            `;

            // إضافة الإشعار إلى الصفحة
            document.body.appendChild(notification);

            // إزالة الإشعار بعد 5 ثوان
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.animation = 'slideOutRight 0.3s ease-in';
                    setTimeout(() => notification.remove(), 300);
                }
            }, 5000);
        }

        // تحديث الإحصائيات بدون إعادة تحميل
        function updateStatsDisplay() {
            // يمكن تحسين هذه الوظيفة لاحقاً لتحديث الإحصائيات ديناميكياً
            // حالياً سنتركها فارغة لتجنب إعادة التحميل
        }

        // اختبار الاتصال بـ API
        function testApiConnection() {
            const csrfToken = getCookie('csrftoken');
            console.log('اختبار الاتصال - CSRF Token:', csrfToken ? 'موجود' : 'غير موجود');

            fetch('/api/test-connection/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    test: 'اختبار الاتصال',
                    timestamp: new Date().toISOString()
                })
            })
            .then(response => {
                console.log('استجابة اختبار الاتصال:', response.status, response.statusText);
                console.log('نوع المحتوى:', response.headers.get('content-type'));

                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    throw new Error(`نوع محتوى غير صحيح: ${contentType}`);
                }

                return response.json();
            })
            .then(data => {
                console.log('نتيجة اختبار الاتصال:', data);
                if (data.success) {
                    alert('✅ الاتصال يعمل بشكل صحيح!\nالمستخدم: ' + data.user);
                } else {
                    alert('❌ فشل الاتصال: ' + data.error);
                }
            })
            .catch(error => {
                console.error('خطأ في اختبار الاتصال:', error);
                alert('❌ خطأ في اختبار الاتصال: ' + error.message);
            });
        }
    </script>

    <!-- مؤشر التزامن -->
    <div class="sync-indicator" id="syncIndicator">
        <i class="fas fa-sync-alt"></i>
        <span>متزامن</span>
    </div>

    <!-- مؤشر آخر تحديث -->
    <div id="lastSyncTime" style="position: fixed; bottom: 60px; left: 20px; font-size: 12px; color: #6c757d;"></div>

    <!-- تحميل نظام التزامن -->
    <script src="{% static 'js/sync-system.js' %}"></script>

    <style>
        /* تنسيقات CSS للنظام الموحد */
        .unified-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }

        .unit-count-icon {
            font-size: 4rem;
            color: #ffc107;
            margin-bottom: 20px;
        }

        .unit-count-title {
            font-size: 2rem;
            font-weight: bold;
            color: #1d3557;
            margin-bottom: 10px;
        }

        .unit-count-subtitle {
            font-size: 1.2rem;
            color: #457b9d;
            margin-bottom: 20px;
        }

        .system-badge {
            margin-bottom: 30px;
        }

        .system-badge .badge {
            font-size: 1rem;
            padding: 8px 16px;
            border-radius: 20px;
        }

        .badge-success {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
        }

        .new-system-alert {
            background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
            border: 2px solid #2196f3;
            border-radius: 15px;
            margin-bottom: 30px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(33, 150, 243, 0.2);
        }

        .new-system-alert .alert-content {
            max-width: 800px;
            margin: 0 auto;
        }

        .new-system-alert i.fa-rocket {
            font-size: 2rem;
            color: #2196f3;
            margin-bottom: 10px;
        }

        .new-system-alert strong {
            color: #1565c0;
            font-size: 1.3rem;
            display: block;
            margin-bottom: 10px;
        }

        .new-system-alert p {
            color: #424242;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .new-system-alert small {
            color: #666;
            font-size: 0.9rem;
        }

        .new-system-alert small i {
            color: #2196f3;
            margin-right: 5px;
        }

        /* تصميم التحكم المحسن */
        .controls-enhanced {
            margin-top: 30px;
        }

        .control-row {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            align-items: end;
        }

        .primary-controls {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            border: 1px solid #e9ecef;
        }

        .navigation-controls {
            justify-content: center;
            gap: 15px;
        }

        .control-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            min-width: 180px;
        }

        .control-item label {
            font-weight: 600;
            color: #495057;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .enhanced-select,
        .enhanced-input {
            min-width: 160px;
            text-align: center;
            border-radius: 8px;
            border: 2px solid #e9ecef;
            padding: 10px 15px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .enhanced-select:focus,
        .enhanced-input:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .shift-info .current-shift-display {
            background: white;
            padding: 10px 15px;
            border-radius: 8px;
            border: 2px solid #e9ecef;
            min-width: 160px;
        }

        .navigation-controls .btn {
            border-radius: 8px;
            font-weight: 500;
            padding: 10px 20px;
            transition: all 0.3s ease;
        }

        .navigation-controls .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .summary-cards-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .summary-card .card-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 15px;
            text-align: center;
        }

        .summary-card .card-body {
            padding: 20px;
        }

        .stats {
            display: flex;
            justify-content: space-around;
            text-align: center;
        }

        .stat-item {
            display: flex;
            flex-direction: column;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #1d3557;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
        }

        .readiness-score {
            font-size: 3rem;
            font-weight: bold;
            text-align: center;
        }

        .readiness-label {
            text-align: center;
            color: #6c757d;
            margin-top: 10px;
        }

        .shifts-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 30px;
        }

        .shifts-section h3 {
            color: #1d3557;
            margin-bottom: 20px;
            text-align: center;
        }

        .shifts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .shift-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            border-left: 4px solid;
        }

        .shift-card.shift_1 {
            border-left-color: #007bff;
        }

        .shift-card.shift_2 {
            border-left-color: #28a745;
        }

        .shift-card.shift_3 {
            border-left-color: #fd7e14;
        }

        .shift-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 10px;
        }

        .shift-progress {
            background: #e9ecef;
            border-radius: 4px;
            height: 8px;
            margin-top: 10px;
        }

        .progress-bar {
            background: #28a745;
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        /* تصميم الأزرار المحسن */
        .main-actions-grid {
            margin-bottom: 30px;
        }

        .action-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .primary-actions {
            margin-bottom: 15px;
        }

        .action-btn-large {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 20px 25px;
            border: none;
            border-radius: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            min-width: 200px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .action-btn-large:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            color: white;
            text-decoration: none;
        }

        .btn-add-personnel {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .btn-add-equipment {
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
        }

        .btn-assignments {
            background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);
        }

        .btn-schedule {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        }

        .btn-icon {
            font-size: 2rem;
            opacity: 0.9;
        }

        .btn-text h4 {
            margin: 0;
            font-size: 1.1rem;
            font-weight: 600;
        }

        .btn-text small {
            opacity: 0.8;
            font-size: 0.85rem;
        }

        .action-btn-small {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            background: white;
            color: #495057;
            text-decoration: none;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .action-btn-small:hover {
            border-color: #007bff;
            color: #007bff;
            background: #f8f9fa;
            text-decoration: none;
            transform: translateY(-1px);
        }

        .btn-interventions:hover { border-color: #dc3545; color: #dc3545; }
        .btn-roles:hover { border-color: #6c757d; color: #6c757d; }
        .btn-refresh:hover { border-color: #28a745; color: #28a745; }
        .btn-test:hover { border-color: #17a2b8; color: #17a2b8; }

        .main-table-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .nav-tabs {
            background: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
        }

        /* CSS للفلاتر المحسنة */
        .table-controls-enhanced {
            margin-bottom: 20px;
        }

        .filters-container {
            display: flex;
            gap: 20px;
            align-items: end;
            flex-wrap: wrap;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            border: 1px solid #e9ecef;
            justify-content: space-between;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 6px;
            min-width: 140px;
        }

        .filter-label {
            font-weight: 600;
            color: #495057;
            font-size: 0.85rem;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .filter-input,
        .filter-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            padding: 8px 12px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .filter-input:focus,
        .filter-select:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .search-group .filter-input {
            min-width: 200px;
        }

        .filter-actions {
            display: flex;
            align-items: end;
        }

        .filter-actions .btn {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .filter-actions .btn:hover {
            transform: translateY(-1px);
        }

        .nav-link {
            color: #495057;
            border: none;
            padding: 15px 25px;
            font-weight: 500;
        }

        .nav-link.active {
            background: white;
            color: #007bff;
            border-bottom: 2px solid #007bff;
        }

        .tab-content {
            padding: 20px;
        }

        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .table-controls {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        .table-info {
            margin-right: auto;
            padding: 5px 10px;
            background: #e3f2fd;
            border-radius: 15px;
            border: 1px solid #bbdefb;
        }

        .table-info small {
            color: #1976d2;
            font-weight: 500;
        }

        /* تحسين الجداول - مستجيبة مع تمرير */
        .table-responsive {
            max-height: 500px; /* حد أقصى للارتفاع (حوالي 6-7 صفوف) */
            overflow-y: auto;
            overflow-x: auto;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            position: relative;
        }

        /* خيار توسيع الحاوي */
        .container-fluid {
            max-width: none !important;
            padding-left: 15px;
            padding-right: 15px;
        }

        /* تحسين عرض الحاوي الرئيسي */
        .main-content {
            width: 100%;
            max-width: 100vw;
        }

        .unified-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 0;
            min-width: 1000px; /* تقليل العرض الأدنى */
        }

        .unified-table th,
        .unified-table td {
            padding: 12px 8px;
            text-align: center;
            border: 1px solid #dee2e6;
            white-space: nowrap; /* منع كسر النص - سيتم تجاوزه للأعمدة المحددة */
            vertical-align: middle;
            font-size: 13px;
        }

        .unified-table th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            font-weight: 600;
            color: #495057;
            position: sticky;
            top: 0;
            z-index: 10;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .unified-table tbody tr:hover {
            background: #f8f9fa;
            transform: scale(1.01);
            transition: all 0.2s ease;
        }

        /* تحسين عرض الأعمدة */
        .unified-table th:nth-child(1), /* رقم التسجيل */
        .unified-table td:nth-child(1) {
            min-width: 100px;
            max-width: 110px;
            font-weight: bold;
        }

        .unified-table th:nth-child(2), /* الاسم */
        .unified-table td:nth-child(2) {
            min-width: 140px;
            max-width: 160px;
            text-align: right;
            white-space: normal; /* السماح بكسر النص */
            word-wrap: break-word;
            line-height: 1.3;
        }

        .unified-table th:nth-child(3), /* الجنس */
        .unified-table td:nth-child(3) {
            min-width: 80px;
        }

        .unified-table th:nth-child(4), /* العمر */
        .unified-table td:nth-child(4) {
            min-width: 80px;
        }

        .unified-table th:nth-child(5), /* تاريخ الالتحاق */
        .unified-table td:nth-child(5) {
            min-width: 110px;
            max-width: 130px;
            white-space: normal;
            line-height: 1.3;
        }

        .unified-table th:nth-child(6), /* الهاتف */
        .unified-table td:nth-child(6) {
            min-width: 100px;
            max-width: 120px;
        }

        .unified-table th:nth-child(7), /* نظام العمل */
        .unified-table td:nth-child(7) {
            min-width: 90px;
            max-width: 110px;
        }

        .unified-table th:nth-child(8), /* الفرقة */
        .unified-table td:nth-child(8) {
            min-width: 90px;
            max-width: 110px;
        }

        .unified-table th:nth-child(9), /* الحالة */
        .unified-table td:nth-child(9) {
            min-width: 120px;
            max-width: 140px;
        }

        .unified-table th:nth-child(10), /* حالة العمل */
        .unified-table td:nth-child(10) {
            min-width: 120px;
            max-width: 140px;
        }

        .unified-table th:nth-child(11), /* إجراءات */
        .unified-table td:nth-child(11) {
            min-width: 160px;
            max-width: 180px;
        }

        /* تحسين جدول الوسائل */
        #vehiclesTable th:nth-child(1), /* الرقم التسلسلي */
        #vehiclesTable td:nth-child(1) {
            min-width: 120px;
            max-width: 140px;
            font-weight: bold;
        }

        #vehiclesTable th:nth-child(2), /* نوع الوسيلة */
        #vehiclesTable td:nth-child(2) {
            min-width: 120px;
            max-width: 140px;
            text-align: right;
            white-space: normal; /* السماح بكسر النص */
            word-wrap: break-word;
            line-height: 1.3;
        }

        #vehiclesTable th:nth-child(3), /* رقم الراديو */
        #vehiclesTable td:nth-child(3) {
            min-width: 100px;
            max-width: 120px;
        }

        #vehiclesTable th:nth-child(4), /* الحالة */
        #vehiclesTable td:nth-child(4) {
            min-width: 160px;
            max-width: 180px;
        }

        #vehiclesTable th:nth-child(5), /* الجاهزية */
        #vehiclesTable td:nth-child(5) {
            min-width: 100px;
            max-width: 120px;
        }

        #vehiclesTable th:nth-child(6), /* إجراءات */
        #vehiclesTable td:nth-child(6) {
            min-width: 160px;
            max-width: 180px;
        }

        .action-buttons {
            display: flex;
            gap: 5px;
            justify-content: center;
            flex-wrap: wrap;
        }

        /* تحسين عرض المعلومات في الخلايا */
        .personnel-info {
            text-align: right;
            line-height: 1.3;
            font-size: 12px;
        }

        .personnel-info strong {
            display: block;
            font-size: 13px;
            margin-bottom: 2px;
        }

        .personnel-info small {
            display: block;
            font-size: 11px;
            color: #6c757d;
        }

        .phone-link {
            color: #007bff;
            text-decoration: none;
            font-weight: 500;
        }

        .phone-link:hover {
            text-decoration: underline;
        }

        /* تحسين القوائم المنسدلة */
        .status-select {
            min-width: 140px;
            font-size: 13px;
        }

        /* تحسين الشارات */
        .badge {
            font-size: 12px;
            padding: 6px 10px;
            border-radius: 15px;
            font-weight: 500;
        }

        /* تحسين عرض حالة الوسائل */
        .status-display-container {
            display: flex;
            flex-direction: column;
            gap: 8px;
            align-items: center;
        }

        .current-status-display {
            margin-bottom: 5px;
        }

        /* شريط التمرير المخصص */
        .table-responsive::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .table-responsive::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .table-responsive::-webkit-scrollbar-thumb {
            background: linear-gradient(45deg, #007bff, #0056b3);
            border-radius: 10px;
        }

        .table-responsive::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(45deg, #0056b3, #004085);
        }

        /* مؤشر التمرير */
        .table-responsive::before {
            content: "⬇️ مرر للأسفل لرؤية المزيد";
            position: absolute;
            bottom: 10px;
            right: 20px;
            background: rgba(0, 123, 255, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 11px;
            z-index: 5;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .table-responsive:hover::before {
            opacity: 1;
        }

        /* تحسين الاستجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .unified-table {
                min-width: 700px; /* تقليل العرض للشاشات الصغيرة */
            }

            .table-responsive {
                max-height: 400px;
            }

            .unified-table th,
            .unified-table td {
                padding: 8px 6px;
                font-size: 11px;
            }

            .table-controls {
                flex-direction: column;
                gap: 8px;
            }

            .table-info {
                margin-right: 0;
                text-align: center;
            }

            /* تقليل أعمدة الشاشات الصغيرة */
            .unified-table th:nth-child(2),
            .unified-table td:nth-child(2) {
                max-width: 120px;
            }

            #vehiclesTable th:nth-child(2),
            #vehiclesTable td:nth-child(2) {
                max-width: 100px;
            }
        }

        /* تأثيرات تفاعلية */
        .unified-table tbody tr {
            transition: all 0.2s ease;
        }

        .unified-table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .unified-table tbody tr:nth-child(odd) {
            background-color: #ffffff;
        }

        .unified-table tbody tr:hover {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%) !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .badge-primary {
            background: #007bff;
            color: white;
        }

        .badge-success {
            background: #28a745;
            color: white;
        }

        .badge-warning {
            background: #ffc107;
            color: #212529;
        }

        .badge-info {
            background: #17a2b8;
            color: white;
        }

        .badge-secondary {
            background: #6c757d;
            color: white;
        }

        .phone-link {
            color: #007bff;
            text-decoration: none;
        }

        .phone-link:hover {
            text-decoration: underline;
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 10px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            background: #007bff;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-body {
            padding: 20px;
        }

        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid #dee2e6;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .close {
            font-size: 1.5rem;
            cursor: pointer;
            background: none;
            border: none;
            color: white;
        }

        .close:hover {
            opacity: 0.7;
        }

        /* رسوم متحركة للإشعارات */
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        .notification-toast {
            animation: slideInRight 0.3s ease;
        }

        /* الأزرار العائمة */
        .floating-buttons {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .floating-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            transition: all 0.3s;
            text-decoration: none;
            font-size: 18px;
            border: none;
            cursor: pointer;
        }

        .floating-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
            color: white;
            text-decoration: none;
        }

        .coordination-btn {
            background-color: #28a745;
        }

        .assignment-btn {
            background-color: #17a2b8;
        }

        .readiness-btn {
            background-color: #dc3545;
        }

        .interventions-btn {
            background-color: #e74c3c;
        }

        .home-btn {
            background-color: #0d47a1;
        }

        .top-btn {
            background-color: #0d6efd;
        }

        /* تنسيقات متجاوبة */
        @media (max-width: 768px) {
            .control-row {
                flex-direction: column;
                gap: 15px;
            }

            .primary-controls {
                padding: 15px;
            }

            .filters-container {
                flex-direction: column;
                gap: 15px;
                padding: 15px;
            }

            .filter-group {
                min-width: 100%;
            }

            .action-row {
                flex-direction: column;
                gap: 15px;
            }

            .action-btn-large {
                min-width: 100%;
                justify-content: center;
            }

            .secondary-actions {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 10px;
            }

            .summary-cards-row {
                grid-template-columns: 1fr;
            }

            .shifts-grid {
                grid-template-columns: 1fr;
            }

            .main-actions {
                flex-direction: column;
                align-items: center;
            }

            .table-header {
                flex-direction: column;
                gap: 15px;
            }

            .table-controls {
                width: 100%;
                justify-content: center;
            }
        }

        /* تأثير بصري للتحديث */
        .status-updated {
            background-color: #d4edda !important;
            transition: background-color 0.3s ease;
        }

        /* إشعارات التحديث */
        .notification-toast {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
            min-width: 300px;
            animation: slideInRight 0.3s ease;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* تحسين مؤشر التزامن */
        .sync-indicator {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: #28a745;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 12px;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .sync-indicator.syncing {
            background: #ffc107;
            color: #000;
        }

        .sync-indicator.error {
            background: #dc3545;
        }

        .sync-indicator i {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* تنسيقات خاصة للقوائم المنسدلة للوسائل */
        .equipment-status-select {
            border: 2px solid #dee2e6;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 14px;
            font-weight: 500;
            background-color: #fff;
            transition: all 0.3s ease;
        }

        .equipment-status-select:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            outline: none;
        }

        .equipment-status-select option {
            padding: 8px;
            font-weight: 500;
        }

        .equipment-status-select option[value="operational"] {
            background-color: #d4edda;
            color: #155724;
        }

        .equipment-status-select option[value="broken"] {
            background-color: #f8d7da;
            color: #721c24;
        }

        .equipment-status-select option[value="maintenance"] {
            background-color: #fff3cd;
            color: #856404;
        }

        /* تحسين عرض حالة الوسائل */
        .equipment-status-operational {
            background-color: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }

        .equipment-status-broken {
            background-color: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }

        .equipment-status-maintenance {
            background-color: #fff3cd;
            color: #856404;
            border-color: #ffeaa7;
        }

        /* رسوم متحركة للإشعارات */
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        /* تحسين مظهر الإشعارات */
        .notification-toast {
            font-family: 'Cairo', sans-serif;
            direction: rtl;
            text-align: right;
        }

        /* تأثير بصري عند تحديث الحالة */
        .status-updated {
            background-color: #e8f5e8 !important;
            transition: background-color 0.5s ease;
        }

        /* تحسين مظهر أزرار الاختبار */
        .btn-secondary {
            background-color: #6c757d;
            border-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
            border-color: #545b62;
        }

        /* رسوم متحركة للنبض */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* تحسين مظهر القوائم المنسدلة مع الأيقونات */
        .status-select option {
            padding: 8px 12px;
            font-weight: bold;
        }

        /* ألوان خاصة للأعوان */
        .personnel-status-present {
            background-color: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }

        .personnel-status-absent {
            background-color: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }

        .personnel-status-mission {
            background-color: #fff3cd;
            color: #856404;
            border-color: #ffeaa7;
        }

        .personnel-status-reserve {
            background-color: #e3f2fd;
            color: #1976d2;
            border-color: #2196f3;
        }

        .personnel-status-reserve:focus {
            box-shadow: 0 0 0 0.2rem rgba(33, 150, 243, 0.25) !important;
        }

        /* تنسيق عرض الفرقة العاملة */
        .current-shift-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 8px 12px;
            margin-top: 5px;
        }

        .shift-badge {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: bold;
            font-size: 14px;
        }

        .shift-badge.shift-a {
            color: #007bff;
        }

        .shift-badge.shift-b {
            color: #28a745;
        }

        .shift-badge.shift-c {
            color: #fd7e14;
        }

        .shift-badge.shift-none {
            color: #6c757d;
        }

        .shift-badge i {
            font-size: 16px;
        }

        #shiftTime {
            font-size: 11px;
            margin-top: 2px;
        }

        /* تنسيقات أزرار التنقل الرئيسية */
        .navigation-actions {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .action-btn {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #dee2e6;
            border-radius: 15px;
            padding: 20px 30px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            min-width: 200px;
            text-align: center;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
            border-color: #007bff;
        }

        .action-btn.active {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            border-color: #0056b3;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0, 123, 255, 0.3);
        }

        .btn-content-inline {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .btn-content-inline i {
            font-size: 24px;
        }

        .btn-content-inline h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }

        /* ألوان مخصصة للأزرار */
        .personnel-btn {
            border-color: #28a745;
        }

        .personnel-btn:hover,
        .personnel-btn.active {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
            border-color: #1e7e34;
            box-shadow: 0 8px 15px rgba(40, 167, 69, 0.3);
        }

        .vehicles-btn {
            border-color: #17a2b8;
        }

        .vehicles-btn:hover,
        .vehicles-btn.active {
            background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
            border-color: #117a8b;
            box-shadow: 0 8px 15px rgba(23, 162, 184, 0.3);
        }

        .assignments-btn {
            border-color: #ffc107;
        }

        .assignments-btn:hover,
        .assignments-btn.active {
            background: linear-gradient(135deg, #ffc107 0%, #d39e00 100%);
            border-color: #d39e00;
            color: #212529;
            box-shadow: 0 8px 15px rgba(255, 193, 7, 0.3);
        }

        .interventions-btn {
            border-color: #dc3545;
        }

        .interventions-btn:hover,
        .interventions-btn.active {
            background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%);
            border-color: #bd2130;
            box-shadow: 0 8px 15px rgba(220, 53, 69, 0.3);
        }

        /* تحسين الاستجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .navigation-actions {
                flex-direction: column;
                align-items: center;
            }

            .action-btn {
                min-width: 250px;
                margin-bottom: 10px;
            }

            .btn-content-inline {
                gap: 10px;
            }

            .btn-content-inline i {
                font-size: 20px;
            }

            .btn-content-inline h3 {
                font-size: 16px;
            }
        }

        /* تحكم في إظهار/إخفاء الأقسام */
        .tab-pane {
            display: none;
        }

        .tab-pane.active {
            display: block;
        }

        .tab-content {
            margin-top: 20px;
        }
    </style>

    <script>
        // دالة تحديث عرض حالة العمل المحسنة مع API
        function updateWorkStatusDisplay(workStatusElement, newStatus, personnelId = null) {
            // إذا كان لدينا معرف العون، نحصل على حالة العمل من الخادم
            if (personnelId) {
                const date = document.getElementById('dateSelect').value;

                fetch(`/api/unified/get-work-status/?personnel_id=${personnelId}&date=${date}&status=${newStatus}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            const workStatus = data.work_status;

                            // تحديث محتوى العنصر
                            workStatusElement.className = `badge ${workStatus.badge_class} work-status`;
                            workStatusElement.innerHTML = `<i class="${workStatus.icon}"></i> ${workStatus.text}`;

                            // تحديث النص الفرعي
                            const parentCell = workStatusElement.closest('td');
                            if (parentCell) {
                                let smallElement = parentCell.querySelector('small');
                                if (!smallElement) {
                                    smallElement = document.createElement('small');
                                    smallElement.className = 'text-muted';
                                    parentCell.appendChild(document.createElement('br'));
                                    parentCell.appendChild(smallElement);
                                }
                                smallElement.textContent = workStatus.sub_text;
                            }
                        } else {
                            console.error('خطأ في الحصول على حالة العمل:', data.error);
                            // استخدام الطريقة الافتراضية
                            updateWorkStatusDisplayFallback(workStatusElement, newStatus);
                        }
                    })
                    .catch(error => {
                        console.error('خطأ في API:', error);
                        // استخدام الطريقة الافتراضية
                        updateWorkStatusDisplayFallback(workStatusElement, newStatus);
                    });
            } else {
                // استخدام الطريقة الافتراضية إذا لم يكن لدينا معرف العون
                updateWorkStatusDisplayFallback(workStatusElement, newStatus);
            }
        }

        // دالة احتياطية لتحديث حالة العمل
        function updateWorkStatusDisplayFallback(workStatusElement, newStatus) {
            let badgeClass = '';
            let icon = '';
            let text = '';
            let subText = '';

            // تحديد حالة العمل بناءً على حالة الحضور (الطريقة الافتراضية)
            if (newStatus === 'standby') {
                badgeClass = 'badge-warning';
                icon = 'fas fa-user-shield';
                text = 'احتياطي';
                subText = 'متاح للتوزيع';
            } else if (newStatus === 'present') {
                badgeClass = 'badge-success';
                icon = 'fas fa-briefcase';
                text = 'قيد العمل';
                subText = 'حسب الجدولة';
            } else if (newStatus === 'absent') {
                badgeClass = 'badge-danger';
                icon = 'fas fa-times';
                text = 'غائب';
                subText = 'غير متاح';
            } else if (newStatus === 'on_mission') {
                badgeClass = 'badge-info';
                icon = 'fas fa-car';
                text = 'في مهمة';
                subText = 'غير متاح';
            } else {
                badgeClass = 'badge-secondary';
                icon = 'fas fa-question-circle';
                text = 'غير محدد';
                subText = '';
            }

            // تحديث محتوى العنصر
            workStatusElement.className = `badge ${badgeClass} work-status`;
            workStatusElement.innerHTML = `
                <i class="${icon}"></i> ${text}
            `;

            // تحديث النص الفرعي إذا وجد
            const parentCell = workStatusElement.closest('td');
            if (parentCell) {
                let smallElement = parentCell.querySelector('small');
                if (!smallElement) {
                    smallElement = document.createElement('small');
                    smallElement.className = 'text-muted';
                    parentCell.appendChild(document.createElement('br'));
                    parentCell.appendChild(smallElement);
                }
                smallElement.textContent = subText;
            }
        }

        // فلترة الأعوان حسب الفرقة ونظام العمل
        function filterPersonnel() {
            const searchTerm = document.getElementById('personnelSearch').value.toLowerCase();
            const statusFilter = document.getElementById('personnelFilter').value;
            const shiftFilter = document.getElementById('shiftFilter').value;
            const workSystemFilter = document.getElementById('workSystemFilter').value;

            const rows = document.querySelectorAll('#personnelTable tbody tr');

            rows.forEach(row => {
                const name = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
                const status = row.getAttribute('data-status');

                // البحث عن badges في الصف
                const badges = row.querySelectorAll('.badge');
                let shiftBadge = null;
                let workSystemBadge = null;

                badges.forEach(badge => {
                    const badgeText = badge.textContent;
                    if (badgeText.includes('الأولى') || badgeText.includes('الثانية') || badgeText.includes('الثالثة')) {
                        shiftBadge = badge;
                    }
                    if (badgeText.includes('24') || badgeText.includes('8')) {
                        workSystemBadge = badge;
                    }
                });

                let showRow = true;

                // فلترة البحث
                if (searchTerm && !name.includes(searchTerm)) {
                    showRow = false;
                }

                // فلترة الحالة
                if (statusFilter && status !== statusFilter) {
                    showRow = false;
                }

                // فلترة الفرقة
                if (shiftFilter && shiftBadge) {
                    const shiftText = shiftBadge.textContent;
                    if (shiftFilter === 'shift_1' && !shiftText.includes('الأولى')) showRow = false;
                    if (shiftFilter === 'shift_2' && !shiftText.includes('الثانية')) showRow = false;
                    if (shiftFilter === 'shift_3' && !shiftText.includes('الثالثة')) showRow = false;
                }

                // فلترة نظام العمل
                if (workSystemFilter && workSystemBadge) {
                    const workSystemText = workSystemBadge.textContent;
                    if (workSystemFilter === '24_hours' && !workSystemText.includes('24')) showRow = false;
                    if (workSystemFilter === '8_hours' && !workSystemText.includes('8')) showRow = false;
                }

                row.style.display = showRow ? '' : 'none';
            });
        }

        // ربط الفلاتر بالأحداث
        document.addEventListener('DOMContentLoaded', function() {
            // ربط أحداث الفلترة
            const personnelSearch = document.getElementById('personnelSearch');
            const personnelFilter = document.getElementById('personnelFilter');
            const shiftFilter = document.getElementById('shiftFilter');
            const workSystemFilter = document.getElementById('workSystemFilter');

            if (personnelSearch) personnelSearch.addEventListener('input', filterPersonnel);
            if (personnelFilter) personnelFilter.addEventListener('change', filterPersonnel);
            if (shiftFilter) shiftFilter.addEventListener('change', filterPersonnel);
            if (workSystemFilter) workSystemFilter.addEventListener('change', filterPersonnel);
        });

        // استقبال رسائل التزامن من صفحة التوزيع وصفحة التدخلات
        window.addEventListener('message', function(event) {
            console.log('تم استقبال رسالة تزامن:', event.data);

            if (event.data.type === 'vehicle_in_intervention') {
                // تحديث حالة الوسيلة إلى "في تدخل"
                const vehicleId = event.data.vehicleId;
                const interventionId = event.data.interventionId;

                updateVehicleInterventionStatus(vehicleId, 'في تدخل', interventionId);

            } else if (event.data.type === 'intervention_completed') {
                // تحديث حالة الوسيلة إلى "متاحة"
                const interventionId = event.data.interventionId;

                // البحث عن جميع الوسائل في هذا التدخل وتحديث حالتها
                document.querySelectorAll('.intervention-status').forEach(statusDiv => {
                    const badge = statusDiv.querySelector('.badge');
                    if (badge && badge.textContent.includes('تدخل')) {
                        updateVehicleInterventionStatus(statusDiv.id.replace('intervention-status-', ''), 'متاحة', null);
                    }
                });

            } else if (event.data.type === 'readiness_update') {
                const vehicleId = event.data.vehicle_id;
                const readinessScore = event.data.readiness_score;
                const readinessStatus = event.data.readiness_status;
                const crewCount = event.data.crew_count;
                const manualConfirm = event.data.manual_confirm;

                console.log(`تحديث جاهزية الوسيلة ${vehicleId}: ${readinessScore}% - ${readinessStatus} - ${crewCount} أعوان`);

                // البحث عن صف الوسيلة في الجدول
                const vehicleRow = document.querySelector(`#vehiclesTable tbody tr[data-vehicle-id="${vehicleId}"]`);
                if (vehicleRow) {
                    // تحديث عمود الجاهزية
                    const readinessContainer = vehicleRow.querySelector('.readiness-indicator');
                    if (readinessContainer) {
                        const scoreElement = readinessContainer.querySelector('.readiness-score');
                        const statusElement = readinessContainer.querySelector('.readiness-status');

                        if (scoreElement) {
                            scoreElement.textContent = `${readinessScore}%`;
                            scoreElement.setAttribute('data-score', readinessScore);

                            // تحديث لون النسبة
                            if (readinessScore >= 80) {
                                scoreElement.style.color = '#28a745';
                            } else if (readinessScore >= 60) {
                                scoreElement.style.color = '#ffc107';
                            } else {
                                scoreElement.style.color = '#dc3545';
                            }
                        }

                        if (statusElement) {
                            statusElement.textContent = readinessStatus;

                            // تحديث class الحالة
                            statusElement.className = 'readiness-status';
                            if (readinessScore >= 80) {
                                statusElement.classList.add('ready');
                            } else if (manualConfirm) {
                                statusElement.classList.add('manually_confirmed');
                            } else {
                                statusElement.classList.add('not_ready');
                            }
                        }
                    }

                    // إظهار إشعار للمستخدم
                    showAlert('تحديث الجاهزية',
                        `تم تحديث جاهزية الوسيلة إلى ${readinessScore}% (${crewCount} أعوان معينين)`,
                        'info');
                }
            }
        });

        // فحص localStorage للتزامن عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            const lastUpdate = localStorage.getItem('last_readiness_update');
            if (lastUpdate) {
                try {
                    const updateData = JSON.parse(lastUpdate);
                    const timeDiff = new Date() - new Date(updateData.timestamp);

                    // إذا كان التحديث خلال آخر 30 ثانية، طبقه
                    if (timeDiff < 30000) {
                        window.dispatchEvent(new MessageEvent('message', {
                            data: {
                                type: 'readiness_update',
                                vehicle_id: updateData.vehicle_id,
                                readiness_score: updateData.readiness_score,
                                readiness_status: updateData.readiness_status,
                                crew_count: updateData.crew_count,
                                manual_confirm: updateData.manual_confirm
                            }
                        }));
                    }
                } catch (e) {
                    console.error('خطأ في تحليل بيانات التزامن:', e);
                }
            }
        });

        // دالة تحديث حالة التدخل للوسيلة
        function updateVehicleInterventionStatus(vehicleId, status, interventionId) {
            const statusDiv = document.getElementById(`intervention-status-${vehicleId}`);
            if (statusDiv) {
                const badge = statusDiv.querySelector('.badge');
                if (badge) {
                    if (status === 'في تدخل') {
                        badge.className = 'badge badge-danger';
                        badge.innerHTML = '<i class="fas fa-ambulance"></i> في تدخل';
                        if (interventionId) {
                            badge.title = `التدخل رقم: ${interventionId}`;
                        }
                    } else {
                        badge.className = 'badge badge-secondary';
                        badge.innerHTML = '<i class="fas fa-check-circle"></i> متاحة';
                        badge.title = '';
                    }
                }
            }
        }

        // فتح صفحة التدخلات اليومية
        function openDailyInterventionsPage() {
            window.open('/coordination-center/daily-interventions/', '_blank');
        }

        // دالة مسح جميع الفلاتر
        function clearAllFilters() {
            // مسح قيم الفلاتر
            const personnelSearch = document.getElementById('personnelSearch');
            const personnelFilter = document.getElementById('personnelFilter');
            const shiftFilter = document.getElementById('shiftFilter');
            const workSystemFilter = document.getElementById('workSystemFilter');

            if (personnelSearch) personnelSearch.value = '';
            if (personnelFilter) personnelFilter.value = '';
            if (shiftFilter) shiftFilter.value = '';
            if (workSystemFilter) workSystemFilter.value = '';

            // إظهار جميع الصفوف
            const rows = document.querySelectorAll('#personnelTable tbody tr');
            rows.forEach(row => {
                row.style.display = '';
            });

            // إشعار المستخدم
            showNotification('تم مسح جميع الفلاتر', 'success');
        }

        // دالة إشعار محسنة
        function showNotification(message, type = 'info') {
            // إنشاء عنصر الإشعار
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} notification-toast`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                animation: slideInRight 0.3s ease;
            `;

            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
                    <span>${message}</span>
                    <button type="button" class="close" style="margin-left: auto; background: none; border: none; font-size: 1.2rem; opacity: 0.7;">
                        <span>&times;</span>
                    </button>
                </div>
            `;

            // إضافة للصفحة
            document.body.appendChild(notification);

            // إزالة تلقائية بعد 3 ثوان
            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);

            // إزالة عند النقر على X
            notification.querySelector('.close').addEventListener('click', () => {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            });
        }

        // وظيفة العودة إلى الأعلى
        document.getElementById('back-to-top').addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    </script>
</body>
</html>
