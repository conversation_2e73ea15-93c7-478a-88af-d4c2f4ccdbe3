{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحويل عون بين الفرق - نظام الحماية المدنية</title>
    
    <!-- نظام CSS الموحد الجديد -->
    <link rel="stylesheet" href="{% static 'css/dpc-unified.css' %}">
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/home.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
</head>

<body>
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle clickable" id="sidebar-toggle">
            </div>
        </div>
    </header>

    {% include 'includes/sidebar.html' %}

    <main class="main-content">
        <div class="container-fluid">
            <!-- رسائل النظام -->
            {% if messages %}
                <div class="messages-container">
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-triangle{% else %}info-circle{% endif %}"></i>
                            {{ message }}
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}

            <!-- عنوان الصفحة -->
            <div class="page-header">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h2 class="page-title">
                            <i class="fas fa-exchange-alt text-warning"></i>
                            تحويل عون بين الفرق
                        </h2>
                        <p class="page-subtitle">
                            الوحدة: <strong>{{ unit.name }}</strong>
                        </p>
                    </div>
                    <div class="col-md-6 text-right">
                        <a href="/coordination-center/unified-morning-check/?unit_id={{ unit.id }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i> العودة للصفحة الموحدة
                        </a>
                    </div>
                </div>
            </div>

            <!-- نموذج تحويل العون -->
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="fas fa-user-edit"></i>
                        تحويل العون بين الفرق
                    </h4>
                </div>
                <div class="card-body">
                    <!-- معلومات العون -->
                    <div class="personnel-info-card mb-4">
                        <div class="row">
                            <div class="col-md-6">
                                <h5><i class="fas fa-user text-primary"></i> معلومات العون</h5>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>الاسم الكامل:</strong></td>
                                        <td>{{ personnel.full_name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>رقم التسجيل:</strong></td>
                                        <td>{{ personnel.registration_number|default:"غير محدد" }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>الرتبة:</strong></td>
                                        <td>{{ personnel.rank|default:"غير محدد" }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>المنصب:</strong></td>
                                        <td>{{ personnel.position|default:"غير محدد" }}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h5><i class="fas fa-users text-info"></i> معلومات الفرقة</h5>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>نظام العمل:</strong></td>
                                        <td>
                                            <span class="badge badge-primary">{{ personnel.get_work_system_display }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>الفرقة الحالية:</strong></td>
                                        <td>
                                            {% if personnel.assigned_shift %}
                                                <span class="badge badge-success">{{ personnel.get_shift_display_arabic }}</span>
                                            {% else %}
                                                <span class="badge badge-secondary">غير محدد</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- نموذج التحويل -->
                    {% if personnel.work_system == '24_hours' %}
                    <form method="post" id="transferForm">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label required">
                                        <i class="fas fa-users text-primary"></i> الفرقة الجديدة
                                    </label>
                                    <select name="target_shift" class="form-control" required>
                                        <option value="">-- اختر الفرقة الجديدة --</option>
                                        {% for shift_value, shift_label in shift_choices %}
                                            {% if shift_value != personnel.assigned_shift %}
                                                <option value="{{ shift_value }}">{{ shift_label }}</option>
                                            {% endif %}
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label required">
                                        <i class="fas fa-comment text-primary"></i> سبب التحويل
                                    </label>
                                    <select name="reason" class="form-control" required>
                                        <option value="">-- اختر سبب التحويل --</option>
                                        <option value="إعادة توزيع الأعوان">إعادة توزيع الأعوان</option>
                                        <option value="طلب العون">طلب العون</option>
                                        <option value="ضرورة خدمية">ضرورة خدمية</option>
                                        <option value="تنظيم العمل">تنظيم العمل</option>
                                        <option value="أخرى">أخرى</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions text-center mt-4">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-check"></i> تأكيد التحويل
                            </button>
                            <a href="/coordination-center/unified-morning-check/?unit_id={{ unit.id }}" class="btn btn-secondary btn-lg ml-3">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                    {% else %}
                    <div class="alert alert-warning text-center">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تنبيه:</strong> هذا العون يعمل بنظام 8 ساعات ولا يمكن تحويله بين الفرق.
                        <br>
                        يمكنك تغيير نظام العمل أولاً إلى نظام 24 ساعة من الصفحة الموحدة.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </main>

    <!-- تضمين ملفات JavaScript -->
    <script src="{% static 'js/sidebar.js' %}"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // إغلاق الرسائل تلقائياً
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);

        // التحقق من النموذج
        document.getElementById('transferForm')?.addEventListener('submit', function(e) {
            const targetShift = document.querySelector('select[name="target_shift"]').value;
            const reason = document.querySelector('select[name="reason"]').value;

            if (!targetShift || !reason) {
                e.preventDefault();
                alert('يرجى ملء جميع الحقول المطلوبة');
                return false;
            }

            // تأكيد التحويل
            const personnelName = '{{ personnel.full_name }}';
            const shiftName = document.querySelector('select[name="target_shift"] option:checked').textContent;
            
            if (!confirm(`هل أنت متأكد من تحويل العون ${personnelName} إلى ${shiftName}؟`)) {
                e.preventDefault();
                return false;
            }
        });
    </script>
</body>
</html>
