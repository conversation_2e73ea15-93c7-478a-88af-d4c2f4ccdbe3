{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحماية المدنية الجزائرية - نموذج حرائق الغابات</title>
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/home.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle" id="sidebar-toggle" style="cursor: pointer;">
            </div>
        </div>
    </header>

    {% include 'includes/sidebar.html' %}

    <main>
        <div class="home-container">
            <div class="fire-details">
                <div class="fire-icon">
                    <i class="fas fa-tree"></i>
                </div>
                <div class="fire-title">نموذج حرائق الغابات</div>

                <div id="alert-container" class="hidden"></div>

                <div class="fire-form">
                    <form id="forestFireForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="telegram_number">رقم البرقية</label>
                                <input type="number" class="form-control" id="telegram_number" name="telegram_number" min="1" required>
                            </div>

                            <div class="form-group">
                                <label for="date">التاريخ</label>
                                <input type="date" class="form-control" id="date" name="date" required lang="ar">
                            </div>
                        </div>

                        <div class="section-title">ساعة التدخل</div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="intervention_hour">الساعات</label>
                                <input type="number" class="form-control" id="intervention_hour" name="intervention_hour" min="0" max="23" value="0" step="1" required>
                                <small class="form-text">سا</small>
                            </div>

                            <div class="form-group">
                                <label for="intervention_minutes">الدقائق</label>
                                <input type="number" class="form-control" id="intervention_minutes" name="intervention_minutes" min="0" max="59" value="0" step="1" required>
                                <small class="form-text">د</small>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="intervening_unit">الوحدة المتدخلة</label>
                                <select class="form-control" id="intervening_unit" name="intervening_unit" required>
                                    <option value="">اختر الوحدة المتدخلة</option>
                                    {% for unit in units %}
                                    <option value="{{ unit.name }}">{{ unit.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="municipality">البلدية</label>
                                <input type="text" class="form-control" id="municipality" name="municipality" required placeholder="أدخل اسم البلدية">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="location_name">المكان المسمى</label>
                                <input type="text" class="form-control" id="location_name" name="location_name" required placeholder="أدخل اسم المكان">
                            </div>
                        </div>

                        <div class="section-title">مدة العملية</div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="operation_duration_hours">الساعات</label>
                                <input type="number" class="form-control" id="operation_duration_hours" name="operation_duration_hours" min="0" value="0" step="1" required>
                                <small class="form-text">سا</small>
                            </div>

                            <div class="form-group">
                                <label for="operation_duration_minutes">الدقائق</label>
                                <input type="number" class="form-control" id="operation_duration_minutes" name="operation_duration_minutes" min="0" max="59" value="0" step="1" required>
                                <small class="form-text">د</small>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="intervention_means">الوسائل المتدخلة</label>
                                <textarea class="form-control multi-line-text" id="intervention_means" name="intervention_means" required placeholder="أدخل الوسائل المستخدمة" rows="2"></textarea>
                            </div>

                            <div class="form-group">
                                <label for="loss_nature">طبيعة الخسائر</label>
                                <textarea class="form-control multi-line-text" id="loss_nature" name="loss_nature" required placeholder="أدخل طبيعة الخسائر" rows="2"></textarea>
                            </div>
                        </div>

                        <div class="section-title">مساحة الخسائر المسجلة</div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="losses_hectare">الخسائر بالهكتار</label>
                                <input type="number" class="form-control" id="losses_hectare" name="losses_hectare" min="0" value="0" step="0.01" required>
                            </div>

                            <div class="form-group">
                                <label for="losses_are">الخسائر بالآر</label>
                                <input type="number" class="form-control" id="losses_are" name="losses_are" min="0" value="0" step="0.01" required>
                            </div>

                            <div class="form-group">
                                <label for="losses_square_meter">الخسائر بالمتر مربع</label>
                                <input type="number" class="form-control" id="losses_square_meter" name="losses_square_meter" min="0" value="0" step="0.01" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="other_loss_nature">طبيعة الخسائر الأخرى</label>
                                <textarea class="form-control multi-line-text" id="other_loss_nature" name="other_loss_nature" placeholder="أدخل طبيعة الخسائر الأخرى (اختياري)" rows="2"></textarea>
                            </div>

                            <div class="form-group">
                                <label for="other_loss_count">عدد الخسائر</label>
                                <input type="number" class="form-control" id="other_loss_count" name="other_loss_count" min="0" value="0" placeholder="عدد الخسائر الأخرى (اختياري)">
                            </div>
                        </div>

                        <div class="section-title">معلومات أخرى حول وضعية التحكم في الحريق و العائلات</div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="fire_control_status">وضعية التحكم في الحريق</label>
                                <select class="form-control" id="fire_control_status" name="fire_control_status" required>
                                    <option value="أخمد نهائيا">أخمد نهائيا</option>
                                    <option value="مسيطر عليه">مسيطر عليه</option>
                                    <option value="خارج عن السيطرة">خارج عن السيطرة</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="injured_count">عدد الجرحى</label>
                                <input type="number" class="form-control" id="injured_count" name="injured_count" min="0" value="0">
                            </div>

                            <div class="form-group">
                                <label for="deaths_count">عدد الوفيات</label>
                                <input type="number" class="form-control" id="deaths_count" name="deaths_count" min="0" value="0">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="evacuated_families_count">عدد العائلات الذين تم إجلاءهم</label>
                                <input type="number" class="form-control" id="evacuated_families_count" name="evacuated_families_count" min="0" value="0">
                            </div>

                            <div class="form-group">
                                <label for="evacuated_people_count">عدد الأشخاص الذين تم إجلاءهم</label>
                                <input type="number" class="form-control" id="evacuated_people_count" name="evacuated_people_count" min="0" value="0">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="evacuation_locations">أماكن إجلاء العائلات بالتفصيل</label>
                                <textarea class="form-control multi-line-text" id="evacuation_locations" name="evacuation_locations" placeholder="أدخل أماكن إجلاء العائلات بالتفصيل (اختياري)" rows="2"></textarea>
                            </div>

                            <div class="form-group">
                                <label for="family_care_measures">الإجراءات المتخذة للتكفل بالعائلات المتضررة</label>
                                <textarea class="form-control multi-line-text" id="family_care_measures" name="family_care_measures" placeholder="أدخل الإجراءات المتخذة للتكفل بالعائلات المتضررة (اختياري)" rows="2"></textarea>
                            </div>
                        </div>

                        <div class="main-buttons-container">
                            <div class="main-buttons">
                                <a href="{% url 'forest_crop_fires' %}" class="main-btn back-btn">
                                    <i class="fas fa-arrow-right"></i> العودة
                                </a>
                                <a href="{% url 'coordination_center' %}" class="main-btn home-btn">
                                    <i class="fas fa-home"></i> مركز التنسيق
                                </a>
                                <button type="button" id="saveButton" class="main-btn save-btn">
                                    <i class="fas fa-save"></i> حفظ
                                </button>
                            </div>
                        </div>

                        <!-- الأزرار العائمة -->
                        <div class="floating-buttons">
                            <a href="{% url 'forest_fires_table' %}" class="floating-btn tables-btn" title="جدول حرائق الغابات">
                                <i class="fas fa-table"></i>
                            </a>
                            <a href="{% url 'coordination_center' %}" class="floating-btn home-btn" title="مركز التنسيق">
                                <i class="fas fa-home"></i>
                            </a>
                            <button type="button" id="floatingSaveBtn" class="floating-btn save-btn" title="حفظ">
                                <i class="fas fa-save"></i>
                            </button>
                            <a href="#" id="back-to-top" class="floating-btn top-btn" title="العودة إلى الأعلى">
                                <i class="fas fa-arrow-up"></i>
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <p>حقوق النشر © 2025 جميع الحقوق محفوظة - تم التطوير بواسطة عبد الرزاق مختاري</p>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('forestFireForm');
            const saveButton = document.getElementById('saveButton');
            const floatingSaveBtn = document.getElementById('floatingSaveBtn');
            const alertContainer = document.getElementById('alert-container');
            const dateInput = document.getElementById('date');
            const backToTopBtn = document.getElementById('back-to-top');

            // Set up Arabic month names
            const arabicMonths = {
                '01': 'جانفي',
                '02': 'فيفري',
                '03': 'مارس',
                '04': 'أفريل',
                '05': 'ماي',
                '06': 'جوان',
                '07': 'جويلية',
                '08': 'أوت',
                '09': 'سبتمبر',
                '10': 'أكتوبر',
                '11': 'نوفمبر',
                '12': 'ديسمبر'
            };

            // Format date display in Arabic
            dateInput.addEventListener('change', function() {
                if (this.value) {
                    const dateParts = this.value.split('-');
                    const year = dateParts[0];
                    const month = dateParts[1];
                    const day = dateParts[2];

                    // Store the formatted date as a data attribute
                    this.setAttribute('data-formatted-date', `${day} ${arabicMonths[month]} ${year}`);
                }
            });

            // Validate time inputs
            const interventionHour = document.getElementById('intervention_hour');
            const interventionMinutes = document.getElementById('intervention_minutes');
            const operationHours = document.getElementById('operation_duration_hours');
            const operationMinutes = document.getElementById('operation_duration_minutes');

            function validateTimeInput(input, max) {
                input.addEventListener('input', function() {
                    let value = parseInt(this.value);
                    if (isNaN(value) || value < 0) {
                        this.value = 0;
                    } else if (max && value > max) {
                        this.value = max;
                    }
                });
            }

            validateTimeInput(interventionHour, 23);
            validateTimeInput(interventionMinutes, 59);
            validateTimeInput(operationHours);
            validateTimeInput(operationMinutes, 59);

            // Auto-clear zeros for integer inputs when user starts typing
            const integerInputs = [
                'telegram_number',
                'intervention_hour',
                'intervention_minutes',
                'operation_duration_hours',
                'operation_duration_minutes',
                'losses_hectare',
                'losses_are',
                'losses_square_meter',
                'other_loss_count',
                'injured_count',
                'deaths_count',
                'evacuated_families_count',
                'evacuated_people_count'
            ];

            integerInputs.forEach(inputId => {
                const input = document.getElementById(inputId);
                if (input) {
                    input.addEventListener('focus', function() {
                        if (this.value === '0') {
                            this.value = '';
                        }
                    });

                    input.addEventListener('blur', function() {
                        if (this.value === '' || this.value === '0') {
                            this.value = '0';
                        }
                    });
                }
            });

            // Back to top functionality
            backToTopBtn.addEventListener('click', function(e) {
                e.preventDefault();
                window.scrollTo({ top: 0, behavior: 'smooth' });
            });

            // Function to show alert
            function showAlert(message, type) {
                alertContainer.className = `alert alert-${type}`;
                alertContainer.textContent = message;
                alertContainer.classList.remove('hidden');

                // Auto hide after 5 seconds
                setTimeout(() => {
                    alertContainer.classList.add('hidden');
                }, 5000);
            }

            // Function to validate form
            function validateForm() {
                const telegramNumber = document.getElementById('telegram_number').value;
                const date = document.getElementById('date').value;
                const interveningUnit = document.getElementById('intervening_unit').value;
                const municipality = document.getElementById('municipality').value;
                const locationName = document.getElementById('location_name').value;
                const interventionMeans = document.getElementById('intervention_means').value;
                const lossNature = document.getElementById('loss_nature').value;

                const missingFields = [];

                if (!telegramNumber) missingFields.push('رقم البرقية');
                if (!date) missingFields.push('التاريخ');
                if (!interveningUnit) missingFields.push('الوحدة المتدخلة');
                if (!municipality) missingFields.push('البلدية');
                if (!locationName) missingFields.push('المكان المسمى');
                if (!interventionMeans) missingFields.push('الوسائل المتدخلة');
                if (!lossNature) missingFields.push('طبيعة الخسائر');

                if (missingFields.length > 0) {
                    showAlert('يرجى ملء الحقول التالية: ' + missingFields.join('، '), 'danger');
                    return false;
                }

                // Check for negative numbers
                if (parseInt(telegramNumber) < 1) {
                    showAlert('رقم البرقية يجب أن يكون أكبر من صفر', 'danger');
                    return false;
                }

                return true;
            }

            // Save function
            function saveForm() {
                if (!validateForm()) {
                    return;
                }

                // Format intervention time
                let intHours = parseInt(document.getElementById('intervention_hour').value) || 0;
                let intMinutes = parseInt(document.getElementById('intervention_minutes').value) || 0;
                const interventionTime = `${intHours} سا ${intMinutes < 10 ? '0' + intMinutes : intMinutes} د`;

                // Format operation duration
                let opHours = parseInt(document.getElementById('operation_duration_hours').value) || 0;
                let opMinutes = parseInt(document.getElementById('operation_duration_minutes').value) || 0;
                const operationDuration = `${opHours} سا ${opMinutes < 10 ? '0' + opMinutes : opMinutes} د`;

                // Prepare data for submission
                const dateInput = document.getElementById('date');
                const formData = {
                    telegram_number: parseInt(document.getElementById('telegram_number').value),
                    date: dateInput.value,
                    formatted_date: dateInput.getAttribute('data-formatted-date') || dateInput.value,
                    intervention_time: interventionTime,
                    intervening_unit: document.getElementById('intervening_unit').value,
                    municipality: document.getElementById('municipality').value,
                    location_name: document.getElementById('location_name').value,
                    operation_duration: operationDuration,
                    intervention_means: document.getElementById('intervention_means').value.replace(/\n/g, '/n'),
                    loss_nature: document.getElementById('loss_nature').value.replace(/\n/g, '/n'),
                    losses_hectare: parseFloat(document.getElementById('losses_hectare').value) || 0,
                    losses_are: parseFloat(document.getElementById('losses_are').value) || 0,
                    losses_square_meter: parseFloat(document.getElementById('losses_square_meter').value) || 0,
                    other_loss_nature: document.getElementById('other_loss_nature').value.replace(/\n/g, '/n') || '',
                    other_loss_count: parseInt(document.getElementById('other_loss_count').value) || 0,
                    // New fields
                    fire_control_status: document.getElementById('fire_control_status').value,
                    injured_count: parseInt(document.getElementById('injured_count').value) || 0,
                    deaths_count: parseInt(document.getElementById('deaths_count').value) || 0,
                    evacuated_families_count: parseInt(document.getElementById('evacuated_families_count').value) || 0,
                    evacuated_people_count: parseInt(document.getElementById('evacuated_people_count').value) || 0,
                    evacuation_locations: document.getElementById('evacuation_locations').value.replace(/\n/g, '/n') || '',
                    family_care_measures: document.getElementById('family_care_measures').value.replace(/\n/g, '/n') || ''
                };

                // Send data to server
                fetch(window.location.href, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify(formData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        showAlert(data.message, 'success');
                        // Reset form
                        form.reset();
                        // Reset time inputs
                        document.getElementById('intervention_hour').value = "0";
                        document.getElementById('intervention_minutes').value = "0";
                        document.getElementById('operation_duration_hours').value = "0";
                        document.getElementById('operation_duration_minutes').value = "0";
                        document.getElementById('losses_hectare').value = "0";
                        document.getElementById('losses_are').value = "0";
                        document.getElementById('losses_square_meter').value = "0";
                        document.getElementById('other_loss_count').value = "0";
                        document.getElementById('injured_count').value = "0";
                        document.getElementById('deaths_count').value = "0";
                        document.getElementById('evacuated_families_count').value = "0";
                        document.getElementById('evacuated_people_count').value = "0";
                    } else {
                        showAlert(data.message, 'danger');
                    }
                })
                .catch(error => {
                    showAlert('حدث خطأ أثناء حفظ البيانات: ' + error, 'danger');
                });
            }

            // Save button click handlers
            saveButton.addEventListener('click', saveForm);
            floatingSaveBtn.addEventListener('click', saveForm);

            // Function to get CSRF token
            function getCookie(name) {
                let cookieValue = null;
                if (document.cookie && document.cookie !== '') {
                    const cookies = document.cookie.split(';');
                    for (let i = 0; i < cookies.length; i++) {
                        const cookie = cookies[i].trim();
                        if (cookie.substring(0, name.length + 1) === (name + '=')) {
                            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                            break;
                        }
                    }
                }
                return cookieValue;
            }
        });
    </script>
    <script src="{% static 'js/sidebar.js' %}"></script>

    <style>
        /* Fire form styles */
        .fire-details {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-top: 30px;
        }

        .fire-icon {
            font-size: 4rem;
            color: #198754;
            margin-bottom: 20px;
            text-align: center;
        }

        .fire-title {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
            color: #1d3557;
        }

        .fire-form {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .form-row {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 20px;
            justify-content: space-between;
        }

        .form-group {
            margin-bottom: 15px;
            flex: 1;
            min-width: 200px;
            margin-left: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #1d3557;
        }

        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            font-size: 1rem;
        }

        .form-control:focus {
            border-color: #1d3557;
            outline: none;
            box-shadow: 0 0 0 0.2rem rgba(29, 53, 87, 0.25);
        }

        /* Multi-line text area styles */
        .multi-line-text {
            resize: vertical;
            min-height: 60px;
            max-height: 120px;
            overflow-y: auto;
            line-height: 1.4;
        }

        .form-text {
            font-size: 0.875rem;
            color: #6c757d;
            margin-top: 5px;
        }

        .section-title {
            font-size: 1.4rem;
            font-weight: bold;
            margin: 20px 0;
            text-align: center;
            color: #1d3557;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .hidden {
            display: none;
        }

        select.form-control {
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%231d3557' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 0.75rem center;
            background-size: 16px 12px;
            padding-right: 2rem;
        }

        /* Main buttons container */
        .main-buttons-container {
            display: flex;
            justify-content: center;
            margin-top: 30px;
            margin-bottom: 20px;
        }

        /* Main buttons */
        .main-buttons {
            display: flex;
            gap: 20px;
            width: 100%;
            max-width: 800px;
            direction: ltr;
        }

        .main-btn {
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: bold;
            font-size: 18px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            transition: all 0.3s;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
            flex: 1;
            min-width: 200px;
            border: none;
        }

        .main-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .save-btn {
            background-color: #28a745;
            color: white;
        }

        .save-btn:hover {
            background-color: #218838;
        }

        .home-btn {
            background-color: #1d3557;
            color: white;
        }

        .home-btn:hover {
            background-color: #457b9d;
        }

        .back-btn {
            background-color: #6c757d;
            color: white;
        }

        .back-btn:hover {
            background-color: #545b62;
        }

        /* Floating buttons styles */
        .floating-buttons {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .floating-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            transition: all 0.3s;
            text-decoration: none;
            font-size: 18px;
            border: none;
            cursor: pointer;
        }

        .floating-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
        }

        .tables-btn {
            background-color: #0d47a1;
        }

        .tables-btn:hover {
            background-color: #0a3880;
        }

        .floating-btn.home-btn {
            background-color: #28a745;
        }

        .floating-btn.home-btn:hover {
            background-color: #218838;
        }

        .floating-btn.save-btn {
            background-color: #dc3545;
        }

        .floating-btn.save-btn:hover {
            background-color: #c82333;
        }

        .top-btn {
            background-color: #0d6efd;
        }

        .top-btn:hover {
            background-color: #0a58ca;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
            }

            .form-group {
                width: 100%;
                margin-left: 0;
            }

            .floating-buttons {
                bottom: 10px;
                right: 10px;
                gap: 8px;
            }

            .floating-btn {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }

            .main-buttons {
                flex-direction: column;
                gap: 15px;
                width: 90%;
            }

            .main-btn {
                width: 100%;
                padding: 12px 20px;
                font-size: 16px;
            }
        }
    </style>
</body>
</html>
