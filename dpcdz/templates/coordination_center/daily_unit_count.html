{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحماية المدنية الجزائرية - التعداد الصباحي للوحدة</title>
    <!-- نظام CSS الموحد الجديد -->
    <link rel="stylesheet" href="{% static 'css/dpc-unified.css' %}">
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/home.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle clickable" id="sidebar-toggle">
            </div>
        </div>
    </header>

    {% include 'includes/sidebar.html' %}

    <main>
        <div class="home-container">
            {% csrf_token %}
            <!-- العنوان الرئيسي -->
            <div class="unit-count-details">
                <div class="unit-count-icon">
                    <i class="fas fa-table"></i>
                </div>
                <div class="unit-count-title">التعداد الصباحي للوحدة - {{ today|date:"d/m/Y" }}</div>
                {% if daily_count %}
                    <div class="unit-count-subtitle">وحدة: {{ daily_count.unit.name }}</div>
                {% endif %}
            </div>

            <!-- اختيار الوحدة (للمدراء فقط) -->
            {% if user_role != 'unit_coordinator' %}
            <div class="unit-selection">
                <div class="form-group">
                    <label for="unit-select">اختر الوحدة:</label>
                    <select id="unit-select" class="form-control" onchange="selectUnit()">
                        <option value="">-- اختر الوحدة --</option>
                        {% for unit in units %}
                            <option value="{{ unit.id }}" {% if daily_count and daily_count.unit.id == unit.id %}selected{% endif %}>
                                {{ unit.name }} - {{ unit.get_wilaya_display }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            {% endif %}

            <!-- أزرار التحكم -->
            {% if daily_count %}
            <div class="control-buttons">
                <a href="{% url 'daily_unit_reports' %}{% if daily_count %}?unit_id={{ daily_count.unit.id }}{% endif %}" class="btn btn-info">
                    <i class="fas fa-chart-bar"></i> التقارير اليومية
                </a>
                <button type="button" class="btn btn-success" onclick="exportToExcel()">
                    <i class="fas fa-file-excel"></i> تصدير Excel
                </button>
            </div>
            {% endif %}

            {% if daily_count %}
            <!-- قسم العتاد البشري -->
            <div class="section-card">
                <div class="section-header">
                    <h3><i class="fas fa-users"></i> العتاد البشري</h3>
                    <div class="header-controls">
                        <input type="text" id="personnelSearch" class="form-control search-input" placeholder="البحث في الأعوان...">
                        <select id="personnelStatusFilter" class="form-control filter-select">
                            <option value="">جميع الحالات</option>
                            <option value="present">حاضر</option>
                            <option value="absent">غائب</option>
                            <option value="on_mission">في مهمة</option>
                        </select>
                        <select id="personnelRankFilter" class="form-control filter-select">
                            <option value="">جميع الرتب</option>
                            <option value="رقيب">رقيب</option>
                            <option value="رقيب أول">رقيب أول</option>
                            <option value="رقيب رئيسي">رقيب رئيسي</option>
                            <option value="مساعد">مساعد</option>
                            <option value="مساعد أول">مساعد أول</option>
                            <option value="مساعد رئيسي">مساعد رئيسي</option>
                            <option value="ملازم">ملازم</option>
                            <option value="ملازم أول">ملازم أول</option>
                            <option value="نقيب">نقيب</option>
                            <option value="رائد">رائد</option>
                            <option value="مقدم">مقدم</option>
                            <option value="عقيد">عقيد</option>
                        </select>
                        <select id="personnelPositionFilter" class="form-control filter-select">
                            <option value="">جميع المناصب</option>
                            <option value="رئيس الوحدة">رئيس الوحدة</option>
                            <option value="مستخلف رئيس الوحدة">مستخلف رئيس الوحدة</option>
                            <option value="قائد الفصيلة">قائد الفصيلة</option>
                            <option value="عون">عون</option>
                            <option value="سائق">سائق</option>
                            <option value="مساعد سائق">مساعد سائق</option>
                            <option value="طبيب">طبيب</option>
                            <option value="ممرض">ممرض</option>
                            <option value="مسعف">مسعف</option>
                            <option value="فني صيانة">فني صيانة</option>
                            <option value="مشغل راديو">مشغل راديو</option>
                            <option value="كاتب">كاتب</option>
                            <option value="محاسب">محاسب</option>
                        </select>
                        <a href="{% url 'add_personnel' %}?unit_id={{ daily_count.unit.id }}" class="btn btn-success">
                            <i class="fas fa-plus"></i> إضافة عون جديد
                        </a>
                    </div>
                </div>

                <div class="table-responsive">
                    <table id="personnelTable" class="table table-striped table-hover table-bordered">
                        <thead>
                            <tr>
                                <th><i class="fas fa-id-card"></i> رقم القيد</th>
                                <th><i class="fas fa-user"></i> الاسم الكامل</th>
                                <th><i class="fas fa-star"></i> الرتبة</th>
                                <th><i class="fas fa-briefcase"></i> المنصب</th>
                                <th><i class="fas fa-check-circle"></i> الحالة</th>
                                <th><i class="fas fa-sticky-note"></i> ملاحظات</th>
                                <th><i class="fas fa-cogs"></i> إجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for person in all_personnel %}
                            <tr data-status="{{ person.daily_status.status }}">
                                <td>{{ person.registration_number }}</td>
                                <td>{{ person.full_name }}</td>
                                <td>{{ person.rank|default:"–" }}</td>
                                <td>{{ person.position|default:"–" }}</td>
                                <td>
                                    <select class="form-control status-select" data-id="{{ person.id }}" data-type="personnel">
                                        <option value="present" {% if person.daily_status.status == 'present' %}selected{% endif %}>حاضر</option>
                                        <option value="absent" {% if person.daily_status.status == 'absent' %}selected{% endif %}>غائب</option>
                                        <option value="on_mission" {% if person.daily_status.status == 'on_mission' %}selected{% endif %}>في مهمة</option>
                                    </select>
                                </td>
                                <td>{{ person.daily_status.notes|default:"–" }}</td>
                                <td>
                                    <button class="btn btn-sm btn-warning edit-personnel"
                                            data-id="{{ person.id }}"
                                            data-registration="{{ person.registration_number }}"
                                            data-name="{{ person.full_name }}"
                                            data-rank="{{ person.rank }}"
                                            data-position="{{ person.position }}"
                                            data-status="{{ person.status }}"
                                            data-notes="{{ person.notes }}">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger delete-personnel" data-id="{{ person.id }}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="6" class="text-center">لا توجد بيانات أعوان</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="quick-stats">
                    <div class="stat-item">
                        <span class="stat-label">إجمالي الأعوان:</span>
                        <span class="stat-value">{{ all_personnel.count }}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">حاضر:</span>
                        <span class="stat-value present-count">{{ all_personnel|length }}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">غائب:</span>
                        <span class="stat-value absent-count">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">في مهمة:</span>
                        <span class="stat-value mission-count">0</span>
                    </div>
                </div>
            </div>

            <!-- قسم الوسائل والعتاد -->
            <div class="section-card">
                <div class="section-header">
                    <h3><i class="fas fa-truck"></i> الوسائل والعتاد المتدخل</h3>
                    <div class="header-controls">
                        <input type="text" id="equipmentSearch" class="form-control search-input" placeholder="البحث في الوسائل...">
                        <select id="equipmentStatusFilter" class="form-control filter-select">
                            <option value="">جميع الحالات</option>
                            <option value="operational">تعمل</option>
                            <option value="broken">معطلة</option>
                            <option value="maintenance">تحت الصيانة</option>
                        </select>
                        <a href="{% url 'add_equipment' %}?unit_id={{ daily_count.unit.id }}" class="btn btn-success">
                            <i class="fas fa-plus"></i> إضافة وسيلة
                        </a>
                    </div>
                </div>

                <div class="table-responsive">
                    <table id="equipmentTable" class="table table-striped table-hover table-bordered">
                        <thead>
                            <tr>
                                <th><i class="fas fa-hashtag"></i> الرقم التسلسلي</th>
                                <th><i class="fas fa-truck"></i> نوع الوسيلة</th>
                                <th><i class="fas fa-broadcast-tower"></i> رقم إشارة الراديو</th>
                                <th><i class="fas fa-check-circle"></i> الحالة</th>
                                <th><i class="fas fa-sticky-note"></i> ملاحظات</th>
                                <th><i class="fas fa-cogs"></i> إجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in all_equipment %}
                            <tr data-status="{{ item.daily_status.status }}">
                                <td>{{ item.serial_number }}</td>
                                <td>{{ item.equipment_type }}</td>
                                <td>{{ item.radio_number|default:"–" }}</td>
                                <td>
                                    <select class="form-control status-select" data-id="{{ item.id }}" data-type="equipment">
                                        <option value="operational" {% if item.daily_status.status == 'operational' %}selected{% endif %}>تعمل</option>
                                        <option value="broken" {% if item.daily_status.status == 'broken' %}selected{% endif %}>معطلة</option>
                                        <option value="maintenance" {% if item.daily_status.status == 'maintenance' %}selected{% endif %}>تحت الصيانة</option>
                                    </select>
                                </td>
                                <td>{{ item.daily_status.notes|default:"–" }}</td>
                                <td>
                                    <button class="btn btn-sm btn-warning edit-equipment"
                                            data-id="{{ item.id }}"
                                            data-serial="{{ item.serial_number }}"
                                            data-type="{{ item.equipment_type }}"
                                            data-radio="{{ item.radio_number }}"
                                            data-status="{{ item.status }}"
                                            data-notes="{{ item.notes }}">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger delete-equipment" data-id="{{ item.id }}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="6" class="text-center">لا توجد بيانات وسائل</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="quick-stats">
                    <div class="stat-item">
                        <span class="stat-label">إجمالي الوسائل:</span>
                        <span class="stat-value">{{ all_equipment.count }}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">تعمل:</span>
                        <span class="stat-value operational-count">{{ all_equipment|length }}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">معطلة:</span>
                        <span class="stat-value broken-count">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">تحت الصيانة:</span>
                        <span class="stat-value maintenance-count">0</span>
                    </div>
                </div>
            </div>

            <!-- قسم التحويل (للصلاحيات العليا فقط) -->
            {% if can_transfer %}
            <div class="section-card">
                <div class="section-header">
                    <h3><i class="fas fa-exchange-alt"></i> قسم التحويل</h3>
                </div>
                
                <div class="transfer-section">
                    <p>هذا القسم مخصص لتحويل العتاد والأعوان بين الوحدات</p>
                    <button type="button" class="btn btn-info" data-toggle="modal" data-target="#transferModal">
                        <i class="fas fa-exchange-alt"></i> تنفيذ تحويل
                    </button>
                </div>

                <!-- نظام التحقق الصباحي المتقدم -->
                {% if morning_summary %}
                <div class="morning-check-system" style="margin-bottom: 30px;">
                    <h3 style="color: #007bff; margin-bottom: 20px;">
                        <i class="fas fa-sun"></i> نظام التحقق الصباحي المتقدم
                    </h3>

                    <!-- ملخص الجاهزية -->
                    <div class="readiness-summary" style="margin-bottom: 25px;">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card text-center" style="border-left: 4px solid #007bff;">
                                    <div class="card-body">
                                        <i class="fas fa-percentage fa-2x text-primary mb-2"></i>
                                        <h4 class="text-primary">{{ morning_summary.overall_readiness_score }}%</h4>
                                        <p class="text-muted">نسبة الجاهزية العامة</p>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="card text-center" style="border-left: 4px solid #28a745;">
                                    <div class="card-body">
                                        <i class="fas fa-clock fa-2x text-success mb-2"></i>
                                        <h5 class="text-success">
                                            {% if daily_schedule.active_shift %}
                                                {{ daily_schedule.active_shift.get_name_display }}
                                            {% else %}
                                                غير محدد
                                            {% endif %}
                                        </h5>
                                        <p class="text-muted">الفرقة العاملة</p>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="card text-center" style="border-left: 4px solid #dc3545;">
                                    <div class="card-body">
                                        <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                                        <h4 class="text-danger">{{ active_alerts.count }}</h4>
                                        <p class="text-muted">تنبيهات نشطة</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- التنبيهات النشطة -->
                    {% if active_alerts %}
                    <div class="active-alerts" style="margin-bottom: 25px;">
                        <h5><i class="fas fa-bell text-warning"></i> التنبيهات النشطة</h5>
                        {% for alert in active_alerts|slice:":3" %}
                        <div class="alert alert-warning">
                            <strong>{{ alert.get_alert_type_display }}:</strong> {{ alert.title }}
                            <span class="badge badge-{{ alert.get_priority_color }} float-right">{{ alert.get_priority_display }}</span>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    <!-- تبويبات النظام المتقدم -->
                    <div class="advanced-tabs">
                        <ul class="nav nav-tabs" id="morningCheckTabs" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="shifts-tab" data-toggle="tab" href="#shifts" role="tab">
                                    <i class="fas fa-users-cog"></i> إدارة الفرق
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="eight-hours-tab" data-toggle="tab" href="#eight-hours" role="tab">
                                    <i class="fas fa-business-time"></i> نظام 8 ساعات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="vehicle-assignment-tab" data-toggle="tab" href="#vehicle-assignment" role="tab">
                                    <i class="fas fa-truck-loading"></i> توزيع الوسائل
                                </a>
                            </li>
                        </ul>

                        <div class="tab-content" id="morningCheckTabContent" style="padding: 20px; border: 1px solid #dee2e6; border-top: none;">
                            <!-- تبويب إدارة الفرق -->
                            <div class="tab-pane fade show active" id="shifts" role="tabpanel">
                                <div class="shifts-management">
                                    <div class="alert alert-info">
                                        <h6>الفرقة العاملة اليوم:</h6>
                                        {% if daily_schedule.active_shift %}
                                            <span class="badge badge-success">{{ daily_schedule.active_shift.get_name_display }}</span>
                                            <span class="badge badge-info">{{ daily_schedule.active_shift.get_shift_type_display }}</span>
                                        {% else %}
                                            <span class="badge badge-warning">لم يتم تحديد فرقة عاملة</span>
                                        {% endif %}
                                    </div>

                                    <h6>الفرق المتاحة:</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm table-bordered">
                                            <thead class="thead-light">
                                                <tr>
                                                    <th>الفرقة</th>
                                                    <th>النوع</th>
                                                    <th>عدد الأعوان</th>
                                                    <th>الحالة</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for shift in available_shifts %}
                                                <tr>
                                                    <td>{{ shift.get_name_display }}</td>
                                                    <td>{{ shift.get_shift_type_display }}</td>
                                                    <td>{{ shift.personnel.count }}</td>
                                                    <td>
                                                        {% if shift.is_active %}
                                                            <span class="badge badge-success">نشط</span>
                                                        {% else %}
                                                            <span class="badge badge-secondary">غير نشط</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        {% if daily_schedule and daily_schedule.active_shift.id != shift.id %}
                                                        <button class="btn btn-sm btn-primary" onclick="activateShift({{ shift.id }})">
                                                            <i class="fas fa-play"></i> تفعيل
                                                        </button>
                                                        {% elif daily_schedule and daily_schedule.active_shift.id == shift.id %}
                                                        <span class="badge badge-success">عاملة حالياً</span>
                                                        {% else %}
                                                        <button class="btn btn-sm btn-primary" onclick="activateShift({{ shift.id }})">
                                                            <i class="fas fa-play"></i> تفعيل
                                                        </button>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <!-- تبويب نظام 8 ساعات -->
                            <div class="tab-pane fade" id="eight-hours" role="tabpanel">
                                <div class="eight-hours-system">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6>أعوان نظام 8 ساعات</h6>
                                        <button class="btn btn-success btn-sm" onclick="addEightHourPersonnel()">
                                            <i class="fas fa-plus"></i> إضافة عون
                                        </button>
                                    </div>

                                    <div class="table-responsive">
                                        <table class="table table-sm table-bordered">
                                            <thead class="thead-light">
                                                <tr>
                                                    <th>الاسم</th>
                                                    <th>فترة العمل</th>
                                                    <th>نوع المهمة</th>
                                                    <th>التوقيت</th>
                                                    <th>الحضور</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for record in eight_hour_personnel %}
                                                <tr>
                                                    <td>{{ record.personnel.full_name }}</td>
                                                    <td>{{ record.get_work_period_display }}</td>
                                                    <td>{{ record.get_task_type_display }}</td>
                                                    <td>{{ record.start_time }} - {{ record.end_time }}</td>
                                                    <td>
                                                        {% if record.is_present %}
                                                            <span class="badge badge-success">حاضر</span>
                                                        {% else %}
                                                            <span class="badge badge-danger">غائب</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        <button class="btn btn-sm btn-primary" onclick="editEightHourRecord({{ record.id }})">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                                {% empty %}
                                                <tr>
                                                    <td colspan="6" class="text-center text-muted">لا توجد سجلات نظام 8 ساعات</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <!-- تبويب توزيع الوسائل -->
                            <div class="tab-pane fade" id="vehicle-assignment" role="tabpanel">
                                <div class="vehicle-assignment">
                                    <h6>توزيع الأعوان على الوسائل</h6>
                                    <p class="text-muted">يمكنك توزيع الأعوان على الوسائل من خلال صفحة جاهزية الوسائل المخصصة.</p>
                                    <a href="{% url 'vehicle_crew_assignment' %}?unit={{ daily_count.unit.id }}&date={{ today|date:'Y-m-d' }}"
                                       class="btn btn-primary">
                                        <i class="fas fa-external-link-alt"></i> انتقال إلى صفحة توزيع الوسائل
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- سجل التحويلات -->
                <div class="transfer-history">
                    <h4>سجل التحويلات الأخيرة</h4>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>العنصر</th>
                                    <th>من وحدة</th>
                                    <th>إلى وحدة</th>
                                    <th>المستخدم</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transfer in transfer_records %}
                                <tr>
                                    <td>{{ transfer.transfer_date|date:"d/m/Y H:i" }}</td>
                                    <td>{{ transfer.item_name }}</td>
                                    <td>{{ transfer.from_unit.name }}</td>
                                    <td>{{ transfer.to_unit.name }}</td>
                                    <td>{{ transfer.transferred_by.username }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center">لا توجد تحويلات</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}
            {% endif %}

            <!-- أزرار العمل الرئيسية -->
            <div class="main-buttons-container">
                <div class="main-buttons">
                    <a href="{% url 'coordination_center' %}" class="main-btn back-btn">
                        <i class="fas fa-arrow-right"></i> مركز التنسيق
                    </a>
                    {% if daily_count %}
                    <a href="{% url 'daily_unit_reports' %}?unit_id={{ daily_count.unit.id }}" class="main-btn reports-btn">
                        <i class="fas fa-chart-bar"></i> التقارير اليومية
                    </a>
                    <button type="button" onclick="exportToExcel()" class="main-btn export-btn">
                        <i class="fas fa-file-excel"></i> تصدير Excel
                    </button>
                {% if can_transfer %}
                <a href="{% url 'manage_roles' %}" class="main-btn roles-btn">
                    <i class="fas fa-users-cog"></i> إدارة الرتب
                </a>
                {% endif %}
                    {% endif %}
                </div>
            </div>

            <!-- الأزرار العائمة -->
            <div class="floating-buttons">
                {% if daily_count %}
                <a href="{% url 'daily_unit_reports' %}?unit_id={{ daily_count.unit.id }}" class="floating-btn reports-btn" title="التقارير اليومية">
                    <i class="fas fa-chart-bar"></i>
                </a>
                {% endif %}
                <a href="{% url 'coordination_center' %}" class="floating-btn home-btn" title="مركز التنسيق">
                    <i class="fas fa-home"></i>
                </a>
                <button type="button" onclick="printPage()" class="floating-btn print-btn" title="طباعة">
                    <i class="fas fa-print"></i>
                </button>
                <a href="#" id="back-to-top" class="floating-btn top-btn" title="العودة إلى الأعلى">
                    <i class="fas fa-arrow-up"></i>
                </a>
            </div>
        </div>
    </main>

    <footer>
        <p>حقوق النشر © 2025 جميع الحقوق محفوظة - تم التطوير بواسطة عبد الرزاق مختاري</p>
    </footer>

    <!-- Modal for adding personnel -->
    <div class="modal fade" id="addPersonnelModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content clean-modal">
                <div class="modal-header clean-header">
                    <div class="header-content">
                        <i class="fas fa-user-plus header-icon"></i>
                        <div class="header-text">
                            <h4 class="modal-title" id="personnelModalTitle">إضافة عون جديد</h4>
                            <p class="modal-subtitle">سيتم حفظ العون بشكل دائم - يمكن تغيير الحالة يومياً</p>
                        </div>
                    </div>
                    <button type="button" class="close clean-close" data-dismiss="modal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body clean-body">
                    <form id="addPersonnelForm">
                        <div class="container-fluid">
                                <!-- الصف الأول: المعلومات الأساسية -->
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label required">
                                                <i class="fas fa-hashtag text-primary"></i> رقم القيد
                                            </label>
                                            <input type="text" name="registration_number" class="form-control clean-input"
                                                   placeholder="مثال: 12345" required>
                                            <small class="form-text text-muted">رقم القيد الخاص بالعون</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label required">
                                                <i class="fas fa-user text-primary"></i> الاسم الكامل
                                            </label>
                                            <input type="text" name="full_name" class="form-control clean-input"
                                                   placeholder="مثال: أحمد محمد علي" required>
                                            <small class="form-text text-muted">الاسم الثلاثي أو الرباعي</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- الصف الثاني: الرتبة والمنصب -->
                                <div class="row mb-5">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">
                                                <i class="fas fa-medal text-warning"></i> الرتبة العسكرية
                                            </label>
                                            <select name="rank" class="form-control clean-select">
                                                <option value="">-- اختر الرتبة --</option>
                                                {% for rank in ranks %}
                                                <option value="{{ rank }}">{{ rank }}</option>
                                                {% endfor %}
                                            </select>
                                            <small class="form-text text-muted">الرتبة العسكرية للعون</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">
                                                <i class="fas fa-briefcase text-info"></i> المنصب الوظيفي
                                            </label>
                                            <select name="position" class="form-control clean-select">
                                                <option value="">-- اختر المنصب --</option>
                                                {% for position in positions %}
                                                <option value="{{ position }}">{{ position }}</option>
                                                {% endfor %}
                                            </select>
                                            <small class="form-text text-muted">المنصب الوظيفي في الوحدة</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- الصف الثالث: الحالة الأولية -->
                                <div class="row mb-5">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label required">
                                                <i class="fas fa-check-circle text-success"></i> الحالة الأولية
                                            </label>
                                            <select name="status" class="form-control clean-select" required>
                                                <option value="present" selected>
                                                    <i class="fas fa-check text-success"></i> حاضر
                                                </option>
                                                <option value="absent">
                                                    <i class="fas fa-times text-danger"></i> غائب
                                                </option>
                                                <option value="on_mission">
                                                    <i class="fas fa-clock text-warning"></i> في مهمة
                                                </option>
                                            </select>
                                            <small class="form-text text-muted">يمكن تغيير الحالة يومياً من الجدول</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">
                                                <i class="fas fa-sticky-note text-secondary"></i> ملاحظات
                                            </label>
                                            <textarea name="notes" class="form-control clean-textarea" rows="3"
                                                      placeholder="أي ملاحظات إضافية حول العون..."></textarea>
                                            <small class="form-text text-muted">ملاحظات اختيارية</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- تنبيه مهم -->
                                <div class="alert alert-info" role="alert">
                                    <i class="fas fa-info-circle"></i>
                                    <strong>ملاحظة مهمة:</strong>
                                    بعد إضافة العون، ستظهر بياناته في الجدول كل يوم. يمكنك تغيير حالته (حاضر/غائب/في مهمة) من الجدول مباشرة.
                                </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer clean-footer">
                    <button type="button" class="btn btn-light btn-lg" data-dismiss="modal">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                    <button type="button" class="btn btn-primary btn-lg" id="savePersonnel">
                        <i class="fas fa-user-plus"></i> إضافة العون
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for adding equipment -->
    <div class="modal fade" id="addEquipmentModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content clean-modal">
                <div class="modal-header clean-header">
                    <div class="header-content">
                        <i class="fas fa-truck header-icon"></i>
                        <div class="header-text">
                            <h4 class="modal-title" id="equipmentModalTitle">إضافة وسيلة جديدة</h4>
                            <p class="modal-subtitle">ستتم إضافة الوسيلة بشكل دائم - يمكن تغيير حالتها يومياً</p>
                        </div>
                    </div>
                    <button type="button" class="close clean-close" data-dismiss="modal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body clean-body">
                    <form id="addEquipmentForm">
                        <div class="container-fluid">
                            <!-- الصف الأول: المعلومات الأساسية -->
                            <div class="row mb-5">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label required">
                                            <i class="fas fa-barcode text-primary"></i> الرقم التسلسلي
                                        </label>
                                        <input type="text" name="serial_number" class="form-control clean-input"
                                               placeholder="مثال: VH-001-2025" required>
                                        <small class="form-text text-muted">الرقم التسلسلي الخاص بالوسيلة</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label required">
                                            <i class="fas fa-truck text-primary"></i> نوع الوسيلة
                                        </label>
                                        <select name="equipment_type" class="form-control clean-select" id="equipmentTypeSelect" required>
                                            <option value="">-- اختر نوع الوسيلة --</option>
                                            <optgroup label="وسائل الإطفاء">
                                                <option value="شاحنة إطفاء">شاحنة إطفاء</option>
                                                <option value="شاحنة مياه">شاحنة مياه</option>
                                                <option value="سيارة إطفاء خفيفة">سيارة إطفاء خفيفة</option>
                                            </optgroup>
                                            <optgroup label="وسائل الإسعاف">
                                                <option value="سيارة إسعاف">سيارة إسعاف</option>
                                                <option value="سيارة إسعاف متقدمة">سيارة إسعاف متقدمة</option>
                                            </optgroup>
                                            <optgroup label="وسائل النقل">
                                                <option value="سيارة خفيفة">سيارة خفيفة</option>
                                                <option value="حافلة نقل">حافلة نقل</option>
                                                <option value="دراجة نارية">دراجة نارية</option>
                                            </optgroup>
                                            <optgroup label="وسائل جوية">
                                                <option value="طائرة هليكوبتر">طائرة هليكوبتر</option>
                                                <option value="طائرة إطفاء">طائرة إطفاء</option>
                                                <option value="طائرة استطلاع">طائرة استطلاع</option>
                                            </optgroup>
                                            <optgroup label="⛵ وسائل بحرية">
                                                <option value="زورق إنقاذ">زورق إنقاذ</option>
                                                <option value="زورق دورية">زورق دورية</option>
                                                <option value="قارب مطاطي">قارب مطاطي</option>
                                                <option value="سفينة إنقاذ">سفينة إنقاذ</option>
                                            </optgroup>
                                            <optgroup label="🏗️ وسائل ثقيلة">
                                                <option value="رافعة">رافعة</option>
                                                <option value="جرار">جرار</option>
                                                <option value="حفارة">حفارة</option>
                                                <option value="بلدوزر">بلدوزر</option>
                                            </optgroup>
                                            <optgroup label="⚙️ أخرى">
                                                <option value="مولد كهربائي">مولد كهربائي</option>
                                                <option value="ضاغط هواء">ضاغط هواء</option>
                                                <option value="أخرى">أخرى</option>
                                            </optgroup>
                                            {% if user_role == 'admin' or user_role == 'wilaya_manager' %}
                                            <optgroup label="إضافة جديد">
                                                <option value="add_new_type">إضافة نوع وسيلة جديد</option>
                                            </optgroup>
                                            {% endif %}
                                        </select>
                                        <small class="form-text text-muted">اختر نوع الوسيلة من القائمة</small>
                                    </div>
                                </div>
                            </div>

                            <!-- حقل إضافة نوع جديد (مخفي افتراضياً) -->
                            <div class="row mb-5 d-none" id="newTypeRow">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-plus text-success"></i> نوع الوسيلة الجديد
                                        </label>
                                        <input type="text" name="new_equipment_type" class="form-control clean-input"
                                               placeholder="أدخل نوع الوسيلة الجديد">
                                        <small class="form-text text-muted">سيتم إضافة هذا النوع إلى القائمة للاستخدام المستقبلي</small>
                                    </div>
                                </div>
                            </div>

                            <!-- الصف الثاني: معلومات الاتصال -->
                            <div class="row mb-5">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-broadcast-tower text-info"></i> رقم إشارة الراديو
                                        </label>
                                        <input type="text" name="radio_number" class="form-control clean-input"
                                               placeholder="مثال: R-123">
                                        <small class="form-text text-muted">رقم إشارة الراديو (اختياري)</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label required">
                                            <i class="fas fa-check-circle text-success"></i> حالة التشغيل الأولية
                                        </label>
                                        <select name="status" class="form-control clean-select" required>
                                            <option value="operational" selected>
                                                <i class="fas fa-check text-success"></i> تعمل
                                            </option>
                                            <option value="broken">
                                                <i class="fas fa-times text-danger"></i> معطلة
                                            </option>
                                            <option value="maintenance">
                                                <i class="fas fa-wrench text-warning"></i> تحت الصيانة
                                            </option>
                                        </select>
                                        <small class="form-text text-muted">يمكن تغيير الحالة يومياً من الجدول</small>
                                    </div>
                                </div>
                            </div>

                            <!-- الصف الثالث: الملاحظات -->
                            <div class="row mb-5">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-sticky-note text-secondary"></i> ملاحظات
                                        </label>
                                        <textarea name="notes" class="form-control clean-textarea" rows="3"
                                                  placeholder="أي ملاحظات حول الوسيلة (حالة، صيانة، مشاكل، إلخ)..."></textarea>
                                        <small class="form-text text-muted">ملاحظات اختيارية</small>
                                    </div>
                                </div>
                            </div>

                            <!-- تنبيه مهم -->
                            <div class="alert alert-info" role="alert">
                                <i class="fas fa-info-circle"></i>
                                <strong>ملاحظة مهمة:</strong>
                                بعد إضافة الوسيلة، ستظهر في الجدول كل يوم. يمكنك تغيير حالتها (تعمل/معطلة/تحت الصيانة) من الجدول مباشرة.
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer clean-footer">
                    <button type="button" class="btn btn-light btn-lg" data-dismiss="modal">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                    <button type="button" class="btn btn-primary btn-lg" id="saveEquipment">
                        <i class="fas fa-truck"></i> إضافة الوسيلة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="{% static 'js/sidebar.js' %}"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        $(document).ready(function() {
            // Initialize filters and search
            initializeFilters();
            updateStatistics();

            // Unit selection for admins
            window.selectUnit = function() {
                const unitId = $('#unit-select').val();
                if (unitId) {
                    window.location.href = '?unit_id=' + unitId;
                }
            };

            // Status change handlers
            $('.status-select').change(function() {
                const id = $(this).data('id');
                const type = $(this).data('type');
                const status = $(this).val();

                $.post('', {
                    action: 'update_' + type + '_status',
                    [type + '_id']: id,
                    status: status,
                    csrfmiddlewaretoken: $('[name=csrfmiddlewaretoken]').val()
                }, function(response) {
                    if (response.success) {
                        showNotification(response.message, 'success');
                        updateStatistics();
                    } else {
                        showNotification(response.message || 'حدث خطأ في التحديث', 'error');
                    }
                });
            });

            // Save personnel
            $('#savePersonnel').click(function() {
                const formData = $('#addPersonnelForm').serialize();
                const action = $('#personnelModalTitle').text().includes('تعديل') ? 'edit_personnel' : 'add_personnel';

                $.post('', formData + '&action=' + action + '&csrfmiddlewaretoken=' + $('[name=csrfmiddlewaretoken]').val(), function(response) {
                    if (response.success) {
                        $('#addPersonnelModal').modal('hide');
                        showNotification(response.message, 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showNotification(response.message || 'حدث خطأ في حفظ البيانات', 'error');
                    }
                });
            });

            // Save equipment
            $('#saveEquipment').click(function() {
                const formData = $('#addEquipmentForm').serialize();
                const action = $('#equipmentModalTitle').text().includes('تعديل') ? 'edit_equipment' : 'add_equipment';

                $.post('', formData + '&action=' + action + '&csrfmiddlewaretoken=' + $('[name=csrfmiddlewaretoken]').val(), function(response) {
                    if (response.success) {
                        $('#addEquipmentModal').modal('hide');
                        showNotification(response.message, 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showNotification(response.message || 'حدث خطأ في حفظ البيانات', 'error');
                    }
                });
            });

            // Edit personnel
            $('.edit-personnel').click(function() {
                const data = $(this).data();
                $('#personnelModalTitle').text('تعديل بيانات العون');
                $('#addPersonnelForm input[name="personnel_id"]').remove();
                $('#addPersonnelForm').append('<input type="hidden" name="personnel_id" value="' + data.id + '">');
                $('#addPersonnelForm input[name="registration_number"]').val(data.registration);
                $('#addPersonnelForm input[name="full_name"]').val(data.name);
                $('#addPersonnelForm select[name="rank"]').val(data.rank);
                $('#addPersonnelForm select[name="position"]').val(data.position);
                $('#addPersonnelForm select[name="status"]').val(data.status);
                $('#addPersonnelForm textarea[name="notes"]').val(data.notes);
                $('#addPersonnelModal').modal('show');
            });

            // Edit equipment
            $('.edit-equipment').click(function() {
                const data = $(this).data();
                $('#equipmentModalTitle').text('تعديل بيانات الوسيلة');
                $('#addEquipmentForm input[name="equipment_id"]').remove();
                $('#addEquipmentForm').append('<input type="hidden" name="equipment_id" value="' + data.id + '">');
                $('#addEquipmentForm input[name="serial_number"]').val(data.serial);
                $('#addEquipmentForm input[name="equipment_type"]').val(data.type);
                $('#addEquipmentForm input[name="radio_number"]').val(data.radio);
                $('#addEquipmentForm select[name="status"]').val(data.status);
                $('#addEquipmentForm textarea[name="notes"]').val(data.notes);
                $('#addEquipmentModal').modal('show');
            });

            // Reset modal when adding new
            $('[data-target="#addPersonnelModal"]').click(function() {
                $('#personnelModalTitle').text('إضافة عون جديد');
                $('#addPersonnelForm')[0].reset();
                $('#addPersonnelForm input[name="personnel_id"]').remove();
            });

            $('[data-target="#addEquipmentModal"]').click(function() {
                $('#equipmentModalTitle').text('إضافة وسيلة جديدة');
                $('#addEquipmentForm')[0].reset();
                $('#addEquipmentForm input[name="equipment_id"]').remove();
            });
        });

        // Initialize filters and search functionality
        function initializeFilters() {
            // Personnel search
            $('#personnelSearch').on('keyup', function() {
                const value = $(this).val().toLowerCase();
                $('#personnelTable tbody tr').filter(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
                });
            });

            // Personnel status filter
            $('#personnelStatusFilter').on('change', function() {
                const status = $(this).val();
                $('#personnelTable tbody tr').filter(function() {
                    $(this).toggle(status === '' || $(this).data('status') === status);
                });
                updateStatistics();
            });

            // Personnel rank filter
            $('#personnelRankFilter').on('change', function() {
                const rank = $(this).val();
                $('#personnelTable tbody tr').filter(function() {
                    const rowRank = $(this).find('td:nth-child(3)').text().trim();
                    $(this).toggle(rank === '' || rowRank === rank);
                });
                updateStatistics();
            });

            // Personnel position filter
            $('#personnelPositionFilter').on('change', function() {
                const position = $(this).val();
                $('#personnelTable tbody tr').filter(function() {
                    const rowPosition = $(this).find('td:nth-child(4)').text().trim();
                    $(this).toggle(position === '' || rowPosition === position);
                });
                updateStatistics();
            });

            // Equipment search
            $('#equipmentSearch').on('keyup', function() {
                const value = $(this).val().toLowerCase();
                $('#equipmentTable tbody tr').filter(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
                });
            });

            // Equipment status filter
            $('#equipmentStatusFilter').on('change', function() {
                const status = $(this).val();
                $('#equipmentTable tbody tr').filter(function() {
                    $(this).toggle(status === '' || $(this).data('status') === status);
                });
            });
        }

        // Update statistics
        function updateStatistics() {
            // Personnel statistics
            const presentCount = $('#personnelTable tbody tr[data-status="present"]:visible').length;
            const absentCount = $('#personnelTable tbody tr[data-status="absent"]:visible').length;
            const missionCount = $('#personnelTable tbody tr[data-status="on_mission"]:visible').length;

            $('.present-count').text(presentCount);
            $('.absent-count').text(absentCount);
            $('.mission-count').text(missionCount);

            // Equipment statistics
            const operationalCount = $('#equipmentTable tbody tr[data-status="operational"]:visible').length;
            const brokenCount = $('#equipmentTable tbody tr[data-status="broken"]:visible').length;
            const maintenanceCount = $('#equipmentTable tbody tr[data-status="maintenance"]:visible').length;

            $('.operational-count').text(operationalCount);
            $('.broken-count').text(brokenCount);
            $('.maintenance-count').text(maintenanceCount);
        }

        // Show notification
        function showNotification(message, type) {
            const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            const notification = `
                <div class="alert ${alertClass} alert-dismissible fade show notification" role="alert">
                    ${message}
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
            `;
            $('.home-container').prepend(notification);

            // Auto dismiss after 3 seconds
            setTimeout(() => {
                $('.notification').alert('close');
            }, 3000);
        }

        // Export to Excel
        function exportToExcel() {
            // This would be implemented with a backend endpoint
            showNotification('ميزة التصدير قيد التطوير', 'info');
        }

        // Print page
        function printPage() {
            window.print();
        }

        // Back to top functionality
        $(document).ready(function() {
            $('#back-to-top').click(function(e) {
                e.preventDefault();
                $('html, body').animate({scrollTop: 0}, 'slow');
            });

            // Show/hide back to top button
            $(window).scroll(function() {
                if ($(this).scrollTop() > 100) {
                    $('#back-to-top').fadeIn();
                } else {
                    $('#back-to-top').fadeOut();
                }
            });
        });
    </script>

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #ffffff;
        }

        /* Unit Count Details - Similar to fire-details */
        .unit-count-details {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-top: 30px;
            margin-bottom: 30px;
        }

        .unit-count-icon {
            font-size: 4rem;
            color: #0d47a1;
            margin-bottom: 20px;
            text-align: center;
        }

        .unit-count-title {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 10px;
            text-align: center;
            color: #1d3557;
        }

        .unit-count-subtitle {
            font-size: 1.2rem;
            text-align: center;
            color: #457b9d;
            margin-bottom: 20px;
        }

        .section-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .section-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .header-controls {
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        .search-input {
            min-width: 200px;
            max-width: 250px;
        }

        .filter-select {
            min-width: 150px;
            max-width: 180px;
        }

        .section-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .table-responsive {
            padding: 20px;
        }

        .table {
            margin: 0;
        }

        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
            color: #333;
            text-align: center;
        }

        .table td {
            text-align: center;
            vertical-align: middle;
        }

        .status-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-present {
            background-color: #d4edda;
            color: #155724;
        }

        .status-absent {
            background-color: #f8d7da;
            color: #721c24;
        }

        .status-on_mission {
            background-color: #fff3cd;
            color: #856404;
        }

        .status-operational {
            background-color: #d4edda;
            color: #155724;
        }

        .status-broken {
            background-color: #f8d7da;
            color: #721c24;
        }

        .status-maintenance {
            background-color: #fff3cd;
            color: #856404;
        }

        .unit-selection {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .form-group {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .form-group label {
            font-weight: 600;
            margin: 0;
            min-width: 100px;
        }

        .form-control {
            flex: 1;
            max-width: 300px;
        }

        .btn {
            border-radius: 8px;
            padding: 12px 20px;
            font-weight: 600;
            transition: all 0.3s;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .transfer-section {
            padding: 20px;
            text-align: center;
        }

        .transfer-history {
            padding: 0 20px 20px;
        }

        .transfer-history h4 {
            margin-bottom: 15px;
            font-size: 16px;
            color: #333;
        }

        /* Clean Modal Styles - Rebuilt from Scratch */
        .clean-modal {
            border-radius: 20px;
            box-shadow: 0 25px 80px rgba(0, 0, 0, 0.2);
            border: none;
            overflow: hidden;
            background: #ffffff;
        }

        .clean-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 25px 30px;
            position: relative;
            border-radius: 20px 20px 0 0;
        }

        .header-content {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .header-icon {
            font-size: 28px;
            color: rgba(255, 255, 255, 0.95);
            background: rgba(255, 255, 255, 0.15);
            padding: 12px;
            border-radius: 12px;
        }

        .header-text h4 {
            margin: 0;
            font-size: 22px;
            font-weight: 700;
            color: white;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        .modal-subtitle {
            margin: 0;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.85);
            margin-top: 4px;
            font-weight: 400;
        }

        .clean-close {
            position: absolute;
            top: 25px;
            left: 30px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .clean-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        /* إعادة تعريف النماذج المنبثقة بشكل صحيح - عرض كامل */
        .modal-dialog {
            margin: 5px auto !important;
            max-width: 98vw !important;
            width: 98vw !important;
            position: relative !important;
        }

        .modal-xl {
            max-width: 98vw !important;
            width: 98vw !important;
        }

        .clean-body {
            padding: 40px !important;
            background: #f8f9fa !important;
            max-height: none !important;
            height: auto !important;
            overflow: visible !important;
            position: static !important;
        }

        .form-container {
            padding: 0 !important;
            max-height: none !important;
            height: auto !important;
            overflow: visible !important;
        }

        /* إزالة جميع قيود الارتفاع */
        .modal-content {
            max-height: none !important;
            height: auto !important;
        }

        /* تحسين شريط التمرير للصفحة الرئيسية فقط */
        body::-webkit-scrollbar {
            width: 8px;
        }

        body::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        body::-webkit-scrollbar-thumb {
            background: #007bff;
            border-radius: 10px;
        }

        body::-webkit-scrollbar-thumb:hover {
            background: #0056b3;
        }

        /* تحسينات إضافية للصفحة - تم نقل modal-xl إلى النظام الموحد */

        /* تحسينات الجداول */
        .table thead th {
            background: var(--gradient-primary) !important;
            color: var(--text-white) !important;
            font-weight: 600;
            text-align: center;
            vertical-align: middle;
            border: none !important;
            padding: var(--spacing-lg) var(--spacing-md);
        }

        .table tbody td {
            text-align: center;
            vertical-align: middle;
            padding: var(--spacing-md);
            border-color: var(--border-light);
        }

        .table tbody tr:hover {
            background-color: var(--primary-light) !important;
            color: var(--primary-dark);
            transform: scale(1.01);
            transition: var(--transition-fast);
        }

        /* تحسينات الأزرار */
        .btn {
            border-radius: var(--border-radius-md);
            font-weight: 500;
            transition: var(--transition-normal);
            box-shadow: var(--shadow-sm);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .btn-success {
            background: var(--gradient-success);
            border: none;
        }

        .btn-primary {
            background: var(--gradient-primary);
            border: none;
        }

        .btn-danger {
            background: var(--gradient-danger);
            border: none;
        }

        /* تحسينات القوائم المنسدلة */
        .status-select {
            border-radius: var(--border-radius-sm);
            border: 1px solid var(--border-color);
            padding: var(--spacing-sm) var(--spacing-md);
            font-size: var(--font-size-sm);
            transition: var(--transition-fast);
        }

        .status-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        /* شارات الحالة */
        .badge {
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--border-radius-xl);
            font-weight: 600;
            font-size: var(--font-size-xs);
        }

        @media (max-width: 767px) {
            .clean-body {
                padding: 15px !important;
            }

            .clean-header {
                padding: 15px 20px !important;
            }

            .header-icon {
                font-size: 22px !important;
                padding: 8px !important;
            }

            .header-text h4 {
                font-size: 16px !important;
            }

            .modal-subtitle {
                font-size: 12px !important;
            }

            .table thead th {
                font-size: var(--font-size-xs);
                padding: var(--spacing-sm);
            }

            .table tbody td {
                font-size: var(--font-size-sm);
                padding: var(--spacing-xs);
            }
        }

        .form-group {
            margin-bottom: 35px;
        }

        .form-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 17px;
        }

        .form-label.required::after {
            content: " *";
            color: #e74c3c;
            font-weight: bold;
        }

        .form-label i {
            font-size: 14px;
            width: 16px;
            text-align: center;
        }

        .clean-input, .clean-select, .clean-textarea {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 18px 22px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: white;
            width: 100%;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            min-height: 50px;
        }

        .clean-input:focus, .clean-select:focus, .clean-textarea:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.25rem rgba(0, 123, 255, 0.15);
            outline: none;
            transform: translateY(-1px);
        }

        .clean-textarea {
            resize: vertical;
            min-height: 100px;
            font-family: inherit;
        }

        .form-text {
            margin-top: 6px;
            font-size: 13px;
        }

        .alert {
            border-radius: 12px;
            border: none;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .alert-info {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            color: #1565c0;
        }

        .clean-footer {
            background: white;
            border: none;
            padding: 35px 40px;
            display: flex;
            justify-content: flex-end;
            gap: 20px;
            border-radius: 0 0 20px 20px;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
        }

        .btn-lg {
            padding: 20px 40px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 12px;
            transition: all 0.3s ease;
            border: none;
            min-width: 200px;
            justify-content: center;
            min-height: 60px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
            color: white;
        }

        .btn-light {
            background: #f8f9fa;
            color: #6c757d;
            border: 2px solid #e9ecef;
        }

        .btn-light:hover {
            background: #e9ecef;
            color: #495057;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        /* Enhanced Modal Size for Desktop and Tablet */
        .modal-xl {
            max-width: 1200px;
        }

        @media (min-width: 992px) {
            .modal-xl {
                max-width: 1400px;
            }
        }

        @media (min-width: 1200px) {
            .modal-xl {
                max-width: 1600px;
            }
        }

        /* حل جذري لمشكلة النماذج المنبثقة */

        /* إعادة تعريف كامل للنماذج */
        .modal {
            padding: 0 !important;
            overflow-x: hidden !important;
            overflow-y: auto !important;
        }

        /* تأكيد إعدادات النماذج المنبثقة النهائية */
        .modal-dialog {
            position: relative !important;
            width: 98vw !important;
            max-width: 98vw !important;
            margin: 5px auto !important;
        }

        .modal-content {
            width: 100% !important;
            height: auto !important;
            max-height: 95vh !important;
            border-radius: 15px !important;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
            border: none !important;
        }

        .clean-body {
            padding: 40px !important;
            overflow-y: auto !important;
            max-height: 80vh !important;
        }

        /* إزالة form-container تماماً */

        /* Main buttons container */
        .main-buttons-container {
            display: flex;
            justify-content: center;
            margin-top: 30px;
            margin-bottom: 20px;
        }

        /* Main buttons */
        .main-buttons {
            display: flex;
            gap: 20px;
            width: 100%;
            max-width: 900px;
            direction: ltr;
        }

        .main-btn {
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: bold;
            font-size: 18px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            transition: all 0.3s;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
            flex: 1;
            min-width: 200px;
            border: none;
        }

        .main-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            text-decoration: none;
            color: white;
        }

        .back-btn {
            background-color: #1d3557;
            color: white;
        }

        .back-btn:hover {
            background-color: #457b9d;
        }

        .reports-btn {
            background-color: #0d47a1;
            color: white;
        }

        .reports-btn:hover {
            background-color: #0a3880;
        }

        .export-btn {
            background-color: #28a745;
            color: white;
        }

        .export-btn:hover {
            background-color: #218838;
        }

        .roles-btn {
            background-color: #6f42c1;
            color: white;
        }

        .roles-btn:hover {
            background-color: #5a32a3;
        }

        /* Floating buttons styles */
        .floating-buttons {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .floating-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            transition: all 0.3s;
            text-decoration: none;
            font-size: 18px;
            border: none;
            cursor: pointer;
        }

        .floating-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
            text-decoration: none;
            color: white;
        }

        .floating-btn.reports-btn {
            background-color: #0d47a1;
        }

        .floating-btn.reports-btn:hover {
            background-color: #0a3880;
        }

        .floating-btn.home-btn {
            background-color: #28a745;
        }

        .floating-btn.home-btn:hover {
            background-color: #218838;
        }

        .floating-btn.print-btn {
            background-color: #6f42c1;
        }

        .floating-btn.print-btn:hover {
            background-color: #5a32a3;
        }

        .floating-btn.top-btn {
            background-color: #0d6efd;
        }

        .floating-btn.top-btn:hover {
            background-color: #0a58ca;
        }

        .control-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .quick-stats {
            display: flex;
            justify-content: space-around;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .stat-item {
            text-align: center;
            min-width: 100px;
        }

        .stat-label {
            display: block;
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .stat-value {
            display: block;
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }

        .table-hover tbody tr:hover {
            background-color: #f5f5f5;
        }

        .status-select {
            min-width: 120px;
            font-size: 12px;
            padding: 5px;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .header-controls {
                width: 100%;
                justify-content: center;
                flex-direction: column;
                gap: 10px;
            }

            .search-input, .filter-select {
                min-width: 100%;
                max-width: 100%;
            }

            .quick-stats {
                flex-direction: column;
                align-items: center;
            }

            .control-buttons {
                flex-direction: column;
                align-items: center;
            }

            .floating-buttons {
                bottom: 10px;
                right: 10px;
                gap: 8px;
            }

            .floating-btn {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }

            .main-buttons {
                flex-direction: column;
                gap: 15px;
                width: 90%;
            }

            .main-btn {
                width: 100%;
                padding: 12px 20px;
                font-size: 16px;
                min-width: auto;
            }

            .unit-count-details {
                padding: 20px;
                margin-top: 20px;
            }

            .unit-count-icon {
                font-size: 3rem;
            }

            .unit-count-title {
                font-size: 1.4rem;
            }

            .unit-count-subtitle {
                font-size: 1rem;
            }

            .section-header {
                flex-direction: column;
                align-items: stretch;
                gap: 15px;
            }

            .table-responsive {
                font-size: 12px;
            }
        }
    </style>

    <script>
        // إدارة إضافة نوع وسيلة جديد
        document.addEventListener('DOMContentLoaded', function() {
            const equipmentTypeSelect = document.getElementById('equipmentTypeSelect');
            const newTypeRow = document.getElementById('newTypeRow');

            if (equipmentTypeSelect) {
                equipmentTypeSelect.addEventListener('change', function() {
                    if (this.value === 'add_new_type') {
                        newTypeRow.style.display = 'block';
                        document.querySelector('input[name="new_equipment_type"]').required = true;
                    } else {
                        newTypeRow.style.display = 'none';
                        document.querySelector('input[name="new_equipment_type"]').required = false;
                    }
                });
            }

            // تحسين حفظ الوسيلة مع النوع الجديد
            const saveEquipmentBtn = document.getElementById('saveEquipment');
            if (saveEquipmentBtn) {
                saveEquipmentBtn.addEventListener('click', function() {
                    const form = document.getElementById('addEquipmentForm');
                    const formData = new FormData(form);

                    // إذا تم اختيار إضافة نوع جديد
                    if (formData.get('equipment_type') === 'add_new_type') {
                        const newType = formData.get('new_equipment_type');
                        if (!newType || newType.trim() === '') {
                            alert('يرجى إدخال نوع الوسيلة الجديد');
                            return;
                        }
                        formData.set('equipment_type', newType.trim());
                        formData.set('is_new_type', 'true');
                    }

                    formData.append('action', 'add_equipment');
                    formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

                    fetch(window.location.href, {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert(data.message);
                            location.reload();
                        } else {
                            alert('خطأ: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('حدث خطأ أثناء حفظ البيانات');
                    });
                });
            }

            // تحسين حفظ العون
            const savePersonnelBtn = document.getElementById('savePersonnel');
            if (savePersonnelBtn) {
                savePersonnelBtn.addEventListener('click', function() {
                    const form = document.getElementById('addPersonnelForm');
                    const formData = new FormData(form);
                    const registrationNumber = formData.get('registration_number');
                    const personnelId = formData.get('personnel_id');

                    // التحقق من رقم القيد المكرر (فقط للأعوان الجدد)
                    if (!personnelId) {
                        const existingRegistrations = [];
                        document.querySelectorAll('#personnelTable tbody tr td:first-child').forEach(function(cell) {
                            const regNum = cell.textContent.trim();
                            if (regNum && regNum !== '–') {
                                existingRegistrations.push(regNum);
                            }
                        });

                        if (existingRegistrations.includes(registrationNumber)) {
                            alert('رقم القيد موجود مسبقاً في هذه الوحدة. يرجى استخدام رقم قيد مختلف.');
                            return;
                        }
                    }

                    formData.append('action', 'add_personnel');
                    formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

                    fetch(window.location.href, {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert(data.message);
                            location.reload();
                        } else {
                            alert('خطأ: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('حدث خطأ أثناء حفظ البيانات');
                    });
                });
            }

            // Morning Check System Functions
            window.activateShift = function(shiftId) {
                if (confirm('هل أنت متأكد من تفعيل هذه الفرقة؟')) {
                    fetch('{% url "shift_management" %}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                        },
                        body: JSON.stringify({
                            action: 'set_active_shift',
                            unit_id: {{ daily_count.unit.id|default:0 }},
                            shift_id: shiftId,
                            date: '{{ today|date:"Y-m-d" }}'
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert(data.message);
                            location.reload();
                        } else {
                            alert('حدث خطأ: ' + data.message);
                        }
                    })
                    .catch(error => {
                        alert('حدث خطأ في الاتصال');
                    });
                }
            };

            window.addEightHourPersonnel = function() {
                alert('سيتم إضافة نموذج إضافة عون نظام 8 ساعات');
                // TODO: إضافة modal لإضافة عون نظام 8 ساعات
            };

            window.editEightHourRecord = function(recordId) {
                alert('سيتم إضافة نموذج تعديل سجل نظام 8 ساعات');
                // TODO: إضافة modal لتعديل سجل نظام 8 ساعات
            };
        });
    </script>
</body>
</html>
