{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة جدولة الفرق - نظام الحماية المدنية</title>
    
    <!-- نظام CSS الموحد الجديد -->
    <link rel="stylesheet" href="{% static 'css/dpc-unified.css' %}">
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/home.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    
    <style>
        .schedule-calendar {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .calendar-header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .calendar-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            background: #ecf0f1;
        }
        
        .calendar-day {
            background: white;
            padding: 15px;
            min-height: 100px;
            border: 1px solid #ecf0f1;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .calendar-day:hover {
            background: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .day-number {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 5px;
        }
        
        .shift-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
            width: 100%;
            margin-top: 5px;
        }
        
        .shift-1 { background: #e74c3c; color: white; }
        .shift-2 { background: #f39c12; color: white; }
        .shift-3 { background: #27ae60; color: white; }
        .shift-none { background: #95a5a6; color: white; }
        
        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .shift-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .today {
            background: #3498db !important;
            color: white;
        }
        
        .weekend {
            background: #ecf0f1;
        }

        /* تحسينات للكروت الجديدة */
        .card {
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }

        .card-header {
            background: linear-gradient(135deg, #3498db, #2c3e50);
            color: white;
            border-bottom: none;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            border: none;
            color: white;
        }

        .btn-warning:hover {
            background: linear-gradient(135deg, #e67e22, #d35400);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, #3498db, #2980b9);
            border: none;
        }

        .btn-info:hover {
            background: linear-gradient(135deg, #2980b9, #1f4e79);
        }

        /* تحسين النماذج */
        .form-control {
            border-radius: 8px;
            border: 2px solid #ecf0f1;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        /* تحسين النافذة المنبثقة */
        .modal-content {
            border-radius: 15px;
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .modal-header {
            background: linear-gradient(135deg, #3498db, #2c3e50);
            color: white;
            border-radius: 15px 15px 0 0;
        }

        .alert-info {
            background: linear-gradient(135deg, #e8f4fd, #d6eaf8);
            border: 1px solid #3498db;
            border-radius: 8px;
        }

        /* تصميم النظام الموحد */
        .bg-gradient-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .border-primary {
            border-color: #667eea !important;
            border-width: 2px !important;
        }

        .form-control-lg {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .form-control-lg:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            transform: translateY(-1px);
        }

        .btn-lg {
            border-radius: 10px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            border: none;
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #ee5a52, #d63031);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(238, 90, 82, 0.3);
        }

        .shadow-lg {
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
        }

        .card-header h4 {
            margin: 0;
            font-weight: 700;
        }

        .text-primary { color: #667eea !important; }
        .text-success { color: #00b894 !important; }
        .text-warning { color: #fdcb6e !important; }
        .text-danger { color: #e17055 !important; }

        /* تأثيرات تفاعلية */
        .form-group {
            position: relative;
        }

        .form-group label {
            margin-bottom: 8px;
            font-size: 14px;
        }

        .alert-info {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            border: none;
            border-radius: 15px;
        }

        .alert-info h6 {
            color: white;
            font-weight: bold;
        }
    </style>
</head>

<body>
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle clickable" id="sidebar-toggle">
            </div>
        </div>
    </header>

    {% include 'includes/sidebar.html' %}

    <main class="main-content">
        <div class="container-fluid">
            <!-- رسائل النظام -->
            {% if messages %}
                <div class="messages-container">
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-triangle{% else %}info-circle{% endif %}"></i>
                            {{ message }}
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}

            <!-- عنوان الصفحة -->
            <div class="page-header">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h2 class="page-title">
                            <i class="fas fa-calendar-alt text-primary"></i>
                            إدارة جدولة الفرق
                        </h2>
                        <p class="page-subtitle">
                            الوحدة: <strong>{{ unit.name }}</strong> | 
                            {{ month_name }} {{ year }}
                        </p>
                    </div>
                    <div class="col-md-6 text-right">
                        <a href="/coordination-center/unified-morning-check/?unit_id={{ unit.id }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i> العودة للصفحة الموحدة
                        </a>
                    </div>
                </div>
            </div>

            <!-- إحصائيات الفرق -->
            <div class="stats-card">
                <h4><i class="fas fa-chart-bar text-info"></i> إحصائيات الأعوان حسب الفرق</h4>
                <div class="shift-stats">
                    <div class="stat-item">
                        <div class="stat-number text-danger">{{ personnel_stats.shift_1 }}</div>
                        <div>الفرقة الأولى</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number text-warning">{{ personnel_stats.shift_2 }}</div>
                        <div>الفرقة الثانية</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number text-success">{{ personnel_stats.shift_3 }}</div>
                        <div>الفرقة الثالثة</div>
                    </div>
                </div>
            </div>

            <!-- نظام الجدولة الموحد البسيط -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card border-primary shadow-lg">
                        <div class="card-header bg-gradient-primary text-white text-center">
                            <h4><i class="fas fa-cogs"></i> نظام الجدولة الموحد</h4>
                            <p class="mb-0 small">عملية واحدة بسيطة لجميع أنواع الجدولة</p>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <!-- اختيار الفرقة العاملة -->
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="font-weight-bold text-primary">
                                            <i class="fas fa-users"></i> الفرقة العاملة
                                        </label>
                                        <select class="form-control form-control-lg" id="simpleWorkingShift">
                                            {% for shift_value, shift_label in shift_choices %}
                                                <option value="{{ shift_value }}">{{ shift_label }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>

                                <!-- اختيار النطاق -->
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="font-weight-bold text-success">
                                            <i class="fas fa-map-marked-alt"></i> النطاق
                                        </label>
                                        <select class="form-control form-control-lg" id="simpleScope">
                                            <option value="unit">الوحدة الحالية فقط</option>
                                            <option value="wilaya" selected>جميع وحدات الولاية</option>
                                            {% if user.userprofile.role == 'admin' %}
                                            <option value="system">جميع وحدات النظام</option>
                                            {% endif %}
                                        </select>
                                    </div>
                                </div>

                                <!-- اختيار المدة -->
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="font-weight-bold text-warning">
                                            <i class="fas fa-calendar-alt"></i> المدة
                                        </label>
                                        <select class="form-control form-control-lg" id="simpleDuration">
                                            <option value="today">اليوم فقط</option>
                                            <option value="month">الشهر الحالي</option>
                                            <option value="year">السنة الحالية</option>
                                            <option value="forever" selected>إلى ما لا نهاية</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- زر التطبيق -->
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="font-weight-bold text-danger">
                                            <i class="fas fa-rocket"></i> التطبيق
                                        </label>
                                        <button type="button" class="btn btn-danger btn-lg btn-block" onclick="applySimpleUpdate()">
                                            <i class="fas fa-sync-alt"></i> تطبيق الجدولة
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- معلومات سريعة -->
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <div class="alert alert-info">
                                        <h6><i class="fas fa-info-circle"></i> كيفية الاستخدام:</h6>
                                        <ul class="mb-0 small">
                                            <li><strong>الفرقة العاملة:</strong> اختر الفرقة التي تريد أن تعمل اليوم</li>
                                            <li><strong>النطاق:</strong> اختر هل تريد التطبيق على الوحدة فقط أم الولاية كاملة</li>
                                            <li><strong>المدة:</strong> اختر كم من الوقت تريد تطبيق هذه الجدولة</li>
                                            <li><strong>النتيجة:</strong> سيتم تحديث الجدولة تلقائياً مع تناوب الفرق يومياً</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            </div>

            <!-- التنقل بين الشهور -->
            <div class="schedule-calendar">
                <div class="calendar-header">
                    <div class="calendar-nav">
                        <a href="?unit_id={{ unit.id }}&year={{ prev_year }}&month={{ prev_month }}" class="btn btn-light btn-sm">
                            <i class="fas fa-chevron-right"></i> الشهر السابق
                        </a>
                        <h3>{{ month_name }} {{ year }}</h3>
                        <a href="?unit_id={{ unit.id }}&year={{ next_year }}&month={{ next_month }}" class="btn btn-light btn-sm">
                            الشهر التالي <i class="fas fa-chevron-left"></i>
                        </a>
                    </div>
                </div>

                <!-- أيام الأسبوع -->
                <div class="calendar-grid">
                    <div class="calendar-day" style="background: #34495e; color: white; font-weight: bold; text-align: center;">الأحد</div>
                    <div class="calendar-day" style="background: #34495e; color: white; font-weight: bold; text-align: center;">الاثنين</div>
                    <div class="calendar-day" style="background: #34495e; color: white; font-weight: bold; text-align: center;">الثلاثاء</div>
                    <div class="calendar-day" style="background: #34495e; color: white; font-weight: bold; text-align: center;">الأربعاء</div>
                    <div class="calendar-day" style="background: #34495e; color: white; font-weight: bold; text-align: center;">الخميس</div>
                    <div class="calendar-day" style="background: #34495e; color: white; font-weight: bold; text-align: center;">الجمعة</div>
                    <div class="calendar-day" style="background: #34495e; color: white; font-weight: bold; text-align: center;">السبت</div>

                    <!-- أيام الشهر -->
                    {% for day_info in month_days %}
                        {% if day_info.day == 1 %}
                            <!-- إضافة خلايا فارغة لبداية الشهر -->
                            {% with day_info.date.weekday as start_weekday %}
                                {% for i in "0123456"|make_list %}
                                    {% if forloop.counter0 < start_weekday %}
                                        <div class="calendar-day" style="background: #ecf0f1;"></div>
                                    {% endif %}
                                {% endfor %}
                            {% endwith %}
                        {% endif %}
                        
                        <div class="calendar-day {% if day_info.date == today %}today{% endif %}" data-date="{{ day_info.date|date:'Y-m-d' }}">
                            <div class="day-number">{{ day_info.day }}</div>
                            
                            {% if day_info.working_shift %}
                                <div class="shift-badge shift-{{ day_info.working_shift|slice:'-1' }}">
                                    {{ day_info.shift_display }}
                                </div>
                            {% else %}
                                <div class="shift-badge shift-none">
                                    غير محدد
                                </div>
                            {% endif %}
                            
                            <div class="mt-2">
                                <select class="form-control form-control-sm shift-selector" data-date="{{ day_info.date|date:'Y-m-d' }}">
                                    <option value="">-- اختر الفرقة --</option>
                                    {% for shift_value, shift_label in shift_choices %}
                                        <option value="{{ shift_value }}" {% if day_info.working_shift == shift_value %}selected{% endif %}>
                                            {{ shift_label }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </main>

    <!-- تضمين ملفات JavaScript -->
    <script src="{% static 'js/sidebar.js' %}"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // إغلاق الرسائل تلقائياً
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);

        // تحديث الفرقة العاملة
        $('.shift-selector').change(function() {
            const date = $(this).data('date');
            const shift = $(this).val();
            
            if (!shift) return;
            
            if (confirm(`هل أنت متأكد من تعيين هذه الفرقة لتاريخ ${date}؟`)) {
                // إنشاء نموذج مخفي وإرساله
                const form = $('<form>', {
                    method: 'POST',
                    action: window.location.href
                });
                
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'csrfmiddlewaretoken',
                    value: $('[name=csrfmiddlewaretoken]').val()
                }));
                
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'action',
                    value: 'update_shift'
                }));
                
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'date',
                    value: date
                }));
                
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'shift',
                    value: shift
                }));
                
                $('body').append(form);
                form.submit();
            } else {
                // إعادة تعيين القيمة السابقة
                $(this).val($(this).find('option[selected]').val());
            }
        });

        // تحديد اليوم الحالي
        const today = new Date().toISOString().split('T')[0];
        $(`.calendar-day[data-date="${today}"]`).addClass('today');





        // دالة عرض الإشعارات
        function showNotification(message, type = 'info') {
            const alertClass = type === 'success' ? 'alert-success' :
                              type === 'warning' ? 'alert-warning' :
                              type === 'error' ? 'alert-danger' : 'alert-info';

            const notification = `
                <div class="alert ${alertClass} alert-dismissible fade show notification-alert" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : type === 'error' ? 'times-circle' : 'info-circle'}"></i>
                    ${message}
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
            `;

            $('body').append(notification);

            // إزالة الإشعار تلقائياً بعد 3 ثوان
            setTimeout(() => {
                $('.notification-alert').fadeOut('slow', function() {
                    $(this).remove();
                });
            }, 3000);
        }

        // تطبيق الجدولة الموحدة البسيطة
        function applySimpleUpdate() {
            const workingShift = $('#simpleWorkingShift').val();
            const scope = $('#simpleScope').val();
            const duration = $('#simpleDuration').val();

            if (!workingShift) {
                showNotification('يرجى اختيار الفرقة العاملة', 'warning');
                return;
            }

            // تحديد النصوص للعرض
            const shiftText = $('#simpleWorkingShift option:selected').text();
            const scopeText = $('#simpleScope option:selected').text();
            const durationText = $('#simpleDuration option:selected').text();

            const confirmMessage = `🚀 تأكيد تطبيق الجدولة\n\n` +
                `👥 الفرقة العاملة: ${shiftText}\n` +
                `🗺️ النطاق: ${scopeText}\n` +
                `📅 المدة: ${durationText}\n\n` +
                `⚠️ سيتم تحديث الجدولة مع تناوب الفرق يومياً\n` +
                `هل تريد المتابعة؟`;

            if (!confirm(confirmMessage)) {
                return;
            }

            showNotification('🔄 جاري تطبيق الجدولة... يرجى الانتظار', 'info');

            const data = {
                unit_id: {{ unit.id }},
                working_shift: workingShift,
                scope: scope,
                duration: duration
            };

            fetch('/api/unified/simple-shift-update/', {
                method: 'POST',
                body: JSON.stringify(data),
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val()
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('✅ تم تطبيق الجدولة بنجاح!', 'success');

                    const details = data.details;
                    let resultMessage = `🎯 تم تطبيق الجدولة بنجاح!\n\n`;
                    resultMessage += `👥 الفرقة العاملة: ${details.working_shift}\n`;
                    resultMessage += `🗺️ النطاق: ${details.scope}\n`;
                    resultMessage += `📅 المدة: ${details.duration}\n`;
                    resultMessage += `📊 عدد الوحدات: ${details.units_count}\n`;
                    resultMessage += `📈 إجمالي الأيام: ${details.total_days.toLocaleString()}\n`;
                    resultMessage += `📅 من: ${details.start_date} إلى: ${details.end_date}\n\n`;
                    resultMessage += `📋 الوحدات المحدثة:\n`;
                    details.units_processed.forEach(unit => {
                        resultMessage += `• ${unit.name}: ${unit.days.toLocaleString()} يوم\n`;
                    });

                    setTimeout(() => {
                        alert(resultMessage);
                        location.reload();
                    }, 1500);
                } else {
                    showNotification('❌ خطأ: ' + (data.error || 'خطأ غير معروف'), 'error');
                }
            })
            .catch(error => {
                console.error('خطأ في تطبيق الجدولة:', error);
                showNotification('حدث خطأ في الاتصال', 'error');
            });
        }
    </script>
</body>
</html>
