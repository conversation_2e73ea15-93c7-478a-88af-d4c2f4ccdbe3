{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة عون جديد - نظام الحماية المدنية</title>
    
    <!-- نظام CSS الموحد الجديد -->
    <link rel="stylesheet" href="{% static 'css/dpc-unified.css' %}">
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/home.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
</head>

<body>
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle clickable" id="sidebar-toggle">
            </div>
        </div>
    </header>

    {% include 'includes/sidebar.html' %}

    <main>
        <div class="home-container">
            <!-- العنوان الرئيسي -->
            <div class="unit-count-details">
                <div class="unit-count-icon">
                    <i class="fas fa-user-plus"></i>
                </div>
                <div class="unit-count-title">إضافة عون جديد</div>
                <div class="unit-count-subtitle">وحدة: {{ selected_unit.name }}</div>
            </div>

            <!-- عرض الرسائل -->
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-triangle{% else %}info-circle{% endif %}"></i>
                        {{ message }}
                        <button type="button" class="close" data-dismiss="alert">
                            <span>&times;</span>
                        </button>
                    </div>
                {% endfor %}
            {% endif %}

            <!-- نموذج إضافة العون -->
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-user-plus"></i> بيانات العون الجديد</h3>
                </div>
                <div class="card-body">
                    <form method="post" id="addPersonnelForm">
                        {% csrf_token %}
                        
                        <div class="row">
                            <!-- الاسم الأول -->
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="form-label required">
                                        <i class="fas fa-user text-primary"></i> الاسم الأول
                                    </label>
                                    <input type="text" name="first_name" class="form-control" required 
                                           placeholder="أدخل الاسم الأول">
                                </div>
                            </div>

                            <!-- اسم العائلة -->
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="form-label required">
                                        <i class="fas fa-user text-primary"></i> اسم العائلة
                                    </label>
                                    <input type="text" name="last_name" class="form-control" required 
                                           placeholder="أدخل اسم العائلة">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- الرتبة -->
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="form-label required">
                                        <i class="fas fa-star text-primary"></i> الرتبة
                                    </label>
                                    <select name="rank" class="form-control" required>
                                        <option value="">-- اختر الرتبة --</option>
                                        {% for rank_code, rank_name in ranks %}
                                            <option value="{{ rank_code }}">{{ rank_name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>

                            <!-- المنصب -->
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="form-label required">
                                        <i class="fas fa-briefcase text-primary"></i> المنصب
                                    </label>
                                    <select name="position" class="form-control" required>
                                        <option value="">-- اختر المنصب --</option>
                                        {% for position_code, position_name in positions %}
                                            <option value="{{ position_code }}">{{ position_name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- رقم التسجيل -->
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="form-label required">
                                        <i class="fas fa-id-card text-primary"></i> رقم التسجيل
                                    </label>
                                    <input type="text" name="registration_number" class="form-control" required
                                           placeholder="أدخل رقم التسجيل">
                                    <small class="form-text text-muted">رقم القيد الخاص بالعون (مطلوب)</small>
                                </div>
                            </div>

                            <!-- الجنس -->
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="form-label required">
                                        <i class="fas fa-venus-mars text-primary"></i> الجنس
                                    </label>
                                    <select name="gender" class="form-control" required>
                                        <option value="">-- اختر الجنس --</option>
                                        <option value="male">ذكر</option>
                                        <option value="female">أنثى</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- تاريخ الميلاد -->
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="form-label required">
                                        <i class="fas fa-calendar text-primary"></i> تاريخ الميلاد
                                    </label>
                                    <input type="date" name="birth_date" class="form-control" required>
                                    <small class="form-text text-muted">لحساب العمر تلقائياً</small>
                                </div>
                            </div>

                            <!-- تاريخ الالتحاق -->
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="form-label required">
                                        <i class="fas fa-calendar-plus text-primary"></i> تاريخ الالتحاق
                                    </label>
                                    <input type="date" name="joining_date" class="form-control" required>
                                    <small class="form-text text-muted">لحساب سنوات الخدمة تلقائياً</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- رقم الهاتف -->
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="form-label required">
                                        <i class="fas fa-phone text-primary"></i> رقم الهاتف
                                    </label>
                                    <input type="tel" name="phone_number" class="form-control" required
                                           placeholder="0555123456" pattern="[0-9]{10}">
                                    <small class="form-text text-muted">رقم الهاتف مع التحقق من النمط</small>
                                </div>
                            </div>

                            <!-- نظام العمل -->
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="form-label required">
                                        <i class="fas fa-clock text-primary"></i> نظام العمل
                                    </label>
                                    <select name="work_system" class="form-control" required onchange="toggleShiftField()">
                                        <option value="">-- اختر نظام العمل --</option>
                                        <option value="24_hours">نظام 24 ساعة</option>
                                        <option value="8_hours">نظام 8 ساعات</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row" id="shiftRow" style="display: none;">
                            <!-- الفرقة -->
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-users text-primary"></i> الفرقة
                                    </label>
                                    <select name="assigned_shift" class="form-control">
                                        <option value="">-- اختر الفرقة --</option>
                                        <option value="shift_1">الفرقة الأولى</option>
                                        <option value="shift_2">الفرقة الثانية</option>
                                        <option value="shift_3">الفرقة الثالثة</option>
                                    </select>
                                    <small class="form-text text-muted">تظهر مع نظام 24 ساعة فقط</small>
                                </div>
                            </div>
                        </div>

                        <!-- تنبيه مهم -->
                        <div class="alert alert-info" role="alert">
                            <i class="fas fa-info-circle"></i>
                            <strong>ملاحظة مهمة:</strong>
                            بعد إضافة العون، ستظهر بياناته في جدول التعداد الصباحي كل يوم. يمكنك تغيير حالته (حاضر/غائب/في مهمة) من الجدول مباشرة.
                        </div>

                        <!-- أزرار العمل -->
                        <div class="action-buttons">
                            <a href="{% url 'daily_unit_count' %}?unit_id={{ selected_unit.id }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right"></i> العودة للتعداد الصباحي
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-user-plus"></i> إضافة العون
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>

    <script src="{% static 'js/sidebar.js' %}"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            // التركيز على الحقل الأول
            const firstInput = document.querySelector('input[name="first_name"]');
            if (firstInput) {
                firstInput.focus();
            }

            // إظهار/إخفاء حقل الفرقة حسب نظام العمل
            function toggleShiftField() {
                const workSystem = document.querySelector('select[name="work_system"]').value;
                const shiftRow = document.getElementById('shiftRow');

                if (workSystem === '24_hours') {
                    shiftRow.style.display = 'block';
                } else {
                    shiftRow.style.display = 'none';
                    document.querySelector('select[name="assigned_shift"]').value = '';
                }
            }

            // التحقق من صحة النموذج
            const form = document.getElementById('addPersonnelForm');
            form.addEventListener('submit', function(e) {
                const firstName = document.querySelector('input[name="first_name"]').value.trim();
                const lastName = document.querySelector('input[name="last_name"]').value.trim();
                const rank = document.querySelector('select[name="rank"]').value;
                const position = document.querySelector('select[name="position"]').value;
                const registrationNumber = document.querySelector('input[name="registration_number"]').value.trim();
                const gender = document.querySelector('select[name="gender"]').value;
                const birthDate = document.querySelector('input[name="birth_date"]').value;
                const joiningDate = document.querySelector('input[name="joining_date"]').value;
                const phoneNumber = document.querySelector('input[name="phone_number"]').value.trim();
                const workSystem = document.querySelector('select[name="work_system"]').value;

                // التحقق من الحقول المطلوبة
                if (!firstName || !lastName || !rank || !position || !registrationNumber ||
                    !gender || !birthDate || !joiningDate || !phoneNumber || !workSystem) {
                    e.preventDefault();
                    alert('يرجى ملء جميع الحقول المطلوبة');
                    return false;
                }

                // التحقق من نمط رقم الهاتف
                const phonePattern = /^[0-9]{10}$/;
                if (!phonePattern.test(phoneNumber)) {
                    e.preventDefault();
                    alert('يرجى إدخال رقم هاتف صحيح (10 أرقام)');
                    return false;
                }

                // التحقق من التواريخ
                const birthDateObj = new Date(birthDate);
                const joiningDateObj = new Date(joiningDate);
                const today = new Date();

                if (birthDateObj >= today) {
                    e.preventDefault();
                    alert('تاريخ الميلاد يجب أن يكون في الماضي');
                    return false;
                }

                if (joiningDateObj > today) {
                    e.preventDefault();
                    alert('تاريخ الالتحاق لا يمكن أن يكون في المستقبل');
                    return false;
                }

                if (joiningDateObj <= birthDateObj) {
                    e.preventDefault();
                    alert('تاريخ الالتحاق يجب أن يكون بعد تاريخ الميلاد');
                    return false;
                }
            });

            // إخفاء الرسائل تلقائياً وإغلاق النافذة عند النجاح
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert');
                alerts.forEach(function(alert) {
                    if (alert.classList.contains('alert-success')) {
                        alert.style.opacity = '0';
                        setTimeout(function() {
                            alert.remove();
                            // إغلاق النافذة وتحديث النافذة الأصلية
                            if (window.opener) {
                                window.opener.focus();
                                // تحديث النافذة الأصلية بعد ثانية
                                setTimeout(function() {
                                    window.opener.location.reload();
                                }, 1000);
                            }
                            window.close();
                        }, 1000);
                    }
                });
            }, 2000);

            // إضافة زر إغلاق النافذة
            const closeButton = document.createElement('button');
            closeButton.innerHTML = '<i class="fas fa-times"></i> إغلاق النافذة';
            closeButton.className = 'btn btn-secondary';
            closeButton.style.position = 'fixed';
            closeButton.style.top = '20px';
            closeButton.style.left = '20px';
            closeButton.style.zIndex = '1000';
            closeButton.onclick = function() {
                window.close();
            };
            document.body.appendChild(closeButton);
        });
    </script>
</body>
</html>
