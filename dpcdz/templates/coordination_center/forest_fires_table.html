{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحماية المدنية الجزائرية - جدول حرائق الغابات</title>
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/home.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="{% static 'css/table-styles.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle" id="sidebar-toggle" style="cursor: pointer;">
            </div>
        </div>
    </header>

    {% include 'includes/sidebar.html' %}

    <main>
        <div class="table-container">
            <h2 class="page-title">جدول حرائق الغابات - مركز التنسيق</h2>



            {% if forest_fires_data %}
            <div class="table-wrapper">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            {% if is_admin %}
                            <th>الولاية</th>
                            {% endif %}
                            <th>رقم البرقية</th>
                            <th>ساعة التدخل</th>
                            <th>الوحدة المتدخلة</th>
                            <th>البلدية</th>
                            <th>المكان المسمى</th>
                            <th>مدة العملية</th>
                            <th>الوسائل المتدخلة</th>
                            <th>طبيعة الخسائر</th>
                            <th>الخسائر بالهكتار</th>
                            <th>الخسائر بالآر</th>
                            <th>الخسائر بالمتر مربع</th>
                            <th>طبيعة الخسائر الأخرى</th>
                            <th>عدد الخسائر</th>
                            <th>وضعية التحكم في الحريق</th>
                            <th>عدد الجرحى</th>
                            <th>عدد الوفيات</th>
                            <th>عدد العائلات المجلاة</th>
                            <th>عدد الأشخاص المجلين</th>
                            <th>أماكن الإجلاء</th>
                            <th>إجراءات التكفل</th>
                            <th>الإجراءات</th>
                        </tr>
                        <tr class="filter-row">
                            <th>
                                <div class="date-filter">
                                    <select id="month-filter" class="filter-select">
                                        <option value="">الشهر</option>
                                        {% for month_code, month_name in months %}
                                        <option value="{{ month_code }}">{{ month_name }}</option>
                                        {% endfor %}
                                    </select>
                                    <select id="year-filter" class="filter-select">
                                        <option value="">السنة</option>
                                        {% for year in years %}
                                        <option value="{{ year }}">{{ year }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </th>
                            {% if is_admin %}
                            <th>
                                <select id="wilaya-filter" class="filter-select">
                                    <option value="">الكل</option>
                                    {% for wilaya_code, wilaya_name in wilaya_choices %}
                                    <option value="{{ wilaya_code }}">{{ wilaya_name }}</option>
                                    {% endfor %}
                                </select>
                            </th>
                            {% endif %}
                            <th></th>
                            <th></th>
                            <th>
                                <select id="unit-filter" class="filter-select">
                                    <option value="">الكل</option>
                                    {% for unit in units %}
                                    <option value="{{ unit }}">{{ unit }}</option>
                                    {% endfor %}
                                </select>
                            </th>
                            <th>
                                <select id="municipality-filter" class="filter-select">
                                    <option value="">الكل</option>
                                    {% for municipality in municipalities %}
                                    <option value="{{ municipality }}">{{ municipality }}</option>
                                    {% endfor %}
                                </select>
                            </th>
                            <th colspan="12"></th>
                            <th>
                                <button id="reset-filters" class="reset-filters-btn">
                                    <i class="fas fa-undo"></i> إعادة تعيين
                                </button>
                            </th>
                        </tr>
                    </thead>
                <tbody>
                    {% for item in forest_fires_data %}
                    <tr data-id="{{ item.id }}" {% if is_admin %}data-wilaya="{{ item.wilaya_code }}"{% endif %}>
                        <td data-iso-date="{{ item.date|date:'Y-m-d' }}">{{ item.formatted_date }}</td>
                        {% if is_admin %}
                        <td>{{ item.wilaya_name }}</td>
                        {% endif %}
                        <td>{{ item.telegram_number }}</td>
                        <td>{{ item.intervention_time }}</td>
                        <td>{{ item.intervening_unit }}</td>
                        <td>{{ item.municipality }}</td>
                        <td class="text-wrap">{{ item.location_name }}</td>
                        <td>{{ item.operation_duration }}</td>
                        <td class="text-wrap">{{ item.intervention_means }}</td>
                        <td class="text-wrap">{{ item.loss_nature }}</td>
                        <td>{{ item.losses_hectare|default:"0" }}</td>
                        <td>{{ item.losses_are|default:"0" }}</td>
                        <td>{{ item.losses_square_meter|default:"0" }}</td>
                        <td class="text-wrap">{{ item.other_loss_nature|default:"-" }}</td>
                        <td>{{ item.other_loss_count|default:"0" }}</td>
                        <td>{{ item.fire_control_status|default:"-" }}</td>
                        <td>{{ item.injured_count|default:"0" }}</td>
                        <td>{{ item.deaths_count|default:"0" }}</td>
                        <td>{{ item.evacuated_families_count|default:"0" }}</td>
                        <td>{{ item.evacuated_people_count|default:"0" }}</td>
                        <td class="text-wrap">{{ item.evacuation_locations|default:"-" }}</td>
                        <td class="text-wrap">{{ item.family_care_measures|default:"-" }}</td>
                        <td class="actions-cell">
                            <a href="#" class="btn-edit" title="تعديل" data-id="{{ item.id }}"><i class="fas fa-edit"></i></a>
                            <a href="#" class="btn-delete" title="حذف" data-id="{{ item.id }}"><i class="fas fa-trash-alt"></i></a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                </table>
            </div>
            {% else %}
            <div class="no-data">
                <p>لا توجد بيانات حرائق غابات مسجلة حتى الآن.</p>
                <a href="{% url 'forest_fires_form' %}" class="btn-add">
                    <i class="fas fa-plus"></i> إضافة أول سجل
                </a>
            </div>
            {% endif %}

            <!-- الأزرار العائمة -->
            <div class="floating-buttons">
                <a href="{% url 'forest_fires_form' %}" class="floating-btn add-btn" title="إضافة جديد">
                    <i class="fas fa-plus"></i>
                </a>
                <a href="{% url 'export_table_to_excel' 'coordination-forest-fires' %}" id="export-excel-floating-btn" class="floating-btn export-btn" title="تصدير إلى Excel">
                    <i class="fas fa-file-excel"></i>
                </a>
                <a href="#" id="scroll-top-btn" class="floating-btn top-btn" title="العودة إلى الأعلى">
                    <i class="fas fa-arrow-up"></i>
                </a>
            </div>

            <!-- Navigation buttons -->
            <div class="navigation-buttons-floating">
                <a href="{% url 'coordination_center' %}" class="floating-nav-btn green-btn">
                    <i class="fas fa-home"></i> مركز التنسيق
                </a>
                <a href="{% url 'forest_crop_fires' %}" class="floating-nav-btn orange-btn">
                    <i class="fas fa-fire"></i> حرائق الغابات والمحاصيل
                </a>
                <a href="{% url 'forest_crop_tables' %}" class="floating-nav-btn purple-btn">
                    <i class="fas fa-arrow-right"></i> صفحة الجداول
                </a>
            </div>
        </div>
    </main>

    <footer>
        <p>حقوق النشر © 2025 جميع الحقوق محفوظة - تم التطوير بواسطة عبد الرزاق مختاري</p>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تلميح للمستخدم عند تحميل الصفحة
            const tableWrapper = document.querySelector('.table-wrapper');
            if (!tableWrapper) return;

            // إضافة فئة لإظهار التلميح
            tableWrapper.classList.add('show-scroll-hint');

            // إزالة التلميح بعد أول تمرير
            tableWrapper.addEventListener('scroll', function() {
                tableWrapper.classList.remove('show-scroll-hint');
            }, { once: true });

            // تفعيل الفلاتر
            const monthFilter = document.getElementById('month-filter');
            const yearFilter = document.getElementById('year-filter');
            const wilayaFilter = document.getElementById('wilaya-filter');
            const unitFilter = document.getElementById('unit-filter');
            const municipalityFilter = document.getElementById('municipality-filter');
            const tableRows = document.querySelectorAll('.data-table tbody tr');

            // دالة لتطبيق الفلاتر
            function applyFilters() {
                const monthValue = monthFilter.value;
                const yearValue = yearFilter.value;
                const wilayaValue = wilayaFilter ? wilayaFilter.value : '';
                const unitValue = unitFilter.value;
                const municipalityValue = municipalityFilter.value;

                console.log("تطبيق الفلاتر:", {
                    شهر: monthValue,
                    سنة: yearValue,
                    ولاية: wilayaValue,
                    وحدة: unitValue,
                    بلدية: municipalityValue
                });

                // تطبيق الفلاتر على كل صف
                tableRows.forEach(row => {
                    // الحصول على قيم الخلايا
                    const dateCell = row.querySelector('td:nth-child(1)');

                    // تحديد مؤشرات الخلايا بناءً على وجود عمود الولاية
                    const isAdminView = row.hasAttribute('data-wilaya');
                    const unitCellIndex = isAdminView ? 5 : 4;
                    const municipalityCellIndex = isAdminView ? 6 : 5;

                    const unitCell = row.querySelector(`td:nth-child(${unitCellIndex})`).textContent.trim();
                    const municipalityCell = row.querySelector(`td:nth-child(${municipalityCellIndex})`).textContent.trim();

                    // الحصول على قيمة الولاية إذا كان المستخدم مسؤولاً
                    const wilayaCode = row.getAttribute('data-wilaya');

                    // استخراج الشهر والسنة من التاريخ
                    let rowMonth = '';
                    let rowYear = '';

                    if (dateCell) {
                        const isoDate = dateCell.getAttribute('data-iso-date');
                        if (isoDate) {
                            const dateParts = isoDate.split('-');
                            if (dateParts.length === 3) {
                                rowYear = dateParts[0];
                                rowMonth = dateParts[1];
                            }
                        } else {
                            // استخدام الدالة المساعدة لاستخراج معلومات التاريخ من النص
                            const dateCellText = dateCell.textContent.trim();
                            const dateInfo = extractDateInfo(dateCellText);
                            rowMonth = dateInfo.month;
                            rowYear = dateInfo.year;
                        }
                    }

                    // تطبيق الفلاتر
                    const matchesMonth = !monthValue || (rowMonth && rowMonth === monthValue);
                    const matchesYear = !yearValue || (rowYear && rowYear === yearValue);
                    const matchesWilaya = !wilayaValue || !wilayaFilter || (wilayaCode && wilayaCode === wilayaValue);
                    const matchesUnit = !unitValue || (unitCell && unitCell === unitValue);
                    const matchesMunicipality = !municipalityValue || (municipalityCell && municipalityCell.includes(municipalityValue));

                    // إظهار أو إخفاء الصف بناءً على نتيجة الفلاتر
                    if (matchesMonth && matchesYear && matchesWilaya && matchesUnit && matchesMunicipality) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });

                // تحديث رابط تصدير Excel ليشمل فقط البيانات المفلترة
                updateExcelExportUrl();
            }

            // تحديث رابط تصدير Excel ليشمل فقط البيانات المفلترة
            function updateExcelExportUrl() {
                const exportBtn = document.getElementById('export-excel-btn');
                const floatingExportBtn = document.getElementById('export-excel-floating-btn');

                // جمع معرفات الصفوف المرئية
                const visibleRows = Array.from(tableRows).filter(row => row.style.display !== 'none');
                const visibleIds = visibleRows.map(row => row.getAttribute('data-id')).filter(id => id);

                const exportUrl = visibleIds.length > 0
                    ? `{% url 'export_table_to_excel' 'coordination-forest-fires' %}?ids=${visibleIds.join(',')}`
                    : `{% url 'export_table_to_excel' 'coordination-forest-fires' %}`;

                // Update both buttons
                if (exportBtn) exportBtn.href = exportUrl;
                if (floatingExportBtn) floatingExportBtn.href = exportUrl;
            }

            // إضافة مستمعي الأحداث للفلاتر
            monthFilter.addEventListener('change', function() {
                applyFilters();
                updateExcelExportUrl();
            });
            yearFilter.addEventListener('change', function() {
                applyFilters();
                updateExcelExportUrl();
            });
            if (wilayaFilter) {
                wilayaFilter.addEventListener('change', function() {
                    applyFilters();
                    updateExcelExportUrl();
                });
            }
            unitFilter.addEventListener('change', function() {
                applyFilters();
                updateExcelExportUrl();
            });
            municipalityFilter.addEventListener('change', function() {
                applyFilters();
                updateExcelExportUrl();
            });

            // إضافة مستمع الحدث لزر إعادة تعيين الفلاتر
            const resetFiltersBtn = document.getElementById('reset-filters');
            if (resetFiltersBtn) {
                resetFiltersBtn.addEventListener('click', function() {
                    // إعادة تعيين جميع الفلاتر
                    monthFilter.value = '';
                    yearFilter.value = '';
                    if (wilayaFilter) {
                        wilayaFilter.value = '';
                    }
                    unitFilter.value = '';
                    municipalityFilter.value = '';

                    // إظهار جميع الصفوف
                    tableRows.forEach(row => {
                        row.style.display = '';
                    });

                    // تحديث رابط تصدير Excel
                    updateExcelExportUrl();

                    console.log("تم إعادة تعيين الفلاتر");
                });
            }

            // تفعيل زر التمرير إلى الأعلى
            const scrollTopBtn = document.getElementById('scroll-top-btn');
            if (scrollTopBtn) {
                scrollTopBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    window.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                    });
                });
            }

            // إضافة معرفات للصفوف لاستخدامها في تصدير Excel
            tableRows.forEach((row, index) => {
                const id = row.querySelector('td:last-child a.btn-edit')?.getAttribute('data-id') || index;
                row.setAttribute('data-id', id);
            });

            // تحديث رابط تصدير Excel عند تحميل الصفحة
            updateExcelExportUrl();

            // Add event listeners for export buttons
            const exportBtn = document.getElementById('export-excel-btn');
            const floatingExportBtn = document.getElementById('export-excel-floating-btn');

            if (exportBtn) {
                exportBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    window.location.href = exportBtn.href;
                });
            }

            if (floatingExportBtn) {
                floatingExportBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    window.location.href = floatingExportBtn.href;
                });
            }

            // تفعيل أزرار التعديل والحذف
            const editButtons = document.querySelectorAll('.btn-edit');
            const deleteButtons = document.querySelectorAll('.btn-delete');

            editButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const row = this.closest('tr');
                    const id = this.getAttribute('data-id') || 'unknown';
                    alert('تعديل العنصر رقم: ' + id);
                    // هنا يمكن إضافة كود لفتح نموذج التعديل
                });
            });

            deleteButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const row = this.closest('tr');
                    const id = this.getAttribute('data-id') || 'unknown';
                    if (confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
                        alert('تم حذف العنصر رقم: ' + id);
                        // هنا يمكن إضافة كود لحذف العنصر من قاعدة البيانات
                    }
                });
            });
        });
    </script>
    <script src="{% static 'js/sidebar.js' %}"></script>
    <script src="{% static 'js/arabic-date-utils.js' %}"></script>

    <style>
        .table-container {
            max-width: 100%;
            margin: 0 auto;
            padding: 20px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 80px;
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            overflow: hidden;
            font-size: 14px;
        }

        .filter-row th {
            padding: 5px;
            vertical-align: middle;
            background-color: #e6f0ff;
            color: #0d47a1;
            position: sticky;
            top: 40px;
            z-index: 9;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .filter-select {
            width: 100%;
            padding: 5px;
            border-radius: 4px;
            border: 1px solid #ddd;
            background-color: #fff;
            font-size: 12px;
            max-width: 150px;
        }

        .date-filter .filter-select {
            min-width: 100px;
        }

        .date-filter {
            display: flex;
            flex-direction: row;
            gap: 8px;
            max-width: 350px;
            width: 100%;
            justify-content: space-between;
        }

        .reset-filters-btn {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px 10px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: background-color 0.3s;
        }

        .reset-filters-btn:hover {
            background-color: #e9ecef;
        }

        .table-wrapper {
            overflow-x: auto;
            margin-top: 20px;
            max-height: 70vh;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .table-wrapper::after {
            content: "← قم بالتمرير للمزيد →";
            position: absolute;
            bottom: 10px;
            right: 50%;
            transform: translateX(50%);
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            opacity: 0.8;
            pointer-events: none;
            animation: fadeOut 3s forwards 3s;
        }

        @keyframes fadeOut {
            from { opacity: 0.8; }
            to { opacity: 0; }
        }

        h2.page-title {
            text-align: center;
            margin-bottom: 30px;
            color: #0d47a1;
            border-bottom: 2px solid #0d47a1;
            padding-bottom: 10px;
        }

        .data-table th,
        .data-table td {
            padding: 8px;
            text-align: center;
            border: 1px solid #ddd;
            white-space: nowrap;
        }

        /* Text wrapping for long content columns */
        .text-wrap {
            white-space: normal !important;
            word-wrap: break-word;
            max-width: 200px;
            line-height: 1.4;
        }

        .data-table th {
            background-color: #0d47a1;
            position: sticky;
            top: 0;
            z-index: 10;
            font-weight: bold;
            color: white;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .data-table th:last-child,
        .data-table td:last-child {
            position: sticky;
            left: 0;
            background-color: #f8f9fa;
            z-index: 5;
            box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
            min-width: 80px;
            text-align: center;
        }

        .data-table tr:hover td:last-child {
            background-color: #e9f5ff;
        }

        .data-table tr:nth-child(even) td:last-child {
            background-color: #f9f9f9;
        }

        .data-table th:first-child {
            min-width: 150px;
            font-weight: bold;
            font-size: 15px;
            padding: 10px;
            background-color: #0d47a1;
            color: white;
        }

        .actions-cell {
            padding: 5px !important;
            white-space: nowrap;
            width: 80px;
        }

        .data-table tr:hover {
            background-color: #f5f5f5;
        }

        .data-table tr:last-child td {
            border-bottom: none;
        }



        .btn-edit {
            color: #0d6efd;
            text-decoration: none;
            margin-right: 10px;
            display: inline-block;
            width: 30px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            border-radius: 50%;
            transition: background-color 0.3s;
        }

        .btn-edit:hover {
            background-color: rgba(13, 110, 253, 0.1);
        }

        .btn-delete {
            color: #dc3545;
            text-decoration: none;
            display: inline-block;
            width: 30px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            border-radius: 50%;
            transition: background-color 0.3s;
        }

        .btn-delete:hover {
            background-color: rgba(220, 53, 69, 0.1);
        }

        .no-data {
            padding: 30px;
            text-align: center;
            color: #6c757d;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .navigation-buttons {
            display: flex;
            justify-content: center;
            gap: 10px;
            position: fixed;
            bottom: 20px;
            left: 20px;
            z-index: 100;
        }

        .btn {
            padding: 10px 15px;
            border-radius: 50px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
        }

        /* Floating Navigation buttons */
        .navigation-buttons-floating {
            position: fixed;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            justify-content: center;
            gap: 15px;
            z-index: 999;
            flex-wrap: wrap;
        }

        .floating-nav-btn {
            padding: 15px 25px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            color: white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            border: none;
            cursor: pointer;
            min-width: 200px;
            justify-content: center;
        }

        .floating-nav-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            color: white;
        }

        /* Green button for مركز التنسيق */
        .green-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .green-btn:hover {
            background: linear-gradient(135deg, #218838, #1ba085);
        }

        /* Orange button for حرائق الغابات والمحاصيل */
        .orange-btn {
            background: linear-gradient(135deg, #fd7e14, #ffc107);
        }

        .orange-btn:hover {
            background: linear-gradient(135deg, #e8690b, #e0a800);
        }

        /* Purple button for صفحة الجداول */
        .purple-btn {
            background: linear-gradient(135deg, #6f42c1, #8e44ad);
        }

        .purple-btn:hover {
            background: linear-gradient(135deg, #5a359a, #7d3c98);
        }

        .btn-gray {
            background-color: #6c757d;
            color: white;
        }

        .floating-buttons {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 100;
        }

        .floating-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            text-decoration: none;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .floating-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.35);
        }

        .add-btn {
            background-color: #28a745;
        }

        .export-btn {
            background-color: #6c757d;
        }

        .top-btn {
            background-color: #0d47a1;
        }

        /* Fixed action buttons at top */
        .table-actions {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 20px;
        }

        .btn-add {
            background-color: #28a745;
            color: white;
            padding: 12px 20px;
            border-radius: 50px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .btn-add:hover {
            background-color: #218838;
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
            color: white;
            text-decoration: none;
        }

        .btn-export {
            background-color: #6c757d;
            color: white;
            padding: 12px 20px;
            border-radius: 50px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .btn-export:hover {
            background-color: #5a6268;
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
            color: white;
            text-decoration: none;
        }



        @media (max-width: 768px) {
            .data-table {
                display: block;
                overflow-x: auto;
                white-space: nowrap;
                margin-bottom: 80px;
            }

            .table-actions {
                flex-direction: column;
                align-items: center;
                gap: 10px;
            }

            .btn-add, .btn-export {
                padding: 8px 12px;
                font-size: 12px;
            }

            .btn-add i, .btn-export i {
                font-size: 14px;
            }

            .btn {
                padding: 8px 12px;
                font-size: 12px;
            }

            .btn i {
                font-size: 14px;
            }

            .navigation-buttons-floating {
                bottom: 20px;
                flex-direction: column;
                align-items: center;
                gap: 10px;
            }

            .floating-nav-btn {
                min-width: 180px;
                padding: 12px 20px;
                font-size: 14px;
            }

            .floating-buttons {
                bottom: 10px;
                right: 10px;
            }

            .floating-btn {
                width: 40px;
                height: 40px;
                font-size: 14px;
            }

            .filter-row th {
                padding: 3px;
                position: sticky;
                top: 40px;
                z-index: 9;
                background-color: #e6f0ff;
                color: #0d47a1;
                box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }

            .filter-select {
                font-size: 10px;
                padding: 3px;
                max-width: 100px;
            }

            .date-filter {
                max-width: 220px;
                flex-direction: row;
                gap: 5px;
            }

            .date-filter .filter-select {
                min-width: 80px;
                font-size: 10px;
            }

            .reset-filters-btn {
                padding: 3px 6px;
                font-size: 10px;
            }

            .text-wrap {
                max-width: 150px;
            }
        }

        @media (max-width: 480px) {
            .navigation-buttons-floating {
                bottom: 10px;
                gap: 8px;
            }

            .floating-nav-btn {
                min-width: 160px;
                padding: 10px 15px;
                font-size: 13px;
            }
        }
    </style>
</body>
</html>
