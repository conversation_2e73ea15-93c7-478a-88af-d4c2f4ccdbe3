{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحماية المدنية الجزائرية - إدارة الرتب والمناصب</title>
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/home.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle" id="sidebar-toggle" style="cursor: pointer;">
            </div>
        </div>
    </header>

    {% include 'includes/sidebar.html' %}

    <main>
        <div class="home-container">
            {% csrf_token %}
            
            <!-- العنوان الرئيسي -->
            <div class="roles-details">
                <div class="roles-icon">
                    <i class="fas fa-users-cog"></i>
                </div>
                <div class="roles-title">إدارة الرتب والمناصب</div>
                <div class="roles-subtitle">إضافة وتعديل وحذف الرتب والمناصب المتاحة بشكل منفصل</div>
            </div>

            <!-- نموذج إضافة رتبة أو منصب جديد -->
            <div class="roles-form">
                <div class="form-header">
                    <h3><i class="fas fa-plus"></i> إضافة رتبة أو منصب جديد</h3>
                </div>
                <form id="addRoleForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="role_type">
                                <i class="fas fa-list text-primary"></i> النوع <span class="text-danger">*</span>
                            </label>
                            <select class="form-control" id="role_type" name="role_type" required>
                                <option value="">-- اختر النوع أولاً --</option>
                                <option value="rank">🎖️ رتبة عسكرية</option>
                                <option value="position">💼 منصب وظيفي</option>
                            </select>
                            <small class="form-text text-muted">اختر ما إذا كنت تريد إضافة رتبة عسكرية أم منصب وظيفي</small>
                        </div>
                        <div class="form-group">
                            <label for="role_name">
                                <i class="fas fa-edit text-success"></i> الاسم <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="role_name" name="role_name" required
                                   placeholder="مثال: عقيد أو رئيس الوحدة">
                            <small class="form-text text-muted">أدخل اسم الرتبة أو المنصب الجديد</small>
                        </div>
                        <div class="form-group">
                            <button type="submit" class="main-btn add-btn">
                                <i class="fas fa-plus"></i> إضافة الرتبة/المنصب
                            </button>
                        </div>
                    </div>

                    <!-- تنبيه توضيحي -->
                    <div class="alert alert-info mt-3" role="alert">
                        <i class="fas fa-info-circle"></i>
                        <strong>ملاحظة:</strong>
                        بعد إضافة الرتبة أو المنصب، ستصبح متاحة في جميع نماذج إضافة الأعوان في النظام.
                    </div>
                </form>
            </div>

            <!-- قائمة الرتب الحالية -->
            <div class="roles-list-section">
                <div class="section-header">
                    <h3><i class="fas fa-star"></i> الرتب العسكرية</h3>
                    <div class="header-controls">
                        <input type="text" id="ranksSearch" class="form-control search-input" placeholder="البحث في الرتب...">
                    </div>
                </div>

                <div class="roles-grid">
                    {% for rank in ranks %}
                    <div class="role-card rank-card" data-role="{{ rank }}">
                        <div class="role-content">
                            <div class="role-name">{{ rank }}</div>
                            <div class="role-actions">
                                <button class="btn btn-sm btn-warning edit-role"
                                        data-role="{{ rank }}"
                                        data-type="rank"
                                        title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-danger delete-role"
                                        data-role="{{ rank }}"
                                        data-type="rank"
                                        title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- قائمة المناصب الحالية -->
            <div class="roles-list-section">
                <div class="section-header">
                    <h3><i class="fas fa-briefcase"></i> المناصب الوظيفية</h3>
                    <div class="header-controls">
                        <input type="text" id="positionsSearch" class="form-control search-input" placeholder="البحث في المناصب...">
                    </div>
                </div>

                <div class="roles-grid">
                    {% for position in positions %}
                    <div class="role-card position-card" data-role="{{ position }}">
                        <div class="role-content">
                            <div class="role-name">{{ position }}</div>
                            <div class="role-actions">
                                <button class="btn btn-sm btn-warning edit-role"
                                        data-role="{{ position }}"
                                        data-type="position"
                                        title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-danger delete-role"
                                        data-role="{{ position }}"
                                        data-type="position"
                                        title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- أزرار العمل الرئيسية -->
            <div class="main-buttons-container">
                <div class="main-buttons">
                    <a href="{% url 'daily_unit_count' %}" class="main-btn back-btn">
                        <i class="fas fa-arrow-right"></i> التعداد الصباحي
                    </a>
                    <a href="{% url 'coordination_center' %}" class="main-btn home-btn">
                        <i class="fas fa-home"></i> مركز التنسيق
                    </a>
                </div>
            </div>

            <!-- الأزرار العائمة -->
            <div class="floating-buttons">
                <a href="{% url 'daily_unit_count' %}" class="floating-btn back-btn" title="التعداد الصباحي">
                    <i class="fas fa-table"></i>
                </a>
                <a href="{% url 'coordination_center' %}" class="floating-btn home-btn" title="مركز التنسيق">
                    <i class="fas fa-home"></i>
                </a>
                <a href="#" id="back-to-top" class="floating-btn top-btn" title="العودة إلى الأعلى">
                    <i class="fas fa-arrow-up"></i>
                </a>
            </div>
        </div>
    </main>

    <!-- Modal for editing role -->
    <div class="modal fade" id="editRoleModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تعديل الرتبة/المنصب</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="editRoleForm">
                        <input type="hidden" id="old_role" name="old_role">
                        <div class="form-group">
                            <label>اسم الرتبة/المنصب الجديد</label>
                            <input type="text" name="new_role" id="new_role" class="form-control" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="saveRoleEdit">حفظ التغييرات</button>
                </div>
            </div>
        </div>
    </div>

    <footer>
        <p>حقوق النشر © 2025 جميع الحقوق محفوظة - تم التطوير بواسطة عبد الرزاق مختاري</p>
    </footer>

    <script src="{% static 'js/sidebar.js' %}"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        $(document).ready(function() {
            // إضافة زر إغلاق النافذة
            const closeButton = $('<button class="btn btn-secondary" style="position: fixed; top: 20px; left: 20px; z-index: 1000;"><i class="fas fa-times"></i> إغلاق النافذة</button>');
            closeButton.click(function() {
                window.close();
            });
            $('body').append(closeButton);
            // Add role form submission
            $('#addRoleForm').submit(function(e) {
                e.preventDefault();
                const roleName = $('#role_name').val().trim();
                const roleType = $('#role_type').val();

                if (!roleType) {
                    showNotification('يرجى اختيار نوع الرتبة أو المنصب', 'error');
                    return;
                }

                if (!roleName) {
                    showNotification('يرجى إدخال اسم الرتبة أو المنصب', 'error');
                    return;
                }

                $.post('', {
                    action: 'add_role',
                    role_name: roleName,
                    role_type: roleType,
                    csrfmiddlewaretoken: $('[name=csrfmiddlewaretoken]').val()
                }, function(response) {
                    if (response.success) {
                        showNotification(response.message, 'success');
                        $('#role_name').val('');
                        $('#role_type').val('');
                        // إعادة تحميل الصفحة لإظهار الرتبة/المنصب الجديد
                        setTimeout(function() {
                            location.reload();
                        }, 1500);
                    } else {
                        showNotification(response.message, 'error');
                    }
                }).fail(function() {
                    showNotification('حدث خطأ أثناء إضافة الرتبة/المنصب', 'error');
                });
            });

            // Edit role
            $(document).on('click', '.edit-role', function() {
                const roleName = $(this).data('role');
                $('#old_role').val(roleName);
                $('#new_role').val(roleName);
                $('#editRoleModal').modal('show');
            });

            // Save role edit
            $('#saveRoleEdit').click(function() {
                const oldRole = $('#old_role').val();
                const newRole = $('#new_role').val().trim();

                if (!newRole) {
                    showNotification('يرجى إدخال اسم الرتبة الجديد', 'error');
                    return;
                }

                $.post('', {
                    action: 'edit_role',
                    old_role: oldRole,
                    new_role: newRole,
                    csrfmiddlewaretoken: $('[name=csrfmiddlewaretoken]').val()
                }, function(response) {
                    if (response.success) {
                        showNotification(response.message, 'success');
                        $('#editRoleModal').modal('hide');
                        // Update role card
                        updateRoleCard(oldRole, newRole);
                    } else {
                        showNotification(response.message, 'error');
                    }
                });
            });

            // Delete role
            $(document).on('click', '.delete-role', function() {
                const roleName = $(this).data('role');

                if (confirm(`هل أنت متأكد من حذف الرتبة "${roleName}"؟`)) {
                    $.post('', {
                        action: 'delete_role',
                        role_name: roleName,
                        csrfmiddlewaretoken: $('[name=csrfmiddlewaretoken]').val()
                    }, function(response) {
                        if (response.success) {
                            showNotification(response.message, 'success');
                            // Remove role card
                            $(`.role-card[data-role="${roleName}"]`).fadeOut(300, function() {
                                $(this).remove();
                            });
                        } else {
                            showNotification(response.message, 'error');
                        }
                    });
                }
            });

            // Search functionality
            $('#rolesSearch').on('keyup', function() {
                const value = $(this).val().toLowerCase();
                $('.role-card').filter(function() {
                    $(this).toggle($(this).find('.role-name').text().toLowerCase().indexOf(value) > -1);
                });
            });

            // Back to top functionality
            $('#back-to-top').click(function(e) {
                e.preventDefault();
                $('html, body').animate({scrollTop: 0}, 'slow');
            });

            $(window).scroll(function() {
                if ($(this).scrollTop() > 100) {
                    $('#back-to-top').fadeIn();
                } else {
                    $('#back-to-top').fadeOut();
                }
            });
        });

        // Helper functions
        function addRoleCard(roleName) {
            const roleCard = `
                <div class="role-card" data-role="${roleName}" style="display: none;">
                    <div class="role-content">
                        <div class="role-name">${roleName}</div>
                        <div class="role-actions">
                            <button class="btn btn-sm btn-warning edit-role"
                                    data-role="${roleName}"
                                    title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger delete-role"
                                    data-role="${roleName}"
                                    title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
            $('.roles-grid').append(roleCard);
            $(`.role-card[data-role="${roleName}"]`).fadeIn(300);
        }

        function updateRoleCard(oldRole, newRole) {
            const roleCard = $(`.role-card[data-role="${oldRole}"]`);
            roleCard.attr('data-role', newRole);
            roleCard.find('.role-name').text(newRole);
            roleCard.find('.edit-role').attr('data-role', newRole);
            roleCard.find('.delete-role').attr('data-role', newRole);
        }

        function showNotification(message, type) {
            const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            const notification = `
                <div class="alert ${alertClass} alert-dismissible fade show notification" role="alert">
                    ${message}
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
            `;
            $('.home-container').prepend(notification);

            setTimeout(() => {
                $('.notification').alert('close');
            }, 3000);
        }
    </script>

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #ffffff;
        }

        /* Roles Details - Similar to unit-count-details */
        .roles-details {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-top: 30px;
            margin-bottom: 30px;
        }

        .roles-icon {
            font-size: 4rem;
            color: #6f42c1;
            margin-bottom: 20px;
            text-align: center;
        }

        .roles-title {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 10px;
            text-align: center;
            color: #1d3557;
        }

        .roles-subtitle {
            font-size: 1.1rem;
            text-align: center;
            color: #457b9d;
            margin-bottom: 20px;
        }

        /* Form styles */
        .roles-form {
            background-color: #f8f9fa;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-bottom: 30px;
        }

        .form-header {
            margin-bottom: 20px;
        }

        .form-header h3 {
            color: #1d3557;
            font-size: 1.4rem;
            margin: 0;
        }

        .form-row {
            display: flex;
            align-items: end;
            gap: 15px;
            flex-wrap: wrap;
        }

        .form-group {
            flex: 1;
            min-width: 200px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #1d3557;
        }

        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            font-size: 1rem;
        }

        .form-control:focus {
            border-color: #6f42c1;
            outline: none;
            box-shadow: 0 0 0 0.2rem rgba(111, 66, 193, 0.25);
        }

        /* Roles list section */
        .roles-list-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 30px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .section-header h3 {
            color: #1d3557;
            font-size: 1.4rem;
            margin: 0;
        }

        .header-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .search-input {
            min-width: 250px;
        }

        /* Roles grid */
        .roles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 15px;
        }

        .role-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s;
        }

        .role-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .role-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .role-name {
            font-weight: 600;
            color: #1d3557;
            font-size: 1rem;
        }

        .role-actions {
            display: flex;
            gap: 5px;
        }

        /* Button styles */
        .main-btn {
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: bold;
            font-size: 18px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            transition: all 0.3s;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
            border: none;
        }

        .main-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            text-decoration: none;
            color: white;
        }

        .add-btn {
            background-color: #6f42c1;
            color: white;
            min-width: 120px;
        }

        .add-btn:hover {
            background-color: #5a32a3;
        }

        .back-btn {
            background-color: #1d3557;
            color: white;
        }

        .back-btn:hover {
            background-color: #457b9d;
        }

        .home-btn {
            background-color: #28a745;
            color: white;
        }

        .home-btn:hover {
            background-color: #218838;
        }

        /* Main buttons container */
        .main-buttons-container {
            display: flex;
            justify-content: center;
            margin-top: 30px;
            margin-bottom: 20px;
        }

        .main-buttons {
            display: flex;
            gap: 20px;
            width: 100%;
            max-width: 600px;
        }

        /* Floating buttons */
        .floating-buttons {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .floating-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            transition: all 0.3s;
            text-decoration: none;
            font-size: 18px;
            border: none;
            cursor: pointer;
        }

        .floating-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
            text-decoration: none;
            color: white;
        }

        .floating-btn.back-btn {
            background-color: #1d3557;
        }

        .floating-btn.home-btn {
            background-color: #28a745;
        }

        .floating-btn.top-btn {
            background-color: #0d6efd;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
            }

            .form-group {
                min-width: 100%;
            }

            .section-header {
                flex-direction: column;
                align-items: stretch;
            }

            .search-input {
                min-width: 100%;
            }

            .roles-grid {
                grid-template-columns: 1fr;
            }

            .main-buttons {
                flex-direction: column;
                gap: 15px;
            }

            .floating-buttons {
                bottom: 10px;
                right: 10px;
            }

            .floating-btn {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }
        }
    </style>
