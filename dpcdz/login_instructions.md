# نظام تسجيل الدخول - تعليمات

## الإعداد

1. قم بإنشاء مستخدم مسؤول (admin) باستخدام الأمر التالي:

```
python create_superuser.py
```

أو استخدم الأمر المدمج في Django:

```
python manage.py createsuperuser
```

2. بعد إنشاء المستخدم المسؤول، يمكنك تسجيل الدخول إلى لوحة الإدارة من خلال:

```
http://your-domain/admin/
```

## إنشاء مستخدمين جدد

فقط المستخدم المسؤول (admin) يمكنه إنشاء مستخدمين جدد. لإنشاء مستخدم جديد:

1. قم بتسجيل الدخول إلى لوحة الإدارة باستخدام بيانات المستخدم المسؤول
2. انتقل إلى قسم "المستخدمين" (Users)
3. انقر على زر "إضافة مستخدم" (Add User)
4. أدخل اسم المستخدم وكلمة المرور
5. حدد الصلاحيات المناسبة للمستخدم

## استخدام النظام

1. عند زيارة الموقع، سيتم توجيهك أولاً إلى صفحة تسجيل الدخول
2. أدخل اسم المستخدم وكلمة المرور
3. بعد تسجيل الدخول بنجاح، سيتم توجيهك إلى الصفحة الرئيسية للنظام
4. يمكنك تسجيل الخروج في أي وقت بالنقر على زر "تسجيل الخروج" في أعلى الصفحة

## ملاحظات

- جميع صفحات النظام محمية وتتطلب تسجيل الدخول
- إذا حاول مستخدم غير مسجل الوصول إلى أي صفحة، سيتم توجيهه إلى صفحة تسجيل الدخول
- فقط المستخدم المسؤول (admin) يمكنه إنشاء مستخدمين جدد