import os
import re

# List of misc-operations sub-pages to update
misc_operations_subpages = [
    'templates/misc_operations/stats.html',
    'templates/misc_operations/security_device_stats.html',
    'templates/misc_operations/exceptional_operations_stats.html',
    'templates/misc_operations/interventions_without_work_stats.html',
]

# Function to update the head section of a template
def update_head_section(content):
    # Add sidebar.css to the head section
    if '<link rel="stylesheet" href="{% static \'css/sidebar.css\' %}">' not in content:
        content = content.replace(
            '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">',
            '<link rel="stylesheet" href="{% static \'css/sidebar.css\' %}">\n    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">'
        )
    return content

# Function to update the header section of a template
def update_header_section(content):
    # Update the logo to be clickable for the sidebar
    if 'class="sidebar-toggle"' not in content:
        content = re.sub(
            r'<div class="logo">\s*<img src="\{% static \'images/civil_protection_logo.png\' %\}" alt="شعار الحماية المدنية">',
            '<div class="logo">\n                <img src="{% static \'images/civil_protection_logo.png\' %}" alt="شعار الحماية المدنية" class="sidebar-toggle" id="sidebar-toggle" style="cursor: pointer;">',
            content
        )
    
    # Add the sidebar include after the header
    if '{% include \'includes/sidebar.html\' %}' not in content:
        content = re.sub(
            r'</header>',
            '</header>\n\n    {% include \'includes/sidebar.html\' %}',
            content
        )
    
    return content

# Function to add the sidebar.js script before the closing body tag
def add_sidebar_js(content):
    if '<script src="{% static \'js/sidebar.js\' %}"></script>' not in content:
        content = re.sub(
            r'</body>',
            '    <script src="{% static \'js/sidebar.js\' %}"></script>\n</body>',
            content
        )
    return content

# Main function to update a template
def update_template(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        content = file.read()
    
    # Update the template
    content = update_head_section(content)
    content = update_header_section(content)
    content = add_sidebar_js(content)
    
    # Write the updated content back to the file
    with open(file_path, 'w', encoding='utf-8') as file:
        file.write(content)
    
    print(f"Updated {file_path}")

# Update all misc-operations sub-pages
for file_path in misc_operations_subpages:
    if os.path.exists(file_path):
        update_template(file_path)
    else:
        print(f"File not found: {file_path}")
