#!/usr/bin/env python
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dpcdz.settings')
django.setup()

from home.models import CoordinationCenterCropFire, CoordinationCenterForestFire
from datetime import datetime

def test_crop_fires():
    print("Testing Crop Fires Model...")
    
    # Check if models exist
    print(f"CoordinationCenterCropFire model: {CoordinationCenterCropFire}")
    print(f"CoordinationCenterForestFire model: {CoordinationCenterForestFire}")
    
    # Check existing data
    crop_fires_count = CoordinationCenterCropFire.objects.count()
    forest_fires_count = CoordinationCenterForestFire.objects.count()
    
    print(f"Existing crop fires: {crop_fires_count}")
    print(f"Existing forest fires: {forest_fires_count}")
    
    # List all crop fires
    if crop_fires_count > 0:
        print("\nExisting crop fires:")
        for fire in CoordinationCenterCropFire.objects.all():
            print(f"  ID: {fire.id}, Date: {fire.date}, Unit: {fire.intervening_unit}")
    
    # Try to create a test crop fire
    try:
        test_fire = CoordinationCenterCropFire.objects.create(
            telegram_number=999,
            date=datetime.now().date(),
            intervention_time="10 سا 30 د",
            intervening_unit="وحدة تجريبية",
            municipality="بلدية تجريبية",
            location_name="مكان تجريبي",
            operation_duration="2 سا 15 د",
            intervention_means="وسائل تجريبية",
            loss_nature="خسائر تجريبية",
            losses_hectare=1.5,
            losses_are=50,
            losses_square_meter=1000,
            other_loss_nature="خسائر أخرى",
            other_loss_count=5,
            fire_control_status="أخمد نهائيا",
            injured_count=0,
            deaths_count=0,
            evacuated_families_count=2,
            evacuated_people_count=8,
            evacuation_locations="مراكز الإيواء",
            family_care_measures="إجراءات الرعاية"
        )
        print(f"\nTest crop fire created successfully with ID: {test_fire.id}")
        
        # Delete the test fire
        test_fire.delete()
        print("Test crop fire deleted successfully")
        
    except Exception as e:
        print(f"Error creating test crop fire: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_crop_fires()
