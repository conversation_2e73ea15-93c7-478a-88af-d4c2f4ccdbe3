from django.shortcuts import render, redirect
from django.contrib.auth.decorators import user_passes_test
from .models import UserProfile, WILAYA_CHOICES

# صلاحيات المستخدمين
def is_admin(user):
    return user.is_authenticated and hasattr(user, 'userprofile') and user.userprofile.role == 'admin'

def is_wilaya_manager(user):
    return user.is_authenticated and hasattr(user, 'userprofile') and user.userprofile.role == 'wilaya_manager'

def is_unit_manager(user):
    return user.is_authenticated and hasattr(user, 'userprofile') and user.userprofile.role == 'unit_manager'

from django.http import JsonResponse
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.auth.models import User
from django.contrib import messages
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils import timezone
import json
from datetime import datetime
from .models import UserProfile, InterventionUnit, UnitPersonnel, PersonnelTransfer, UnitEquipment, EightHourPersonnel
from .forms import UserCreationFormWithWilaya, InterventionUnitForm

def root_view(request):
    # If user is authenticated, redirect to home page
    if request.user.is_authenticated:
        return redirect('home')
    # Otherwise redirect to login page
    return redirect('login')
import os
import json
import datetime
from datetime import datetime
from django.utils import timezone
from .models import (
    GeneralFireData, ResidentialFireData, PublicAreaFireData,
    ForestAgriculturalFireData, MiscOperationsData, InstitutionalFireData,
    TrafficAccident, InterventionUnit, CoordinationCenterForestFire, CoordinationCenterCropFire,
    # Morning Check System Models
    WorkShift, ShiftPersonnel, DailyShiftSchedule, EightHourPersonnel,
    ReadinessAlert, MorningCheckSummary, UnitPersonnel, UnitEquipment,
    DailyPersonnelStatus, DailyEquipmentStatus, VehicleCrewAssignment, VehicleReadiness,
    DailyUnitCount
)
from data_entry.models import MedicalEvacuation, InterventionType, InterventionNature

def login_view(request):
    if request.user.is_authenticated:
        return redirect('home')

    if request.method == 'POST':
        # Clear any existing session
        request.session.flush()
        # Add debug logging
        print("Login attempt received")
        username = request.POST.get('username')
        password = request.POST.get('password')

        # Authenticate user
        user = authenticate(request, username=username, password=password)

        if user is not None:
            # Log in the user
            login(request, user)

            # Redirect to home page
            return redirect('home')
        else:
            # Return error message for invalid credentials
            return render(request, 'login.html', {'error_message': 'اسم المستخدم أو كلمة المرور غير صحيحة'})

    # For GET requests, render the login form
    return render(request, 'login.html')

def logout_view(request):
    logout(request)
    return redirect('login')

@login_required(login_url='login')
def profile_view(request):
    return render(request, 'profile.html')

@login_required(login_url='login')
def home_view(request):
    return render(request, 'home/index.html')

@csrf_exempt
@login_required(login_url='login')
def medical_evacuation_view(request):
    # Get the user's profile
    user = request.user
    is_admin = user.is_superuser or user.is_staff
    user_wilaya = user.userprofile.wilaya

    # Get intervention units - admins can see all units, regular users only see their wilaya's units
    if is_admin:
        units = InterventionUnit.objects.all().order_by('wilaya', 'name')
    else:
        units = InterventionUnit.objects.filter(wilaya=user_wilaya).order_by('name')

    if request.method == 'POST':
        # Check if it's a JSON request (AJAX)
        if request.content_type == 'application/json':
            try:
                # Parse JSON data
                data = json.loads(request.body)

                # Format date
                date_str = data.get('date')
                from datetime import datetime
                formatted_date = datetime.strptime(date_str, '%Y-%m-%d').date()

                # Get or create intervention type
                from data_entry.models import InterventionType, InterventionNature, MedicalEvacuation
                from django.db import transaction

                try:
                    with transaction.atomic():
                        # First try to get existing intervention type
                        try:
                            intervention_type = InterventionType.objects.get(
                                name=data.get('intervention_type')
                            )
                        except InterventionType.DoesNotExist:
                            # If it doesn't exist, create it
                            intervention_type = InterventionType.objects.create(
                                name=data.get('intervention_type')
                            )

                        # First try to get existing intervention nature
                        try:
                            intervention_nature = InterventionNature.objects.get(
                                name=data.get('intervention_nature'),
                                intervention_type=intervention_type
                            )
                        except InterventionNature.DoesNotExist:
                            # If it doesn't exist, create it
                            intervention_nature = InterventionNature.objects.create(
                                name=data.get('intervention_nature'),
                                intervention_type=intervention_type
                            )

                        # Get the unit
                        unit = InterventionUnit.objects.get(name=data.get('unit'))

                        # Check if detailed fields should be used
                        detailed_intervention_types = ['الاختناق', 'التسممات', 'الحروق', 'الانفجارات', 'إجلاء المرضى', 'الغرقى']
                        use_detailed_fields = data.get('intervention_type') in detailed_intervention_types

                        # Create MedicalEvacuation object
                        medical_evacuation = MedicalEvacuation.objects.create(
                            date=formatted_date,
                            unit=unit,
                            intervention_type=intervention_type,
                            intervention_nature=intervention_nature,
                            is_indoor=data.get('location') == 'indoor',
                            operations_count=int(data.get('detailed_operations_count' if use_detailed_fields else 'operations_count', 0)),
                            interventions_count=int(data.get('detailed_interventions_count' if use_detailed_fields else 'interventions_count', 0)),
                            # Detailed fields for all intervention types
                            paramedics_children_count=int(data.get('paramedics_children_count', 0)) if use_detailed_fields else 0,
                            paramedics_women_count=int(data.get('paramedics_women_count', 0)) if use_detailed_fields else 0,
                            paramedics_men_count=int(data.get('paramedics_men_count', 0)) if use_detailed_fields else 0,
                            deaths_children_count=int(data.get('deaths_children_count', 0)) if use_detailed_fields else 0,
                            deaths_women_count=int(data.get('deaths_women_count', 0)) if use_detailed_fields else 0,
                            deaths_men_count=int(data.get('deaths_men_count', 0)) if use_detailed_fields else 0
                        )

                        # Save to Excel
                        excel_path = os.path.join('media', 'medical_evacuation_data.xlsx')

                        # Create data dictionary for Excel
                        excel_data = {
                            'التاريخ': date_str,
                            'الوحدة المتدخلة': data.get('unit'),
                            'نوع التدخل': data.get('intervention_type'),
                            'طبيعة التدخل': data.get('intervention_nature'),
                            'داخل المنزل': 1 if data.get('location') == 'indoor' else 0,
                            'خارج المنزل': 1 if data.get('location') == 'outdoor' else 0,
                            'عدد العمليات': int(data.get('detailed_operations_count' if use_detailed_fields else 'operations_count', 0)),
                            'عدد التدخلات': int(data.get('detailed_interventions_count' if use_detailed_fields else 'interventions_count', 0)),
                        }

                        # Add detailed fields to Excel data if using detailed intervention types
                        if use_detailed_fields:
                            excel_data.update({
                                'عدد المسعفين أطفال': int(data.get('paramedics_children_count', 0)),
                                'عدد المسعفين نساء': int(data.get('paramedics_women_count', 0)),
                                'عدد المسعفين رجال': int(data.get('paramedics_men_count', 0)),
                                'مجموع المسعفين التفصيلي': int(data.get('paramedics_children_count', 0)) + int(data.get('paramedics_women_count', 0)) + int(data.get('paramedics_men_count', 0)),
                                'عدد الوفيات أطفال': int(data.get('deaths_children_count', 0)),
                                'عدد الوفيات نساء': int(data.get('deaths_women_count', 0)),
                                'عدد الوفيات رجال': int(data.get('deaths_men_count', 0)),
                                'مجموع الوفيات التفصيلي': int(data.get('deaths_children_count', 0)) + int(data.get('deaths_women_count', 0)) + int(data.get('deaths_men_count', 0))
                            })

                        # Use the utility function to save to Excel
                        from .utils import save_to_excel
                        # Pass the user and form type to organize files by wilaya
                        success, message = save_to_excel(excel_path, excel_data, user=request.user, form_type='medical_evacuation')
                except Exception as e:
                    return JsonResponse({'status': 'error', 'message': f'حدث خطأ أثناء حفظ البيانات: {str(e)}'})

                if success:
                    return JsonResponse({'status': 'success', 'message': 'تم حفظ البيانات بنجاح'})
                else:
                    # If Excel save failed but database save succeeded, return partial success
                    return JsonResponse({
                        'status': 'partial_success',
                        'message': f'تم حفظ البيانات في قاعدة البيانات ولكن حدثت مشكلة في حفظ ملف Excel: {message}'
                    })

            except Exception as e:
                return JsonResponse({'status': 'error', 'message': f'حدث خطأ: {str(e)}'})
        else:
            # Regular form submission
            date_str = request.POST.get('date')
            unit_name = request.POST.get('unit')
            intervention_type_name = request.POST.get('intervention_type')
            intervention_nature_name = request.POST.get('intervention_nature')
            location = request.POST.get('location')

            # Check if detailed fields should be used
            detailed_intervention_types = ['الاختناق', 'التسممات', 'الحروق', 'الانفجارات', 'إجلاء المرضى', 'الغرقى']
            use_detailed_fields = intervention_type_name in detailed_intervention_types

            operations_count = int(request.POST.get('detailed_operations_count' if use_detailed_fields else 'operations_count', 0))
            interventions_count = int(request.POST.get('detailed_interventions_count' if use_detailed_fields else 'interventions_count', 0))

            # Detailed fields for all intervention types
            paramedics_children_count = int(request.POST.get('paramedics_children_count', 0)) if use_detailed_fields else 0
            paramedics_women_count = int(request.POST.get('paramedics_women_count', 0)) if use_detailed_fields else 0
            paramedics_men_count = int(request.POST.get('paramedics_men_count', 0)) if use_detailed_fields else 0
            deaths_children_count = int(request.POST.get('deaths_children_count', 0)) if use_detailed_fields else 0
            deaths_women_count = int(request.POST.get('deaths_women_count', 0)) if use_detailed_fields else 0
            deaths_men_count = int(request.POST.get('deaths_men_count', 0)) if use_detailed_fields else 0

            # Format date
            from datetime import datetime
            formatted_date = datetime.strptime(date_str, '%Y-%m-%d').date()

            # Get or create intervention type and nature
            from data_entry.models import InterventionType, InterventionNature, MedicalEvacuation
            from django.db import transaction

            try:
                with transaction.atomic():
                    # First try to get existing intervention type
                    try:
                        intervention_type = InterventionType.objects.get(
                            name=intervention_type_name
                        )
                    except InterventionType.DoesNotExist:
                        # If it doesn't exist, create it
                        intervention_type = InterventionType.objects.create(
                            name=intervention_type_name
                        )

                    # First try to get existing intervention nature
                    try:
                        intervention_nature = InterventionNature.objects.get(
                            name=intervention_nature_name,
                            intervention_type=intervention_type
                        )
                    except InterventionNature.DoesNotExist:
                        # If it doesn't exist, create it
                        intervention_nature = InterventionNature.objects.create(
                            name=intervention_nature_name,
                            intervention_type=intervention_type
                        )

                    # Get the unit
                    unit = InterventionUnit.objects.get(name=unit_name)

                    # Create MedicalEvacuation object
                    medical_evacuation = MedicalEvacuation.objects.create(
                        date=formatted_date,
                        unit=unit,
                        intervention_type=intervention_type,
                        intervention_nature=intervention_nature,
                        is_indoor=location == 'indoor',
                        operations_count=operations_count,
                        interventions_count=interventions_count,
                        # Detailed fields for all intervention types
                        paramedics_children_count=paramedics_children_count,
                        paramedics_women_count=paramedics_women_count,
                        paramedics_men_count=paramedics_men_count,
                        deaths_children_count=deaths_children_count,
                        deaths_women_count=deaths_women_count,
                        deaths_men_count=deaths_men_count
                    )

                    # Show success message
                    from django.contrib import messages
                    messages.success(request, 'تم حفظ بيانات الإجلاء الصحي بنجاح')

            except Exception as e:
                # Show error message
                from django.contrib import messages
                messages.error(request, f'حدث خطأ أثناء حفظ البيانات: {str(e)}')

            # Redirect to the same page to avoid form resubmission
            from django.shortcuts import redirect
            return redirect('medical_evacuation')

    context = {
        'units': units,
        'is_admin': is_admin
    }

    return render(request, 'medical_evacuation/index.html', context)

@login_required(login_url='login')
def tables_dashboard_view(request):
    return render(request, 'tables_dashboard.html')

@login_required(login_url='login')
def data_statistics_view(request):
    """Clean implementation of data statistics view"""
    try:
        from .statistics_service import StatisticsService

        # Get user information
        user = request.user
        is_admin = user.is_superuser or user.is_staff

        # Check if user has profile
        if not hasattr(user, 'userprofile'):
            messages.error(request, 'لم يتم العثور على ملف تعريف المستخدم')
            return redirect('home')

        # Initialize statistics service
        stats_service = StatisticsService(user, is_admin)

        # Get filter parameters from request
        filters = {
            'table_type': request.GET.get('table_type', ''),
            'year': request.GET.get('year', ''),
            'month': request.GET.get('month', ''),
            'wilaya': request.GET.get('wilaya', ''),
            'unit': request.GET.get('unit', ''),
            'intervention_type': request.GET.get('intervention_type', ''),
            'intervention_nature': request.GET.get('intervention_nature', ''),
            'accident_type': request.GET.get('accident_type', ''),
            'accident_nature': request.GET.get('accident_nature', ''),
            'fire_type': request.GET.get('fire_type', ''),
            'operation_type': request.GET.get('operation_type', ''),
        }

        # Get filter options for dropdowns
        user_units = stats_service.get_user_units()
        intervention_types = InterventionType.objects.all()
        intervention_natures = InterventionNature.objects.all()

        # Get unique values for filters from database
        accident_types = list(TrafficAccident.objects.values_list('accident_type', flat=True).distinct().exclude(accident_type__isnull=True).exclude(accident_type=''))
        accident_natures = list(TrafficAccident.objects.values_list('accident_nature', flat=True).distinct().exclude(accident_nature__isnull=True).exclude(accident_nature=''))
        fire_types = list(GeneralFireData.objects.values_list('fire_type', flat=True).distinct().exclude(fire_type__isnull=True).exclude(fire_type=''))
        operation_types = list(MiscOperationsData.objects.values_list('operation_type', flat=True).distinct().exclude(operation_type__isnull=True).exclude(operation_type=''))

        # Generate statistics if table type is selected
        dynamic_data = {}
        if filters['table_type']:
            print(f"Generating statistics for table type: {filters['table_type']}")
            dynamic_data[filters['table_type']] = stats_service.get_statistics_for_table_type(
                filters['table_type'], filters
            )
            print(f"Generated statistics: {dynamic_data[filters['table_type']]}")

        # Get common filter data
        from .utils import ALGERIAN_ARABIC_MONTHS
        months = list(ALGERIAN_ARABIC_MONTHS.items())
        months.sort(key=lambda x: int(x[0]))

        current_year = datetime.datetime.now().year
        years = list(range(current_year, 2019, -1))

        # Get wilaya choices for admin users
        wilaya_choices = []
        if is_admin:
            from .models import WILAYA_CHOICES
            wilaya_choices = WILAYA_CHOICES

        context = {
            'is_admin': is_admin,
            'user_units': user_units,
            'dynamic_data': json.dumps(dynamic_data),
            'months': months,
            'years': years,
            'wilaya_choices': wilaya_choices,
            # Current filter values
            'filter_table_type': filters['table_type'],
            'filter_year': filters['year'],
            'filter_month': filters['month'],
            'filter_wilaya': filters['wilaya'],
            'filter_unit': filters['unit'],
            'filter_intervention_type': filters['intervention_type'],
            'filter_intervention_nature': filters['intervention_nature'],
            'filter_accident_type': filters['accident_type'],
            'filter_accident_nature': filters['accident_nature'],
            'filter_fire_type': filters['fire_type'],
            'filter_operation_type': filters['operation_type'],
            # Filter options for dropdowns
            'intervention_types': intervention_types,
            'intervention_natures': intervention_natures,
            'accident_types': accident_types,
            'accident_natures': accident_natures,
            'fire_types': fire_types,
            'operation_types': operation_types,
        }

        return render(request, 'data_statistics.html', context)

    except Exception as e:
        print(f"Error in data_statistics_view: {e}")
        import traceback
        traceback.print_exc()
        messages.error(request, f'حدث خطأ في تحميل صفحة الإحصائيات: {str(e)}')
        return redirect('home')


# Old statistics functions removed - now using StatisticsService


# Removed old get_medical_evacuation_statistics function



# Removed old get_traffic_accident_statistics function


# All old statistics functions removed - now using StatisticsService


@login_required(login_url='login')
def export_statistics_pdf(request):
    """Export statistics data as HTML for PDF printing"""
    try:
        from django.template.loader import render_to_string
        from django.http import HttpResponse
        from .statistics_service import StatisticsService

        # Get filter parameters
        filters = {
            'table_type': request.GET.get('table_type', ''),
            'year': request.GET.get('year', ''),
            'month': request.GET.get('month', ''),
            'wilaya': request.GET.get('wilaya', ''),
            'unit': request.GET.get('unit', ''),
            'intervention_type': request.GET.get('intervention_type', ''),
            'intervention_nature': request.GET.get('intervention_nature', ''),
            'accident_type': request.GET.get('accident_type', ''),
            'accident_nature': request.GET.get('accident_nature', ''),
            'fire_type': request.GET.get('fire_type', ''),
            'operation_type': request.GET.get('operation_type', ''),
        }

        # Get user info
        user = request.user
        is_admin = user.is_superuser or user.is_staff

        # Initialize statistics service
        stats_service = StatisticsService(user, is_admin)

        # Get statistics data
        dynamic_data = {}
        if filters['table_type']:
            dynamic_data[filters['table_type']] = stats_service.get_statistics_for_table_type(
                filters['table_type'], filters
            )

        # Map table types to Arabic names
        table_names = {
            'medical_evacuation': 'الإجلاء الصحي',
            'traffic_accidents': 'حوادث المرور',
            'general_fire': 'الجدول العام للحرائق',
            'residential_fires': 'الحرائق في البنايات المخصصة للسكن',
            'institutional_fires': 'الحرائق في المؤسسات المصنفة',
            'public_area_fires': 'الحرائق في الأماكن المستقبِلة للجمهور',
            'forest_agricultural_fires': 'حرائق الغابات و المحاصيل',
            'misc_operations': 'العمليات المختلفة',
            'security_device': 'الجهاز الأمني',
            'exceptional_operations': 'العمليات الاستثنائية',
            'interventions_without_work': 'التدخلات بدون عمل'
        }

        # Prepare context for template
        context = {
            'dynamic_data': dynamic_data,
            'filters': filters,
            'table_names': table_names,
            'is_admin': is_admin,
            'is_pdf_export': True,  # Flag to indicate this is for PDF export
        }

        # Add month name if available
        if filters['month']:
            from .utils import ALGERIAN_ARABIC_MONTHS
            context['month_name'] = ALGERIAN_ARABIC_MONTHS.get(filters['month'], filters['month'])

        # Render HTML template for PDF
        html_string = render_to_string('data_statistics_pdf.html', context)

        # Return HTML response that can be printed to PDF
        response = HttpResponse(html_string, content_type='text/html')
        return response

    except Exception as e:
        print(f"Error in export_statistics_pdf: {e}")
        # Fallback to basic PDF if there's an error
        return export_statistics_pdf_basic(request)


def export_statistics_pdf_basic(request):
    """Fallback basic PDF export using ReportLab"""
    try:
        from django.http import HttpResponse
        from reportlab.lib.pagesizes import A4, landscape
        from reportlab.lib import colors
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
        from reportlab.lib.units import inch
        import io
        from .statistics_service import StatisticsService

        # Get filter parameters
        filters = {
            'table_type': request.GET.get('table_type', ''),
            'year': request.GET.get('year', ''),
            'month': request.GET.get('month', ''),
            'wilaya': request.GET.get('wilaya', ''),
            'unit': request.GET.get('unit', ''),
            'intervention_type': request.GET.get('intervention_type', ''),
            'intervention_nature': request.GET.get('intervention_nature', ''),
            'accident_type': request.GET.get('accident_type', ''),
            'accident_nature': request.GET.get('accident_nature', ''),
            'fire_type': request.GET.get('fire_type', ''),
            'operation_type': request.GET.get('operation_type', ''),
        }

        # Get user info
        user = request.user
        is_admin = user.is_superuser or user.is_staff

        # Initialize statistics service
        stats_service = StatisticsService(user, is_admin)

        # Get statistics data
        dynamic_data = {}
        if filters['table_type']:
            dynamic_data[filters['table_type']] = stats_service.get_statistics_for_table_type(
                filters['table_type'], filters
            )

        # Create PDF
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=landscape(A4), rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)

        # Container for the 'Flowable' objects
        elements = []

        # Define styles
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=1,  # Center alignment
        )

        # Add title
        title_text = "Data Statistics Report"
        if filters['table_type']:
            table_names = {
                'medical_evacuation': 'Medical Evacuation',
                'traffic_accidents': 'Traffic Accidents',
                'general_fire': 'General Fire Data',
                'residential_fires': 'Residential Fires',
                'institutional_fires': 'Institutional Fires',
                'public_area_fires': 'Public Area Fires',
                'forest_agricultural_fires': 'Forest Agricultural Fires',
                'misc_operations': 'Miscellaneous Operations',
                'security_device': 'Security Device',
                'exceptional_operations': 'Exceptional Operations',
                'interventions_without_work': 'Interventions Without Work'
            }
            table_name = table_names.get(filters['table_type'], filters['table_type'])
            title_text += f" - {table_name}"

        if filters['year']:
            title_text += f" - {filters['year']}"

        title = Paragraph(title_text, title_style)
        elements.append(title)
        elements.append(Spacer(1, 12))

        # Add data tables
        if dynamic_data and filters['table_type'] in dynamic_data:
            data = dynamic_data[filters['table_type']]

            # Create main statistics table
            main_stats = [['Indicator', 'Value']]

            # Add main statistics
            main_fields = [
                ('total_operations', 'Total Operations'),
                ('total_interventions', 'Total Interventions'),
                ('total_fires', 'Total Fires'),
                ('total_accidents', 'Total Accidents'),
                ('total_paramedics', 'Total Paramedics'),
                ('total_injuries', 'Total Injuries'),
                ('total_deaths', 'Total Deaths'),
                ('total_records', 'Total Records')
            ]

            for field, label in main_fields:
                if field in data and data[field] is not None:
                    main_stats.append([label, str(data[field])])

            if len(main_stats) > 1:
                main_table = Table(main_stats, colWidths=[4*inch, 2*inch])
                main_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 14),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.lightblue),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('FONTSIZE', (0, 1), (-1, -1), 12),
                ]))
                elements.append(main_table)

        else:
            no_data = Paragraph("No data available for export", styles['Normal'])
            elements.append(no_data)

        # Build PDF
        doc.build(elements)

        # Get the value of the BytesIO buffer and write it to the response
        pdf = buffer.getvalue()
        buffer.close()

        response = HttpResponse(content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="statistics_{filters["table_type"]}_{filters["year"]}.pdf"'
        response.write(pdf)

        return response

    except Exception as e:
        print(f"Error in export_statistics_pdf_basic: {e}")
        from django.http import HttpResponse
        response = HttpResponse("Error generating PDF", content_type='text/plain')
        return response


@login_required(login_url='login')
def debug_medical_evacuation_view(request):
    """Debug view to check medical evacuation data"""
    from data_entry.models import InterventionType, InterventionNature, MedicalEvacuation
    from django.http import HttpResponse

    # Get all intervention types
    types = InterventionType.objects.all()

    # Get all intervention natures
    natures = InterventionNature.objects.all()

    # Get all medical evacuations
    evacuations = MedicalEvacuation.objects.all()

    # Build response
    response = "<h1>Debug Medical Evacuation Data</h1>"

    response += "<h2>Intervention Types</h2>"
    response += "<ul>"
    for t in types:
        response += f"<li>ID: {t.id}, Name: {t.name}</li>"
    response += "</ul>"

    response += "<h2>Intervention Natures</h2>"
    response += "<ul>"
    for n in natures:
        response += f"<li>ID: {n.id}, Name: {n.name}, Type: {n.intervention_type.name} (ID: {n.intervention_type_id})</li>"
    response += "</ul>"

    response += "<h2>Medical Evacuations</h2>"
    response += "<ul>"
    for e in evacuations:
        response += f"<li>ID: {e.id}, Date: {e.date}, Type: {e.intervention_type.name} (ID: {e.intervention_type_id}), Nature: {e.intervention_nature.name} (ID: {e.intervention_nature_id})</li>"
    response += "</ul>"

    return HttpResponse(response)

@login_required(login_url='login')
def table_general_fire_view(request):
    # Get the user's profile
    user = request.user
    is_admin = user.is_superuser or user.is_staff
    user_wilaya = user.userprofile.wilaya

    # Get intervention units - admins can see all units, regular users only see their wilaya's units
    if is_admin:
        user_units = InterventionUnit.objects.all().order_by('wilaya', 'name')
    else:
        user_units = InterventionUnit.objects.filter(wilaya=user_wilaya).order_by('name')

    # Get unique fire types from the database
    fire_types = GeneralFireData.objects.values_list('fire_type', flat=True).distinct()

    # Get data from general fire data model
    if is_admin:
        general_fire_data = GeneralFireData.objects.all().order_by('-date')
    else:
        general_fire_data = GeneralFireData.objects.filter(
            intervening_unit__in=user_units.values_list('name', flat=True)
        ).order_by('-date')

    # Format dates in Algerian Arabic format and add wilaya information for admin users
    from .utils import format_date_algerian_arabic
    for item in general_fire_data:
        item.formatted_date = format_date_algerian_arabic(item.date)

        # Add wilaya information for admin users
        if is_admin:
            # Get the unit's wilaya
            try:
                unit = InterventionUnit.objects.get(name=item.intervening_unit)
                item.wilaya_code = unit.wilaya
                # Get the wilaya name from the choices
                from .models import WILAYA_CHOICES
                item.wilaya_name = dict(WILAYA_CHOICES).get(unit.wilaya, 'غير محدد')
            except InterventionUnit.DoesNotExist:
                item.wilaya_code = '00'
                item.wilaya_name = 'غير محدد'

    # Get unique months for filtering
    from .utils import ALGERIAN_ARABIC_MONTHS
    months = list(ALGERIAN_ARABIC_MONTHS.items())
    months.sort(key=lambda x: int(x[0]))  # Sort by month number

    # Get unique years for filtering
    years = general_fire_data.dates('date', 'year')
    years = [year.year for year in years]
    years.sort(reverse=True)  # Sort years in descending order

    # Get wilaya choices for filtering (for admin users)
    from .models import WILAYA_CHOICES
    wilaya_choices = list(WILAYA_CHOICES)
    wilaya_choices.sort(key=lambda x: int(x[0]))  # Sort by wilaya code

    context = {
        'is_admin': is_admin,
        'general_fire_data': general_fire_data,
        'user_units': user_units,
        'fire_types': fire_types,
        'months': months,
        'years': years,
        'wilaya_choices': wilaya_choices
    }

    return render(request, 'tables/general_fire_table.html', context)

@login_required(login_url='login')
def table_medical_evacuation_view(request):
    # Get the user's profile
    user = request.user
    is_admin = user.is_superuser or user.is_staff
    user_wilaya = user.userprofile.wilaya

    # Get intervention units - admins can see all units, regular users only see their wilaya's units
    if is_admin:
        user_units = InterventionUnit.objects.all().order_by('wilaya', 'name')
    else:
        user_units = InterventionUnit.objects.filter(wilaya=user_wilaya).order_by('name')

    # Define intervention types
    intervention_types = [
        'الاختناق',
        'التسممات',
        'الحروق',
        'الانفجارات',
        'إجلاء المرضى',
        'الغرقى'
    ]

    # Define intervention natures by type
    intervention_natures_by_type = {
        'الاختناق': [
            'بالغاز الطبيعي أو البوتان',
            'بغاز أحادي أكسيد الكربون',
            'بإنسداد المجاري التنفسية',
            'بالأماكن المغلقة'
        ],
        'التسممات': [
            'بمواد غذائية',
            'بالأدوية',
            'بمواد التنظيف',
            'بلسعات/عضات حيوانات',
            'أخرى'
        ],
        'الحروق': [
            'ألسنة النار',
            'مواد سائلة ساخنة',
            'مواد كيميائية/مشعة',
            'صعقات كهربائية'
        ],
        'الانفجارات': [
            'غاز البوتان/البروبان',
            'الغاز الطبيعي',
            'الأجهزة الكهرومنزلية',
            'أجهزة التدفئة',
            'أخرى'
        ],
        'إجلاء المرضى': [
            'إجلاء الجرحى',
            'إجلاء الإختناقات',
            'إجلاء التسممات',
            'إجلاء الحروق',
            'إجلاء الإنفجارات',
            'إجلاء السقوط',
            'إجلاء الشنق',
            'إجلاء المرضى'
        ],
        'الغرقى': [
            'الغرق في المسطحات المائية',
            'الغرق في السدود',
            'الغرق في الأودية',
            'الغرق في الشواطئ',
            'الغرق في أماكن أخرى'
        ]
    }

    # Get all intervention natures
    intervention_natures = []
    for natures in intervention_natures_by_type.values():
        intervention_natures.extend(natures)

    # Get medical evacuation data from database
    from django.db import connection

    # Base SQL query
    sql = """
        SELECT
            me.id,
            me.date,
            CASE
                WHEN me.is_indoor = true THEN 'داخل المنزل'
                ELSE 'خارج المنزل'
            END as location,
            me.operations_count,
            me.interventions_count,
            me.paramedics_children_count,
            me.paramedics_women_count,
            me.paramedics_men_count,
            me.deaths_children_count,
            me.deaths_women_count,
            me.deaths_men_count,
            it.name as intervention_type,
            inn.name as intervention_nature,
            iu.name as unit_name,
            iu.wilaya
        FROM
            data_entry_medicalevacuation me
        INNER JOIN
            home_interventionunit iu ON me.unit_id = iu.id
        INNER JOIN
            data_entry_interventiontype it ON me.intervention_type_id = it.id
        INNER JOIN
            data_entry_interventionnature inn ON me.intervention_nature_id = inn.id
        WHERE 1=1
    """

    # Apply user permission filter
    params = []
    if not is_admin:
        sql += " AND iu.wilaya = %s"
        params.append(user_wilaya)

    # Order by date descending
    sql += " ORDER BY me.date DESC"

    # Execute the query
    with connection.cursor() as cursor:
        cursor.execute(sql, params)
        columns = [col[0] for col in cursor.description]
        results = cursor.fetchall()

    # Format the results
    medical_evacuation_data = []
    from .utils import format_date_algerian_arabic

    for row in results:
        result_dict = dict(zip(columns, row))

        # Format date for display
        date_obj = result_dict['date']
        formatted_date = format_date_algerian_arabic(date_obj)

        # Create data object
        data_obj = type('MedicalEvacuationData', (), {
            'id': result_dict['id'],
            'formatted_date': formatted_date,
            'unit': result_dict['unit_name'],
            'intervention_type': result_dict['intervention_type'],
            'intervention_nature': result_dict['intervention_nature'],
            'location': result_dict['location'],
            'operations_count': result_dict['operations_count'],
            'interventions_count': result_dict['interventions_count'],
            # Detailed fields for all intervention types
            'paramedics_children_count': result_dict['paramedics_children_count'],
            'paramedics_women_count': result_dict['paramedics_women_count'],
            'paramedics_men_count': result_dict['paramedics_men_count'],
            'deaths_children_count': result_dict['deaths_children_count'],
            'deaths_women_count': result_dict['deaths_women_count'],
            'deaths_men_count': result_dict['deaths_men_count'],
            # Calculated totals
            'total_paramedics_detailed_count': result_dict['paramedics_children_count'] + result_dict['paramedics_women_count'] + result_dict['paramedics_men_count'],
            'total_deaths_detailed_count': result_dict['deaths_children_count'] + result_dict['deaths_women_count'] + result_dict['deaths_men_count'],
        })

        # Add wilaya information for admin users
        if is_admin:
            # Get wilaya name from code
            wilaya_code = result_dict['wilaya']
            from .models import WILAYA_CHOICES
            data_obj.wilaya_name = dict(WILAYA_CHOICES).get(wilaya_code, 'غير محدد')

        medical_evacuation_data.append(data_obj)

    # Get months for date filtering
    from .utils import ALGERIAN_ARABIC_MONTHS
    months = list(ALGERIAN_ARABIC_MONTHS.items())
    months.sort(key=lambda x: int(x[0]))  # Sort by month number

    # Get years from data or current year down to 2020 if no data
    years_from_data = set()
    for item in medical_evacuation_data:
        date_parts = item.formatted_date.split(' ')
        if len(date_parts) >= 3:
            years_from_data.add(date_parts[2])  # Year is the third part

    if years_from_data:
        years = sorted(list(years_from_data), reverse=True)
    else:
        import datetime
        current_year = datetime.datetime.now().year
        years = list(range(current_year, 2019, -1))

    # Get wilaya choices for admin users
    wilaya_choices = []
    if is_admin:
        from .models import WILAYA_CHOICES
        wilaya_choices = WILAYA_CHOICES

    context = {
        'is_admin': is_admin,
        'user_units': user_units,
        'intervention_types': intervention_types,
        'intervention_natures': intervention_natures,
        'intervention_natures_by_type': intervention_natures_by_type,
        'medical_evacuation_data': medical_evacuation_data,
        'months': months,
        'years': years,
        'wilaya_choices': wilaya_choices
    }

    return render(request, 'tables/medical_evacuation_table.html', context)

@login_required(login_url='login')
def get_traffic_accident_data(request, id):
    """API endpoint to get all data for a specific traffic accident record"""
    try:
        # Get the user's profile
        user = request.user
        is_admin = user.is_superuser or user.is_staff
        user_wilaya = user.userprofile.wilaya

        # Get the traffic accident record
        traffic_accident = TrafficAccident.objects.get(id=id)

        # Check if the user has permission to view this record
        if not is_admin and traffic_accident.unit not in [unit.name for unit in InterventionUnit.objects.filter(wilaya=user_wilaya)]:
            return JsonResponse({'error': 'You do not have permission to view this record'}, status=403)

        # Format the date
        from .utils import format_date_arabic
        formatted_date = format_date_arabic(traffic_accident.date)

        # Prepare the data
        data = {
            'id': traffic_accident.id,
            'date': traffic_accident.date.strftime('%Y-%m-%d'),
            'formatted_date': formatted_date,
            'unit': traffic_accident.unit,
            'accident_type': traffic_accident.accident_type,
            'accident_nature': traffic_accident.accident_nature,
            'accidents_count': traffic_accident.accidents_count,
            'operations_count': traffic_accident.operations_count,
            'road_type': traffic_accident.road_type,
            'driver_age': traffic_accident.driver_age,
            'casualties_men': traffic_accident.casualties_men,
            'casualties_women': traffic_accident.casualties_women,
            'casualties_children': traffic_accident.casualties_children,
            'total_casualties': traffic_accident.total_casualties,
            'fatalities_men': traffic_accident.fatalities_men,
            'fatalities_women': traffic_accident.fatalities_women,
            'fatalities_children': traffic_accident.fatalities_children,
            'total_fatalities': traffic_accident.total_fatalities,
            'fuel_cars': traffic_accident.fuel_cars,
            'lpg_cars': traffic_accident.lpg_cars,
            'trucks': traffic_accident.trucks,
            'buses': traffic_accident.buses,
            'motorcycles': traffic_accident.motorcycles,
            'tractors': traffic_accident.tractors,
            'directed_transport': traffic_accident.directed_transport,
            'other_vehicles': traffic_accident.other_vehicles,
            'time_06_09': traffic_accident.time_06_09,
            'time_09_12': traffic_accident.time_09_12,
            'time_12_14': traffic_accident.time_12_14,
            'time_14_16': traffic_accident.time_14_16,
            'time_16_20': traffic_accident.time_16_20,
            'time_20_00': traffic_accident.time_20_00,
            'time_00_06': traffic_accident.time_00_06,
            'sunday': traffic_accident.sunday,
            'monday': traffic_accident.monday,
            'tuesday': traffic_accident.tuesday,
            'wednesday': traffic_accident.wednesday,
            'thursday': traffic_accident.thursday,
            'friday': traffic_accident.friday,
            'saturday': traffic_accident.saturday,
        }

        return JsonResponse(data)
    except TrafficAccident.DoesNotExist:
        return JsonResponse({'error': 'Record not found'}, status=404)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@login_required(login_url='login')
def table_traffic_accidents_view(request):
    # Get the user's profile
    user = request.user
    is_admin = user.is_superuser or user.is_staff
    user_wilaya = user.userprofile.wilaya

    # Get intervention units - admins can see all units, regular users only see their wilaya's units
    if is_admin:
        user_units = InterventionUnit.objects.all().order_by('wilaya', 'name')
    else:
        user_units = InterventionUnit.objects.filter(wilaya=user_wilaya).order_by('name')

    # Get unique accident types from the database
    accident_types = TrafficAccident.objects.values_list('accident_type', flat=True).distinct()

    # Get unique accident natures from the database
    accident_natures = TrafficAccident.objects.values_list('accident_nature', flat=True).distinct()

    # Get unique road types from the database
    road_types = TrafficAccident.objects.values_list('road_type', flat=True).distinct()

    # Get unique driver age categories from the database
    driver_ages = TrafficAccident.objects.values_list('driver_age', flat=True).distinct()

    # Get data from traffic accidents model
    if is_admin:
        traffic_accidents_data = TrafficAccident.objects.all().order_by('-date')
    else:
        traffic_accidents_data = TrafficAccident.objects.filter(
            unit__in=user_units.values_list('name', flat=True)
        ).order_by('-date')

    # Format dates in Algerian Arabic format and add wilaya information for admin users
    from .utils import format_date_algerian_arabic
    for item in traffic_accidents_data:
        item.formatted_date = format_date_algerian_arabic(item.date)

        # Add wilaya information for admin users
        if is_admin:
            # Get the unit's wilaya
            try:
                unit = InterventionUnit.objects.get(name=item.unit)
                item.wilaya_code = unit.wilaya
                # Get the wilaya name from the choices
                from .models import WILAYA_CHOICES
                item.wilaya_name = dict(WILAYA_CHOICES).get(unit.wilaya, 'غير محدد')
            except InterventionUnit.DoesNotExist:
                item.wilaya_code = '00'
                item.wilaya_name = 'غير محدد'

    # Get unique months for filtering
    from .utils import ALGERIAN_ARABIC_MONTHS
    months = list(ALGERIAN_ARABIC_MONTHS.items())
    months.sort(key=lambda x: int(x[0]))  # Sort by month number

    # Get unique years for filtering
    years = traffic_accidents_data.dates('date', 'year')
    years = [year.year for year in years]
    years.sort(reverse=True)  # Sort years in descending order

    # Get wilaya choices for filtering (for admin users)
    from .models import WILAYA_CHOICES
    wilaya_choices = list(WILAYA_CHOICES)
    wilaya_choices.sort(key=lambda x: int(x[0]))  # Sort by wilaya code

    context = {
        'is_admin': is_admin,
        'traffic_accidents_data': traffic_accidents_data,
        'user_units': user_units,
        'accident_types': accident_types,
        'accident_natures': accident_natures,
        'road_types': road_types,
        'driver_ages': driver_ages,
        'months': months,
        'years': years,
        'wilaya_choices': wilaya_choices
    }

    return render(request, 'tables/traffic_accidents_table.html', context)





@login_required(login_url='login')
def table_public_area_fires_view(request):
    # Get the user's profile
    user = request.user
    is_admin = user.is_superuser or user.is_staff
    user_wilaya = user.userprofile.wilaya

    # Get intervention units - admins can see all units, regular users only see their wilaya's units
    if is_admin:
        user_units = InterventionUnit.objects.all().order_by('wilaya', 'name')
    else:
        user_units = InterventionUnit.objects.filter(wilaya=user_wilaya).order_by('name')

    # Get unique institution types from the database
    institution_types = PublicAreaFireData.objects.values_list('institution_type', flat=True).distinct()

    # Get unique institution categories from the database
    institution_categories = PublicAreaFireData.objects.values_list('institution_category', flat=True).distinct()

    # Get data from public area fires model
    if is_admin:
        public_area_fires_data = PublicAreaFireData.objects.all().order_by('-date')
    else:
        public_area_fires_data = PublicAreaFireData.objects.filter(
            intervening_unit__in=user_units.values_list('name', flat=True)
        ).order_by('-date')

    # Format dates in Algerian Arabic format and add wilaya information for admin users
    from .utils import format_date_algerian_arabic
    for item in public_area_fires_data:
        item.formatted_date = format_date_algerian_arabic(item.date)

        # Add wilaya information for admin users
        if is_admin:
            # Get the unit's wilaya
            try:
                unit = InterventionUnit.objects.get(name=item.intervening_unit)
                item.wilaya_code = unit.wilaya
                # Get the wilaya name from the choices
                from .models import WILAYA_CHOICES
                item.wilaya_name = dict(WILAYA_CHOICES).get(unit.wilaya, 'غير محدد')
            except InterventionUnit.DoesNotExist:
                item.wilaya_code = '00'
                item.wilaya_name = 'غير محدد'

    # Get unique months for filtering
    from .utils import ALGERIAN_ARABIC_MONTHS
    months = list(ALGERIAN_ARABIC_MONTHS.items())
    months.sort(key=lambda x: int(x[0]))  # Sort by month number

    # Get unique years for filtering
    years = public_area_fires_data.dates('date', 'year')
    years = [year.year for year in years]
    years.sort(reverse=True)  # Sort years in descending order

    # Get wilaya choices for filtering (for admin users)
    from .models import WILAYA_CHOICES
    wilaya_choices = list(WILAYA_CHOICES)
    wilaya_choices.sort(key=lambda x: int(x[0]))  # Sort by wilaya code

    context = {
        'is_admin': is_admin,
        'public_area_fires_data': public_area_fires_data,
        'user_units': user_units,
        'institution_types': institution_types,
        'institution_categories': institution_categories,
        'months': months,
        'years': years,
        'wilaya_choices': wilaya_choices
    }

    return render(request, 'tables/public_area_fires_table.html', context)

@login_required(login_url='login')
def table_institutional_fires_view(request):
    # Get the user's profile
    user = request.user
    is_admin = user.is_superuser or user.is_staff
    user_wilaya = user.userprofile.wilaya

    # Get intervention units - admins can see all units, regular users only see their wilaya's units
    if is_admin:
        user_units = InterventionUnit.objects.all().order_by('wilaya', 'name')
    else:
        user_units = InterventionUnit.objects.filter(wilaya=user_wilaya).order_by('name')

    # Get unique incident types from the database
    incident_types = InstitutionalFireData.objects.values_list('incident_type', flat=True).distinct()

    # Get unique activity natures from the database
    activity_natures = InstitutionalFireData.objects.values_list('activity_nature', flat=True).distinct()

    # Get unique activities from the database
    activities = InstitutionalFireData.objects.values_list('activity', flat=True).distinct()

    # Get unique class types from the database
    class_types = InstitutionalFireData.objects.values_list('class_type', flat=True).distinct()

    # Get data from institutional fires model
    if is_admin:
        institutional_fires_data = InstitutionalFireData.objects.all().order_by('-date')
    else:
        institutional_fires_data = InstitutionalFireData.objects.filter(
            intervening_unit__in=user_units.values_list('name', flat=True)
        ).order_by('-date')

    # Format dates in Algerian Arabic format and add wilaya information for admin users
    from .utils import format_date_algerian_arabic
    for item in institutional_fires_data:
        item.formatted_date = format_date_algerian_arabic(item.date)

        # Add wilaya information for admin users
        if is_admin:
            # Get the unit's wilaya
            try:
                unit = InterventionUnit.objects.get(name=item.intervening_unit)
                item.wilaya_code = unit.wilaya
                # Get the wilaya name from the choices
                from .models import WILAYA_CHOICES
                item.wilaya_name = dict(WILAYA_CHOICES).get(unit.wilaya, 'غير محدد')
            except InterventionUnit.DoesNotExist:
                item.wilaya_code = '00'
                item.wilaya_name = 'غير محدد'

    # Get unique months for filtering
    from .utils import ALGERIAN_ARABIC_MONTHS
    months = list(ALGERIAN_ARABIC_MONTHS.items())
    months.sort(key=lambda x: int(x[0]))  # Sort by month number

    # Get unique years for filtering
    years = institutional_fires_data.dates('date', 'year')
    years = [year.year for year in years]
    years.sort(reverse=True)  # Sort years in descending order

    # Get wilaya choices for filtering (for admin users)
    from .models import WILAYA_CHOICES
    wilaya_choices = list(WILAYA_CHOICES)
    wilaya_choices.sort(key=lambda x: int(x[0]))  # Sort by wilaya code

    context = {
        'is_admin': is_admin,
        'institutional_fires_data': institutional_fires_data,
        'user_units': user_units,
        'incident_types': incident_types,
        'activity_natures': activity_natures,
        'activities': activities,
        'class_types': class_types,
        'months': months,
        'years': years,
        'wilaya_choices': wilaya_choices
    }

    return render(request, 'tables/institutional_fires_table.html', context)

@login_required(login_url='login')
def table_residential_fires_view(request):
    # Get the user's profile
    user = request.user
    is_admin = user.is_superuser or user.is_staff
    user_wilaya = user.userprofile.wilaya

    # Get intervention units - admins can see all units, regular users only see their wilaya's units
    if is_admin:
        user_units = InterventionUnit.objects.all().order_by('wilaya', 'name')
    else:
        user_units = InterventionUnit.objects.filter(wilaya=user_wilaya).order_by('name')

    # Get data from residential fires model
    if is_admin:
        residential_fires_data = ResidentialFireData.objects.all().order_by('-date')
    else:
        residential_fires_data = ResidentialFireData.objects.filter(
            intervening_unit__in=user_units.values_list('name', flat=True)
        ).order_by('-date')

    # Format dates in Algerian Arabic format and add wilaya information for admin users
    from .utils import format_date_algerian_arabic
    for item in residential_fires_data:
        item.formatted_date = format_date_algerian_arabic(item.date)

        # Add wilaya information for admin users
        if is_admin:
            # Get the unit's wilaya
            try:
                unit = InterventionUnit.objects.get(name=item.intervening_unit)
                item.wilaya_code = unit.wilaya
                # Get the wilaya name from the choices
                from .models import WILAYA_CHOICES
                item.wilaya_name = dict(WILAYA_CHOICES).get(unit.wilaya, 'غير محدد')
            except InterventionUnit.DoesNotExist:
                item.wilaya_code = '00'
                item.wilaya_name = 'غير محدد'

    # Get unique months for filtering
    from .utils import ALGERIAN_ARABIC_MONTHS
    months = list(ALGERIAN_ARABIC_MONTHS.items())
    months.sort(key=lambda x: int(x[0]))  # Sort by month number

    # Get unique years for filtering
    years = residential_fires_data.dates('date', 'year')
    years = [year.year for year in years]
    years.sort(reverse=True)  # Sort years in descending order

    # Get wilaya choices for filtering (for admin users)
    from .models import WILAYA_CHOICES
    wilaya_choices = list(WILAYA_CHOICES)
    wilaya_choices.sort(key=lambda x: int(x[0]))  # Sort by wilaya code

    context = {
        'is_admin': is_admin,
        'residential_fires_data': residential_fires_data,
        'user_units': user_units,
        'months': months,
        'years': years,
        'wilaya_choices': wilaya_choices
    }

    return render(request, 'tables/residential_fires_table.html', context)

@login_required(login_url='login')
def table_exceptional_operations_view(request):
    # Get the user's profile
    user = request.user
    is_admin = user.is_superuser or user.is_staff
    user_wilaya = user.userprofile.wilaya

    # Get intervention units - admins can see all units, regular users only see their wilaya's units
    if is_admin:
        user_units = InterventionUnit.objects.all().order_by('wilaya', 'name')
    else:
        user_units = InterventionUnit.objects.filter(wilaya=user_wilaya).order_by('name')

    # Define exceptional operation types with their exact names as in the form
    exceptional_operation_types = [
        'تزويد بالماء',
        'فتح القبور',
        'إنقاذ الحيوانات',
        'التكفل باللاجئين'
    ]

    # Get data from misc operations model filtered by exceptional operations
    if is_admin:
        exceptional_operations_data = MiscOperationsData.objects.filter(
            operation_type__in=exceptional_operation_types
        ).order_by('-date')
    else:
        exceptional_operations_data = MiscOperationsData.objects.filter(
            intervening_unit__in=user_units.values_list('name', flat=True),
            operation_type__in=exceptional_operation_types
        ).order_by('-date')

    # Format dates in Algerian Arabic format and add wilaya information for admin users
    from .utils import format_date_algerian_arabic
    for item in exceptional_operations_data:
        item.formatted_date = format_date_algerian_arabic(item.date)

        # Add wilaya information for admin users
        if is_admin:
            # Get the unit's wilaya
            try:
                unit = InterventionUnit.objects.get(name=item.intervening_unit)
                item.wilaya_code = unit.wilaya
                # Get the wilaya name from the choices
                from .models import WILAYA_CHOICES
                item.wilaya_name = dict(WILAYA_CHOICES).get(unit.wilaya, 'غير محدد')
            except InterventionUnit.DoesNotExist:
                item.wilaya_code = '00'
                item.wilaya_name = 'غير محدد'

    # Get unique months for filtering
    from .utils import ALGERIAN_ARABIC_MONTHS
    months = list(ALGERIAN_ARABIC_MONTHS.items())
    months.sort(key=lambda x: int(x[0]))  # Sort by month number

    # Get unique years for filtering
    years = exceptional_operations_data.dates('date', 'year')
    years = [year.year for year in years]
    years.sort(reverse=True)  # Sort years in descending order

    # Get wilaya choices for filtering (for admin users)
    from .models import WILAYA_CHOICES
    wilaya_choices = list(WILAYA_CHOICES)
    wilaya_choices.sort(key=lambda x: int(x[0]))  # Sort by wilaya code

    # Get the Excel data for each exceptional operation to display all form fields
    from .utils import read_from_excel

    # Path to the Excel file
    excel_path = os.path.join('media', 'exceptional_operations_stats_data.xlsx')

    # Use the utility function to read from Excel
    success, result = read_from_excel(excel_path)

    if success:
        # Create a dictionary to map date, unit, and operation type to other fields
        excel_data = {}
        for _, row in result.iterrows():
            date_str = str(row.get('التاريخ', ''))
            unit_str = str(row.get('الوحدات المتدخلة', ''))
            operation_type = str(row.get('أنواع العمليات الإستثنائية', ''))
            key = f"{date_str}_{unit_str}_{operation_type}"

            # Check if this record belongs to the user's wilaya
            # Skip if not admin and the unit is not in the user's wilaya
            if not is_admin:
                # Get the unit's wilaya
                try:
                    unit_obj = InterventionUnit.objects.get(name=unit_str)
                    unit_wilaya = unit_obj.wilaya
                    # Skip if unit is not in user's wilaya
                    if unit_wilaya != user_wilaya:
                        continue
                except InterventionUnit.DoesNotExist:
                    # If unit doesn't exist in database, skip this record
                    continue

            # Only process animal rescue operations which have resources
            if operation_type == 'إنقاذ الحيوانات':
                excel_data[key] = {
                    'ambulances': row.get('سيارة إسعاف', 0),
                    'fire_trucks': row.get('شاحنة إطفاء', 0),
                    'mechanical_ladders': row.get('السلم الميكانيكي', 0),
                    'other_resources': row.get('وسائل أخرى', 0)
                }

        # Enhance exceptional_operations_data with Excel data
        for item in exceptional_operations_data:
            date_str = str(item.date)
            unit = str(item.intervening_unit)
            operation_type = str(item.operation_type)
            key = f"{date_str}_{unit}_{operation_type}"

            if key in excel_data:
                for field, value in excel_data[key].items():
                    setattr(item, field, value)
    else:
        # Log the error but continue without Excel data
        print(f"Error reading Excel file: {result}")

    context = {
        'is_admin': is_admin,
        'exceptional_operations_data': exceptional_operations_data,
        'user_units': user_units,
        'operation_types': exceptional_operation_types,
        'months': months,
        'years': years,
        'wilaya_choices': wilaya_choices
    }

    return render(request, 'tables/exceptional_operations_table.html', context)

@login_required(login_url='login')
def table_misc_operations_view(request):
    # Get the user's profile
    user = request.user
    is_admin = user.is_superuser or user.is_staff
    user_wilaya = user.userprofile.wilaya

    # Get intervention units - admins can see all units, regular users only see their wilaya's units
    if is_admin:
        user_units = InterventionUnit.objects.all().order_by('wilaya', 'name')
    else:
        user_units = InterventionUnit.objects.filter(wilaya=user_wilaya).order_by('name')

    # Define misc operation types with their exact names as in the form
    misc_operation_types = [
        'نقل الجثث',
        'الغرق',
        'أشخاص دون مأوى',
        'عمليات أخرى'
    ]

    # Get data from misc operations model
    if is_admin:
        misc_operations_data = MiscOperationsData.objects.filter(
            operation_type__in=misc_operation_types
        ).order_by('-date')
    else:
        misc_operations_data = MiscOperationsData.objects.filter(
            intervening_unit__in=user_units.values_list('name', flat=True),
            operation_type__in=misc_operation_types
        ).order_by('-date')

    # Format dates in Algerian Arabic format and add wilaya information for admin users
    from .utils import format_date_algerian_arabic
    for item in misc_operations_data:
        item.formatted_date = format_date_algerian_arabic(item.date)

        # Add wilaya information for admin users
        if is_admin:
            # Get the unit's wilaya
            try:
                unit = InterventionUnit.objects.get(name=item.intervening_unit)
                item.wilaya_code = unit.wilaya
                # Get the wilaya name from the choices
                from .models import WILAYA_CHOICES
                item.wilaya_name = dict(WILAYA_CHOICES).get(unit.wilaya, 'غير محدد')
            except InterventionUnit.DoesNotExist:
                item.wilaya_code = '00'
                item.wilaya_name = 'غير محدد'

    # Get unique months for filtering
    from .utils import ALGERIAN_ARABIC_MONTHS
    months = list(ALGERIAN_ARABIC_MONTHS.items())
    months.sort(key=lambda x: int(x[0]))  # Sort by month number

    # Get unique years for filtering
    years = misc_operations_data.dates('date', 'year')
    years = [year.year for year in years]
    years.sort(reverse=True)  # Sort years in descending order

    # Get wilaya choices for filtering (for admin users)
    from .models import WILAYA_CHOICES
    wilaya_choices = list(WILAYA_CHOICES)
    wilaya_choices.sort(key=lambda x: int(x[0]))  # Sort by wilaya code

    context = {
        'is_admin': is_admin,
        'misc_operations_data': misc_operations_data,
        'user_units': user_units,
        'operation_types': misc_operation_types,
        'months': months,
        'years': years,
        'wilaya_choices': wilaya_choices
    }

    return render(request, 'tables/misc_operations_table.html', context)

@login_required(login_url='login')
def table_forest_agricultural_fires_view(request):
    # Get the user's profile
    user = request.user
    is_admin = user.is_superuser or user.is_staff
    user_wilaya = user.userprofile.wilaya

    # Get intervention units - admins can see all units, regular users only see their wilaya's units
    if is_admin:
        user_units = InterventionUnit.objects.all().order_by('wilaya', 'name')
    else:
        user_units = InterventionUnit.objects.filter(wilaya=user_wilaya).order_by('name')

    # Define fire types with their exact names as in the form
    fire_types = [
        'حرائق الغابات و الأدغال',
        'حرائق المحاصيل',
        'حرائق الأعشاب',
        'حرائق الأعلاف',
        'حرائق النخيل',
        'حرائق الأشجار المثمرة'
    ]

    # Get data from forest agricultural fires model
    if is_admin:
        forest_agricultural_fires_data = ForestAgriculturalFireData.objects.all().order_by('-date')
    else:
        forest_agricultural_fires_data = ForestAgriculturalFireData.objects.filter(
            intervening_unit__in=user_units.values_list('name', flat=True)
        ).order_by('-date')

    # Format dates in Algerian Arabic format and add wilaya information for admin users
    from .utils import format_date_algerian_arabic
    for item in forest_agricultural_fires_data:
        item.formatted_date = format_date_algerian_arabic(item.date)

        # Add wilaya information for admin users
        if is_admin:
            # Get the unit's wilaya
            try:
                unit = InterventionUnit.objects.get(name=item.intervening_unit)
                item.wilaya_code = unit.wilaya
                # Get the wilaya name from the choices
                from .models import WILAYA_CHOICES
                item.wilaya_name = dict(WILAYA_CHOICES).get(unit.wilaya, 'غير محدد')
            except InterventionUnit.DoesNotExist:
                item.wilaya_code = '00'
                item.wilaya_name = 'غير محدد'

    # Get unique months for filtering
    from .utils import ALGERIAN_ARABIC_MONTHS
    months = list(ALGERIAN_ARABIC_MONTHS.items())
    months.sort(key=lambda x: int(x[0]))  # Sort by month number

    # Get unique years for filtering
    years = forest_agricultural_fires_data.dates('date', 'year')
    years = [year.year for year in years]
    years.sort(reverse=True)  # Sort years in descending order

    # Get unique loss types for filtering
    loss_types = forest_agricultural_fires_data.values_list('loss_type', flat=True).distinct()

    # Get wilaya choices for filtering (for admin users)
    from .models import WILAYA_CHOICES
    wilaya_choices = list(WILAYA_CHOICES)
    wilaya_choices.sort(key=lambda x: int(x[0]))  # Sort by wilaya code

    context = {
        'is_admin': is_admin,
        'forest_agricultural_fires_data': forest_agricultural_fires_data,
        'user_units': user_units,
        'fire_types': fire_types,
        'loss_types': loss_types,
        'months': months,
        'years': years,
        'wilaya_choices': wilaya_choices
    }

    return render(request, 'tables/forest_agricultural_fires_table.html', context)

@login_required(login_url='login')
def table_interventions_without_work_view(request):
    # Get the user's profile
    user = request.user
    is_admin = user.is_superuser or user.is_staff

    # Get data from misc operations model filtered by interventions without work
    if is_admin:
        interventions_without_work_data = MiscOperationsData.objects.filter(
            number_of_interventions=0
        ).order_by('-date')
    else:
        user_wilaya = user.userprofile.wilaya
        interventions_without_work_data = MiscOperationsData.objects.filter(
            intervening_unit__in=InterventionUnit.objects.filter(wilaya=user_wilaya).values_list('name', flat=True),
            number_of_interventions=0
        ).order_by('-date')

    # Get the Excel data for each intervention without work to display all form fields
    from .utils import read_from_excel

    # Path to the Excel file
    excel_path = os.path.join('media', 'interventions_without_work_stats_data.xlsx')

    # Get the user's wilaya for filtering
    user_wilaya = user.userprofile.wilaya

    # Use the utility function to read from Excel
    success, result = read_from_excel(excel_path)

    if success:
        # Create a dictionary to map date, unit, and intervention type to other fields
        excel_data = {}
        for _, row in result.iterrows():
            date_str = str(row.get('التاريخ', ''))
            unit_str = str(row.get('الوحدات المتدخلة', ''))
            intervention_type = str(row.get('أنواع التدخلات بدون عمل', ''))
            key = f"{date_str}_{unit_str}_{intervention_type}"

            # Check if this record belongs to the user's wilaya
            # Skip if not admin and the unit is not in the user's wilaya
            if not is_admin:
                # Get the unit's wilaya
                try:
                    unit_obj = InterventionUnit.objects.get(name=unit_str)
                    unit_wilaya = unit_obj.wilaya
                    # Skip if unit is not in user's wilaya
                    if unit_wilaya != user_wilaya:
                        continue
                except InterventionUnit.DoesNotExist:
                    # If unit doesn't exist in database, skip this record
                    continue

            excel_data[key] = {
                'cars': row.get('سيارة', 0),
                'trucks': row.get('شاحنة', 0),
                'other_resources': row.get('أخرى', 0)
            }

        # Enhance interventions_without_work_data with Excel data
        for item in interventions_without_work_data:
            date_str = str(item.date)
            unit = str(item.intervening_unit)
            operation_type = str(item.operation_type)
            key = f"{date_str}_{unit}_{operation_type}"

            if key in excel_data:
                for field, value in excel_data[key].items():
                    setattr(item, field, value)
    else:
        # Log the error but continue without Excel data
        print(f"Error reading Excel file: {result}")

    # Get intervention units - admins can see all units, regular users only see their wilaya's units
    if is_admin:
        user_units = InterventionUnit.objects.all().order_by('wilaya', 'name')
    else:
        user_units = InterventionUnit.objects.filter(wilaya=user_wilaya).order_by('name')

    # Define intervention types with their exact names as in the form
    intervention_types = [
        'الإسعاف و الإجلاء',
        'حوادث المرور',
        'الحرائق',
        'العمليات المختلفة'
    ]

    # Format dates in Algerian Arabic format and add wilaya information for admin users
    from .utils import format_date_algerian_arabic
    for item in interventions_without_work_data:
        item.formatted_date = format_date_algerian_arabic(item.date)

        # Add wilaya information for admin users
        if is_admin:
            # Get the unit's wilaya
            try:
                unit = InterventionUnit.objects.get(name=item.intervening_unit)
                item.wilaya_code = unit.wilaya
                # Get the wilaya name from the choices
                from .models import WILAYA_CHOICES
                item.wilaya_name = dict(WILAYA_CHOICES).get(unit.wilaya, 'غير محدد')
            except InterventionUnit.DoesNotExist:
                item.wilaya_code = '00'
                item.wilaya_name = 'غير محدد'

    # Get unique months for filtering
    from .utils import ALGERIAN_ARABIC_MONTHS
    months = list(ALGERIAN_ARABIC_MONTHS.items())
    months.sort(key=lambda x: int(x[0]))  # Sort by month number

    # Get unique years for filtering
    years = interventions_without_work_data.dates('date', 'year')
    years = [year.year for year in years]
    years.sort(reverse=True)  # Sort years in descending order

    # Get wilaya choices for filtering (for admin users)
    from .models import WILAYA_CHOICES
    wilaya_choices = list(WILAYA_CHOICES)
    wilaya_choices.sort(key=lambda x: int(x[0]))  # Sort by wilaya code

    context = {
        'is_admin': is_admin,
        'interventions_without_work_data': interventions_without_work_data,
        'user_units': user_units,
        'intervention_types': intervention_types,
        'months': months,
        'years': years,
        'wilaya_choices': wilaya_choices
    }

    return render(request, 'tables/interventions_without_work_table.html', context)





@csrf_exempt
@login_required(login_url='login')
def traffic_accidents_view(request):
    # Get the user's profile
    user = request.user
    is_admin = user.is_superuser or user.is_staff
    user_wilaya = user.userprofile.wilaya

    # Get intervention units - admins can see all units, regular users only see their wilaya's units
    if is_admin:
        units = InterventionUnit.objects.all().order_by('wilaya', 'name')
    else:
        units = InterventionUnit.objects.filter(wilaya=user_wilaya).order_by('name')

    # Handle POST request (form submission)
    if request.method == 'POST':
        try:
            # Parse JSON data from request body
            data = json.loads(request.body)

            # Format date from d/m/Y to Y-m-d for database
            date_str = data.get('date')
            try:
                date_parts = date_str.split('/')
                if len(date_parts) == 3:
                    day, month, year = date_parts
                    formatted_date = f"{year}-{month}-{day}"
                else:
                    formatted_date = date_str
            except:
                formatted_date = date_str

            # Create TrafficAccident object
            traffic_accident = TrafficAccident(
                date=formatted_date,
                unit=data.get('unit'),
                accident_type=data.get('accident_type'),
                accident_nature=data.get('accident_nature'),
                accidents_count=int(data.get('accidents_count', 0)),
                operations_count=int(data.get('operations_count', 0)),
                road_type=data.get('road_type'),
                driver_age=data.get('driver_age'),

                # Human losses
                casualties_men=int(data.get('casualties_men', 0)),
                casualties_women=int(data.get('casualties_women', 0)),
                casualties_children=int(data.get('casualties_children', 0)),
                total_casualties=int(data.get('total_casualties', 0)),

                fatalities_men=int(data.get('fatalities_men', 0)),
                fatalities_women=int(data.get('fatalities_women', 0)),
                fatalities_children=int(data.get('fatalities_children', 0)),
                total_fatalities=int(data.get('total_fatalities', 0)),

                # Material losses
                fuel_cars=int(data.get('fuel_cars', 0)),
                lpg_cars=int(data.get('lpg_cars', 0)),
                trucks=int(data.get('trucks', 0)),
                buses=int(data.get('buses', 0)),
                motorcycles=int(data.get('motorcycles', 0)),
                tractors=int(data.get('tractors', 0)),
                directed_transport=int(data.get('directed_transport', 0)),
                other_vehicles=int(data.get('other_vehicles', 0)),

                # Time slots
                time_06_09=int(data.get('time_06_09', 0)),
                time_09_12=int(data.get('time_09_12', 0)),
                time_12_14=int(data.get('time_12_14', 0)),
                time_14_16=int(data.get('time_14_16', 0)),
                time_16_20=int(data.get('time_16_20', 0)),
                time_20_00=int(data.get('time_20_00', 0)),
                time_00_06=int(data.get('time_00_06', 0)),

                # Days of week
                sunday=int(data.get('sunday', 0)),
                monday=int(data.get('monday', 0)),
                tuesday=int(data.get('tuesday', 0)),
                wednesday=int(data.get('wednesday', 0)),
                thursday=int(data.get('thursday', 0)),
                friday=int(data.get('friday', 0)),
                saturday=int(data.get('saturday', 0))
            )

            # Save to database
            traffic_accident.save()

            # Save to Excel
            excel_path = os.path.join('media', 'traffic_accidents_data.xlsx')

            # Create data dictionary for Excel
            excel_data = {
                'التاريخ': date_str,
                'الوحدة المتدخلة': data.get('unit'),
                'نوع الحادث': data.get('accident_type'),
                'طبيعة الحادث': data.get('accident_nature'),
                'عدد الحوادث': int(data.get('accidents_count', 0)),
                'عدد العمليات': int(data.get('operations_count', 0)),
                'نوع الطريق': data.get('road_type'),
                'فئة السائقين': data.get('driver_age'),

                # Human losses
                'عدد الجرحى رجال': int(data.get('casualties_men', 0)),
                'عدد الجرحى نساء': int(data.get('casualties_women', 0)),
                'عدد الجرحى أطفال': int(data.get('casualties_children', 0)),
                'مجموع الجرحى': int(data.get('total_casualties', 0)),

                'عدد الوفيات رجال': int(data.get('fatalities_men', 0)),
                'عدد الوفيات نساء': int(data.get('fatalities_women', 0)),
                'عدد الوفيات أطفال': int(data.get('fatalities_children', 0)),
                'مجموع الوفيات': int(data.get('total_fatalities', 0)),

                # Material losses
                'سيارات وقود': int(data.get('fuel_cars', 0)),
                'سيارات غاز مميع': int(data.get('lpg_cars', 0)),
                'شاحنات': int(data.get('trucks', 0)),
                'حافلات': int(data.get('buses', 0)),
                'دراجات': int(data.get('motorcycles', 0)),
                'جرارات': int(data.get('tractors', 0)),
                'النقل موجه': int(data.get('directed_transport', 0)),
                'أخرى': int(data.get('other_vehicles', 0)),

                # Time slots
                '06:00 - 09:00': int(data.get('time_06_09', 0)),
                '09:00 - 12:00': int(data.get('time_09_12', 0)),
                '12:00 - 14:00': int(data.get('time_12_14', 0)),
                '14:00 - 16:00': int(data.get('time_14_16', 0)),
                '16:00 - 20:00': int(data.get('time_16_20', 0)),
                '20:00 - 00:00': int(data.get('time_20_00', 0)),
                '00:00 - 06:00': int(data.get('time_00_06', 0)),

                # Days of week
                'الأحد': int(data.get('sunday', 0)),
                'الإثنين': int(data.get('monday', 0)),
                'الثلاثاء': int(data.get('tuesday', 0)),
                'الأربعاء': int(data.get('wednesday', 0)),
                'الخميس': int(data.get('thursday', 0)),
                'الجمعة': int(data.get('friday', 0)),
                'السبت': int(data.get('saturday', 0))
            }

            # Use the utility function to save to Excel
            from .utils import save_to_excel
            # Pass the user and form type to organize files by wilaya
            success, message = save_to_excel(excel_path, excel_data, user=request.user, form_type='traffic_accidents')

            if success:
                return JsonResponse({'status': 'success', 'message': 'تم حفظ البيانات بنجاح'})
            else:
                # If Excel save failed but database save succeeded, return partial success
                return JsonResponse({
                    'status': 'partial_success',
                    'message': f'تم حفظ البيانات في قاعدة البيانات ولكن حدثت مشكلة في حفظ ملف Excel: {message}'
                })

        except Exception as e:
            return JsonResponse({'status': 'error', 'message': f'حدث خطأ: {str(e)}'})

    return render(request, 'traffic_accidents/index.html', {'units': units})

@login_required(login_url='login')
def fires_view(request):
    return render(request, 'fires/index.html')

from .utils import handle_post_request

@csrf_exempt
@login_required(login_url='login')
def general_fire_table_view(request):
    # Get the user's profile
    user = request.user
    is_admin = user.is_superuser or user.is_staff
    user_wilaya = user.userprofile.wilaya

    # Get intervention units - admins can see all units, regular users only see their wilaya's units
    if is_admin:
        units = InterventionUnit.objects.all().order_by('wilaya', 'name')
    else:
        units = InterventionUnit.objects.filter(wilaya=user_wilaya).order_by('name')

    def process_data(data):
        # Extract data from the request
        date = data.get('date')
        formatted_date = data.get('formatted_date', date)
        intervening_unit = data.get('intervening_unit')
        fire_type = data.get('fire_type')
        number_of_fires = data.get('number_of_fires')
        number_of_interventions = data.get('number_of_interventions')
        number_of_injured = data.get('number_of_injured')
        number_of_deaths = data.get('number_of_deaths')

        # Validate required fields
        missing_fields = []
        if not date: missing_fields.append('التاريخ')
        if not intervening_unit: missing_fields.append('الوحدات المتدخلة')
        if not fire_type: missing_fields.append('أنواع الحرائق')
        if number_of_fires is None: missing_fields.append('عدد الحرائق')
        if number_of_interventions is None: missing_fields.append('عدد التدخلات')
        if number_of_injured is None: missing_fields.append('عدد الجرحى')
        if number_of_deaths is None: missing_fields.append('عدد الوفيات')

        if missing_fields:
            return JsonResponse({
                'status': 'error',
                'message': 'يرجى ملء الحقول التالية: ' + '، '.join(missing_fields)
            })

        # Save to database
        GeneralFireData.objects.create(
            date=date,
            intervening_unit=intervening_unit,
            fire_type=fire_type,
            number_of_fires=number_of_fires,
            number_of_interventions=number_of_interventions,
            number_of_injured=number_of_injured,
            number_of_deaths=number_of_deaths
        )

        # Save to Excel
        excel_path = os.path.join('media', 'general_fire_data.xlsx')

        # Create data dictionary
        excel_data = {
            'التاريخ': formatted_date,
            'الوحدات المتدخلة': intervening_unit,
            'أنواع الحرائق': fire_type,
            'عدد الحرائق': number_of_fires,
            'عدد التدخلات': number_of_interventions,
            'عدد الجرحى': number_of_injured,
            'عدد الوفيات': number_of_deaths
        }

        # Use the utility function to save to Excel
        from .utils import save_to_excel
        # Pass the user and form type to organize files by wilaya
        success, message = save_to_excel(excel_path, excel_data, user=request.user, form_type='general_fire')

        if success:
            return JsonResponse({'status': 'success', 'message': 'تم حفظ البيانات بنجاح'})
        else:
            # If Excel save failed but database save succeeded, return partial success
            return JsonResponse({
                'status': 'partial_success',
                'message': f'تم حفظ البيانات في قاعدة البيانات ولكن حدثت مشكلة في حفظ ملف Excel: {message}'
            })

    # معالجة طلب POST
    post_response = handle_post_request(request, process_data)
    if post_response:
        return post_response

    return render(request, 'fires/general_fire_table.html', {'units': units})

@csrf_exempt
@login_required(login_url='login')
def residential_fires_view(request):
    # Get the user's profile
    user = request.user
    is_admin = user.is_superuser or user.is_staff
    user_wilaya = user.userprofile.wilaya

    # Get intervention units - admins can see all units, regular users only see their wilaya's units
    if is_admin:
        units = InterventionUnit.objects.all().order_by('wilaya', 'name')
    else:
        units = InterventionUnit.objects.filter(wilaya=user_wilaya).order_by('name')

    def process_data(data):
        # Extract data from the request
        date = data.get('date')
        formatted_date = data.get('formatted_date', date)
        intervening_unit = data.get('intervening_unit')

        # Family type
        family_first = data.get('family_first', False)
        family_second = data.get('family_second', False)
        family_third = data.get('family_third', False)
        family_fourth = data.get('family_fourth', False)

        # Fire statistics
        number_of_fires = data.get('number_of_fires')
        number_of_interventions = data.get('number_of_interventions')
        number_of_injured = data.get('number_of_injured')
        number_of_deaths = data.get('number_of_deaths')

        # Resources used
        ambulances = data.get('ambulances')
        fire_trucks = data.get('fire_trucks')
        mechanical_ladders = data.get('mechanical_ladders')
        other_resources = data.get('other_resources')

        # Intervention duration
        intervention_duration = data.get('intervention_duration')

        # Validate required fields
        missing_fields = []
        if not date: missing_fields.append('التاريخ')
        if not intervening_unit: missing_fields.append('الوحدات المتدخلة')
        if not (family_first or family_second or family_third or family_fourth):
            missing_fields.append('العائلة')
        if number_of_fires is None: missing_fields.append('عدد الحرائق')
        if number_of_interventions is None: missing_fields.append('عدد التدخلات')
        if number_of_injured is None: missing_fields.append('عدد الجرحى')
        if number_of_deaths is None: missing_fields.append('عدد الوفيات')
        if ambulances is None: missing_fields.append('سيارة إسعاف')
        if fire_trucks is None: missing_fields.append('شاحنة إطفاء')
        if mechanical_ladders is None: missing_fields.append('السلم الميكانيكي')
        if other_resources is None: missing_fields.append('وسائل أخرى')
        if not intervention_duration: missing_fields.append('مدة التدخل')

        if missing_fields:
            return JsonResponse({
                'status': 'error',
                'message': 'يرجى ملء الحقول التالية: ' + '، '.join(missing_fields)
            })

        # Save to database
        ResidentialFireData.objects.create(
            date=date,
            intervening_unit=intervening_unit,
            family_first=family_first,
            family_second=family_second,
            family_third=family_third,
            family_fourth=family_fourth,
            number_of_fires=number_of_fires,
            number_of_interventions=number_of_interventions,
            number_of_injured=number_of_injured,
            number_of_deaths=number_of_deaths,
            ambulances=ambulances,
            fire_trucks=fire_trucks,
            mechanical_ladders=mechanical_ladders,
            other_resources=other_resources,
            intervention_duration=intervention_duration
        )

        # Save to Excel
        excel_path = os.path.join('media', 'residential_fire_data.xlsx')

        # Create data dictionary
        excel_data = {
            'التاريخ': formatted_date,
            'الوحدات المتدخلة': intervening_unit,
            'العائلة الأولى': 1 if family_first else 0,
            'العائلة الثانية': 1 if family_second else 0,
            'العائلة الثالثة': 1 if family_third else 0,
            'العائلة الرابعة': 1 if family_fourth else 0,
            'عدد الحرائق': number_of_fires,
            'عدد التدخلات': number_of_interventions,
            'عدد الجرحى': number_of_injured,
            'عدد الوفيات': number_of_deaths,
            'سيارة إسعاف': ambulances,
            'شاحنة إطفاء': fire_trucks,
            'السلم الميكانيكي': mechanical_ladders,
            'وسائل أخرى': other_resources,
            'مدة التدخل': intervention_duration
        }

        # Use the utility function to save to Excel
        from .utils import save_to_excel
        # Pass the user and form type to organize files by wilaya
        success, message = save_to_excel(excel_path, excel_data, user=request.user, form_type='residential_fires')

        if success:
            return JsonResponse({'status': 'success', 'message': 'تم حفظ البيانات بنجاح'})
        else:
            # If Excel save failed but database save succeeded, return partial success
            return JsonResponse({
                'status': 'partial_success',
                'message': f'تم حفظ البيانات في قاعدة البيانات ولكن حدثت مشكلة في حفظ ملف Excel: {message}'
            })

    # معالجة طلب POST
    post_response = handle_post_request(request, process_data)
    if post_response:
        return post_response

    return render(request, 'fires/residential_fires.html', {'units': units})

@csrf_exempt
@login_required(login_url='login')
def institutional_fires_view(request):
    # Get the user's profile
    user = request.user
    is_admin = user.is_superuser or user.is_staff
    user_wilaya = user.userprofile.wilaya

    # Get intervention units - admins can see all units, regular users only see their wilaya's units
    if is_admin:
        units = InterventionUnit.objects.all().order_by('wilaya', 'name')
    else:
        units = InterventionUnit.objects.filter(wilaya=user_wilaya).order_by('name')

    def process_data(data):
        # Extract data from the request
        date = data.get('date')
        formatted_date = data.get('formatted_date', date)
        intervening_unit = data.get('intervening_unit')
        incident_type = data.get('incident_type')
        operations_count = data.get('operations_count')

        # Institution information
        activity_nature = data.get('activity_nature')
        institution_name = data.get('institution_name')
        activity = data.get('activity')
        class_type = data.get('class_type')

        # Approval
        year_pii = data.get('year_pii', 0)
        year_edd = data.get('year_edd', 0)

        # Activity location
        zone_zr = data.get('zone_zr', 0)
        zone_zu = data.get('zone_zu', 0)
        zone_za = data.get('zone_za', 0)
        zone_zi = data.get('zone_zi', 0)

        # Human losses
        injured_count = data.get('injured_count')
        deaths_count = data.get('deaths_count')

        # Resources used
        ambulances = data.get('ambulances')
        fire_trucks = data.get('fire_trucks')
        mechanical_ladders = data.get('mechanical_ladders')
        other_resources = data.get('other_resources')

        # Intervention duration
        intervention_duration = data.get('intervention_duration')

        # Validate required fields
        missing_fields = []
        if not date: missing_fields.append('التاريخ')
        if not intervening_unit: missing_fields.append('الوحدات المتدخلة')
        if not incident_type: missing_fields.append('نوع الحادث')
        if operations_count is None: missing_fields.append('عدد العمليات')
        if not activity_nature: missing_fields.append('طبيعة النشاط')
        if not institution_name: missing_fields.append('إسم المؤسسة المصنفة')
        if not activity: missing_fields.append('النشاط')
        if not class_type: missing_fields.append('الصنف')
        if not (zone_zr or zone_zu or zone_za or zone_zi): missing_fields.append('مكان النشاط')
        if injured_count is None: missing_fields.append('عدد الجرحى')
        if deaths_count is None: missing_fields.append('عدد الوفيات')
        if ambulances is None: missing_fields.append('سيارة إسعاف')
        if fire_trucks is None: missing_fields.append('شاحنة إطفاء')
        if mechanical_ladders is None: missing_fields.append('السلم الميكانيكي')
        if other_resources is None: missing_fields.append('وسائل أخرى')
        if not intervention_duration: missing_fields.append('مدة التدخل')

        if missing_fields:
            return JsonResponse({
                'status': 'error',
                'message': 'يرجى ملء الحقول التالية: ' + '، '.join(missing_fields)
            })

        # Save to database
        InstitutionalFireData.objects.create(
            date=date,
            intervening_unit=intervening_unit,
            incident_type=incident_type,
            operations_count=operations_count,
            activity_nature=activity_nature,
            institution_name=institution_name,
            activity=activity,
            class_type=class_type,
            year_pii=year_pii == 1,
            year_edd=year_edd == 1,
            zone_zr=zone_zr == 1,
            zone_zu=zone_zu == 1,
            zone_za=zone_za == 1,
            zone_zi=zone_zi == 1,
            injured_count=injured_count,
            deaths_count=deaths_count,
            ambulances=ambulances,
            fire_trucks=fire_trucks,
            mechanical_ladders=mechanical_ladders,
            other_resources=other_resources,
            intervention_duration=intervention_duration
        )

        # Save to Excel
        excel_path = os.path.join('media', 'institutional_fire_data.xlsx')

        # Create data dictionary
        excel_data = {
            'التاريخ': formatted_date,
            'الوحدات المتدخلة': intervening_unit,
            'نوع الحادث': incident_type,
            'عدد العمليات': operations_count,
            'طبيعة النشاط': activity_nature,
            'إسم المؤسسة المصنفة': institution_name,
            'النشاط': activity,
            'الصنف': class_type,
            'السنة (PII)': year_pii,
            'السنة (EDD)': year_edd,
            'منطقة فلاحية (ZR)': zone_zr,
            'منطقة حضرية (ZU)': zone_zu,
            'منطقة النشاطات التجارية (ZA)': zone_za,
            'منطقة صناعية (ZI)': zone_zi,
            'عدد الجرحى': injured_count,
            'عدد الوفيات': deaths_count,
            'سيارة إسعاف': ambulances,
            'شاحنة إطفاء': fire_trucks,
            'السلم الميكانيكي': mechanical_ladders,
            'وسائل أخرى': other_resources,
            'مدة التدخل': intervention_duration
        }

        # Use the utility function to save to Excel
        from .utils import save_to_excel
        # Pass the user and form type to organize files by wilaya
        success, message = save_to_excel(excel_path, excel_data, user=request.user, form_type='institutional_fires')

        if success:
            return JsonResponse({'status': 'success', 'message': 'تم حفظ البيانات بنجاح'})
        else:
            # If Excel save failed but database save succeeded, return partial success
            return JsonResponse({
                'status': 'partial_success',
                'message': f'تم حفظ البيانات في قاعدة البيانات ولكن حدثت مشكلة في حفظ ملف Excel: {message}'
            })

    # معالجة طلب POST
    post_response = handle_post_request(request, process_data)
    if post_response:
        return post_response

    return render(request, 'fires/institutional_fires.html', {'units': units})

@csrf_exempt
@login_required(login_url='login')
def public_area_fires_view(request):
    # Get the user's profile
    user = request.user
    is_admin = user.is_superuser or user.is_staff
    user_wilaya = user.userprofile.wilaya

    # Get intervention units - admins can see all units, regular users only see their wilaya's units
    if is_admin:
        units = InterventionUnit.objects.all().order_by('wilaya', 'name')
    else:
        units = InterventionUnit.objects.filter(wilaya=user_wilaya).order_by('name')

    def process_data(data):
        # Extract data from the request
        date = data.get('date')
        formatted_date = data.get('formatted_date', date)
        intervening_unit = data.get('intervening_unit')

        # Fire statistics
        number_of_fires = data.get('number_of_fires')
        number_of_interventions = data.get('number_of_interventions')
        number_of_injured = data.get('number_of_injured')
        number_of_deaths = data.get('number_of_deaths')

        # Institution information
        institution_name = data.get('institution_name')
        institution_type = data.get('institution_type')
        institution_category = data.get('institution_category')

        # Resources used
        ambulances = data.get('ambulances')
        fire_trucks = data.get('fire_trucks')
        mechanical_ladders = data.get('mechanical_ladders')
        other_resources = data.get('other_resources')

        # Intervention duration
        intervention_duration = data.get('intervention_duration')

        # Validate required fields
        missing_fields = []
        if not date: missing_fields.append('التاريخ')
        if not intervening_unit: missing_fields.append('الوحدات المتدخلة')
        if number_of_fires is None: missing_fields.append('عدد الحرائق')
        if number_of_interventions is None: missing_fields.append('عدد التدخلات')
        if number_of_injured is None: missing_fields.append('عدد الجرحى')
        if number_of_deaths is None: missing_fields.append('عدد الوفيات')
        if not institution_name: missing_fields.append('إسم المؤسسة')
        if not institution_type: missing_fields.append('النوع')
        if not institution_category: missing_fields.append('الصنف')
        if ambulances is None: missing_fields.append('سيارة إسعاف')
        if fire_trucks is None: missing_fields.append('شاحنة إطفاء')
        if mechanical_ladders is None: missing_fields.append('السلم الميكانيكي')
        if other_resources is None: missing_fields.append('وسائل أخرى')
        if not intervention_duration: missing_fields.append('مدة التدخل')

        if missing_fields:
            return JsonResponse({
                'status': 'error',
                'message': 'يرجى ملء الحقول التالية: ' + '، '.join(missing_fields)
            })

        # Save to database
        PublicAreaFireData.objects.create(
            date=date,
            intervening_unit=intervening_unit,
            number_of_fires=number_of_fires,
            number_of_interventions=number_of_interventions,
            number_of_injured=number_of_injured,
            number_of_deaths=number_of_deaths,
            institution_name=institution_name,
            institution_type=institution_type,
            institution_category=institution_category,
            ambulances=ambulances,
            fire_trucks=fire_trucks,
            mechanical_ladders=mechanical_ladders,
            other_resources=other_resources,
            intervention_duration=intervention_duration
        )

        # Save to Excel
        excel_path = os.path.join('media', 'public_area_fire_data.xlsx')

        # Create data dictionary
        excel_data = {
            'التاريخ': formatted_date,
            'الوحدات المتدخلة': intervening_unit,
            'عدد الحرائق': number_of_fires,
            'عدد التدخلات': number_of_interventions,
            'عدد الجرحى': number_of_injured,
            'عدد الوفيات': number_of_deaths,
            'إسم المؤسسة': institution_name,
            'النوع': institution_type,
            'الصنف': institution_category,
            'سيارة إسعاف': ambulances,
            'شاحنة إطفاء': fire_trucks,
            'السلم الميكانيكي': mechanical_ladders,
            'وسائل أخرى': other_resources,
            'مدة التدخل': intervention_duration
        }

        # Use the utility function to save to Excel
        from .utils import save_to_excel
        # Pass the user and form type to organize files by wilaya
        success, message = save_to_excel(excel_path, excel_data, user=request.user, form_type='public_area_fires')

        if success:
            return JsonResponse({'status': 'success', 'message': 'تم حفظ البيانات بنجاح'})
        else:
            # If Excel save failed but database save succeeded, return partial success
            return JsonResponse({
                'status': 'partial_success',
                'message': f'تم حفظ البيانات في قاعدة البيانات ولكن حدثت مشكلة في حفظ ملف Excel: {message}'
            })

    # معالجة طلب POST
    post_response = handle_post_request(request, process_data)
    if post_response:
        return post_response

    return render(request, 'fires/public_area_fires.html', {'units': units})

@csrf_exempt
@login_required(login_url='login')
def forest_agricultural_fires_view(request):
    # Get the user's profile
    user = request.user
    is_admin = user.is_superuser or user.is_staff
    user_wilaya = user.userprofile.wilaya

    # Get intervention units - admins can see all units, regular users only see their wilaya's units
    if is_admin:
        units = InterventionUnit.objects.all().order_by('wilaya', 'name')
    else:
        units = InterventionUnit.objects.filter(wilaya=user_wilaya).order_by('name')

    def process_data(data):
        # Extract data from the request
        date = data.get('date')
        formatted_date = data.get('formatted_date', date)
        municipality = data.get('municipality')
        intervening_unit = data.get('intervening_unit')
        fire_type = data.get('fire_type')

        # Fire statistics
        number_of_fires = data.get('number_of_fires')
        number_of_interventions = data.get('number_of_interventions')

        # Losses based on fire type
        losses_hectare = data.get('losses_hectare')
        losses_are = data.get('losses_are')
        losses_square_meter = data.get('losses_square_meter')
        losses_count = data.get('losses_count')

        # Loss type description
        loss_type = data.get('loss_type')

        # Validate required fields
        missing_fields = []
        if not date: missing_fields.append('التاريخ')
        if not municipality: missing_fields.append('البلدية')
        if not intervening_unit: missing_fields.append('الوحدات المتدخلة')
        if not fire_type: missing_fields.append('نوع الحريق')
        if number_of_fires is None: missing_fields.append('عدد الحرائق')
        if number_of_interventions is None: missing_fields.append('عدد التدخلات')

        # Validate losses based on fire type
        if fire_type in ['حرائق الغابات و الأدغال', 'حرائق المحاصيل', 'حرائق الأعشاب']:
            if losses_hectare is None: missing_fields.append('الخسائر بالهكتار')
            if losses_are is None: missing_fields.append('الخسائر بالآر')
            if losses_square_meter is None: missing_fields.append('الخسائر بالمتر مربع')
        elif fire_type in ['حرائق الأعلاف', 'حرائق النخيل', 'حرائق الأشجار المثمرة']:
            if losses_count is None: missing_fields.append('عدد الخسائر')

        if not loss_type: missing_fields.append('نوع الخسائر')

        if missing_fields:
            return JsonResponse({
                'status': 'error',
                'message': 'يرجى ملء الحقول التالية: ' + '، '.join(missing_fields)
            })

        # Save to database
        ForestAgriculturalFireData.objects.create(
            date=date,
            municipality=municipality,
            intervening_unit=intervening_unit,
            fire_type=fire_type,
            number_of_fires=number_of_fires,
            number_of_interventions=number_of_interventions,
            losses_hectare=losses_hectare,
            losses_are=losses_are,
            losses_square_meter=losses_square_meter,
            losses_count=losses_count,
            loss_type=loss_type
        )

        # Save to Excel
        excel_path = os.path.join('media', 'forest_agricultural_fire_data.xlsx')

        # Create data dictionary
        excel_data = {
            'التاريخ': formatted_date,
            'البلدية': municipality,
            'الوحدات المتدخلة': intervening_unit,
            'نوع الحريق': fire_type,
            'عدد الحرائق': number_of_fires,
            'عدد التدخلات': number_of_interventions,
            'نوع الخسائر': loss_type
        }

        # Add losses based on fire type
        if fire_type in ['حرائق الغابات و الأدغال', 'حرائق المحاصيل', 'حرائق الأعشاب']:
            excel_data['الخسائر بالهكتار'] = losses_hectare
            excel_data['الخسائر بالآر'] = losses_are
            excel_data['الخسائر بالمتر مربع'] = losses_square_meter
        elif fire_type in ['حرائق الأعلاف', 'حرائق النخيل', 'حرائق الأشجار المثمرة']:
            excel_data['عدد الخسائر'] = losses_count

        # Use the utility function to save to Excel
        from .utils import save_to_excel
        # Pass the user and form type to organize files by wilaya
        success, message = save_to_excel(excel_path, excel_data, user=request.user, form_type='forest_agricultural_fires')

        if success:
            return JsonResponse({'status': 'success', 'message': 'تم حفظ البيانات بنجاح'})
        else:
            # If Excel save failed but database save succeeded, return partial success
            return JsonResponse({
                'status': 'partial_success',
                'message': f'تم حفظ البيانات في قاعدة البيانات ولكن حدثت مشكلة في حفظ ملف Excel: {message}'
            })

    # معالجة طلب POST
    post_response = handle_post_request(request, process_data)
    if post_response:
        return post_response

    return render(request, 'fires/forest_agricultural_fires.html', {'units': units})

@login_required(login_url='login')
def misc_operations_view(request):
    return render(request, 'misc_operations/index.html')

@csrf_exempt
@login_required(login_url='login')
def misc_operations_stats_view(request):
    # Get the user's profile
    user = request.user
    is_admin = user.is_superuser or user.is_staff
    user_wilaya = user.userprofile.wilaya

    # Get intervention units - admins can see all units, regular users only see their wilaya's units
    if is_admin:
        units = InterventionUnit.objects.all().order_by('wilaya', 'name')
    else:
        units = InterventionUnit.objects.filter(wilaya=user_wilaya).order_by('name')

    def process_data(data):
        # Extract data from the request
        date = data.get('date')
        formatted_date = data.get('formatted_date', date)
        intervening_unit = data.get('intervening_unit')
        operation_type = data.get('operation_type')
        number_of_operations = data.get('number_of_operations')
        number_of_interventions = data.get('number_of_interventions')

        # Validate required fields
        missing_fields = []
        if not date: missing_fields.append('التاريخ')
        if not intervening_unit: missing_fields.append('الوحدات المتدخلة')
        if not operation_type: missing_fields.append('أنواع العمليات')
        if number_of_operations is None: missing_fields.append('عدد العمليات')
        if number_of_interventions is None: missing_fields.append('عدد التدخلات')

        # Validate human losses fields based on operation type
        if operation_type in ['نقل الجثث', 'أشخاص دون مأوى']:
            number_of_women = data.get('number_of_women')
            number_of_men = data.get('number_of_men')
            number_of_children = data.get('number_of_children')

            if number_of_women is None: missing_fields.append('عدد النساء')
            if number_of_men is None: missing_fields.append('عدد الرجال')
            if number_of_children is None: missing_fields.append('عدد الأطفال')
        else:
            number_of_rescuers = data.get('number_of_rescuers')
            number_of_deaths = data.get('number_of_deaths')

            if number_of_rescuers is None: missing_fields.append('عدد المسعفين')
            if number_of_deaths is None: missing_fields.append('عدد الوفيات')

        if missing_fields:
            return JsonResponse({
                'status': 'error',
                'message': 'يرجى ملء الحقول التالية: ' + '، '.join(missing_fields)
            })

        # Create model instance with common fields
        misc_operation = MiscOperationsData(
            date=date,
            intervening_unit=intervening_unit,
            operation_type=operation_type,
            number_of_operations=number_of_operations,
            number_of_interventions=number_of_interventions
        )

        # Add human losses data based on operation type
        if operation_type in ['نقل الجثث', 'أشخاص دون مأوى']:
            misc_operation.number_of_women = number_of_women
            misc_operation.number_of_men = number_of_men
            misc_operation.number_of_children = number_of_children
        else:
            misc_operation.number_of_rescuers = number_of_rescuers
            misc_operation.number_of_deaths = number_of_deaths

        # Save to database
        misc_operation.save()

        # Save to Excel
        excel_path = os.path.join('media', 'misc_operations_data.xlsx')

        # Create data dictionary
        excel_data = {
            'التاريخ': formatted_date,
            'الوحدات المتدخلة': intervening_unit,
            'أنواع العمليات': operation_type,
            'عدد العمليات': number_of_operations,
            'عدد التدخلات': number_of_interventions
        }

        # Add human losses data based on operation type
        if operation_type in ['نقل الجثث', 'أشخاص دون مأوى']:
            excel_data['عدد النساء'] = number_of_women
            excel_data['عدد الرجال'] = number_of_men
            excel_data['عدد الأطفال'] = number_of_children
        else:
            excel_data['عدد المسعفين'] = number_of_rescuers
            excel_data['عدد الوفيات'] = number_of_deaths

        # Use the utility function to save to Excel
        from .utils import save_to_excel
        # Pass the user and form type to organize files by wilaya
        success, message = save_to_excel(excel_path, excel_data, user=request.user, form_type='misc_operations')

        if success:
            return JsonResponse({'status': 'success', 'message': 'تم حفظ البيانات بنجاح'})
        else:
            # If Excel save failed but database save succeeded, return partial success
            return JsonResponse({
                'status': 'partial_success',
                'message': f'تم حفظ البيانات في قاعدة البيانات ولكن حدثت مشكلة في حفظ ملف Excel: {message}'
            })

    # معالجة طلب POST
    post_response = handle_post_request(request, process_data)
    if post_response:
        return post_response

    return render(request, 'misc_operations/stats.html', {'units': units})

@csrf_exempt
@login_required(login_url='login')
def security_device_stats_view(request):
    # Get the user's profile
    user = request.user
    is_admin = user.is_superuser or user.is_staff
    user_wilaya = user.userprofile.wilaya

    # Get intervention units - admins can see all units, regular users only see their wilaya's units
    if is_admin:
        units = InterventionUnit.objects.all().order_by('wilaya', 'name')
    else:
        units = InterventionUnit.objects.filter(wilaya=user_wilaya).order_by('name')

    def process_data(data):
        # Extract data from the request
        date = data.get('date')
        formatted_date = data.get('formatted_date', date)
        intervening_unit = data.get('intervening_unit')
        security_device_type = data.get('security_device_type')
        number_of_operations = data.get('number_of_operations')
        number_of_interventions = data.get('number_of_interventions')
        number_of_rescuers = data.get('number_of_rescuers')
        number_of_deaths = data.get('number_of_deaths')

        # Validate required fields
        missing_fields = []
        if not date: missing_fields.append('التاريخ')
        if not intervening_unit: missing_fields.append('الوحدات المتدخلة')
        if not security_device_type: missing_fields.append('أنواع الأجهزة الأمنية')
        if number_of_operations is None: missing_fields.append('عدد العمليات')
        if number_of_interventions is None: missing_fields.append('عدد التدخلات')
        if number_of_rescuers is None: missing_fields.append('عدد المسعفين')
        if number_of_deaths is None: missing_fields.append('عدد الوفيات')

        if missing_fields:
            return JsonResponse({
                'status': 'error',
                'message': 'يرجى ملء الحقول التالية: ' + '، '.join(missing_fields)
            })

        # Save to database - using MiscOperationsData model with operation_type set to security device type
        MiscOperationsData.objects.create(
            date=date,
            intervening_unit=intervening_unit,
            operation_type=security_device_type,  # Using security_device_type as operation_type
            number_of_operations=number_of_operations,
            number_of_interventions=number_of_interventions,
            number_of_rescuers=number_of_rescuers,
            number_of_deaths=number_of_deaths
        )

        # Save to Excel - using a separate file for security device stats
        excel_path = os.path.join('media', 'security_device_stats_data.xlsx')

        # Create data dictionary
        excel_data = {
            'التاريخ': formatted_date,
            'الوحدات المتدخلة': intervening_unit,
            'أنواع الأجهزة الأمنية': security_device_type,
            'عدد العمليات': number_of_operations,
            'عدد التدخلات': number_of_interventions,
            'عدد المسعفين': number_of_rescuers,
            'عدد الوفيات': number_of_deaths
        }

        # Use the utility function to save to Excel
        from .utils import save_to_excel
        # Pass the user and form type to organize files by wilaya
        success, message = save_to_excel(excel_path, excel_data, user=request.user, form_type='security_device')

        if success:
            return JsonResponse({'status': 'success', 'message': 'تم حفظ البيانات بنجاح'})
        else:
            # If Excel save failed but database save succeeded, return partial success
            return JsonResponse({
                'status': 'partial_success',
                'message': f'تم حفظ البيانات في قاعدة البيانات ولكن حدثت مشكلة في حفظ ملف Excel: {message}'
            })

    # معالجة طلب POST
    post_response = handle_post_request(request, process_data)
    if post_response:
        return post_response

    return render(request, 'misc_operations/security_device_stats.html', {'units': units})

@csrf_exempt
@login_required(login_url='login')
def exceptional_operations_stats_view(request):
    # Get the user's profile
    user = request.user
    is_admin = user.is_superuser or user.is_staff
    user_wilaya = user.userprofile.wilaya

    # Get intervention units - admins can see all units, regular users only see their wilaya's units
    if is_admin:
        units = InterventionUnit.objects.all().order_by('wilaya', 'name')
    else:
        units = InterventionUnit.objects.filter(wilaya=user_wilaya).order_by('name')

    def process_data(data):
        # Extract data from the request
        date = data.get('date')
        formatted_date = data.get('formatted_date', date)
        intervening_unit = data.get('intervening_unit')
        operation_type = data.get('operation_type')
        number_of_operations = data.get('number_of_operations')
        number_of_interventions = data.get('number_of_interventions')

        # Validate required fields
        missing_fields = []
        if not date: missing_fields.append('التاريخ')
        if not intervening_unit: missing_fields.append('الوحدات المتدخلة')
        if not operation_type: missing_fields.append('أنواع العمليات الإستثنائية')
        if number_of_operations is None: missing_fields.append('عدد العمليات')
        if number_of_interventions is None: missing_fields.append('عدد التدخلات')

        # Validate resources if animal rescue is selected
        if operation_type == 'إنقاذ الحيوانات':
            ambulances = data.get('ambulances')
            fire_trucks = data.get('fire_trucks')
            mechanical_ladders = data.get('mechanical_ladders')
            other_resources = data.get('other_resources')

            if ambulances is None: missing_fields.append('سيارة إسعاف')
            if fire_trucks is None: missing_fields.append('شاحنة إطفاء')
            if mechanical_ladders is None: missing_fields.append('السلم الميكانيكي')
            if other_resources is None: missing_fields.append('أخرى')

        if missing_fields:
            return JsonResponse({
                'status': 'error',
                'message': 'يرجى ملء الحقول التالية: ' + '، '.join(missing_fields)
            })

        # Create model instance with common fields
        misc_operation = MiscOperationsData(
            date=date,
            intervening_unit=intervening_unit,
            operation_type=operation_type,
            number_of_operations=number_of_operations,
            number_of_interventions=number_of_interventions
        )

        # Add resources data if animal rescue is selected
        if operation_type == 'إنقاذ الحيوانات':
            misc_operation.ambulances = ambulances
            misc_operation.fire_trucks = fire_trucks
            misc_operation.mechanical_ladders = mechanical_ladders
            misc_operation.other_resources = other_resources

        # Save to database
        misc_operation.save()

        # Save to Excel - using a separate file for exceptional operations stats
        excel_path = os.path.join('media', 'exceptional_operations_stats_data.xlsx')

        # Create data dictionary
        excel_data = {
            'التاريخ': formatted_date,
            'الوحدات المتدخلة': intervening_unit,
            'أنواع العمليات الإستثنائية': operation_type,
            'عدد العمليات': number_of_operations,
            'عدد التدخلات': number_of_interventions
        }

        # Add resources data if animal rescue is selected
        if operation_type == 'إنقاذ الحيوانات':
            excel_data['سيارة إسعاف'] = ambulances
            excel_data['شاحنة إطفاء'] = fire_trucks
            excel_data['السلم الميكانيكي'] = mechanical_ladders
            excel_data['أخرى'] = other_resources

        # Use the utility function to save to Excel
        from .utils import save_to_excel
        # Pass the user and form type to organize files by wilaya
        success, message = save_to_excel(excel_path, excel_data, user=request.user, form_type='exceptional_operations')

        if success:
            return JsonResponse({'status': 'success', 'message': 'تم حفظ البيانات بنجاح'})
        else:
            # If Excel save failed but database save succeeded, return partial success
            return JsonResponse({
                'status': 'partial_success',
                'message': f'تم حفظ البيانات في قاعدة البيانات ولكن حدثت مشكلة في حفظ ملف Excel: {message}'
            })

    # معالجة طلب POST
    post_response = handle_post_request(request, process_data)
    if post_response:
        return post_response

    return render(request, 'misc_operations/exceptional_operations_stats.html', {'units': units})

@csrf_exempt
@login_required(login_url='login')
def interventions_without_work_stats_view(request):
    # Get the user's profile
    user = request.user
    is_admin = user.is_superuser or user.is_staff
    user_wilaya = user.userprofile.wilaya

    # Get intervention units - admins can see all units, regular users only see their wilaya's units
    if is_admin:
        units = InterventionUnit.objects.all().order_by('wilaya', 'name')
    else:
        units = InterventionUnit.objects.filter(wilaya=user_wilaya).order_by('name')

    def process_data(data):
        # Extract data from the request
        date = data.get('date')
        formatted_date = data.get('formatted_date', date)
        intervening_unit = data.get('intervening_unit')
        intervention_type = data.get('intervention_type')
        operation_count = data.get('operation_count')

        # Resources used
        cars = data.get('cars')
        trucks = data.get('trucks')
        other_resources = data.get('other_resources')

        # Validate required fields
        missing_fields = []
        if not date: missing_fields.append('التاريخ')
        if not intervening_unit: missing_fields.append('الوحدات المتدخلة')
        if not intervention_type: missing_fields.append('أنواع التدخلات بدون عمل')
        if operation_count is None: missing_fields.append('عدد العمليات')
        if cars is None: missing_fields.append('سيارة')
        if trucks is None: missing_fields.append('شاحنة')
        if other_resources is None: missing_fields.append('أخرى')

        if missing_fields:
            return JsonResponse({
                'status': 'error',
                'message': 'يرجى ملء الحقول التالية: ' + '، '.join(missing_fields)
            })

        # Create model instance with common fields
        misc_operation = MiscOperationsData(
            date=date,
            intervening_unit=intervening_unit,
            operation_type=intervention_type,
            number_of_operations=operation_count,
            number_of_interventions=0,  # For interventions without work, set interventions to 0
            cars=cars,
            trucks=trucks,
            other_resources=other_resources
        )

        # Save to database
        misc_operation.save()

        # Save to Excel - using a separate file for interventions without work stats
        excel_path = os.path.join('media', 'interventions_without_work_stats_data.xlsx')

        # Create data dictionary
        excel_data = {
            'التاريخ': formatted_date,
            'الوحدات المتدخلة': intervening_unit,
            'أنواع التدخلات بدون عمل': intervention_type,
            'إجمالي عدد العمليات': operation_count,
            'سيارة': cars,
            'شاحنة': trucks,
            'أخرى': other_resources
        }

        # Use the utility function to save to Excel
        from .utils import save_to_excel
        # Pass the user and form type to organize files by wilaya
        success, message = save_to_excel(excel_path, excel_data, user=request.user, form_type='interventions_without_work')

        if success:
            return JsonResponse({'status': 'success', 'message': 'تم حفظ البيانات بنجاح'})
        else:
            # If Excel save failed but database save succeeded, return partial success
            return JsonResponse({
                'status': 'partial_success',
                'message': f'تم حفظ البيانات في قاعدة البيانات ولكن حدثت مشكلة في حفظ ملف Excel: {message}'
            })

    # معالجة طلب POST
    post_response = handle_post_request(request, process_data)
    if post_response:
        return post_response

    return render(request, 'misc_operations/interventions_without_work_stats.html', {'units': units})

@login_required
@user_passes_test(is_admin)
def settings_view(request):
    try:
        profile = request.user.userprofile
        if profile.role != 'admin':
            messages.error(request, 'صلاحية الدخول مرفوضة! يلزم حساب مسؤول')
            return redirect('home')

        if not profile.wilaya:
            messages.warning(request, 'الرجاء تعيين ولاية للمستخدم أولاً')

    except UserProfile.DoesNotExist:
        messages.error(request, 'الملف الشخصي غير موجود!')
        return redirect('home')
    except Exception as e:
        messages.error(request, f'حدث خطأ تقني: {str(e)}')
        return redirect('home')

    users = User.objects.select_related('userprofile').all()
    units = InterventionUnit.objects.all().order_by('wilaya', 'name')
    user_form = UserCreationFormWithWilaya()
    unit_form = InterventionUnitForm()

    if request.method == 'POST':
        # Handle user creation
        if 'create_user' in request.POST:
            user_form = UserCreationFormWithWilaya(request.POST)
            if user_form.is_valid():
                user = user_form.save()
                UserProfile.objects.create(
                    user=user,
                    wilaya=user_form.cleaned_data['wilaya'],
                    role=user_form.cleaned_data['role']
                )
                messages.success(request, 'تم إنشاء المستخدم بنجاح')
                return redirect('settings')

        # Handle unit creation
        elif 'create_unit' in request.POST:
            unit_form = InterventionUnitForm(request.POST)
            if unit_form.is_valid():
                unit = unit_form.save(commit=False)
                unit.created_by = request.user

                # Check if this is Souk Ahras (41)
                if unit.wilaya == '41':
                    # Create default units if they don't exist
                    default_units = InterventionUnit.get_souk_ahras_defaults()
                    for default_unit in default_units:
                        InterventionUnit.objects.get_or_create(
                            wilaya='41',
                            code=default_unit['code'],
                            defaults={
                                'name': default_unit['name'],
                                'is_default': True
                            }
                        )

                unit.save()
                messages.success(request, 'تم إضافة الوحدة المتدخلة بنجاح')
                return redirect('settings')

    context = {
        'users': users,
        'units': units,
        'user_form': user_form,
        'unit_form': unit_form,
        'wilaya_choices': WILAYA_CHOICES
    }
    return render(request, 'settings/index.html', context)

@login_required
@user_passes_test(is_admin)
def delete_unit(request, unit_id):
    if request.method == 'POST':
        try:
            unit = InterventionUnit.objects.get(id=unit_id)
            if not unit.is_default:  # Only allow deletion of non-default units
                unit.delete()
                return JsonResponse({'status': 'success'})
            return JsonResponse({'status': 'error', 'message': 'لا يمكن حذف الوحدات الافتراضية'})
        except InterventionUnit.DoesNotExist:
            return JsonResponse({'status': 'error', 'message': 'الوحدة غير موجودة'})
    return JsonResponse({'status': 'error', 'message': 'طريقة غير مسموح بها'})

@login_required(login_url='login')
def coordination_center_view(request):
    return render(request, 'coordination_center/index.html')

@login_required(login_url='login')
def data_entry_forms_view(request):
    return render(request, 'coordination_center/data_entry_forms.html')

@login_required(login_url='login')
def telegram_forms_view(request):
    return render(request, 'coordination_center/telegram_forms.html')

@login_required(login_url='login')
def daily_telegrams_view(request):
    return render(request, 'coordination_center/daily_telegrams.html')

@login_required(login_url='login')
def urgent_telegrams_view(request):
    return render(request, 'coordination_center/urgent_telegrams.html')

@login_required(login_url='login')
def daily_interventions_view(request):
    """صفحة التدخلات اليومية مع التزامن"""
    from .models import DailyIntervention, UnitEquipment, VehicleInterventionStatus, InterventionUnit
    from datetime import date

    # الحصول على الوحدة الحالية للمستخدم
    user_profile = getattr(request.user, 'userprofile', None)
    if user_profile and user_profile.intervention_units.exists():
        current_unit = user_profile.intervention_units.first()
    else:
        current_unit = InterventionUnit.objects.first()

    # الحصول على جميع التدخلات لليوم الحالي (وليس فقط المكتملة)
    today_interventions = DailyIntervention.objects.filter(
        unit=current_unit,
        date=date.today()
    ).order_by('-created_at')

    # الحصول على الوسائل المتاحة (متزامنة من صفحة الجاهزية)
    available_vehicles = UnitEquipment.objects.filter(
        unit=current_unit,
        is_active=True
    ).exclude(
        intervention_status__status='in_intervention',
        intervention_status__date=date.today()
    )

    context = {
        'current_unit': current_unit,
        'today_interventions': today_interventions,
        'available_vehicles': available_vehicles,
    }

    return render(request, 'coordination_center/daily_interventions.html', context)

@login_required(login_url='login')
def forest_crop_fires_view(request):
    return render(request, 'coordination_center/forest_crop_fires.html')

@login_required(login_url='login')
def water_leaks_view(request):
    return render(request, 'coordination_center/water_leaks.html')

@csrf_exempt
@login_required(login_url='login')
def forest_fires_form_view(request):
    # Get the user's profile
    user = request.user
    is_admin = user.is_superuser or user.is_staff
    user_wilaya = user.userprofile.wilaya

    # Get intervention units - admins can see all units, regular users only see their wilaya's units
    if is_admin:
        units = InterventionUnit.objects.all().order_by('wilaya', 'name')
    else:
        units = InterventionUnit.objects.filter(wilaya=user_wilaya).order_by('name')

    if request.method == 'POST':
        try:
            import json
            data = json.loads(request.body)

            # Format date
            date_str = data.get('date')
            from datetime import datetime
            formatted_date = datetime.strptime(date_str, '%Y-%m-%d').date()

            # Create forest fire record
            forest_fire = CoordinationCenterForestFire.objects.create(
                telegram_number=data.get('telegram_number'),
                date=formatted_date,
                intervention_time=data.get('intervention_time'),
                intervening_unit=data.get('intervening_unit'),
                municipality=data.get('municipality'),
                location_name=data.get('location_name'),
                operation_duration=data.get('operation_duration'),
                intervention_means=data.get('intervention_means'),
                loss_nature=data.get('loss_nature'),
                losses_hectare=data.get('losses_hectare', 0),
                losses_are=data.get('losses_are', 0),
                losses_square_meter=data.get('losses_square_meter', 0),
                other_loss_nature=data.get('other_loss_nature', ''),
                other_loss_count=data.get('other_loss_count', 0),
                # New fields
                fire_control_status=data.get('fire_control_status', 'أخمد نهائيا'),
                injured_count=data.get('injured_count', 0),
                deaths_count=data.get('deaths_count', 0),
                evacuated_families_count=data.get('evacuated_families_count', 0),
                evacuated_people_count=data.get('evacuated_people_count', 0),
                evacuation_locations=data.get('evacuation_locations', ''),
                family_care_measures=data.get('family_care_measures', '')
            )

            # Save to Excel
            excel_path = os.path.join('media', 'coordination_center_forest_fires.xlsx')

            # Create data dictionary for Excel
            excel_data = {
                'رقم البرقية': data.get('telegram_number'),
                'التاريخ': data.get('formatted_date', date_str),
                'ساعة التدخل': data.get('intervention_time'),
                'الوحدة المتدخلة': data.get('intervening_unit'),
                'البلدية': data.get('municipality'),
                'المكان المسمى': data.get('location_name'),
                'مدة العملية': data.get('operation_duration'),
                'الوسائل المتدخلة': data.get('intervention_means'),
                'طبيعة الخسائر': data.get('loss_nature'),
                'الخسائر بالهكتار': data.get('losses_hectare', 0),
                'الخسائر بالآر': data.get('losses_are', 0),
                'الخسائر بالمتر مربع': data.get('losses_square_meter', 0),
                'طبيعة الخسائر الأخرى': data.get('other_loss_nature', ''),
                'عدد الخسائر': data.get('other_loss_count', 0)
            }

            # Use the utility function to save to Excel
            from .utils import save_to_excel
            success, message = save_to_excel(excel_path, excel_data, user=request.user, form_type='coordination_center_forest_fires')

            if success:
                return JsonResponse({
                    'status': 'success',
                    'message': 'تم حفظ بيانات حريق الغابات بنجاح'
                })
            else:
                # If Excel save failed but database save succeeded, return partial success
                return JsonResponse({
                    'status': 'partial_success',
                    'message': f'تم حفظ البيانات في قاعدة البيانات ولكن حدثت مشكلة في حفظ ملف Excel: {message}'
                })

        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': f'حدث خطأ أثناء حفظ البيانات: {str(e)}'
            })

    context = {
        'units': units,
        'user': user,
        'is_admin': is_admin,
        'user_wilaya': user_wilaya
    }

    return render(request, 'coordination_center/forest_fires_form.html', context)

@csrf_exempt
@login_required(login_url='login')
def crop_fires_form_view(request):
    # Get the user's profile
    user = request.user
    is_admin = user.is_superuser or user.is_staff

    # Get user wilaya safely
    user_wilaya = None
    if hasattr(user, 'userprofile') and user.userprofile:
        user_wilaya = user.userprofile.wilaya

    # Get intervention units for the user's wilaya
    if is_admin:
        units = InterventionUnit.objects.all()
    else:
        units = InterventionUnit.objects.filter(wilaya=user_wilaya) if user_wilaya else InterventionUnit.objects.none()

    if request.method == 'POST':
        try:
            import json
            data = json.loads(request.body)
            print(f"DEBUG: Received crop fire data: {data}")

            # Parse date
            date_str = data.get('date')
            from datetime import datetime
            formatted_date = datetime.strptime(date_str, '%Y-%m-%d').date()
            print(f"DEBUG: Parsed date: {formatted_date}")

            # Create crop fire record
            print(f"DEBUG: Creating crop fire with data: {data}")
            crop_fire = CoordinationCenterCropFire.objects.create(
                telegram_number=data.get('telegram_number'),
                date=formatted_date,
                intervention_time=data.get('intervention_time'),
                intervening_unit=data.get('intervening_unit'),
                municipality=data.get('municipality'),
                location_name=data.get('location_name'),
                operation_duration=data.get('operation_duration'),
                intervention_means=data.get('intervention_means', ''),
                loss_nature=data.get('loss_nature', ''),
                losses_hectare=data.get('losses_hectare', 0),
                losses_are=data.get('losses_are', 0),
                losses_square_meter=data.get('losses_square_meter', 0),
                other_loss_nature=data.get('other_loss_nature', ''),
                other_loss_count=data.get('other_loss_count', 0),
                # New fields
                fire_control_status=data.get('fire_control_status', 'أخمد نهائيا'),
                injured_count=data.get('injured_count', 0),
                deaths_count=data.get('deaths_count', 0),
                evacuated_families_count=data.get('evacuated_families_count', 0),
                evacuated_people_count=data.get('evacuated_people_count', 0),
                evacuation_locations=data.get('evacuation_locations', ''),
                family_care_measures=data.get('family_care_measures', '')
            )

            print(f"DEBUG: Crop fire created successfully with ID: {crop_fire.id}")
            print(f"DEBUG: Total crop fires in database: {CoordinationCenterCropFire.objects.count()}")

            # Save to Excel
            excel_path = os.path.join('media', 'coordination_center_crop_fires.xlsx')

            # Create data dictionary for Excel
            excel_data = {
                'رقم البرقية': data.get('telegram_number'),
                'التاريخ': data.get('formatted_date', date_str),
                'ساعة التدخل': data.get('intervention_time'),
                'الوحدة المتدخلة': data.get('intervening_unit'),
                'البلدية': data.get('municipality'),
                'المكان المسمى': data.get('location_name'),
                'مدة العملية': data.get('operation_duration'),
                'الوسائل المتدخلة': data.get('intervention_means'),
                'طبيعة الخسائر': data.get('loss_nature'),
                'الخسائر بالهكتار': data.get('losses_hectare', 0),
                'الخسائر بالآر': data.get('losses_are', 0),
                'الخسائر بالمتر مربع': data.get('losses_square_meter', 0),
                'طبيعة الخسائر الأخرى': data.get('other_loss_nature', ''),
                'عدد الخسائر': data.get('other_loss_count', 0)
            }

            # Use the utility function to save to Excel
            from .utils import save_to_excel
            success, message = save_to_excel(excel_path, excel_data, user=request.user, form_type='coordination_center_crop_fires')

            if success:
                return JsonResponse({
                    'status': 'success',
                    'message': 'تم حفظ بيانات حريق المحاصيل الزراعية بنجاح'
                })
            else:
                # If Excel save failed but database save succeeded, return partial success
                return JsonResponse({
                    'status': 'partial_success',
                    'message': f'تم حفظ البيانات في قاعدة البيانات ولكن حدثت مشكلة في حفظ ملف Excel: {message}'
                })

        except Exception as e:
            print(f"DEBUG: Error creating crop fire: {str(e)}")
            import traceback
            traceback.print_exc()
            return JsonResponse({
                'status': 'error',
                'message': f'حدث خطأ أثناء حفظ البيانات: {str(e)}'
            })

    context = {
        'units': units,
        'user': user,
        'is_admin': is_admin,
        'user_wilaya': user_wilaya
    }

    return render(request, 'coordination_center/crop_fires_form.html', context)

@login_required(login_url='login')
def forest_crop_tables_view(request):
    return render(request, 'coordination_center/forest_crop_tables.html')

@login_required(login_url='login')
def forest_fires_table_view(request):
    # Get the user's profile
    user = request.user
    is_admin = user.is_superuser or user.is_staff
    user_wilaya = user.userprofile.wilaya

    # Get forest fires data - admins can see all data, regular users only see their wilaya's data
    if is_admin:
        forest_fires_data = CoordinationCenterForestFire.objects.all().order_by('-date', '-telegram_number')
    else:
        # Filter by user's wilaya through the intervening unit
        user_units = InterventionUnit.objects.filter(wilaya=user_wilaya).values_list('name', flat=True)
        forest_fires_data = CoordinationCenterForestFire.objects.filter(
            intervening_unit__in=user_units
        ).order_by('-date', '-telegram_number')

    # Format data for display
    formatted_data = []
    for item in forest_fires_data:
        # Get wilaya info from the intervening unit
        try:
            unit = InterventionUnit.objects.get(name=item.intervening_unit)
            wilaya_code = unit.wilaya
            # Get the wilaya name from the choices
            from .models import WILAYA_CHOICES
            wilaya_name = dict(WILAYA_CHOICES).get(unit.wilaya, 'غير محدد')
        except InterventionUnit.DoesNotExist:
            wilaya_name = "غير محدد"
            wilaya_code = "00"

        # Format date in Arabic
        arabic_months = {
            1: 'جانفي', 2: 'فيفري', 3: 'مارس', 4: 'أفريل',
            5: 'ماي', 6: 'جوان', 7: 'جويلية', 8: 'أوت',
            9: 'سبتمبر', 10: 'أكتوبر', 11: 'نوفمبر', 12: 'ديسمبر'
        }
        formatted_date = f"{item.date.day} {arabic_months[item.date.month]} {item.date.year}"

        formatted_item = {
            'id': item.id,
            'date': item.date,
            'formatted_date': formatted_date,
            'telegram_number': item.telegram_number,
            'intervention_time': item.intervention_time,
            'intervening_unit': item.intervening_unit,
            'municipality': item.municipality,
            'location_name': item.location_name,
            'operation_duration': item.operation_duration,
            'intervention_means': item.intervention_means.replace('/n', '\n') if item.intervention_means else '',
            'loss_nature': item.loss_nature.replace('/n', '\n') if item.loss_nature else '',
            'losses_hectare': item.losses_hectare,
            'losses_are': item.losses_are,
            'losses_square_meter': item.losses_square_meter,
            'other_loss_nature': item.other_loss_nature.replace('/n', '\n') if item.other_loss_nature else '',
            'other_loss_count': item.other_loss_count,
            # New fields
            'fire_control_status': item.fire_control_status,
            'injured_count': item.injured_count,
            'deaths_count': item.deaths_count,
            'evacuated_families_count': item.evacuated_families_count,
            'evacuated_people_count': item.evacuated_people_count,
            'evacuation_locations': item.evacuation_locations.replace('/n', '\n') if item.evacuation_locations else '',
            'family_care_measures': item.family_care_measures.replace('/n', '\n') if item.family_care_measures else '',
            'wilaya_name': wilaya_name,
            'wilaya_code': wilaya_code
        }
        formatted_data.append(formatted_item)

    # Get filter options
    if is_admin:
        units = CoordinationCenterForestFire.objects.values_list('intervening_unit', flat=True).distinct()
        municipalities = CoordinationCenterForestFire.objects.values_list('municipality', flat=True).distinct()
    else:
        units = CoordinationCenterForestFire.objects.filter(
            intervening_unit__in=user_units
        ).values_list('intervening_unit', flat=True).distinct()
        municipalities = CoordinationCenterForestFire.objects.filter(
            intervening_unit__in=user_units
        ).values_list('municipality', flat=True).distinct()

    # Get years and months for filtering
    years = CoordinationCenterForestFire.objects.dates('date', 'year').values_list('date__year', flat=True).distinct()
    months = [
        ('01', 'جانفي'), ('02', 'فيفري'), ('03', 'مارس'), ('04', 'أفريل'),
        ('05', 'ماي'), ('06', 'جوان'), ('07', 'جويلية'), ('08', 'أوت'),
        ('09', 'سبتمبر'), ('10', 'أكتوبر'), ('11', 'نوفمبر'), ('12', 'ديسمبر')
    ]

    # Get wilaya choices for admin
    wilaya_choices = []
    if is_admin:
        from .models import WILAYA_CHOICES
        wilaya_choices = list(WILAYA_CHOICES)
        wilaya_choices.sort(key=lambda x: int(x[0]))  # Sort by wilaya code

    context = {
        'forest_fires_data': formatted_data,
        'units': units,
        'municipalities': municipalities,
        'years': years,
        'months': months,
        'wilaya_choices': wilaya_choices,
        'is_admin': is_admin,
        'user_wilaya': user_wilaya
    }

    return render(request, 'coordination_center/forest_fires_table.html', context)

@login_required(login_url='login')
def crop_fires_table_view(request):
    # Get the user's profile
    user = request.user
    is_admin = user.is_superuser or user.is_staff

    # Get user wilaya safely
    user_wilaya = None
    if hasattr(user, 'userprofile') and user.userprofile:
        user_wilaya = user.userprofile.wilaya

    print(f"DEBUG: Crop fires table view - User: {user.username}, Admin: {is_admin}, Wilaya: {user_wilaya}")

    # Get crop fires data - admins can see all data, regular users only see their wilaya's data
    if is_admin:
        crop_fires_data = CoordinationCenterCropFire.objects.all().order_by('-date', '-telegram_number')
        print(f"DEBUG: Admin user - Total crop fires: {crop_fires_data.count()}")
    else:
        if user_wilaya:
            # Filter by user's wilaya through the intervening unit
            user_units = InterventionUnit.objects.filter(wilaya=user_wilaya).values_list('name', flat=True)
            print(f"DEBUG: User units: {list(user_units)}")
            crop_fires_data = CoordinationCenterCropFire.objects.filter(
                intervening_unit__in=user_units
            ).order_by('-date', '-telegram_number')
            print(f"DEBUG: Filtered crop fires for user: {crop_fires_data.count()}")
        else:
            # User has no wilaya, show no data
            crop_fires_data = CoordinationCenterCropFire.objects.none()
            print(f"DEBUG: User has no wilaya, showing no data")

        # Also check all crop fires to see if any exist
        all_crop_fires = CoordinationCenterCropFire.objects.all()
        print(f"DEBUG: Total crop fires in database: {all_crop_fires.count()}")
        if all_crop_fires.exists():
            for fire in all_crop_fires:
                print(f"DEBUG: Crop fire - ID: {fire.id}, Unit: {fire.intervening_unit}, Date: {fire.date}")

    # Get unique values for filters
    if is_admin:
        units = InterventionUnit.objects.all().values_list('name', flat=True).distinct()
        municipalities = CoordinationCenterCropFire.objects.values_list('municipality', flat=True).distinct()
    else:
        if user_wilaya:
            user_units = InterventionUnit.objects.filter(wilaya=user_wilaya).values_list('name', flat=True)
            units = user_units
            municipalities = CoordinationCenterCropFire.objects.filter(
                intervening_unit__in=user_units
            ).values_list('municipality', flat=True).distinct()
        else:
            units = []
            municipalities = []

    # Get unique years and months from the data
    years = crop_fires_data.dates('date', 'year', order='DESC')
    years = [year.year for year in years]

    # Arabic months for filter
    months = [
        ('01', 'جانفي'), ('02', 'فيفري'), ('03', 'مارس'), ('04', 'أفريل'),
        ('05', 'ماي'), ('06', 'جوان'), ('07', 'جويلية'), ('08', 'أوت'),
        ('09', 'سبتمبر'), ('10', 'أكتوبر'), ('11', 'نوفمبر'), ('12', 'ديسمبر')
    ]

    # Wilaya choices for admin users
    wilaya_choices = []
    if is_admin:
        from .models import WILAYA_CHOICES
        wilaya_choices = WILAYA_CHOICES

    # Format data for display
    formatted_data = []
    for item in crop_fires_data:
        # Get wilaya info from the intervening unit
        try:
            unit = InterventionUnit.objects.get(name=item.intervening_unit)
            wilaya_code = unit.wilaya
            # Get the wilaya name from the choices
            from .models import WILAYA_CHOICES
            wilaya_name = dict(WILAYA_CHOICES).get(unit.wilaya, 'غير محدد')
        except InterventionUnit.DoesNotExist:
            wilaya_name = "غير محدد"
            wilaya_code = "00"

        # Format date in Arabic
        arabic_months = {
            1: 'جانفي', 2: 'فيفري', 3: 'مارس', 4: 'أفريل',
            5: 'ماي', 6: 'جوان', 7: 'جويلية', 8: 'أوت',
            9: 'سبتمبر', 10: 'أكتوبر', 11: 'نوفمبر', 12: 'ديسمبر'
        }
        formatted_date = f"{item.date.day} {arabic_months[item.date.month]} {item.date.year}"

        formatted_item = {
            'id': item.id,
            'date': item.date,
            'formatted_date': formatted_date,
            'telegram_number': item.telegram_number,
            'intervention_time': item.intervention_time,
            'intervening_unit': item.intervening_unit,
            'municipality': item.municipality,
            'location_name': item.location_name,
            'operation_duration': item.operation_duration,
            'intervention_means': item.intervention_means.replace('/n', '\n') if item.intervention_means else '',
            'loss_nature': item.loss_nature.replace('/n', '\n') if item.loss_nature else '',
            'losses_hectare': item.losses_hectare,
            'losses_are': item.losses_are,
            'losses_square_meter': item.losses_square_meter,
            'other_loss_nature': item.other_loss_nature.replace('/n', '\n') if item.other_loss_nature else '',
            'other_loss_count': item.other_loss_count,
            # New fields
            'fire_control_status': item.fire_control_status,
            'injured_count': item.injured_count,
            'deaths_count': item.deaths_count,
            'evacuated_families_count': item.evacuated_families_count,
            'evacuated_people_count': item.evacuated_people_count,
            'evacuation_locations': item.evacuation_locations.replace('/n', '\n') if item.evacuation_locations else '',
            'family_care_measures': item.family_care_measures.replace('/n', '\n') if item.family_care_measures else '',
            'wilaya_name': wilaya_name,
            'wilaya_code': wilaya_code
        }

        formatted_data.append(formatted_item)

    # Debug information
    print(f"DEBUG: Total crop fires found: {crop_fires_data.count()}")
    print(f"DEBUG: User wilaya: {user_wilaya}")
    print(f"DEBUG: Is admin: {is_admin}")
    print(f"DEBUG: Formatted data count: {len(formatted_data)}")

    context = {
        'crop_fires_data': formatted_data,
        'units': units,
        'municipalities': municipalities,
        'years': years,
        'months': months,
        'wilaya_choices': wilaya_choices,
        'is_admin': is_admin,
        'user_wilaya': user_wilaya
    }

    return render(request, 'coordination_center/crop_fires_table.html', context)

@csrf_exempt
@login_required(login_url='login')
def export_coordination_forest_fires_excel(request):
    if request.method == 'POST':
        try:
            import json
            from django.http import HttpResponse
            from openpyxl import Workbook
            from openpyxl.styles import Font, Alignment, PatternFill
            from openpyxl.utils import get_column_letter
            import io

            data = json.loads(request.body)
            export_data = data.get('data', [])

            if not export_data:
                return JsonResponse({'error': 'لا توجد بيانات للتصدير'}, status=400)

            # Create workbook
            wb = Workbook()
            ws = wb.active
            ws.title = "حرائق الغابات - مركز التنسيق"

            # Get user info
            user = request.user
            user_wilaya = user.userprofile.wilaya if hasattr(user, 'userprofile') else None

            # Get wilaya name
            wilaya_name = "جميع الولايات"
            if user_wilaya:
                from .models import WILAYA_CHOICES
                wilaya_name = dict(WILAYA_CHOICES).get(user_wilaya, user_wilaya)

            # Add title with black bold font
            ws.merge_cells('A1:O1')
            title_cell = ws['A1']
            title_cell.value = f"جدول حرائق الغابات - مركز التنسيق - {wilaya_name}"
            title_cell.font = Font(name='Arial', size=16, bold=True, color='000000')  # Black font
            title_cell.alignment = Alignment(horizontal='center', vertical='center')
            title_cell.fill = PatternFill(start_color='E6E6FA', end_color='E6E6FA', fill_type='solid')  # Light background

            # Headers
            headers = [
                'التاريخ', 'رقم البرقية', 'ساعة التدخل', 'الوحدة المتدخلة', 'البلدية',
                'المكان المسمى', 'مدة العملية', 'الوسائل المتدخلة', 'طبيعة الخسائر',
                'الخسائر بالهكتار', 'الخسائر بالآر', 'الخسائر بالمتر مربع',
                'طبيعة الخسائر الأخرى', 'عدد الخسائر'
            ]

            # Add headers with black bold fonts and proper background
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=3, column=col)
                cell.value = header
                cell.font = Font(name='Arial', size=12, bold=True, color='000000')  # Black font
                cell.fill = PatternFill(start_color='E6E6FA', end_color='E6E6FA', fill_type='solid')  # Light background
                cell.alignment = Alignment(horizontal='center', vertical='center')

            # Add data
            last_row = 4
            for row_idx, item in enumerate(export_data, 4):
                ws.cell(row=row_idx, column=1, value=item.get('التاريخ', ''))
                ws.cell(row=row_idx, column=2, value=item.get('رقم البرقية', ''))
                ws.cell(row=row_idx, column=3, value=item.get('ساعة التدخل', ''))
                ws.cell(row=row_idx, column=4, value=item.get('الوحدة المتدخلة', ''))
                ws.cell(row=row_idx, column=5, value=item.get('البلدية', ''))
                ws.cell(row=row_idx, column=6, value=item.get('المكان المسمى', ''))
                ws.cell(row=row_idx, column=7, value=item.get('مدة العملية', ''))
                ws.cell(row=row_idx, column=8, value=item.get('الوسائل المتدخلة', ''))
                ws.cell(row=row_idx, column=9, value=item.get('طبيعة الخسائر', ''))

                # Convert numeric values for calculations
                try:
                    hectare_val = float(str(item.get('الخسائر بالهكتار', '0')).replace(',', '') or 0)
                except:
                    hectare_val = 0
                try:
                    are_val = float(str(item.get('الخسائر بالآر', '0')).replace(',', '') or 0)
                except:
                    are_val = 0
                try:
                    sqm_val = float(str(item.get('الخسائر بالمتر مربع', '0')).replace(',', '') or 0)
                except:
                    sqm_val = 0
                try:
                    loss_count_val = int(str(item.get('عدد الخسائر', '0')).replace(',', '') or 0)
                except:
                    loss_count_val = 0

                ws.cell(row=row_idx, column=10, value=hectare_val)
                ws.cell(row=row_idx, column=11, value=are_val)
                ws.cell(row=row_idx, column=12, value=sqm_val)
                ws.cell(row=row_idx, column=13, value=item.get('طبيعة الخسائر الأخرى', ''))
                ws.cell(row=row_idx, column=14, value=loss_count_val)

                # Style data cells with RTL alignment
                for col in range(1, 15):
                    cell = ws.cell(row=row_idx, column=col)
                    cell.font = Font(name='Arial', size=11)
                    if col in [10, 11, 12, 14]:  # Numeric columns
                        cell.alignment = Alignment(horizontal='center', vertical='center')
                    else:  # Text columns - RTL
                        cell.alignment = Alignment(horizontal='right', vertical='center', reading_order=2)

                last_row = row_idx

            # Add sum row
            if export_data:
                sum_row = last_row + 1
                ws.cell(row=sum_row, column=1, value="المجموع")

                # Calculate sums for numeric columns
                ws.cell(row=sum_row, column=10, value=f"=SUM(J4:J{last_row})")  # Hectares
                ws.cell(row=sum_row, column=11, value=f"=SUM(K4:K{last_row})")  # Ares
                ws.cell(row=sum_row, column=12, value=f"=SUM(L4:L{last_row})")  # Square meters
                ws.cell(row=sum_row, column=14, value=f"=SUM(N4:N{last_row})")  # Loss count

                # Style sum row with amber background and black bold fonts
                for col in range(1, 15):
                    cell = ws.cell(row=sum_row, column=col)
                    cell.font = Font(name='Arial', size=12, bold=True, color='000000')  # Black font
                    cell.fill = PatternFill(start_color='FFC000', end_color='FFC000', fill_type='solid')  # Amber background
                    if col in [10, 11, 12, 14]:  # Numeric columns
                        cell.alignment = Alignment(horizontal='center', vertical='center')
                    else:  # Text columns
                        cell.alignment = Alignment(horizontal='right', vertical='center', reading_order=2)

            # Auto-adjust column widths
            for col in range(1, 15):
                column_letter = get_column_letter(col)
                max_length = 0
                for row in ws[column_letter]:
                    try:
                        if len(str(row.value)) > max_length:
                            max_length = len(str(row.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width

            # Save to memory
            output = io.BytesIO()
            wb.save(output)
            output.seek(0)

            # Create response
            response = HttpResponse(
                output.getvalue(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = 'attachment; filename="حرائق_الغابات_مركز_التنسيق.xlsx"'

            return response

        except Exception as e:
            return JsonResponse({'error': f'حدث خطأ أثناء تصدير البيانات: {str(e)}'}, status=500)

    return JsonResponse({'error': 'طريقة غير مسموحة'}, status=405)

@login_required(login_url='login')
def daily_telegrams_view(request):
    return render(request, 'coordination_center/daily_telegrams.html')

@login_required(login_url='login')
def urgent_telegrams_view(request):
    return render(request, 'coordination_center/urgent_telegrams.html')



@login_required(login_url='login')
def forest_crop_fires_view(request):
    return render(request, 'coordination_center/forest_crop_fires.html')

@login_required(login_url='login')
def water_leaks_view(request):
    return render(request, 'coordination_center/water_leaks.html')

@login_required(login_url='login')
def advanced_morning_check_view(request):
    """
    نظام التحقق الصباحي المتقدم - Advanced Morning Verification System
    نظام مستقل شامل لإدارة التعداد الصباحي والتحقق من الجاهزية
    """
    from django.db.models import Q, Count, Case, When, IntegerField
    from datetime import datetime, timedelta
    import json

    # Get user information
    user = request.user
    is_admin = user.is_superuser or user.is_staff

    # Get user profile safely
    user_wilaya = None
    user_role = 'unit_coordinator'  # default role
    user_unit = None

    if hasattr(user, 'userprofile') and user.userprofile:
        user_wilaya = user.userprofile.wilaya
        user_role = user.userprofile.role
        if user_role == 'unit_coordinator':
            # Get the first assigned unit for unit coordinators
            user_units = user.userprofile.intervention_units.all()
            user_unit = user_units.first() if user_units.exists() else None

    # Get selected unit and date
    selected_unit_id = request.GET.get('unit_id')
    selected_date_str = request.GET.get('date')

    # Parse selected date or use today
    if selected_date_str:
        try:
            selected_date = datetime.strptime(selected_date_str, '%Y-%m-%d').date()
        except ValueError:
            selected_date = timezone.now().date()
    else:
        selected_date = timezone.now().date()

    # Get available units based on user role
    if is_admin:
        units = InterventionUnit.objects.all().order_by('name')
    elif user_role == 'unit_coordinator' and user_unit:
        # Unit coordinators can only see their assigned units
        units = user.userprofile.intervention_units.all().order_by('name')
    elif user_wilaya:
        units = InterventionUnit.objects.filter(wilaya=user_wilaya).order_by('name')
    else:
        units = InterventionUnit.objects.none()

    # Determine selected unit
    selected_unit = None
    if user_role == 'unit_coordinator' and user_unit:
        selected_unit = user_unit
        selected_unit_id = str(user_unit.id)
    elif selected_unit_id:
        try:
            selected_unit = units.get(id=selected_unit_id)
        except InterventionUnit.DoesNotExist:
            selected_unit = None

    # If no unit selected and user has access to units, select first one
    if not selected_unit and units.exists():
        selected_unit = units.first()
        selected_unit_id = str(selected_unit.id)

    # Initialize context
    context = {
        'units': units,
        'selected_unit': selected_unit,
        'selected_date': selected_date,
        'selected_unit_id': selected_unit_id,
        'user': user,
        'is_admin': is_admin,
        'user_wilaya': user_wilaya,
        'user_role': user_role,
        'user_unit': user_unit,
    }

    if not selected_unit:
        return render(request, 'morning_check/index.html', context)

    # Get or create daily count
    daily_count, created = DailyUnitCount.objects.get_or_create(
        unit=selected_unit,
        date=selected_date,
        defaults={'created_by': user}
    )

    # Get personnel and equipment data
    unit_personnel = UnitPersonnel.objects.filter(unit=selected_unit).order_by('full_name')
    unit_equipment = UnitEquipment.objects.filter(unit=selected_unit).order_by('equipment_type', 'serial_number')

    # Get daily status for personnel and equipment
    personnel_with_status = []
    for person in unit_personnel:
        status, created = DailyPersonnelStatus.objects.get_or_create(
            personnel=person,
            date=selected_date,
            defaults={'status': 'present', 'notes': '', 'updated_by': user}
        )
        person.daily_status = status
        personnel_with_status.append(person)

    equipment_with_status = []
    for item in unit_equipment:
        status, created = DailyEquipmentStatus.objects.get_or_create(
            equipment=item,
            date=selected_date,
            defaults={'status': 'operational', 'notes': '', 'updated_by': user}
        )
        item.daily_status = status
        equipment_with_status.append(item)

    # Get or create morning summary
    morning_summary, created = MorningCheckSummary.objects.get_or_create(
        unit=selected_unit,
        date=selected_date,
        defaults={
            'created_by': user,
            'total_personnel': unit_personnel.count(),
            'present_personnel': 0,
            'total_equipment': unit_equipment.count(),
            'operational_equipment': 0,
            'readiness_score': 0
        }
    )

    # Calculate statistics
    present_count = sum(1 for p in personnel_with_status if p.daily_status.status == 'present')
    absent_count = sum(1 for p in personnel_with_status if p.daily_status.status == 'absent')
    mission_count = sum(1 for p in personnel_with_status if p.daily_status.status == 'on_mission')

    operational_count = sum(1 for e in equipment_with_status if e.daily_status.status == 'operational')
    broken_count = sum(1 for e in equipment_with_status if e.daily_status.status == 'broken')
    maintenance_count = sum(1 for e in equipment_with_status if e.daily_status.status == 'maintenance')

    # Calculate readiness score
    total_personnel = unit_personnel.count()
    total_equipment = unit_equipment.count()

    if total_personnel > 0 and total_equipment > 0:
        personnel_readiness = (present_count / total_personnel) * 100 * 0.4
        equipment_readiness = (operational_count / total_equipment) * 100 * 0.4
        assignment_readiness = 20  # Default assignment score
        readiness_score = int(personnel_readiness + equipment_readiness + assignment_readiness)
    else:
        readiness_score = 0

    # Update morning summary
    morning_summary.total_personnel = total_personnel
    morning_summary.present_personnel = present_count
    morning_summary.total_equipment = total_equipment
    morning_summary.operational_equipment = operational_count
    morning_summary.readiness_score = readiness_score
    morning_summary.save()

    # Get available shifts
    available_shifts = WorkShift.objects.filter(unit=selected_unit).order_by('name')

    # Get daily schedule - only if there are available shifts
    daily_schedule = None
    if available_shifts.exists():
        try:
            daily_schedule = DailyShiftSchedule.objects.get(
                unit=selected_unit,
                date=selected_date
            )
        except DailyShiftSchedule.DoesNotExist:
            # Create with first available shift as default
            daily_schedule = DailyShiftSchedule.objects.create(
                unit=selected_unit,
                date=selected_date,
                active_shift=available_shifts.first(),
                created_by=user
            )

    # Get eight hour personnel
    eight_hour_personnel = EightHourPersonnel.objects.filter(
        unit=selected_unit,
        date=selected_date
    ).order_by('full_name')

    # Get active alerts
    active_alerts = ReadinessAlert.objects.filter(
        unit=selected_unit,
        date=selected_date,
        status='active'
    ).order_by('-priority', '-created_at')

    # إحصائيات الفرق الثلاث
    shift_stats = {}
    for shift_choice in UnitPersonnel.SHIFT_CHOICES:
        shift_key = shift_choice[0]
        shift_name = shift_choice[1]
        # تصفية القائمة بدلاً من استخدام filter على QuerySet
        shift_personnel = [p for p in personnel_with_status if p.assigned_shift == shift_key]
        present_personnel = [p for p in shift_personnel if hasattr(p, 'daily_status') and p.daily_status.status == 'present']

        shift_stats[shift_key] = {
            'name': shift_name,
            'total_count': len(shift_personnel),
            'present_count': len(present_personnel),
            'personnel_list': shift_personnel
        }

    # إحصائيات نظام العمل
    work_system_stats = {
        '24_hours': personnel_with_status.filter(work_system='24_hours').count(),
        '8_hours': personnel_with_status.filter(work_system='8_hours').count(),
    }

    # Update context with all data
    context.update({
        'daily_count': daily_count,
        'unit_personnel': personnel_with_status,
        'unit_equipment': equipment_with_status,
        'morning_summary': morning_summary,
        'available_shifts': available_shifts,
        'daily_schedule': daily_schedule,
        'eight_hour_personnel': eight_hour_personnel,
        'active_alerts': active_alerts,
        'present_count': present_count,
        'absent_count': absent_count,
        'mission_count': mission_count,
        'operational_count': operational_count,
        'broken_count': broken_count,
        'maintenance_count': maintenance_count,
        'readiness_score': readiness_score,
        # البيانات الجديدة
        'shift_stats': shift_stats,
        'work_system_stats': work_system_stats,
        'shift_choices': UnitPersonnel.SHIFT_CHOICES,
        'gender_choices': UnitPersonnel.GENDER_CHOICES,
        'work_system_choices': UnitPersonnel.WORK_SYSTEM_CHOICES,
    })

    return render(request, 'morning_check/index.html', context)


@login_required(login_url='login')
def unified_morning_check_view(request):
    """النظام الموحد للتحقق الصباحي"""
    from datetime import date, datetime
    from .models import (
        DailyUnitCount, UnitPersonnel, UnitEquipment,
        VehicleCrewAssignment, VehicleReadiness,
        DailyPersonnelStatus, DailyEquipmentStatus
    )

    user = request.user
    is_admin = user.is_superuser or user.is_staff

    # التحقق من الصلاحيات
    if not hasattr(user, 'userprofile'):
        messages.error(request, 'لم يتم العثور على ملف تعريف المستخدم')
        return redirect('home')

    user_profile = user.userprofile

    # الحصول على الوحدات المتاحة
    if is_admin:
        units = InterventionUnit.objects.all()
    elif user_profile.role == 'wilaya_manager':
        units = InterventionUnit.objects.filter(wilaya=user_profile.wilaya)
    elif user_profile.role in ['unit_manager', 'unit_coordinator']:
        units = user_profile.intervention_units.all()
    else:
        messages.error(request, 'ليس لديك صلاحية للوصول إلى هذه الصفحة')
        return redirect('home')

    # الحصول على الوحدة المحددة
    unit_id = request.GET.get('unit_id')
    selected_unit = None
    if unit_id:
        try:
            selected_unit = units.get(id=unit_id)
        except InterventionUnit.DoesNotExist:
            messages.error(request, 'الوحدة المحددة غير متاحة')

    # إذا لم تكن هناك وحدة محددة، اختر الأولى
    if not selected_unit and units.exists():
        selected_unit = units.first()

    if not selected_unit:
        return render(request, 'coordination_center/unified_morning_check.html', {
            'units': units,
            'error': 'لا توجد وحدات متاحة'
        })

    # الحصول على التاريخ
    today = date.today()
    selected_date_str = request.GET.get('date', today.strftime('%Y-%m-%d'))
    try:
        selected_date = datetime.strptime(selected_date_str, '%Y-%m-%d').date()
    except ValueError:
        selected_date = today

    # الحصول على أو إنشاء التعداد اليومي
    daily_count, created = DailyUnitCount.objects.get_or_create(
        unit=selected_unit,
        date=selected_date,
        defaults={'created_by': user}
    )

    # الحصول على الأعوان مع حالاتهم اليومية
    personnel = UnitPersonnel.objects.filter(
        unit=selected_unit,
        is_active=True
    ).order_by('full_name')

    personnel_with_status = []
    for person in personnel:
        status, created = DailyPersonnelStatus.objects.get_or_create(
            personnel=person,
            date=selected_date,
            defaults={'status': 'present', 'updated_by': user}
        )
        person.daily_status = status
        personnel_with_status.append(person)

    # الحصول على الوسائل مع حالاتها اليومية
    equipment = UnitEquipment.objects.filter(
        unit=selected_unit,
        is_active=True
    ).order_by('equipment_type', 'serial_number')

    equipment_with_status = []
    for item in equipment:
        status, created = DailyEquipmentStatus.objects.get_or_create(
            equipment=item,
            date=selected_date,
            defaults={'status': 'operational', 'updated_by': user}
        )
        item.daily_status = status

        # الحصول على جاهزية الوسيلة
        readiness, created = VehicleReadiness.objects.get_or_create(
            vehicle=item,
            date=selected_date
        )
        # إعادة حساب الجاهزية دائماً لضمان الدقة
        readiness.calculate_readiness_score()
        readiness.save()
        item.readiness = readiness

        equipment_with_status.append(item)

    # حساب الإحصائيات
    stats = calculate_unified_stats(selected_unit, selected_date, personnel_with_status, equipment_with_status)

    context = {
        'units': units,
        'selected_unit': selected_unit,
        'selected_date': selected_date,
        'daily_count': daily_count,
        'personnel': personnel_with_status,
        'equipment': equipment_with_status,
        'stats': stats,
        'is_admin': is_admin,
        'user_profile': user_profile,
        'today': today,
    }

    return render(request, 'coordination_center/unified_morning_check.html', context)


def calculate_unified_stats(unit, date, personnel, equipment):
    """حساب الإحصائيات الموحدة"""

    # إحصائيات الأعوان
    total_personnel = len(personnel)
    present_personnel = sum(1 for p in personnel if p.daily_status.status == 'present')
    absent_personnel = sum(1 for p in personnel if p.daily_status.status == 'absent')
    mission_personnel = sum(1 for p in personnel if p.daily_status.status == 'on_mission')

    # إحصائيات الفرق
    shift_stats = {}
    for shift_key, shift_name in UnitPersonnel.SHIFT_CHOICES:
        shift_personnel = [p for p in personnel if p.assigned_shift == shift_key]
        shift_present = sum(1 for p in shift_personnel if p.daily_status.status == 'present')

        shift_stats[shift_key] = {
            'name': shift_name,
            'total': len(shift_personnel),
            'present': shift_present,
            'personnel': shift_personnel
        }

    # إحصائيات الوسائل
    total_vehicles = len(equipment)
    ready_vehicles = sum(1 for e in equipment if e.readiness.status in ['ready', 'manually_confirmed'])
    not_ready_vehicles = sum(1 for e in equipment if e.readiness.status == 'not_ready')

    # إحصائيات الوسائل في التدخل
    from .models import VehicleInterventionStatus
    intervention_vehicles = VehicleInterventionStatus.objects.filter(
        vehicle__unit=unit,
        status='in_intervention',
        date=date
    ).count()

    # حساب الجاهزية العامة
    if total_personnel > 0 and total_vehicles > 0:
        personnel_readiness = (present_personnel / total_personnel) * 40
        vehicles_readiness = (ready_vehicles / total_vehicles) * 40

        # جاهزية التوزيع (20%)
        from .models import VehicleCrewAssignment
        assigned_vehicles = VehicleCrewAssignment.objects.filter(
            vehicle__unit=unit,
            assignment_date=date
        ).values('vehicle').distinct().count()

        assignment_readiness = (assigned_vehicles / total_vehicles) * 20 if total_vehicles > 0 else 0

        overall_readiness = int(personnel_readiness + vehicles_readiness + assignment_readiness)
    else:
        overall_readiness = 0

    return {
        'total_personnel': total_personnel,
        'present_personnel': present_personnel,
        'absent_personnel': absent_personnel,
        'mission_personnel': mission_personnel,
        'shift_stats': shift_stats,
        'total_vehicles': total_vehicles,
        'ready_vehicles': ready_vehicles,
        'not_ready_vehicles': not_ready_vehicles,
        'intervention_vehicles': intervention_vehicles,
        'overall_readiness': overall_readiness,
    }


@login_required(login_url='login')
def redirect_to_unified_system(request):
    """إعادة توجيه الصفحات القديمة للنظام الموحد الجديد"""
    unit_id = request.GET.get('unit_id', '')
    date = request.GET.get('date', '')

    # بناء URL الجديد
    redirect_url = '/coordination-center/unified-morning-check/'
    params = []

    if unit_id:
        params.append(f'unit_id={unit_id}')
    if date:
        params.append(f'date={date}')

    if params:
        redirect_url += '?' + '&'.join(params)

    return redirect(redirect_url)


@login_required(login_url='login')
@require_http_methods(["POST"])
def add_personnel_unified(request):
    """إضافة عون جديد - النظام الموحد"""
    try:
        data = json.loads(request.body)

        # التحقق من الصلاحيات
        user = request.user
        if not hasattr(user, 'userprofile'):
            return JsonResponse({'success': False, 'error': 'ملف تعريف المستخدم غير موجود'})

        # الحصول على الوحدة
        unit_id = data.get('unit_id')
        try:
            unit = InterventionUnit.objects.get(id=unit_id)
        except InterventionUnit.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'الوحدة غير موجودة'})

        # التحقق من رقم التسجيل
        registration_number = data.get('personnel_registration_number', '').strip()
        if not registration_number:
            return JsonResponse({'success': False, 'error': 'رقم التسجيل مطلوب'})

        if UnitPersonnel.objects.filter(personnel_registration_number=registration_number).exists():
            return JsonResponse({'success': False, 'error': 'رقم التسجيل موجود مسبقاً'})

        # إنشاء العون الجديد
        personnel = UnitPersonnel.objects.create(
            unit=unit,
            personnel_id=data.get('personnel_id', ''),
            full_name=data.get('full_name', '').strip(),
            rank=data.get('rank', ''),
            position=data.get('position', ''),
            personnel_registration_number=registration_number,
            gender=data.get('gender', ''),
            age=data.get('age') if data.get('age') else None,
            phone_number=data.get('phone_number', ''),
            work_system=data.get('work_system', '24_hours'),
            assigned_shift=data.get('assigned_shift', '') if data.get('work_system') == '24_hours' else None,
            created_by=user
        )

        return JsonResponse({
            'success': True,
            'message': 'تم إضافة العون بنجاح',
            'personnel_id': personnel.id
        })

    except json.JSONDecodeError:
        return JsonResponse({'success': False, 'error': 'خطأ في تنسيق البيانات'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': f'خطأ غير متوقع: {str(e)}'})


@login_required(login_url='login')
@require_http_methods(["POST"])
def update_personnel_status_unified(request):
    """تحديث حالة العون - النظام الموحد"""
    try:
        data = json.loads(request.body)

        personnel_id = data.get('personnel_id')
        new_status = data.get('status')
        date_str = data.get('date')

        if not all([personnel_id, new_status, date_str]):
            return JsonResponse({'success': False, 'error': 'بيانات ناقصة'})

        # الحصول على العون
        try:
            personnel = UnitPersonnel.objects.get(id=personnel_id)
        except UnitPersonnel.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'العون غير موجود'})

        # تحويل التاريخ
        try:
            date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return JsonResponse({'success': False, 'error': 'تنسيق التاريخ غير صحيح'})

        # تحديث الحالة
        status, created = DailyPersonnelStatus.objects.get_or_create(
            personnel=personnel,
            date=date_obj,
            defaults={'status': new_status, 'updated_by': request.user}
        )

        if not created:
            status.status = new_status
            status.updated_by = request.user
            status.save()

        return JsonResponse({
            'success': True,
            'message': 'تم تحديث حالة العون بنجاح'
        })

    except json.JSONDecodeError:
        return JsonResponse({'success': False, 'error': 'خطأ في تنسيق البيانات'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': f'خطأ غير متوقع: {str(e)}'})


@login_required(login_url='login')
@require_http_methods(["POST"])
def delete_personnel_unified(request):
    """حذف عون - النظام الموحد"""
    try:
        data = json.loads(request.body)
        personnel_id = data.get('personnel_id')

        if not personnel_id:
            return JsonResponse({'success': False, 'error': 'معرف العون مطلوب'})

        try:
            personnel = UnitPersonnel.objects.get(id=personnel_id)
        except UnitPersonnel.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'العون غير موجود'})

        # حذف العون (soft delete)
        personnel.is_active = False
        personnel.save()

        return JsonResponse({
            'success': True,
            'message': 'تم حذف العون بنجاح'
        })

    except json.JSONDecodeError:
        return JsonResponse({'success': False, 'error': 'خطأ في تنسيق البيانات'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': f'خطأ غير متوقع: {str(e)}'})


@csrf_exempt
@require_http_methods(["POST"])
def transfer_personnel_unified(request):
    """تحويل عون بين الفرق - النظام الموحد"""
    # التحقق من تسجيل الدخول
    if not request.user.is_authenticated:
        return JsonResponse({'success': False, 'error': 'يجب تسجيل الدخول أولاً'})

    try:
        data = json.loads(request.body)

        personnel_id = data.get('personnel_id')
        target_shift = data.get('target_shift')
        reason = data.get('reason', '').strip()

        if not all([personnel_id, target_shift]):
            return JsonResponse({'success': False, 'error': 'بيانات ناقصة'})

        try:
            personnel = UnitPersonnel.objects.get(id=personnel_id)
        except UnitPersonnel.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'العون غير موجود'})

        # التحقق من أن العون يعمل بنظام 24 ساعة
        if personnel.work_system != '24_hours':
            return JsonResponse({'success': False, 'error': 'لا يمكن تحويل أعوان نظام 8 ساعات بين الفرق'})

        # حفظ الفرقة القديمة
        old_shift = personnel.assigned_shift

        # تحديث الفرقة
        personnel.assigned_shift = target_shift
        personnel.save()

        # إنشاء سجل التحويل (إذا كان النموذج موجود)
        try:
            from .models import PersonnelTransfer
            PersonnelTransfer.objects.create(
                unit=personnel.unit,
                personnel=personnel,
                from_shift=old_shift or 'غير محدد',
                to_shift=target_shift,
                transfer_reason=reason,
                transfer_date=timezone.now().date(),
                status='completed',
                requested_by=request.user,
                approved_by=request.user
            )
        except ImportError:
            pass  # النموذج غير موجود

        return JsonResponse({
            'success': True,
            'message': 'تم تحويل العون بنجاح'
        })

    except json.JSONDecodeError:
        return JsonResponse({'success': False, 'error': 'خطأ في تنسيق البيانات'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': f'خطأ غير متوقع: {str(e)}'})


@csrf_exempt
@require_http_methods(["POST"])
def get_current_working_shift_info(request):
    """الحصول على معلومات الفرقة العاملة حالياً"""
    try:
        unit_id = request.GET.get('unit_id')
        if not unit_id:
            return JsonResponse({'success': False, 'error': 'معرف الوحدة مطلوب'})

        from .models import InterventionUnit, ShiftSchedule
        from django.utils import timezone

        try:
            unit = InterventionUnit.objects.get(id=unit_id)
        except InterventionUnit.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'الوحدة غير موجودة'})

        now = timezone.now()
        current_shift = ShiftSchedule.get_current_working_shift(unit, now)

        # الحصول على الجدولة الحالية
        current_schedule = ShiftSchedule.objects.filter(
            unit=unit,
            start_datetime__lte=now,
            end_datetime__gte=now,
            is_active=True
        ).first()

        shift_names = {
            'shift_1': 'الفرقة الأولى (A)',
            'shift_2': 'الفرقة الثانية (B)',
            'shift_3': 'الفرقة الثالثة (C)'
        }

        if current_schedule:
            return JsonResponse({
                'success': True,
                'current_shift': current_shift,
                'shift_display': shift_names.get(current_shift, 'غير محدد'),
                'start_time': current_schedule.start_datetime.strftime('%Y-%m-%d %H:%M'),
                'end_time': current_schedule.end_datetime.strftime('%Y-%m-%d %H:%M'),
                'remaining_hours': int((current_schedule.end_datetime - now).total_seconds() / 3600)
            })
        else:
            return JsonResponse({
                'success': True,
                'current_shift': None,
                'shift_display': 'لا توجد فرقة عاملة',
                'message': 'لم يتم تحديد جدولة للفرق'
            })

    except Exception as e:
        return JsonResponse({'success': False, 'error': f'خطأ: {str(e)}'})


@csrf_exempt
@require_http_methods(["POST"])
def change_work_system_unified(request):
    """تغيير نظام العمل للعون - النظام الموحد"""
    try:
        data = json.loads(request.body)

        personnel_id = data.get('personnel_id')
        new_work_system = data.get('new_work_system')

        if not all([personnel_id, new_work_system]):
            return JsonResponse({'success': False, 'error': 'بيانات ناقصة'})

        if new_work_system not in ['24_hours', '8_hours']:
            return JsonResponse({'success': False, 'error': 'نظام العمل غير صحيح'})

        try:
            personnel = UnitPersonnel.objects.get(id=personnel_id)
        except UnitPersonnel.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'العون غير موجود'})

        # حفظ النظام القديم
        old_system = personnel.work_system

        # تحديث نظام العمل
        personnel.work_system = new_work_system

        # إذا تم التغيير إلى نظام 8 ساعات، إزالة الفرقة
        if new_work_system == '8_hours':
            personnel.assigned_shift = None

        personnel.save()

        # إنشاء سجل للتغيير (اختياري)
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"تم تغيير نظام العمل للعون {personnel.full_name} من {old_system} إلى {new_work_system}")

        system_names = {
            '24_hours': 'نظام 24 ساعة',
            '8_hours': 'نظام 8 ساعات'
        }

        return JsonResponse({
            'success': True,
            'message': f'تم تغيير نظام العمل إلى {system_names[new_work_system]} بنجاح'
        })

    except json.JSONDecodeError:
        return JsonResponse({'success': False, 'error': 'خطأ في تنسيق البيانات'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': f'خطأ غير متوقع: {str(e)}'})


@csrf_exempt
@require_http_methods(["GET"])
def get_current_working_shift_info(request):
    """الحصول على معلومات الفرقة العاملة حالياً"""
    try:
        unit_id = request.GET.get('unit_id')
        if not unit_id:
            return JsonResponse({'success': False, 'error': 'معرف الوحدة مطلوب'})

        from .models import InterventionUnit, ShiftSchedule
        from django.utils import timezone

        try:
            unit = InterventionUnit.objects.get(id=unit_id)
        except InterventionUnit.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'الوحدة غير موجودة'})

        now = timezone.now()
        current_shift = ShiftSchedule.get_current_working_shift(unit, now)

        # الحصول على الجدولة الحالية
        current_schedule = ShiftSchedule.objects.filter(
            unit=unit,
            start_datetime__lte=now,
            end_datetime__gte=now,
            is_active=True
        ).first()

        shift_names = {
            'shift_1': 'الفرقة الأولى (A)',
            'shift_2': 'الفرقة الثانية (B)',
            'shift_3': 'الفرقة الثالثة (C)'
        }

        if current_schedule:
            return JsonResponse({
                'success': True,
                'current_shift': current_shift,
                'shift_display': shift_names.get(current_shift, 'غير محدد'),
                'start_time': current_schedule.start_datetime.strftime('%Y-%m-%d %H:%M'),
                'end_time': current_schedule.end_datetime.strftime('%Y-%m-%d %H:%M'),
                'remaining_hours': int((current_schedule.end_datetime - now).total_seconds() / 3600)
            })
        else:
            return JsonResponse({
                'success': True,
                'current_shift': None,
                'shift_display': 'لا توجد فرقة عاملة',
                'message': 'لم يتم تحديد جدولة للفرق'
            })

    except Exception as e:
        return JsonResponse({'success': False, 'error': f'خطأ: {str(e)}'})


@csrf_exempt
@login_required(login_url='login')
@require_http_methods(["GET"])
def get_personnel_work_status(request):
    """الحصول على حالة العمل للعون (عمل/راحة) مع التفاصيل"""
    try:
        personnel_id = request.GET.get('personnel_id')
        date_str = request.GET.get('date')
        status = request.GET.get('status')  # حالة الحضور الحالية

        if not all([personnel_id, date_str, status]):
            return JsonResponse({'success': False, 'error': 'بيانات ناقصة'})

        # الحصول على العون
        try:
            personnel = UnitPersonnel.objects.get(id=personnel_id)
        except UnitPersonnel.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'العون غير موجود'})

        # تحويل التاريخ
        try:
            date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return JsonResponse({'success': False, 'error': 'تنسيق التاريخ غير صحيح'})

        # تحديد حالة العمل بناءً على حالة الحضور
        if status == 'standby':
            # الاحتياطي دائماً متاح للتوزيع
            work_status = {
                'status': 'standby',
                'badge_class': 'badge-warning',
                'icon': 'fas fa-user-shield',
                'text': 'احتياطي',
                'sub_text': 'متاح للتوزيع'
            }
        elif status == 'present':
            # للحاضرين، نحتاج للتحقق من حالة العمل (عمل/راحة)
            work_status_today = personnel.get_work_status_today(date_obj)

            if work_status_today == 'working':
                work_status = {
                    'status': 'working',
                    'badge_class': 'badge-success',
                    'icon': 'fas fa-briefcase',
                    'text': 'قيد العمل',
                    'sub_text': f'نظام {personnel.get_work_system_display()}'
                }
            elif work_status_today == 'resting':
                work_status = {
                    'status': 'resting',
                    'badge_class': 'badge-secondary',
                    'icon': 'fas fa-bed',
                    'text': 'راحة',
                    'sub_text': 'حسب الجدولة'
                }
            elif work_status_today == 'unassigned':
                work_status = {
                    'status': 'unassigned',
                    'badge_class': 'badge-warning',
                    'icon': 'fas fa-question-circle',
                    'text': 'غير مخصص',
                    'sub_text': 'لا توجد فرقة'
                }
            else:
                work_status = {
                    'status': 'unknown',
                    'badge_class': 'badge-light',
                    'icon': 'fas fa-question-circle',
                    'text': 'غير محدد',
                    'sub_text': ''
                }
        elif status == 'absent':
            work_status = {
                'status': 'absent',
                'badge_class': 'badge-danger',
                'icon': 'fas fa-times',
                'text': 'غائب',
                'sub_text': 'غير متاح'
            }
        elif status == 'on_mission':
            work_status = {
                'status': 'on_mission',
                'badge_class': 'badge-info',
                'icon': 'fas fa-car',
                'text': 'في مهمة',
                'sub_text': 'غير متاح'
            }
        else:
            work_status = {
                'status': 'unknown',
                'badge_class': 'badge-secondary',
                'icon': 'fas fa-question-circle',
                'text': 'غير محدد',
                'sub_text': ''
            }

        return JsonResponse({
            'success': True,
            'work_status': work_status,
            'personnel_info': {
                'name': personnel.full_name,
                'work_system': personnel.work_system,
                'assigned_shift': personnel.assigned_shift,
                'shift_display': personnel.get_shift_display_arabic()
            }
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': f'خطأ: {str(e)}'})


@csrf_exempt
@require_http_methods(["POST"])
def create_shift_schedule(request):
    """إنشاء جدولة الفرق"""
    try:
        data = json.loads(request.body)
        unit_id = data.get('unit_id')
        start_date_str = data.get('start_date')

        if not all([unit_id, start_date_str]):
            return JsonResponse({'success': False, 'error': 'بيانات ناقصة'})

        from .models import InterventionUnit, ShiftSchedule
        from datetime import datetime

        try:
            unit = InterventionUnit.objects.get(id=unit_id)
        except InterventionUnit.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'الوحدة غير موجودة'})

        start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()

        # إنشاء دورة الفرق
        ShiftSchedule.create_shift_cycle(unit, start_date, request.user)

        return JsonResponse({
            'success': True,
            'message': 'تم إنشاء جدولة الفرق بنجاح'
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': f'خطأ: {str(e)}'})


@csrf_exempt
@require_http_methods(["POST"])
def update_wilaya_shifts(request):
    """تحديث جدولة الفرق لجميع وحدات الولاية"""
    try:
        data = json.loads(request.body)
        unit_id = data.get('unit_id')
        target_date_str = data.get('date')
        working_shift = data.get('shift')

        if not all([unit_id, target_date_str, working_shift]):
            return JsonResponse({'success': False, 'error': 'جميع المعاملات مطلوبة'})

        from .models import InterventionUnit, ShiftSchedule, UnitPersonnel, UnitEquipment
        from datetime import datetime, timedelta
        from django.utils import timezone

        # الحصول على الوحدة الحالية لمعرفة الولاية
        try:
            current_unit = InterventionUnit.objects.get(id=unit_id)
        except InterventionUnit.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'الوحدة غير موجودة'})

        # الحصول على جميع وحدات نفس الولاية
        wilaya_units = InterventionUnit.objects.filter(wilaya=current_unit.wilaya)

        target_date = datetime.strptime(target_date_str, '%Y-%m-%d').date()
        target_datetime = timezone.make_aware(datetime.combine(target_date, datetime.min.time().replace(hour=8)))
        end_datetime = target_datetime + timedelta(hours=24)

        updated_units = []

        # تحديث جميع وحدات الولاية
        for unit in wilaya_units:
            # البحث عن الجدولة الموجودة
            schedule = ShiftSchedule.objects.filter(
                unit=unit,
                start_datetime__date=target_date
            ).first()

            if schedule:
                schedule.working_shift = working_shift
                schedule.save()
            else:
                # إنشاء جدولة جديدة
                ShiftSchedule.objects.create(
                    unit=unit,
                    working_shift=working_shift,
                    start_datetime=target_datetime,
                    end_datetime=end_datetime,
                    created_by=request.user
                )

            updated_units.append(unit.name)

        # إحصائيات إضافية
        total_personnel = 0
        total_equipment = 0

        for unit in wilaya_units:
            # عدد الأعوان في كل وحدة
            personnel_count = UnitPersonnel.objects.filter(unit=unit, is_active=True).count()
            total_personnel += personnel_count

            # عدد الوسائل في كل وحدة
            equipment_count = UnitEquipment.objects.filter(unit=unit, is_active=True).count()
            total_equipment += equipment_count

        return JsonResponse({
            'success': True,
            'message': f'تم تحديث جدولة {len(updated_units)} وحدة في الولاية',
            'updated_units': updated_units,
            'wilaya': current_unit.get_wilaya_display(),
            'statistics': {
                'total_units': len(updated_units),
                'total_personnel': total_personnel,
                'total_equipment': total_equipment,
                'shift_display': dict(ShiftSchedule.SHIFT_CHOICES).get(working_shift, working_shift)
            }
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': f'خطأ: {str(e)}'})


@csrf_exempt
@require_http_methods(["GET"])
def get_work_systems_info(request):
    """قراءة معلومات أنظمة العمل من الملفات"""
    try:
        import os
        from django.conf import settings

        # مسارات الملفات
        work_systems_file = os.path.join(settings.BASE_DIR, 'التعداد الصباحي المحدث', 'أنظمة العمل داخل الوحدة.md')
        memory_file = os.path.join(settings.BASE_DIR, 'التعداد الصباحي المحدث', 'Memory_check.md')

        work_systems_content = ""
        memory_content = ""

        # قراءة ملف أنظمة العمل
        if os.path.exists(work_systems_file):
            with open(work_systems_file, 'r', encoding='utf-8') as f:
                work_systems_content = f.read()

        # قراءة ملف الذاكرة
        if os.path.exists(memory_file):
            with open(memory_file, 'r', encoding='utf-8') as f:
                memory_content = f.read()

        # استخراج المعلومات المهمة
        shift_info = {
            'shift_24_48': {
                'name': 'نظام 24/48 ساعة',
                'description': 'كل فصيلة تعمل 24 ساعة ثم ترتاح 48 ساعة',
                'shifts': ['فصيلة A', 'فصيلة B', 'فصيلة C'],
                'work_hours': '24 ساعة متواصلة',
                'rest_hours': '48 ساعة راحة'
            },
            'shift_8_hours': {
                'name': 'نظام 8 ساعات',
                'description': 'للأعوان الإداريين والدعم التقني',
                'shifts': ['فترة صباحية', 'فترة مسائية', 'فترة ليلية'],
                'work_hours': '8 ساعات يومياً',
                'work_days': 'الأحد إلى الخميس'
            }
        }

        return JsonResponse({
            'success': True,
            'shift_info': shift_info,
            'work_systems_content': work_systems_content[:1000] + '...' if len(work_systems_content) > 1000 else work_systems_content,
            'memory_summary': memory_content[:500] + '...' if len(memory_content) > 500 else memory_content,
            'last_updated': '19 يوليو 2025'
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': f'خطأ في قراءة الملفات: {str(e)}'})


@csrf_exempt
@require_http_methods(["POST"])
def create_yearly_schedule(request):
    """إنشاء جدولة سنوية شاملة"""
    try:
        data = json.loads(request.body)
        unit_id = data.get('unit_id')
        year = data.get('year')
        apply_to_wilaya = data.get('apply_to_wilaya', False)

        if not unit_id or not year:
            return JsonResponse({'success': False, 'error': 'معرف الوحدة والسنة مطلوبان'})

        from .models import InterventionUnit, ShiftSchedule

        try:
            unit = InterventionUnit.objects.get(id=unit_id)
        except InterventionUnit.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'الوحدة غير موجودة'})

        if apply_to_wilaya:
            # تطبيق على جميع وحدات الولاية
            wilaya_units = InterventionUnit.objects.filter(wilaya=unit.wilaya)
            results = []

            for wilaya_unit in wilaya_units:
                result = ShiftSchedule.create_yearly_schedule(wilaya_unit, int(year), request.user)
                results.append(f"{wilaya_unit.name}: {result}")

            return JsonResponse({
                'success': True,
                'message': f'تم إنشاء جدولة سنوية لجميع وحدات الولاية ({len(wilaya_units)} وحدة)',
                'results': results,
                'year': year,
                'wilaya': unit.get_wilaya_display()
            })
        else:
            # تطبيق على الوحدة المحددة فقط
            result = ShiftSchedule.create_yearly_schedule(unit, int(year), request.user)

            return JsonResponse({
                'success': True,
                'message': result,
                'year': year,
                'unit': unit.name
            })

    except Exception as e:
        return JsonResponse({'success': False, 'error': f'خطأ: {str(e)}'})


@csrf_exempt
@require_http_methods(["POST"])
def simple_shift_update(request):
    """تحديث بسيط للفرق - عملية واحدة موحدة"""
    try:
        data = json.loads(request.body)
        unit_id = data.get('unit_id')
        working_shift = data.get('working_shift')
        scope = data.get('scope', 'unit')  # unit, wilaya, system
        duration = data.get('duration', 'forever')  # today, month, year, forever

        if not unit_id or not working_shift:
            return JsonResponse({'success': False, 'error': 'معرف الوحدة والفرقة مطلوبان'})

        from .models import InterventionUnit, ShiftSchedule
        from datetime import datetime, timedelta
        from django.utils import timezone
        import calendar

        try:
            unit = InterventionUnit.objects.get(id=unit_id)
        except InterventionUnit.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'الوحدة غير موجودة'})

        # تحديد الوحدات المستهدفة
        if scope == 'system':
            target_units = InterventionUnit.objects.all()
        elif scope == 'wilaya':
            target_units = InterventionUnit.objects.filter(wilaya=unit.wilaya)
        else:
            target_units = [unit]

        # تحديد التواريخ المستهدفة
        today = datetime.now().date()

        if duration == 'today':
            start_date = today
            end_date = today
        elif duration == 'month':
            start_date = today.replace(day=1)
            end_date = today.replace(day=calendar.monthrange(today.year, today.month)[1])
        elif duration == 'year':
            start_date = today.replace(month=1, day=1)
            end_date = today.replace(month=12, day=31)
        else:  # forever
            start_date = today
            end_date = datetime(today.year + 10, 12, 31).date()

        # تطبيق التحديث
        shifts = ['shift_1', 'shift_2', 'shift_3']
        current_shift_index = shifts.index(working_shift)
        total_days = 0
        units_processed = []

        for target_unit in target_units:
            unit_days = 0
            shift_index = current_shift_index

            current_date = start_date
            while current_date <= end_date:
                shift_start = timezone.make_aware(datetime.combine(current_date, datetime.min.time().replace(hour=8)))
                shift_end = shift_start + timedelta(hours=24)

                # حذف الجدولة الموجودة
                ShiftSchedule.objects.filter(
                    unit=target_unit,
                    start_datetime__date=current_date
                ).delete()

                # إنشاء جدولة جديدة
                ShiftSchedule.objects.create(
                    unit=target_unit,
                    working_shift=shifts[shift_index],
                    start_datetime=shift_start,
                    end_datetime=shift_end,
                    created_by=request.user,
                    is_active=True
                )

                # تناوب الفرق يومياً
                shift_index = (shift_index + 1) % 3
                total_days += 1
                unit_days += 1
                current_date += timedelta(days=1)

            units_processed.append({
                'name': target_unit.name,
                'days': unit_days
            })

        # تحديد النطاق والمدة للرسالة
        scope_text = {
            'unit': 'الوحدة المحددة',
            'wilaya': f'جميع وحدات {unit.get_wilaya_display()}',
            'system': 'جميع وحدات النظام'
        }

        duration_text = {
            'today': 'اليوم فقط',
            'month': 'الشهر الحالي',
            'year': 'السنة الحالية',
            'forever': 'إلى ما لا نهاية'
        }

        return JsonResponse({
            'success': True,
            'message': f'تم تحديث الجدولة بنجاح!',
            'details': {
                'working_shift': dict(ShiftSchedule.SHIFT_CHOICES).get(working_shift),
                'scope': scope_text.get(scope),
                'duration': duration_text.get(duration),
                'units_count': len(target_units),
                'total_days': total_days,
                'start_date': start_date.strftime('%Y-%m-%d'),
                'end_date': end_date.strftime('%Y-%m-%d'),
                'units_processed': units_processed
            }
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': f'خطأ: {str(e)}'})


from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views.decorators.http import require_http_methods

def release_vehicle_crew_assignments(vehicle, date_obj, logger=None):
    """تحرير جميع الأعوان المعينين على وسيلة معطلة"""
    try:
        from .models import VehicleCrewAssignment

        # العثور على جميع الأعوان المعينين على هذه الوسيلة في هذا التاريخ
        active_assignments = VehicleCrewAssignment.objects.filter(
            vehicle=vehicle,
            assignment_date=date_obj,
            is_active=True
        )

        released_personnel = []
        for assignment in active_assignments:
            # تعطيل التعيين
            assignment.is_active = False
            assignment.save()
            released_personnel.append({
                'name': assignment.personnel.full_name,
                'role': assignment.get_role_display()
            })
            if logger:
                logger.info(f"تم تحرير العون {assignment.personnel.full_name} ({assignment.get_role_display()}) من الوسيلة {vehicle.equipment_type}")

        return released_personnel

    except Exception as e:
        if logger:
            logger.error(f"خطأ في تحرير الأعوان: {str(e)}")
        return []

@csrf_exempt
@require_http_methods(["POST"])
def update_equipment_status_unified(request):
    """تحديث حالة الوسيلة - النظام الموحد"""
    import logging
    logger = logging.getLogger(__name__)

    # إضافة headers للاستجابة JSON
    response_headers = {'Content-Type': 'application/json; charset=utf-8'}

    # التحقق من تسجيل الدخول
    if not request.user.is_authenticated:
        response = JsonResponse({'success': False, 'error': 'يجب تسجيل الدخول أولاً'})
        for key, value in response_headers.items():
            response[key] = value
        return response

    try:
        # تسجيل معلومات الطلب للتتبع
        logger.info(f"تحديث حالة الوسيلة - المستخدم: {request.user.username}")
        logger.info(f"محتوى الطلب: {request.body.decode('utf-8')}")

        data = json.loads(request.body)

        equipment_id = data.get('equipment_id')
        new_status = data.get('status')
        date_str = data.get('date')

        logger.info(f"البيانات المستلمة: equipment_id={equipment_id}, status={new_status}, date={date_str}")

        if not all([equipment_id, new_status, date_str]):
            return JsonResponse({'success': False, 'error': 'بيانات ناقصة'})

        try:
            equipment = UnitEquipment.objects.get(id=equipment_id)
            logger.info(f"الوسيلة موجودة: {equipment.serial_number}")
        except UnitEquipment.DoesNotExist:
            logger.error(f"الوسيلة غير موجودة: {equipment_id}")
            return JsonResponse({'success': False, 'error': 'الوسيلة غير موجودة'})

        try:
            date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            logger.error(f"تنسيق التاريخ غير صحيح: {date_str}")
            return JsonResponse({'success': False, 'error': 'تنسيق التاريخ غير صحيح'})

        # تحديث الحالة
        status, created = DailyEquipmentStatus.objects.get_or_create(
            equipment=equipment,
            date=date_obj,
            defaults={'status': new_status, 'updated_by': request.user}
        )

        if not created:
            old_status = status.status
            status.status = new_status
            status.updated_by = request.user
            status.save()
            logger.info(f"تم تحديث الحالة من {old_status} إلى {new_status}")
        else:
            logger.info(f"تم إنشاء حالة جديدة: {new_status}")

        # تحرير الأعوان المعينين إذا تم تعطيل الوسيلة
        released_personnel = []
        if new_status in ['broken', 'maintenance']:
            released_personnel = release_vehicle_crew_assignments(equipment, date_obj, logger)
            if released_personnel:
                logger.info(f"تم تحرير {len(released_personnel)} أعوان من الوسيلة المعطلة")

        # تحديث جاهزية الوسيلة
        try:
            readiness, created = VehicleReadiness.objects.get_or_create(
                vehicle=equipment,
                date=date_obj
            )
            readiness.calculate_readiness_score()
            readiness.save()
            logger.info("تم تحديث جاهزية الوسيلة")
        except Exception as e:
            logger.warning(f"خطأ في تحديث الجاهزية: {str(e)}")

        # رسالة النجاح مع معلومات إضافية
        success_message = 'تم تحديث حالة الوسيلة بنجاح'
        if released_personnel:
            success_message += f' وتم تحرير {len(released_personnel)} أعوان'
            # إضافة تفاصيل الأعوان المحررين للسجل
            personnel_names = [p['name'] for p in released_personnel]
            logger.info(f"الأعوان المحررين: {', '.join(personnel_names)}")

        return JsonResponse({
            'success': True,
            'message': success_message,
            'released_personnel_count': len(released_personnel) if released_personnel else 0
        })

    except json.JSONDecodeError as e:
        logger.error(f"خطأ في تنسيق JSON: {str(e)}")
        return JsonResponse({'success': False, 'error': 'خطأ في تنسيق البيانات'})
    except Exception as e:
        logger.error(f"خطأ غير متوقع: {str(e)}")
        import traceback
        logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return JsonResponse({'success': False, 'error': f'خطأ غير متوقع: {str(e)}'})


@csrf_exempt
@require_http_methods(["POST"])
def test_api_connection(request):
    """اختبار الاتصال بـ API"""
    if not request.user.is_authenticated:
        return JsonResponse({'success': False, 'error': 'غير مسجل الدخول'})

    try:
        data = json.loads(request.body)
        return JsonResponse({
            'success': True,
            'message': 'الاتصال يعمل بشكل صحيح',
            'user': request.user.username,
            'data_received': data
        })
    except:
        return JsonResponse({'success': False, 'error': 'خطأ في البيانات'})

@csrf_exempt
@login_required(login_url='login')
def daily_unit_count_view(request):
    """Enhanced view for daily unit count page with persistent data"""
    from .models import DailyUnitCount, PersonnelCount, EquipmentCount, TransferRecord
    from datetime import date

    # Get the user's profile
    user = request.user
    is_admin = user.is_superuser or user.is_staff

    # Get user wilaya and role safely
    user_wilaya = None
    user_role = None
    user_unit = None
    if hasattr(user, 'userprofile') and user.userprofile:
        user_wilaya = user.userprofile.wilaya
        user_role = user.userprofile.role

    # Get intervention units based on user role
    if is_admin or user_role == 'admin':
        units = InterventionUnit.objects.all().order_by('wilaya', 'name')
    elif user_role == 'wilaya_manager':
        units = InterventionUnit.objects.filter(wilaya=user_wilaya).order_by('name')
    elif user_role == 'unit_coordinator':
        # Unit coordinator can only see their assigned unit
        units = user.userprofile.intervention_units.filter(wilaya=user_wilaya)
        if units.exists():
            user_unit = units.first()
    else:
        units = InterventionUnit.objects.filter(wilaya=user_wilaya).order_by('name')

    # Get today's date
    today = date.today()

    # Get or create daily count for today
    daily_count = None
    if user_role == 'unit_coordinator' and user_unit:
        # For unit coordinators, automatically get/create for their unit
        daily_count, created = DailyUnitCount.objects.get_or_create(
            unit=user_unit,
            date=today,
            defaults={'created_by': user}
        )
    elif units.exists():
        # For other roles, get existing or allow selection
        selected_unit_id = request.GET.get('unit_id')
        if selected_unit_id:
            try:
                selected_unit = units.get(id=selected_unit_id)
                daily_count, created = DailyUnitCount.objects.get_or_create(
                    unit=selected_unit,
                    date=today,
                    defaults={'created_by': user}
                )
            except InterventionUnit.DoesNotExist:
                pass

    # Handle POST requests
    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'create_daily_count':
            unit_id = request.POST.get('unit_id')
            if unit_id and units.filter(id=unit_id).exists():
                unit = units.get(id=unit_id)
                daily_count, created = DailyUnitCount.objects.get_or_create(
                    unit=unit,
                    date=today,
                    defaults={'created_by': user}
                )
                return JsonResponse({
                    'success': True,
                    'message': 'تم إنشاء التعداد الصباحي بنجاح',
                    'daily_count_id': daily_count.id
                })

        elif action == 'add_personnel' and daily_count:
            from .models import UnitPersonnel, DailyPersonnelStatus

            registration_number = request.POST.get('registration_number')
            full_name = request.POST.get('full_name')
            rank = request.POST.get('rank')
            position = request.POST.get('position')

            # التحقق من وجود العون مسبقاً
            existing_personnel = UnitPersonnel.objects.filter(
                unit=daily_count.unit,
                registration_number=registration_number
            ).first()

            if existing_personnel:
                # إذا كان العون موجود، تحديث بياناته وتفعيله
                existing_personnel.full_name = full_name
                existing_personnel.rank = rank
                existing_personnel.position = position
                existing_personnel.is_active = True
                existing_personnel.save()

                messages.success(request, f'تم تحديث بيانات العون {full_name} بنجاح')
                personnel = existing_personnel
            else:
                # إنشاء عون جديد
                try:
                    personnel = UnitPersonnel.objects.create(
                        unit=daily_count.unit,
                        registration_number=registration_number,
                        full_name=full_name,
                        rank=rank,
                        position=position,
                        created_by=user
                    )
                    messages.success(request, f'تم إضافة العون {full_name} بنجاح')
                except Exception as e:
                    error_message = str(e)
                    if 'UNIQUE constraint failed' in error_message:
                        error_message = f'رقم القيد {registration_number} موجود مسبقاً في هذه الوحدة'
                    messages.error(request, f'حدث خطأ أثناء إضافة العون: {error_message}')
                    return JsonResponse({'success': False, 'message': f'حدث خطأ أثناء إضافة العون: {error_message}'})

            # إنشاء أو تحديث الحالة اليومية
            status, status_created = DailyPersonnelStatus.objects.get_or_create(
                personnel=personnel,
                date=today,
                defaults={
                    'status': request.POST.get('status'),
                    'notes': request.POST.get('notes', ''),
                    'updated_by': user
                }
            )

            if not status_created:
                status.status = request.POST.get('status')
                status.notes = request.POST.get('notes', '')
                status.updated_by = user
                status.save()

            return JsonResponse({'success': True, 'message': 'تم إضافة العون بنجاح'})

        elif action == 'edit_personnel' and daily_count:
            personnel_id = request.POST.get('personnel_id')
            try:
                from .models import UnitPersonnel, DailyPersonnelStatus
                personnel = UnitPersonnel.objects.get(id=personnel_id, unit=daily_count.unit)

                # تحديث البيانات الأساسية للعون
                personnel.registration_number = request.POST.get('registration_number')
                personnel.full_name = request.POST.get('full_name')
                personnel.rank = request.POST.get('rank')
                personnel.position = request.POST.get('position')
                personnel.save()

                # تحديث الحالة اليومية
                daily_status, created = DailyPersonnelStatus.objects.get_or_create(
                    personnel=personnel,
                    date=today,
                    defaults={'updated_by': user}
                )
                daily_status.status = request.POST.get('status')
                daily_status.notes = request.POST.get('notes', '')
                daily_status.updated_by = user
                daily_status.save()

                return JsonResponse({'success': True, 'message': 'تم تحديث بيانات العون بنجاح'})
            except UnitPersonnel.DoesNotExist:
                return JsonResponse({'success': False, 'message': 'العون غير موجود'})

        elif action == 'update_personnel_status' and daily_count:
            personnel_id = request.POST.get('personnel_id')
            new_status = request.POST.get('status')
            try:
                from .models import UnitPersonnel, DailyPersonnelStatus
                personnel = UnitPersonnel.objects.get(id=personnel_id, unit=daily_count.unit)

                # تحديث الحالة اليومية فقط
                daily_status, created = DailyPersonnelStatus.objects.get_or_create(
                    personnel=personnel,
                    date=today,
                    defaults={'updated_by': user}
                )
                daily_status.status = new_status
                daily_status.updated_by = user
                daily_status.save()

                return JsonResponse({'success': True, 'message': 'تم تحديث حالة العون بنجاح'})
            except UnitPersonnel.DoesNotExist:
                return JsonResponse({'success': False, 'message': 'العون غير موجود'})

        elif action == 'update_personnel_status' and daily_count:
            personnel_id = request.POST.get('personnel_id')
            new_status = request.POST.get('status')
            try:
                personnel = PersonnelCount.objects.get(id=personnel_id, daily_count=daily_count)
                personnel.status = new_status
                personnel.save()
                return JsonResponse({'success': True, 'message': 'تم تحديث حالة العون بنجاح'})
            except PersonnelCount.DoesNotExist:
                return JsonResponse({'success': False, 'message': 'العون غير موجود'})

        elif action == 'add_equipment' and daily_count:
            from .models import UnitEquipment, DailyEquipmentStatus, EquipmentType

            equipment_type = request.POST.get('equipment_type')
            is_new_type = request.POST.get('is_new_type') == 'true'

            # إذا كان نوع جديد، أضفه إلى قاعدة البيانات
            if is_new_type and equipment_type:
                # التحقق من صلاحيات الإضافة
                user_role = None
                if hasattr(user, 'userprofile') and user.userprofile:
                    user_role = user.userprofile.role

                is_admin = user.is_superuser or user.is_staff
                can_add_types = is_admin or user_role in ['admin', 'wilaya_manager']

                if not can_add_types:
                    return JsonResponse({
                        'success': False,
                        'message': 'ليس لديك صلاحية لإضافة أنواع وسائل جديدة'
                    })

                # إضافة النوع الجديد
                EquipmentType.objects.get_or_create(
                    name=equipment_type,
                    defaults={
                        'category': 'مخصص',
                        'created_by': user
                    }
                )

            # إنشاء الوسيلة في قائمة الوحدة المستمرة
            equipment, created = UnitEquipment.objects.get_or_create(
                unit=daily_count.unit,
                serial_number=request.POST.get('serial_number'),
                defaults={
                    'equipment_type': equipment_type,
                    'radio_number': request.POST.get('radio_number', ''),
                    'created_by': user
                }
            )

            if not created:
                # تحديث البيانات إذا كانت الوسيلة موجودة
                equipment.equipment_type = equipment_type
                equipment.radio_number = request.POST.get('radio_number', '')
                equipment.is_active = True
                equipment.save()

            # إنشاء أو تحديث الحالة اليومية
            status, status_created = DailyEquipmentStatus.objects.get_or_create(
                equipment=equipment,
                date=today,
                defaults={
                    'status': request.POST.get('status'),
                    'notes': request.POST.get('notes', ''),
                    'updated_by': user
                }
            )

            if not status_created:
                status.status = request.POST.get('status')
                status.notes = request.POST.get('notes', '')
                status.updated_by = user
                status.save()

            message = 'تم إضافة الوسيلة بنجاح'
            if is_new_type:
                message += f' وتم إضافة نوع الوسيلة الجديد "{equipment_type}" إلى النظام'

            return JsonResponse({'success': True, 'message': message})

        elif action == 'edit_equipment' and daily_count:
            equipment_id = request.POST.get('equipment_id')
            try:
                equipment = EquipmentCount.objects.get(id=equipment_id, daily_count=daily_count)
                equipment.serial_number = request.POST.get('serial_number')
                equipment.equipment_type = request.POST.get('equipment_type')
                equipment.radio_number = request.POST.get('radio_number', '')
                equipment.status = request.POST.get('status')
                equipment.notes = request.POST.get('notes', '')
                equipment.save()
                return JsonResponse({'success': True, 'message': 'تم تحديث بيانات الوسيلة بنجاح'})
            except EquipmentCount.DoesNotExist:
                return JsonResponse({'success': False, 'message': 'الوسيلة غير موجودة'})

        elif action == 'update_equipment_status' and daily_count:
            equipment_id = request.POST.get('equipment_id')
            new_status = request.POST.get('status')
            try:
                equipment = EquipmentCount.objects.get(id=equipment_id, daily_count=daily_count)
                equipment.status = new_status
                equipment.save()
                return JsonResponse({'success': True, 'message': 'تم تحديث حالة الوسيلة بنجاح'})
            except EquipmentCount.DoesNotExist:
                return JsonResponse({'success': False, 'message': 'الوسيلة غير موجودة'})

    # Get all personnel and equipment for the unit (persistent data)
    from .models import UnitPersonnel, UnitEquipment, DailyPersonnelStatus, DailyEquipmentStatus

    all_personnel = []
    all_equipment = []
    if daily_count:
        # الحصول على جميع الأعوان النشطين في الوحدة
        unit_personnel = UnitPersonnel.objects.filter(unit=daily_count.unit, is_active=True).order_by('full_name')

        # إضافة الحالة اليومية لكل عون
        for personnel in unit_personnel:
            daily_status, created = DailyPersonnelStatus.objects.get_or_create(
                personnel=personnel,
                date=today,
                defaults={
                    'status': 'present',
                    'updated_by': user
                }
            )
            personnel.daily_status = daily_status
            all_personnel.append(personnel)

        # الحصول على جميع الوسائل النشطة في الوحدة
        unit_equipment = UnitEquipment.objects.filter(unit=daily_count.unit, is_active=True).order_by('equipment_type')

        # إضافة الحالة اليومية لكل وسيلة
        for equipment in unit_equipment:
            daily_status, created = DailyEquipmentStatus.objects.get_or_create(
                equipment=equipment,
                date=today,
                defaults={
                    'status': 'operational',
                    'updated_by': user
                }
            )
            equipment.daily_status = daily_status
            all_equipment.append(equipment)

    # Get recent transfer records
    transfer_records = TransferRecord.objects.filter(
        from_unit__in=units
    ).order_by('-transfer_date')[:10] if units.exists() else []

    # الحصول على قوائم الرتب والمناصب
    from .models import PersonnelRank, PersonnelPosition
    ranks = PersonnelRank.objects.filter(is_active=True).values_list('name', flat=True) if hasattr(PersonnelRank, 'is_active') else PersonnelRank.objects.values_list('name', flat=True)
    positions = PersonnelPosition.objects.filter(is_active=True).values_list('name', flat=True) if hasattr(PersonnelPosition, 'is_active') else PersonnelPosition.objects.values_list('name', flat=True)

    # ========================================
    # Morning Check System Integration
    # ========================================
    morning_summary = None
    available_shifts = []
    daily_schedule = None
    eight_hour_personnel = []
    active_alerts = []

    if daily_count:
        from .models import (MorningCheckSummary, WorkShift, DailyShiftSchedule,
                           EightHourPersonnel, ReadinessAlert, VehicleCrewAssignment, VehicleReadiness)

        # الحصول على أو إنشاء ملخص التحقق الصباحي
        morning_summary, created = MorningCheckSummary.objects.get_or_create(
            unit=daily_count.unit,
            date=today,
            defaults={
                'created_by': user,
                'overall_readiness_score': 0,
                'is_fully_ready': False
            }
        )

        # الحصول على الفرق المتاحة
        available_shifts = WorkShift.objects.filter(unit=daily_count.unit, is_active=True)

        # الحصول على الجدولة اليومية
        daily_schedule = DailyShiftSchedule.objects.filter(
            unit=daily_count.unit,
            date=today
        ).first()

        # الحصول على أعوان نظام 8 ساعات
        eight_hour_personnel = EightHourPersonnel.objects.filter(
            unit=daily_count.unit,
            date=today
        )

        # الحصول على التنبيهات النشطة
        active_alerts = ReadinessAlert.objects.filter(
            unit=daily_count.unit,
            date=today,
            status='active'
        ).order_by('-priority', '-created_at')

        # حساب الإحصائيات وتحديث ملخص التحقق الصباحي
        total_personnel = len(all_personnel)
        present_personnel = sum(1 for p in all_personnel if p.daily_status.status == 'present')
        absent_personnel = sum(1 for p in all_personnel if p.daily_status.status == 'absent')
        on_mission_personnel = sum(1 for p in all_personnel if p.daily_status.status == 'on_mission')

        total_vehicles = len(all_equipment)
        operational_vehicles = sum(1 for e in all_equipment if e.daily_status.status == 'operational')
        maintenance_vehicles = sum(1 for e in all_equipment if e.daily_status.status == 'under_maintenance')
        out_of_service_vehicles = sum(1 for e in all_equipment if e.daily_status.status == 'out_of_service')

        # تحديث ملخص التحقق الصباحي
        morning_summary.total_personnel = total_personnel
        morning_summary.present_personnel = present_personnel
        morning_summary.absent_personnel = absent_personnel
        morning_summary.on_mission_personnel = on_mission_personnel
        morning_summary.total_vehicles = total_vehicles
        morning_summary.ready_vehicles = operational_vehicles
        morning_summary.under_maintenance_vehicles = maintenance_vehicles
        morning_summary.not_ready_vehicles = maintenance_vehicles + out_of_service_vehicles
        morning_summary.active_shift = daily_schedule.active_shift if daily_schedule else None

        # حساب نسبة الجاهزية
        morning_summary.calculate_readiness_score()
        morning_summary.save()

    context = {
        'units': units,
        'user_unit': user_unit,
        'daily_count': daily_count,
        'all_personnel': all_personnel,
        'all_equipment': all_equipment,
        'transfer_records': transfer_records,
        'today': today,
        'user': user,
        'is_admin': is_admin,
        'user_wilaya': user_wilaya,
        'user_role': user_role,
        'can_transfer': is_admin or user_role in ['admin', 'wilaya_manager'],
        'can_edit': True,  # All users can edit their unit's data
        'ranks': list(ranks),
        'positions': list(positions),
        # Morning Check System data
        'morning_summary': morning_summary,
        'available_shifts': available_shifts,
        'daily_schedule': daily_schedule,
        'eight_hour_personnel': eight_hour_personnel,
        'active_alerts': active_alerts,
    }

    return render(request, 'coordination_center/daily_unit_count.html', context)

@login_required(login_url='login')
def daily_unit_reports_view(request):
    """View for daily unit reports with filters - محدث للنماذج الجديدة"""
    from .models import DailyUnitCount, UnitPersonnel, UnitEquipment, DailyPersonnelStatus
    from datetime import date, timedelta
    from django.db.models import Count, Q

    # Get the user's profile
    user = request.user
    is_admin = user.is_superuser or user.is_staff

    # Get user wilaya and role safely
    user_wilaya = None
    user_role = None
    if hasattr(user, 'userprofile') and user.userprofile:
        user_wilaya = user.userprofile.wilaya
        user_role = user.userprofile.role

    # Get intervention units based on user role
    if is_admin or user_role == 'admin':
        units = InterventionUnit.objects.all().order_by('wilaya', 'name')
    elif user_role == 'wilaya_manager':
        units = InterventionUnit.objects.filter(wilaya=user_wilaya).order_by('name')
    elif user_role == 'unit_coordinator':
        units = user.userprofile.intervention_units.filter(wilaya=user_wilaya)
    else:
        units = InterventionUnit.objects.filter(wilaya=user_wilaya).order_by('name')

    # Get filter parameters
    selected_unit_id = request.GET.get('unit_id')
    selected_date = request.GET.get('date')
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    report_type = request.GET.get('report_type', 'daily')

    # Default date range
    if not date_from and not selected_date:
        if report_type == 'monthly':
            # الشهر الحالي
            today = date.today()
            first_day = date(today.year, today.month, 1)
            if today.month == 12:
                last_day = date(today.year + 1, 1, 1) - timedelta(days=1)
            else:
                last_day = date(today.year, today.month + 1, 1) - timedelta(days=1)
            date_from = first_day.strftime('%Y-%m-%d')
            date_to = last_day.strftime('%Y-%m-%d')
        else:
            # آخر 7 أيام للتقرير اليومي
            date_from = (date.today() - timedelta(days=7)).strftime('%Y-%m-%d')
            date_to = date.today().strftime('%Y-%m-%d')

    # إذا تم اختيار تاريخ واحد فقط، انسخه للآخر
    if date_from and not date_to:
        date_to = date_from
    elif date_to and not date_from:
        date_from = date_to

    # إنشاء قائمة التقارير اليومية
    daily_reports = []

    # تحديد الوحدات المطلوبة
    if selected_unit_id:
        target_units = units.filter(id=selected_unit_id)
    else:
        target_units = units

    # تحديد التواريخ المطلوبة
    if selected_date:
        from datetime import datetime
        target_dates = [datetime.strptime(selected_date, '%Y-%m-%d').date()]
    else:
        from datetime import datetime
        start_date = datetime.strptime(date_from, '%Y-%m-%d').date()
        end_date = datetime.strptime(date_to, '%Y-%m-%d').date()
        target_dates = []
        current_date = start_date
        while current_date <= end_date:
            target_dates.append(current_date)
            current_date += timedelta(days=1)

    # إنشاء التقارير لكل وحدة وتاريخ
    for unit in target_units:
        for report_date in target_dates:
            # الحصول على أعوان الوحدة النشطين
            unit_personnel = UnitPersonnel.objects.filter(unit=unit, is_active=True)

            # الحصول على وسائل الوحدة النشطة
            unit_equipment = UnitEquipment.objects.filter(unit=unit, is_active=True)

            # الحصول على حالات الأعوان لهذا التاريخ
            personnel_statuses = DailyPersonnelStatus.objects.filter(
                personnel__unit=unit,
                date=report_date
            )

            # حساب إحصائيات الأعوان
            present_count = personnel_statuses.filter(status='present').count()
            absent_count = personnel_statuses.filter(status='absent').count()
            on_mission_count = personnel_statuses.filter(status='on_mission').count()

            # إذا لم توجد حالات مسجلة، اعتبر جميع الأعوان حاضرين
            if not personnel_statuses.exists() and unit_personnel.exists():
                present_count = unit_personnel.count()
                absent_count = 0
                on_mission_count = 0

            # حساب إحصائيات الوسائل (افتراضياً جميعها تعمل)
            operational_equipment = unit_equipment.count()
            broken_equipment = 0
            maintenance_equipment = 0

            # إنشاء تقرير يومي
            daily_report = {
                'date': report_date,
                'unit': unit,
                'total_personnel': unit_personnel.count(),
                'present_personnel': present_count,
                'absent_personnel': absent_count,
                'on_mission_personnel': on_mission_count,
                'total_equipment': unit_equipment.count(),
                'operational_equipment': operational_equipment,
                'broken_equipment': broken_equipment,
                'maintenance_equipment': maintenance_equipment,
            }

            daily_reports.append(daily_report)

    # حساب الإحصائيات الإجمالية - منطق جديد ومحسن
    if report_type == 'monthly':
        # للتقارير الشهرية: حساب منطقي وعصري
        unique_personnel = set()
        unique_equipment = set()

        for unit in target_units:
            unit_personnel = UnitPersonnel.objects.filter(unit=unit, is_active=True)
            unit_equipment = UnitEquipment.objects.filter(unit=unit, is_active=True)

            for person in unit_personnel:
                unique_personnel.add(person.id)
            for equipment in unit_equipment:
                unique_equipment.add(equipment.id)

        # حساب معدل الحضور اليومي (متوسط الأعوان الحاضرين يومياً)
        total_days_with_data = len([r for r in daily_reports if r['total_personnel'] > 0])
        avg_present = sum(report['present_personnel'] for report in daily_reports) / max(total_days_with_data, 1)
        avg_absent = sum(report['absent_personnel'] for report in daily_reports) / max(total_days_with_data, 1)
        avg_mission = sum(report['on_mission_personnel'] for report in daily_reports) / max(total_days_with_data, 1)

        # حساب معدل الحضور كنسبة مئوية
        total_personnel_count = len(unique_personnel)
        attendance_rate = (avg_present / max(total_personnel_count, 1)) * 100 if total_personnel_count > 0 else 0

        stats = {
            'total_days': len(target_dates),
            'total_personnel': len(unique_personnel),
            'avg_present': round(avg_present, 1),
            'avg_absent': round(avg_absent, 1),
            'avg_mission': round(avg_mission, 1),
            'attendance_rate': round(attendance_rate, 1),
            'total_equipment': len(unique_equipment),
        }
    else:
        # للتقارير اليومية: الحساب التقليدي
        total_personnel = sum(report['total_personnel'] for report in daily_reports)
        total_present = sum(report['present_personnel'] for report in daily_reports)
        total_equipment = sum(report['total_equipment'] for report in daily_reports)
        total_operational = sum(report['operational_equipment'] for report in daily_reports)

        stats = {
            'total_days': len(set((report['date'], report['unit'].id) for report in daily_reports)),
            'total_personnel': total_personnel,
            'total_equipment': total_equipment,
            'present_personnel': total_present,
            'operational_equipment': total_operational,
        }

    # إحصائيات حالة الأعوان - منطق جديد ومحسن
    if report_type == 'monthly':
        # للتقارير الشهرية: عرض المتوسطات اليومية بدلاً من المجاميع المضللة
        personnel_stats = [
            {'status': 'present', 'count': stats['avg_present']},
            {'status': 'absent', 'count': stats['avg_absent']},
            {'status': 'on_mission', 'count': stats['avg_mission']},
        ]

        # لا نحتاج إحصائيات الوسائل في التقارير الشهرية
        equipment_stats = []
    else:
        # للتقارير اليومية: الحساب التقليدي
        personnel_stats = [
            {'status': 'present', 'count': sum(report['present_personnel'] for report in daily_reports)},
            {'status': 'absent', 'count': sum(report['absent_personnel'] for report in daily_reports)},
            {'status': 'on_mission', 'count': sum(report['on_mission_personnel'] for report in daily_reports)},
        ]

        equipment_stats = [
            {'status': 'operational', 'count': sum(report['operational_equipment'] for report in daily_reports)},
            {'status': 'broken', 'count': sum(report['broken_equipment'] for report in daily_reports)},
            {'status': 'maintenance', 'count': sum(report['maintenance_equipment'] for report in daily_reports)},
        ]

    # إنشاء جدول تفصيلي للأعوان (للتقارير الشهرية)
    personnel_details = []
    if report_type == 'monthly' and target_units.exists():
        from datetime import datetime
        start_date = datetime.strptime(date_from, '%Y-%m-%d').date()
        end_date = datetime.strptime(date_to, '%Y-%m-%d').date()

        for unit in target_units:
            unit_personnel = UnitPersonnel.objects.filter(unit=unit, is_active=True)

            for person in unit_personnel:
                # حساب أيام الحضور والغياب والمهام لكل شخص
                person_statuses = DailyPersonnelStatus.objects.filter(
                    personnel=person,
                    date__range=[start_date, end_date]
                )

                present_days = person_statuses.filter(status='present').count()
                absent_days = person_statuses.filter(status='absent').count()
                mission_days = person_statuses.filter(status='on_mission').count()

                # حساب الأيام التي لم يتم تسجيل حالة فيها (افتراضياً حاضر)
                total_recorded_days = present_days + absent_days + mission_days
                total_period_days = (end_date - start_date).days + 1
                unrecorded_days = total_period_days - total_recorded_days

                # إضافة الأيام غير المسجلة للحضور
                present_days += unrecorded_days

                # حساب النسب المئوية
                attendance_rate = (present_days / total_period_days * 100) if total_period_days > 0 else 0
                absence_rate = (absent_days / total_period_days * 100) if total_period_days > 0 else 0
                mission_rate = (mission_days / total_period_days * 100) if total_period_days > 0 else 0

                personnel_details.append({
                    'personnel': person,
                    'unit': unit,
                    'present_days': present_days,
                    'absent_days': absent_days,
                    'mission_days': mission_days,
                    'total_days': total_period_days,
                    'attendance_rate': round(attendance_rate, 1),
                    'absence_rate': round(absence_rate, 1),
                    'mission_rate': round(mission_rate, 1),
                })

        # ترتيب حسب الوحدة ثم الاسم
        personnel_details.sort(key=lambda x: (x['unit'].name, x['personnel'].full_name))

    # التحقق من طلب التصدير
    if request.GET.get('export') == 'excel':
        return export_daily_reports_to_excel(request, daily_reports, personnel_details, stats, report_type, date_from, date_to)

    context = {
        'units': units,
        'daily_reports': daily_reports,
        'stats': stats,
        'personnel_stats': personnel_stats,
        'equipment_stats': equipment_stats,
        'personnel_details': personnel_details,
        'selected_unit_id': selected_unit_id,
        'selected_date': selected_date,
        'date_from': date_from,
        'date_to': date_to,
        'report_type': report_type,
        'user_role': user_role,
        'can_view_all': is_admin or user_role in ['admin', 'wilaya_manager']
    }

    # إنشاء تقرير الأشخاص الجديد
    personnel_details = []
    equipment_details = []

    for unit in target_units:
        # تقرير الأشخاص
        unit_personnel = UnitPersonnel.objects.filter(unit=unit, is_active=True)

        for person in unit_personnel:
            # حساب أيام الحضور والغياب والمهام لكل شخص
            person_statuses = DailyPersonnelStatus.objects.filter(
                personnel=person,
                date__range=[start_date, end_date]
            )

            present_days = person_statuses.filter(status='present').count()
            absent_days = person_statuses.filter(status='absent').count()
            mission_days = person_statuses.filter(status='on_mission').count()

            # حساب الأيام التي لم يتم تسجيل حالة فيها (افتراضياً حاضر)
            total_recorded_days = present_days + absent_days + mission_days
            total_period_days = (end_date - start_date).days + 1
            unrecorded_days = max(0, total_period_days - total_recorded_days)

            # إضافة الأيام غير المسجلة للحضور
            present_days += unrecorded_days

            # حساب النسب المئوية
            attendance_rate = (present_days / total_period_days * 100) if total_period_days > 0 else 0

            personnel_details.append({
                'personnel': person,
                'unit': unit,
                'present_days': present_days,
                'absent_days': absent_days,
                'mission_days': mission_days,
                'total_days': total_period_days,
                'attendance_rate': round(attendance_rate, 1),
            })

        # تقرير المعدات
        unit_equipment = UnitEquipment.objects.filter(unit=unit, is_active=True)

        for equipment in unit_equipment:
            # حساب أيام التشغيل والتعطل والصيانة (افتراضياً تعمل)
            operational_days = total_period_days  # افتراضياً جميع الأيام
            broken_days = 0
            maintenance_days = 0

            # حساب معدل التشغيل
            operational_rate = (operational_days / total_period_days * 100) if total_period_days > 0 else 0

            equipment_details.append({
                'equipment': equipment,
                'unit': unit,
                'operational_days': operational_days,
                'broken_days': broken_days,
                'maintenance_days': maintenance_days,
                'total_days': total_period_days,
                'operational_rate': round(operational_rate, 1),
            })

    # ترتيب حسب الوحدة ثم الاسم
    personnel_details.sort(key=lambda x: (x['unit'].name, x['personnel'].full_name))
    equipment_details.sort(key=lambda x: (x['unit'].name, x['equipment'].equipment_type))

    context = {
        'units': units,
        'personnel_details': personnel_details,
        'equipment_details': equipment_details,
        'selected_unit_id': selected_unit_id,
        'date_from': date_from,
        'date_to': date_to,
        'report_type': report_type,
        'user_role': user_role,
        'can_view_all': is_admin or user_role in ['admin', 'wilaya_manager']
    }

    return render(request, 'coordination_center/daily_unit_reports.html', context)

def handle_export_request(request, export_type, target_units, start_date, end_date):
    """معالجة طلبات التصدير المختلفة"""
    # سيتم تطبيق هذه الوظيفة لاحقاً
    from django.http import HttpResponse
    return HttpResponse("التصدير قيد التطوير")

def export_daily_reports_to_excel(request, daily_reports, personnel_details, stats, report_type, date_from, date_to):
    """تصدير التقارير اليومية إلى Excel"""
    import openpyxl
    from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
    from django.http import HttpResponse
    from datetime import datetime

    # إنشاء مصنف Excel جديد
    wb = openpyxl.Workbook()

    # حذف الورقة الافتراضية
    wb.remove(wb.active)

    # إنشاء ورقة التقارير اليومية
    ws_daily = wb.create_sheet("التقارير اليومية")

    # تنسيق الخلايا
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    border = Border(left=Side(style='thin'), right=Side(style='thin'),
                   top=Side(style='thin'), bottom=Side(style='thin'))
    center_alignment = Alignment(horizontal='center', vertical='center')

    # رؤوس الأعمدة للتقارير اليومية
    headers_daily = [
        'التاريخ', 'الوحدة', 'إجمالي الأعوان', 'الحاضرون', 'الغائبون',
        'في مهمة', 'إجمالي الوسائل', 'العاملة', 'المعطلة', 'تحت الصيانة'
    ]

    # كتابة رؤوس الأعمدة
    for col, header in enumerate(headers_daily, 1):
        cell = ws_daily.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = center_alignment
        cell.border = border

    # كتابة بيانات التقارير اليومية
    for row, report in enumerate(daily_reports, 2):
        ws_daily.cell(row=row, column=1, value=report['date'].strftime('%d/%m/%Y')).border = border
        ws_daily.cell(row=row, column=2, value=report['unit'].name).border = border
        ws_daily.cell(row=row, column=3, value=report['total_personnel']).border = border
        ws_daily.cell(row=row, column=4, value=report['present_personnel']).border = border
        ws_daily.cell(row=row, column=5, value=report['absent_personnel']).border = border
        ws_daily.cell(row=row, column=6, value=report['on_mission_personnel']).border = border
        ws_daily.cell(row=row, column=7, value=report['total_equipment']).border = border
        ws_daily.cell(row=row, column=8, value=report['operational_equipment']).border = border
        ws_daily.cell(row=row, column=9, value=report['broken_equipment']).border = border
        ws_daily.cell(row=row, column=10, value=report['maintenance_equipment']).border = border

    # تعديل عرض الأعمدة
    for col in range(1, len(headers_daily) + 1):
        ws_daily.column_dimensions[openpyxl.utils.get_column_letter(col)].width = 15

    # إضافة ورقة التفاصيل الشخصية للتقارير الشهرية
    if report_type == 'monthly' and personnel_details:
        ws_personnel = wb.create_sheet("تفاصيل الأعوان")

        headers_personnel = [
            'رقم القيد', 'الاسم الكامل', 'الرتبة', 'المنصب', 'الوحدة',
            'أيام الحضور', 'أيام الغياب', 'أيام المهام', 'إجمالي الأيام',
            'نسبة الحضور %', 'نسبة الغياب %', 'نسبة المهام %'
        ]

        # كتابة رؤوس الأعمدة
        for col, header in enumerate(headers_personnel, 1):
            cell = ws_personnel.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = center_alignment
            cell.border = border

        # كتابة بيانات الأعوان
        for row, detail in enumerate(personnel_details, 2):
            ws_personnel.cell(row=row, column=1, value=detail['personnel'].registration_number).border = border
            ws_personnel.cell(row=row, column=2, value=detail['personnel'].full_name).border = border
            ws_personnel.cell(row=row, column=3, value=detail['personnel'].rank or "–").border = border
            ws_personnel.cell(row=row, column=4, value=detail['personnel'].position or "–").border = border
            ws_personnel.cell(row=row, column=5, value=detail['unit'].name).border = border
            ws_personnel.cell(row=row, column=6, value=detail['present_days']).border = border
            ws_personnel.cell(row=row, column=7, value=detail['absent_days']).border = border
            ws_personnel.cell(row=row, column=8, value=detail['mission_days']).border = border
            ws_personnel.cell(row=row, column=9, value=detail['total_days']).border = border
            ws_personnel.cell(row=row, column=10, value=f"{detail['attendance_rate']}%").border = border
            ws_personnel.cell(row=row, column=11, value=f"{detail['absence_rate']}%").border = border
            ws_personnel.cell(row=row, column=12, value=f"{detail['mission_rate']}%").border = border

        # تعديل عرض الأعمدة
        for col in range(1, len(headers_personnel) + 1):
            ws_personnel.column_dimensions[openpyxl.utils.get_column_letter(col)].width = 15

    # إنشاء استجابة HTTP للتحميل
    response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

    # تحديد اسم الملف
    filename = f"تقرير_{report_type}_{date_from}_إلى_{date_to}.xlsx"
    response['Content-Disposition'] = f'attachment; filename="{filename}"'

    # حفظ المصنف في الاستجابة
    wb.save(response)

    return response

@csrf_exempt
@login_required(login_url='login')
def shift_schedule_management(request):
    """صفحة إدارة جدولة الفرق"""
    from .models import InterventionUnit, ShiftSchedule, UnitPersonnel
    from datetime import datetime, date, timedelta
    from django.utils import timezone
    import calendar

    # الحصول على المعاملات
    unit_id = request.GET.get('unit_id')
    year = int(request.GET.get('year', datetime.now().year))
    month = int(request.GET.get('month', datetime.now().month))

    if not unit_id:
        messages.error(request, 'يرجى اختيار الوحدة')
        return redirect('unified_morning_check')

    try:
        unit = InterventionUnit.objects.get(id=unit_id)
    except InterventionUnit.DoesNotExist:
        messages.error(request, 'الوحدة غير موجودة')
        return redirect('unified_morning_check')

    # معالجة إنشاء جدولة جديدة
    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'create_monthly_schedule':
            try:
                result = ShiftSchedule.create_monthly_schedule(unit, year, month, request.user)
                messages.success(request, result)
            except Exception as e:
                messages.error(request, f'حدث خطأ: {str(e)}')

        elif action == 'update_shift':
            date_str = request.POST.get('date')
            new_shift = request.POST.get('shift')

            try:
                target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                target_datetime = timezone.make_aware(datetime.combine(target_date, datetime.min.time().replace(hour=8)))

                # البحث عن الجدولة الموجودة
                schedule = ShiftSchedule.objects.filter(
                    unit=unit,
                    start_datetime__date=target_date
                ).first()

                if schedule:
                    schedule.working_shift = new_shift
                    schedule.save()
                    messages.success(request, f'تم تحديث الفرقة العاملة لتاريخ {target_date}')
                else:
                    # إنشاء جدولة جديدة
                    end_datetime = target_datetime + timedelta(hours=24)
                    ShiftSchedule.objects.create(
                        unit=unit,
                        working_shift=new_shift,
                        start_datetime=target_datetime,
                        end_datetime=end_datetime,
                        created_by=request.user
                    )
                    messages.success(request, f'تم إنشاء جدولة جديدة لتاريخ {target_date}')

            except Exception as e:
                messages.error(request, f'حدث خطأ: {str(e)}')

    # الحصول على الجدولة الحالية للشهر
    start_date = date(year, month, 1)
    days_in_month = calendar.monthrange(year, month)[1]
    end_date = date(year, month, days_in_month)

    schedules = ShiftSchedule.objects.filter(
        unit=unit,
        start_datetime__date__range=[start_date, end_date]
    ).order_by('start_datetime')

    # إنشاء قاموس للجدولة حسب التاريخ
    schedule_dict = {}
    for schedule in schedules:
        schedule_dict[schedule.start_datetime.date()] = schedule

    # إنشاء قائمة أيام الشهر مع الجدولة
    month_days = []
    for day in range(1, days_in_month + 1):
        current_date = date(year, month, day)
        schedule = schedule_dict.get(current_date)

        month_days.append({
            'date': current_date,
            'day': day,
            'schedule': schedule,
            'working_shift': schedule.working_shift if schedule else None,
            'shift_display': schedule.get_working_shift_display_arabic() if schedule else 'غير محدد'
        })

    # الحصول على إحصائيات الأعوان حسب الفرق
    personnel_stats = {}
    for shift in ['shift_1', 'shift_2', 'shift_3']:
        count = UnitPersonnel.objects.filter(
            unit=unit,
            assigned_shift=shift,
            work_system='24_hours'
        ).count()
        personnel_stats[shift] = count

    context = {
        'unit': unit,
        'year': year,
        'month': month,
        'month_name': calendar.month_name[month],
        'month_days': month_days,
        'personnel_stats': personnel_stats,
        'today': date.today(),
        'shift_choices': [
            ('shift_1', 'الفرقة الأولى'),
            ('shift_2', 'الفرقة الثانية'),
            ('shift_3', 'الفرقة الثالثة'),
        ],
        'prev_month': month - 1 if month > 1 else 12,
        'prev_year': year if month > 1 else year - 1,
        'next_month': month + 1 if month < 12 else 1,
        'next_year': year if month < 12 else year + 1,
    }

    return render(request, 'coordination_center/shift_schedule.html', context)

@csrf_exempt
@login_required(login_url='login')
def transfer_personnel_page(request):
    """صفحة تحويل العون بين الفرق"""
    from .models import UnitPersonnel, InterventionUnit

    # الحصول على المعاملات
    unit_id = request.GET.get('unit_id')
    personnel_id = request.GET.get('personnel_id')

    if not unit_id or not personnel_id:
        messages.error(request, 'معاملات غير صحيحة')
        return redirect('unified_morning_check')

    try:
        unit = InterventionUnit.objects.get(id=unit_id)
        personnel = UnitPersonnel.objects.get(id=personnel_id, unit=unit)
    except (InterventionUnit.DoesNotExist, UnitPersonnel.DoesNotExist):
        messages.error(request, 'الوحدة أو العون غير موجود')
        return redirect('unified_morning_check')

    # معالجة النموذج
    if request.method == 'POST':
        target_shift = request.POST.get('target_shift')
        reason = request.POST.get('reason', '').strip()

        if not target_shift or not reason:
            messages.error(request, 'يرجى ملء جميع الحقول المطلوبة')
        else:
            try:
                # حفظ الفرقة القديمة
                old_shift = personnel.assigned_shift

                # تحديث الفرقة
                personnel.assigned_shift = target_shift
                personnel.save()

                # إنشاء سجل التحويل
                try:
                    from .models import PersonnelTransfer
                    PersonnelTransfer.objects.create(
                        unit=personnel.unit,
                        personnel=personnel,
                        from_shift=old_shift or 'غير محدد',
                        to_shift=target_shift,
                        transfer_reason=reason,
                        transfer_date=timezone.now().date(),
                        status='completed',
                        requested_by=request.user,
                        approved_by=request.user
                    )
                except ImportError:
                    pass  # النموذج غير موجود

                messages.success(request, f'تم تحويل العون {personnel.full_name} بنجاح')
                return redirect(f'/coordination-center/unified-morning-check/?unit_id={unit_id}')

            except Exception as e:
                messages.error(request, f'حدث خطأ: {str(e)}')

    # الفرق المتاحة
    shift_choices = [
        ('shift_1', 'الفرقة الأولى'),
        ('shift_2', 'الفرقة الثانية'),
        ('shift_3', 'الفرقة الثالثة'),
    ]

    context = {
        'unit': unit,
        'personnel': personnel,
        'shift_choices': shift_choices,
    }

    return render(request, 'coordination_center/transfer_personnel.html', context)

@csrf_exempt
@login_required(login_url='login')
def add_personnel_view(request):
    """View for adding new personnel to a unit"""
    from .models import UnitPersonnel
    from datetime import date

    # Get the user's profile
    user = request.user
    is_admin = user.is_superuser or user.is_staff

    # Get user wilaya and role safely
    user_wilaya = None
    user_role = None
    user_unit = None
    if hasattr(user, 'userprofile') and user.userprofile:
        user_wilaya = user.userprofile.wilaya
        user_role = user.userprofile.role
        if user_role == 'unit_coordinator':
            user_unit = user.userprofile.intervention_units.first()

    # Get available units based on user role
    if is_admin:
        units = InterventionUnit.objects.all()
    elif user_role == 'wilaya_manager' and user_wilaya:
        units = InterventionUnit.objects.filter(wilaya=user_wilaya)
    elif user_role in ['unit_manager', 'unit_coordinator'] and hasattr(user, 'userprofile'):
        units = user.userprofile.intervention_units.all()
    else:
        units = InterventionUnit.objects.none()

    # Get unit from URL parameter
    unit_id = request.GET.get('unit_id')
    selected_unit = None
    if unit_id:
        try:
            selected_unit = units.get(id=unit_id)
        except InterventionUnit.DoesNotExist:
            messages.error(request, 'الوحدة المحددة غير متاحة')
            return redirect('daily_unit_count')
    elif user_unit:
        selected_unit = user_unit

    if not selected_unit:
        messages.error(request, 'يجب تحديد وحدة صالحة')
        return redirect('daily_unit_count')

    # Handle POST request
    if request.method == 'POST':
        try:
            # Get form data
            first_name = request.POST.get('first_name', '').strip()
            last_name = request.POST.get('last_name', '').strip()
            rank = request.POST.get('rank', '').strip()
            position = request.POST.get('position', '').strip()
            registration_number = request.POST.get('registration_number', '').strip()
            gender = request.POST.get('gender', '').strip()
            birth_date = request.POST.get('birth_date', '').strip()
            joining_date = request.POST.get('joining_date', '').strip()
            phone_number = request.POST.get('phone_number', '').strip()
            work_system = request.POST.get('work_system', '').strip()
            assigned_shift = request.POST.get('assigned_shift', '').strip()

            # Validate required fields
            required_fields = [first_name, last_name, rank, position, registration_number,
                             gender, birth_date, joining_date, phone_number, work_system]

            if not all(required_fields):
                # Get ranks and positions for error response
                from .models import PersonnelRank, PersonnelPosition
                ranks = [(rank.name, rank.name) for rank in PersonnelRank.objects.all()]
                positions = [(position.name, position.name) for position in PersonnelPosition.objects.all()]

                messages.error(request, 'جميع الحقول المطلوبة يجب ملؤها')
                return render(request, 'coordination_center/add_personnel.html', {
                    'selected_unit': selected_unit,
                    'ranks': ranks,
                    'positions': positions,
                    'user_role': user_role,
                })

            # Create full name
            full_name = f"{first_name} {last_name}"

            # Check if personnel already exists
            if UnitPersonnel.objects.filter(
                unit=selected_unit,
                full_name=full_name,
                registration_number=registration_number
            ).exists():
                # Get ranks and positions for error response
                from .models import PersonnelRank, PersonnelPosition
                ranks = [(rank.name, rank.name) for rank in PersonnelRank.objects.all()]
                positions = [(position.name, position.name) for position in PersonnelPosition.objects.all()]

                messages.error(request, 'هذا العون موجود مسبقاً في الوحدة')
                return render(request, 'coordination_center/add_personnel.html', {
                    'selected_unit': selected_unit,
                    'ranks': ranks,
                    'positions': positions,
                    'user_role': user_role,
                })

            # Parse dates
            from datetime import datetime
            birth_date_obj = datetime.strptime(birth_date, '%Y-%m-%d').date() if birth_date else None
            joining_date_obj = datetime.strptime(joining_date, '%Y-%m-%d').date() if joining_date else None

            # Create new personnel
            personnel = UnitPersonnel.objects.create(
                unit=selected_unit,
                registration_number=registration_number,
                first_name=first_name,
                last_name=last_name,
                full_name=full_name,
                rank=rank,
                position=position,
                gender=gender,
                birth_date=birth_date_obj,
                joining_date=joining_date_obj,
                phone_number=phone_number,
                work_system=work_system,
                assigned_shift=assigned_shift if work_system == '24_hours' else None,
                created_by=user
            )

            messages.success(request, f'تم إضافة العون {full_name} بنجاح')
            return redirect(f'/coordination-center/daily-unit-count/?unit_id={selected_unit.id}')

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إضافة العون: {str(e)}')

    # Get ranks and positions from the database
    from .models import PersonnelRank, PersonnelPosition

    # Create default ranks if none exist
    if not PersonnelRank.objects.exists():
        default_ranks = [
            'رقيب', 'رقيب أول', 'رقيب رئيسي', 'مساعد', 'مساعد أول',
            'مساعد رئيسي', 'ملازم', 'ملازم أول', 'نقيب', 'رائد', 'مقدم', 'عقيد'
        ]
        for rank_name in default_ranks:
            PersonnelRank.objects.get_or_create(name=rank_name, defaults={'created_by': user})

    # Create default positions if none exist
    if not PersonnelPosition.objects.exists():
        default_positions = [
            'قائد الوحدة', 'نائب قائد الوحدة', 'رئيس مصلحة', 'مسؤول العمليات',
            'مسؤول الإسعاف', 'مسؤول الإطفاء', 'سائق', 'عون إطفاء', 'مسعف',
            'تقني', 'إداري', 'حارس'
        ]
        for position_name in default_positions:
            PersonnelPosition.objects.get_or_create(name=position_name, defaults={'created_by': user})

    ranks = [(rank.name, rank.name) for rank in PersonnelRank.objects.all()]
    positions = [(position.name, position.name) for position in PersonnelPosition.objects.all()]

    context = {
        'selected_unit': selected_unit,
        'ranks': ranks,
        'positions': positions,
        'user_role': user_role,
    }

    return render(request, 'coordination_center/add_personnel.html', context)

@csrf_exempt
@login_required(login_url='login')
def add_equipment_view(request):
    """View for adding new equipment to a unit"""
    from .models import UnitEquipment

    # Get the user's profile
    user = request.user
    is_admin = user.is_superuser or user.is_staff

    # Get user wilaya and role safely
    user_wilaya = None
    user_role = None
    user_unit = None
    if hasattr(user, 'userprofile') and user.userprofile:
        user_wilaya = user.userprofile.wilaya
        user_role = user.userprofile.role
        if user_role == 'unit_coordinator':
            user_unit = user.userprofile.intervention_units.first()

    # Get available units based on user role
    if is_admin:
        units = InterventionUnit.objects.all()
    elif user_role == 'wilaya_manager' and user_wilaya:
        units = InterventionUnit.objects.filter(wilaya=user_wilaya)
    elif user_role in ['unit_manager', 'unit_coordinator'] and hasattr(user, 'userprofile'):
        units = user.userprofile.intervention_units.all()
    else:
        units = InterventionUnit.objects.none()

    # Get unit from URL parameter
    unit_id = request.GET.get('unit_id')
    selected_unit = None
    if unit_id:
        try:
            selected_unit = units.get(id=unit_id)
        except InterventionUnit.DoesNotExist:
            messages.error(request, 'الوحدة المحددة غير متاحة')
            return redirect('daily_unit_count')
    elif user_unit:
        selected_unit = user_unit

    if not selected_unit:
        messages.error(request, 'يجب تحديد وحدة صالحة')
        return redirect('daily_unit_count')

    # Handle POST request
    if request.method == 'POST':
        try:
            # Get form data
            equipment_type = request.POST.get('equipment_type', '').strip()
            radio_number = request.POST.get('radio_number', '').strip()
            license_plate = request.POST.get('license_plate', '').strip()

            # Handle new equipment type
            if equipment_type == 'add_new_type':
                new_type = request.POST.get('new_equipment_type', '').strip()
                if new_type:
                    equipment_type = new_type
                else:
                    messages.error(request, 'يجب إدخال نوع الوسيلة الجديد')
                    return render(request, 'coordination_center/add_equipment.html', {
                        'selected_unit': selected_unit,
                        'user_role': user_role,
                    })

            # Validate required fields
            if not equipment_type:
                messages.error(request, 'نوع الوسيلة مطلوب')
                return render(request, 'coordination_center/add_equipment.html', {
                    'selected_unit': selected_unit,
                    'user_role': user_role,
                })

            # Check if equipment already exists
            if UnitEquipment.objects.filter(
                unit=selected_unit,
                equipment_type=equipment_type,
                radio_number=radio_number,
                serial_number=license_plate
            ).exists():
                messages.error(request, 'هذه الوسيلة موجودة مسبقاً في الوحدة')
                return render(request, 'coordination_center/add_equipment.html', {
                    'selected_unit': selected_unit,
                    'user_role': user_role,
                })

            # Create new equipment
            equipment = UnitEquipment.objects.create(
                unit=selected_unit,
                equipment_type=equipment_type,
                radio_number=radio_number,
                serial_number=license_plate,
                created_by=user
            )

            messages.success(request, f'تم إضافة الوسيلة {equipment_type} بنجاح')
            return redirect(f'/coordination-center/daily-unit-count/?unit_id={selected_unit.id}')

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إضافة الوسيلة: {str(e)}')

    context = {
        'selected_unit': selected_unit,
        'user_role': user_role,
    }

    return render(request, 'coordination_center/add_equipment.html', context)

@login_required(login_url='login')
def manage_roles_view(request):
    """View for managing personnel roles and positions"""
    from django.http import JsonResponse
    from .models import PersonnelRank, PersonnelPosition

    # Check if user has admin privileges
    user = request.user
    is_admin = user.is_superuser or user.is_staff
    user_role = None
    if hasattr(user, 'userprofile') and user.userprofile:
        user_role = user.userprofile.role

    # Only admins and wilaya managers can manage roles
    if not (is_admin or user_role in ['admin', 'wilaya_manager']):
        return redirect('daily_unit_count')

    # Default ranks and positions
    default_ranks = [
        'رقيب',
        'رقيب أول',
        'رقيب رئيسي',
        'مساعد',
        'مساعد أول',
        'مساعد رئيسي',
        'ملازم',
        'ملازم أول',
        'نقيب',
        'رائد',
        'مقدم',
        'عقيد'
    ]

    default_positions = [
        'رئيس الوحدة',
        'مستخلف رئيس الوحدة',
        'قائد الفصيلة',
        'عون',
        'سائق',
        'مساعد سائق',
        'طبيب',
        'ممرض',
        'مسعف',
        'فني صيانة',
        'مشغل راديو',
        'كاتب',
        'محاسب'
    ]

    # Handle POST requests for adding/editing roles
    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'add_role':
            new_role = request.POST.get('role_name', '').strip()
            role_type = request.POST.get('role_type', '').strip()

            if not new_role:
                return JsonResponse({
                    'success': False,
                    'message': 'اسم الرتبة أو المنصب مطلوب'
                })

            if role_type == 'rank':
                # Add new rank
                rank, created = PersonnelRank.objects.get_or_create(
                    name=new_role,
                    defaults={'created_by': user}
                )
                if created:
                    return JsonResponse({
                        'success': True,
                        'message': f'تم إضافة الرتبة "{new_role}" بنجاح'
                    })
                else:
                    return JsonResponse({
                        'success': False,
                        'message': 'هذه الرتبة موجودة مسبقاً'
                    })
            elif role_type == 'position':
                # Add new position
                position, created = PersonnelPosition.objects.get_or_create(
                    name=new_role,
                    defaults={'created_by': user}
                )
                if created:
                    return JsonResponse({
                        'success': True,
                        'message': f'تم إضافة المنصب "{new_role}" بنجاح'
                    })
                else:
                    return JsonResponse({
                        'success': False,
                        'message': 'هذا المنصب موجود مسبقاً'
                    })
            else:
                return JsonResponse({
                    'success': False,
                    'message': 'يرجى اختيار نوع الرتبة أو المنصب'
                })

        elif action == 'edit_role':
            old_role = request.POST.get('old_role')
            new_role = request.POST.get('new_role', '').strip()
            if old_role and new_role:
                # In a real implementation, you would update the database
                return JsonResponse({
                    'success': True,
                    'message': f'تم تحديث الرتبة من "{old_role}" إلى "{new_role}" بنجاح'
                })
            else:
                return JsonResponse({
                    'success': False,
                    'message': 'بيانات الرتبة مطلوبة'
                })

        elif action == 'delete_role':
            role_to_delete = request.POST.get('role_name')
            if role_to_delete:
                # In a real implementation, you would check if role is in use
                # and then delete from database
                return JsonResponse({
                    'success': True,
                    'message': f'تم حذف الرتبة "{role_to_delete}" بنجاح'
                })
            else:
                return JsonResponse({
                    'success': False,
                    'message': 'اسم الرتبة مطلوب'
                })

    # Get all ranks and positions from database
    all_ranks = PersonnelRank.objects.all().order_by('name')
    all_positions = PersonnelPosition.objects.all().order_by('name')

    # Create default ranks and positions if they don't exist
    for rank_name in default_ranks:
        PersonnelRank.objects.get_or_create(name=rank_name, defaults={'created_by': user})

    for position_name in default_positions:
        PersonnelPosition.objects.get_or_create(name=position_name, defaults={'created_by': user})

    # Refresh the lists after creating defaults
    all_ranks = PersonnelRank.objects.all().order_by('name')
    all_positions = PersonnelPosition.objects.all().order_by('name')

    context = {
        'ranks': [rank.name for rank in all_ranks],
        'positions': [position.name for position in all_positions],
        'user_role': user_role,
        'is_admin': is_admin,
        'can_manage': is_admin or user_role in ['admin', 'wilaya_manager']
    }

    return render(request, 'coordination_center/manage_roles.html', context)

@login_required(login_url='login')
def export_table_to_excel(request, table_type):
    from django.http import HttpResponse
    import pandas as pd
    import numpy as np

    # Get the user's profile
    user = request.user
    is_admin = user.is_superuser or user.is_staff

    # Get user wilaya safely
    user_wilaya = None
    if hasattr(user, 'userprofile') and user.userprofile:
        user_wilaya = user.userprofile.wilaya

    # Get wilaya name for the filename
    wilaya_name = dict(WILAYA_CHOICES).get(user_wilaya, 'جميع_الولايات') if user_wilaya else 'جميع_الولايات'

    # Check if we have filtered IDs from the request
    filtered_ids = request.GET.get('ids')

    if table_type == 'traffic_accidents':
        # Get data from traffic accidents model
        base_queryset = TrafficAccident.objects

        # Apply user permission filter
        if not is_admin and user_wilaya:
            base_queryset = base_queryset.filter(
                unit__in=InterventionUnit.objects.filter(wilaya=user_wilaya).values_list('name', flat=True)
            )

        # Apply filters from request parameters
        month = request.GET.get('month')
        year = request.GET.get('year')
        unit = request.GET.get('unit')
        accident_type = request.GET.get('accident_type')
        accident_nature = request.GET.get('accident_nature')

        if month:
            base_queryset = base_queryset.filter(date__month=month)
        if year:
            base_queryset = base_queryset.filter(date__year=year)
        if unit:
            base_queryset = base_queryset.filter(unit=unit)
        if accident_type:
            base_queryset = base_queryset.filter(accident_type=accident_type)
        if accident_nature:
            base_queryset = base_queryset.filter(accident_nature=accident_nature)

        # Apply filtered IDs if provided
        if filtered_ids:
            id_list = filtered_ids.split(',')
            queryset = base_queryset.filter(id__in=id_list).order_by('-date')
        else:
            queryset = base_queryset.all().order_by('-date')

        # Create DataFrame
        data = []
        for item in queryset:
            data_row = {
                'التاريخ': item.date,
                'الوحدة المتدخلة': item.unit,
                'نوع الحادث': item.accident_type,
                'طبيعة الحادث': item.accident_nature,
                'عدد الحوادث': item.accidents_count,
                'عدد العمليات': item.operations_count,
                'نوع الطريق': item.road_type,
                'فئة السائقين': item.driver_age,
                'عدد الجرحى رجال': item.casualties_men,
                'عدد الجرحى نساء': item.casualties_women,
                'عدد الجرحى أطفال': item.casualties_children,
                'مجموع الجرحى': item.total_casualties,
                'عدد الوفيات رجال': item.fatalities_men,
                'عدد الوفيات نساء': item.fatalities_women,
                'عدد الوفيات أطفال': item.fatalities_children,
                'مجموع الوفيات': item.total_fatalities,
                'سيارات وقود': item.fuel_cars,
                'سيارات غاز مميع': item.lpg_cars,
                'شاحنات': item.trucks,
                'حافلات': item.buses,
                'دراجات': item.motorcycles,
                'جرارات': item.tractors,
                'النقل موجه': item.directed_transport,
                'أخرى': item.other_vehicles,
                '06:00 - 09:00': item.time_06_09,
                '09:00 - 12:00': item.time_09_12,
                '12:00 - 14:00': item.time_12_14,
                '14:00 - 16:00': item.time_14_16,
                '16:00 - 20:00': item.time_16_20,
                '20:00 - 00:00': item.time_20_00,
                '00:00 - 06:00': item.time_00_06,
                'الأحد': item.sunday,
                'الإثنين': item.monday,
                'الثلاثاء': item.tuesday,
                'الأربعاء': item.wednesday,
                'الخميس': item.thursday,
                'الجمعة': item.friday,
                'السبت': item.saturday,
            }

            # Add wilaya name for admin users
            if is_admin:
                try:
                    unit_obj = InterventionUnit.objects.get(name=item.unit)
                    wilaya_name_from_code = dict(WILAYA_CHOICES).get(unit_obj.wilaya, 'غير محدد')
                    data_row['الولاية'] = wilaya_name_from_code
                except InterventionUnit.DoesNotExist:
                    data_row['الولاية'] = 'غير محدد'

            data.append(data_row)

        # Arabic filename will be generated by get_arabic_filename function

    elif table_type == 'general-fire':
        # Get data from general fire data model
        base_queryset = GeneralFireData.objects

        # Apply user permission filter
        if not is_admin:
            base_queryset = base_queryset.filter(
                intervening_unit__in=InterventionUnit.objects.filter(wilaya=user_wilaya).values_list('name', flat=True)
            )

        # Apply filtered IDs if provided
        if filtered_ids:
            id_list = filtered_ids.split(',')
            queryset = base_queryset.filter(id__in=id_list).order_by('-date')
        else:
            queryset = base_queryset.all().order_by('-date')

        # Create DataFrame
        data = []
        for item in queryset:
            data.append({
                'التاريخ': item.date,
                'الوحدات المتدخلة': item.intervening_unit,
                'أنواع الحرائق': item.fire_type,
                'عدد الحرائق': item.number_of_fires,
                'عدد التدخلات': item.number_of_interventions,
                'عدد الجرحى': item.number_of_injured,
                'عدد الوفيات': item.number_of_deaths,
            })

        # Arabic filename will be generated by get_arabic_filename function



    elif table_type == 'public-area-fires':
        # Get data from public area fires model
        # Create base queryset
        base_queryset = PublicAreaFireData.objects

        # Apply user permission filter
        if not is_admin:
            base_queryset = base_queryset.filter(
                intervening_unit__in=InterventionUnit.objects.filter(wilaya=user_wilaya).values_list('name', flat=True)
            )

        # Apply filtered IDs if provided
        if filtered_ids:
            id_list = filtered_ids.split(',')
            queryset = base_queryset.filter(id__in=id_list).order_by('-date')
        else:
            queryset = base_queryset.all().order_by('-date')

        # Create DataFrame
        data = []
        for item in queryset:
            data.append({
                'التاريخ': item.date,
                'الوحدات المتدخلة': item.intervening_unit,
                'عدد الحرائق': item.number_of_fires,
                'عدد التدخلات': item.number_of_interventions,
                'عدد الجرحى': item.number_of_injured,
                'عدد الوفيات': item.number_of_deaths,
                'إسم المؤسسة': item.institution_name,
                'النوع': item.institution_type,
                'الصنف': item.institution_category,
                'سيارة إسعاف': item.ambulances,
                'شاحنة إطفاء': item.fire_trucks,
                'السلم الميكانيكي': item.mechanical_ladders,
                'وسائل أخرى': item.other_resources,
                'مدة التدخل': item.intervention_duration,
            })

        # Arabic filename will be generated by get_arabic_filename function

    elif table_type == 'institutional-fires':
        # Get data from institutional fires model
        # Create base queryset
        base_queryset = InstitutionalFireData.objects

        # Apply user permission filter
        if not is_admin:
            base_queryset = base_queryset.filter(
                intervening_unit__in=InterventionUnit.objects.filter(wilaya=user_wilaya).values_list('name', flat=True)
            )

        # Apply filtered IDs if provided
        if filtered_ids:
            id_list = filtered_ids.split(',')
            queryset = base_queryset.filter(id__in=id_list).order_by('-date')
        else:
            queryset = base_queryset.all().order_by('-date')

        # Create DataFrame
        data = []
        for item in queryset:
            data.append({
                'التاريخ': item.date,
                'الوحدات المتدخلة': item.intervening_unit,
                'نوع الحادث': item.incident_type,
                'عدد العمليات': item.operations_count,
                'طبيعة النشاط': item.activity_nature,
                'إسم المؤسسة المصنفة': item.institution_name,
                'النشاط': item.activity,
                'الصنف': item.class_type,
                'السنة (PII)': 1 if item.year_pii else 0,
                'السنة (EDD)': 1 if item.year_edd else 0,
                'منطقة فلاحية (ZR)': 1 if item.zone_zr else 0,
                'منطقة حضرية (ZU)': 1 if item.zone_zu else 0,
                'منطقة النشاطات التجارية (ZA)': 1 if item.zone_za else 0,
                'منطقة صناعية (ZI)': 1 if item.zone_zi else 0,
                'عدد الجرحى': item.injured_count,
                'عدد الوفيات': item.deaths_count,
                'سيارة إسعاف': item.ambulances,
                'شاحنة إطفاء': item.fire_trucks,
                'السلم الميكانيكي': item.mechanical_ladders,
                'وسائل أخرى': item.other_resources,
                'مدة التدخل': item.intervention_duration,
            })

        # Arabic filename will be generated by get_arabic_filename function

    elif table_type == 'residential-fires':
        # Get data from residential fires model
        # Create base queryset
        base_queryset = ResidentialFireData.objects

        # Apply user permission filter
        if not is_admin:
            base_queryset = base_queryset.filter(
                intervening_unit__in=InterventionUnit.objects.filter(wilaya=user_wilaya).values_list('name', flat=True)
            )

        # Apply filtered IDs if provided
        if filtered_ids:
            id_list = filtered_ids.split(',')
            queryset = base_queryset.filter(id__in=id_list).order_by('-date')
        else:
            queryset = base_queryset.all().order_by('-date')

        # Create DataFrame
        data = []
        for item in queryset:
            data.append({
                'التاريخ': item.date,
                'الوحدات المتدخلة': item.intervening_unit,
                'العائلة الأولى': 1 if item.family_first else 0,
                'العائلة الثانية': 1 if item.family_second else 0,
                'العائلة الثالثة': 1 if item.family_third else 0,
                'العائلة الرابعة': 1 if item.family_fourth else 0,
                'عدد الحرائق': item.number_of_fires,
                'عدد التدخلات': item.number_of_interventions,
                'عدد الجرحى': item.number_of_injured,
                'عدد الوفيات': item.number_of_deaths,
                'سيارة إسعاف': item.ambulances,
                'شاحنة إطفاء': item.fire_trucks,
                'السلم الميكانيكي': item.mechanical_ladders,
                'وسائل أخرى': item.other_resources,
                'مدة التدخل': item.intervention_duration,
            })

        # Arabic filename will be generated by get_arabic_filename function

    elif table_type == 'exceptional-operations':
        # Get data from misc operations model filtered by exceptional operations
        # Create base queryset with exceptional operations filter
        base_queryset = MiscOperationsData.objects.filter(
            operation_type__in=['إنقاذ الحيوانات', 'الكوارث الطبيعية', 'الحوادث التكنولوجية']
        )

        # Apply user permission filter
        if not is_admin:
            base_queryset = base_queryset.filter(
                intervening_unit__in=InterventionUnit.objects.filter(wilaya=user_wilaya).values_list('name', flat=True)
            )

        # Apply filtered IDs if provided
        if filtered_ids:
            id_list = filtered_ids.split(',')
            queryset = base_queryset.filter(id__in=id_list).order_by('-date')
        else:
            queryset = base_queryset.all().order_by('-date')

        # Create DataFrame
        data = []
        for item in queryset:
            data_row = {
                'التاريخ': item.date,
                'الوحدات المتدخلة': item.intervening_unit,
                'أنواع العمليات الإستثنائية': item.operation_type,
                'عدد العمليات': item.number_of_operations,
                'عدد التدخلات': item.number_of_interventions,
            }

            # Add resources data if animal rescue
            if item.operation_type == 'إنقاذ الحيوانات':
                data_row['سيارة إسعاف'] = item.ambulances or 0
                data_row['شاحنة إطفاء'] = item.fire_trucks or 0
                data_row['السلم الميكانيكي'] = item.mechanical_ladders or 0
                data_row['وسائل أخرى'] = item.other_resources or 0

            data.append(data_row)

        # Arabic filename will be generated by get_arabic_filename function

    elif table_type == 'misc-operations':
        # Get data from misc operations model
        # Create base queryset with misc operations filter
        base_queryset = MiscOperationsData.objects.filter(
            operation_type__in=['نقل الجثث', 'أشخاص دون مأوى', 'عمليات مختلفة']
        )

        # Apply user permission filter
        if not is_admin:
            base_queryset = base_queryset.filter(
                intervening_unit__in=InterventionUnit.objects.filter(wilaya=user_wilaya).values_list('name', flat=True)
            )

        # Apply filtered IDs if provided
        if filtered_ids:
            id_list = filtered_ids.split(',')
            queryset = base_queryset.filter(id__in=id_list).order_by('-date')
        else:
            queryset = base_queryset.all().order_by('-date')

        # Create DataFrame
        data = []
        for item in queryset:
            data_row = {
                'التاريخ': item.date,
                'الوحدات المتدخلة': item.intervening_unit,
                'أنواع العمليات': item.operation_type,
                'عدد العمليات': item.number_of_operations,
                'عدد التدخلات': item.number_of_interventions,
            }

            # Add human losses data based on operation type
            if item.operation_type in ['نقل الجثث', 'أشخاص دون مأوى']:
                data_row['عدد النساء'] = item.number_of_women
                data_row['عدد الرجال'] = item.number_of_men
                data_row['عدد الأطفال'] = item.number_of_children
            else:
                data_row['عدد المسعفين'] = item.number_of_rescuers
                data_row['عدد الوفيات'] = item.number_of_deaths

            data.append(data_row)

        # Arabic filename will be generated by get_arabic_filename function

    elif table_type == 'forest-agricultural-fires':
        # Get data from forest agricultural fires model
        # Create base queryset
        base_queryset = ForestAgriculturalFireData.objects

        # Apply user permission filter
        if not is_admin:
            base_queryset = base_queryset.filter(
                intervening_unit__in=InterventionUnit.objects.filter(wilaya=user_wilaya).values_list('name', flat=True)
            )

        # Apply filtered IDs if provided
        if filtered_ids:
            id_list = filtered_ids.split(',')
            queryset = base_queryset.filter(id__in=id_list).order_by('-date')
        else:
            queryset = base_queryset.all().order_by('-date')

        # Create DataFrame
        data = []
        for item in queryset:
            data_row = {
                'التاريخ': item.date,
                'البلدية': item.municipality,
                'الوحدات المتدخلة': item.intervening_unit,
                'نوع الحريق': item.fire_type,
                'عدد الحرائق': item.number_of_fires,
                'عدد التدخلات': item.number_of_interventions,
                'نوع الخسائر': item.loss_type,
            }

            # Add losses based on fire type
            if item.fire_type in ['حرائق الغابات و الأدغال', 'حرائق المحاصيل', 'حرائق الأعشاب']:
                data_row['الخسائر بالهكتار'] = item.losses_hectare
                data_row['الخسائر بالآر'] = item.losses_are
                data_row['الخسائر بالمتر مربع'] = item.losses_square_meter
            elif item.fire_type in ['حرائق الأعلاف', 'حرائق النخيل', 'حرائق الأشجار المثمرة']:
                data_row['عدد الخسائر'] = item.losses_count

            data.append(data_row)

        # Arabic filename will be generated by get_arabic_filename function

    elif table_type == 'interventions-without-work':
        # Get data from misc operations model filtered by interventions without work
        # Create base queryset with interventions without work filter
        base_queryset = MiscOperationsData.objects.filter(
            number_of_interventions=0
        )

        # Apply user permission filter
        if not is_admin:
            base_queryset = base_queryset.filter(
                intervening_unit__in=InterventionUnit.objects.filter(wilaya=user_wilaya).values_list('name', flat=True)
            )

        # Apply filtered IDs if provided
        if filtered_ids:
            id_list = filtered_ids.split(',')
            queryset = base_queryset.filter(id__in=id_list).order_by('-date')
        else:
            queryset = base_queryset.all().order_by('-date')

        # Create DataFrame
        data = []
        for item in queryset:
            data.append({
                'التاريخ': item.date,
                'الوحدات المتدخلة': item.intervening_unit,
                'أنواع التدخلات بدون عمل': item.operation_type,
                'ع ع أو ع ح': item.number_of_operations,
                'سيارة': item.cars or 0,
                'شاحنة': item.trucks or 0,
                'وسائل أخرى': item.other_resources or 0,
            })

        # Arabic filename will be generated by get_arabic_filename function

    elif table_type == 'medical_evacuation':
        # Get data from MedicalEvacuation model
        from django.db import connection

        # Apply filters from request parameters
        filters = []
        params = []

        # Get filter parameters from request
        month = request.GET.get('month')
        year = request.GET.get('year')
        unit = request.GET.get('unit')
        intervention_type = request.GET.get('intervention_type')
        intervention_nature = request.GET.get('intervention_nature')
        location = request.GET.get('location')
        wilaya_filter = request.GET.get('wilaya')

        # Base SQL query
        sql = """
            SELECT
                me.id,
                me.date,
                CASE
                    WHEN me.is_indoor = true THEN 'داخل المنزل'
                    ELSE 'خارج المنزل'
                END as location,
                me.operations_count,
                me.interventions_count,
                me.paramedics_children_count,
                me.paramedics_women_count,
                me.paramedics_men_count,
                me.deaths_children_count,
                me.deaths_women_count,
                me.deaths_men_count,
                it.name as intervention_type,
                inn.name as intervention_nature,
                iu.name as unit_name,
                iu.wilaya
            FROM
                data_entry_medicalevacuation me
            INNER JOIN
                home_interventionunit iu ON me.unit_id = iu.id
            INNER JOIN
                data_entry_interventiontype it ON me.intervention_type_id = it.id
            INNER JOIN
                data_entry_interventionnature inn ON me.intervention_nature_id = inn.id
            WHERE 1=1
        """

        # Add filters based on request parameters
        if month:
            filters.append("EXTRACT(MONTH FROM me.date) = %s")
            params.append(month)

        if year:
            filters.append("EXTRACT(YEAR FROM me.date) = %s")
            params.append(year)

        if unit:
            filters.append("iu.name = %s")
            params.append(unit)

        if intervention_type:
            filters.append("it.name = %s")
            params.append(intervention_type)

        if intervention_nature:
            filters.append("inn.name = %s")
            params.append(intervention_nature)

        if location:
            if location == 'داخل المنزل':
                filters.append("me.is_indoor = true")
            elif location == 'خارج المنزل':
                filters.append("me.is_indoor = false")

        # Apply user permission filter
        if not is_admin:
            filters.append("iu.wilaya = %s")
            params.append(user_wilaya)
        elif wilaya_filter:
            filters.append("iu.wilaya = %s")
            params.append(wilaya_filter)

        # Add filters to SQL query
        if filters:
            sql += " AND " + " AND ".join(filters)

        # Order by date descending
        sql += " ORDER BY me.date DESC"

        # Execute the query
        with connection.cursor() as cursor:
            cursor.execute(sql, params)
            columns = [col[0] for col in cursor.description]
            results = cursor.fetchall()

        # Create DataFrame
        data = []
        for row in results:
            result_dict = dict(zip(columns, row))

            # Format date for display
            from datetime import datetime
            date_obj = result_dict['date']

            # Get month name in Arabic
            month_names = {
                1: 'جانفي', 2: 'فيفري', 3: 'مارس', 4: 'أفريل',
                5: 'ماي', 6: 'جوان', 7: 'جويلية', 8: 'أوت',
                9: 'سبتمبر', 10: 'أكتوبر', 11: 'نوفمبر', 12: 'ديسمبر'
            }

            # Format date as "DD month_name YYYY"
            formatted_date = f"{date_obj.day} {month_names[date_obj.month]} {date_obj.year}"

            # Get wilaya name from code
            wilaya_code = result_dict['wilaya']
            wilaya_name_from_code = dict(WILAYA_CHOICES).get(wilaya_code, 'غير معروف')

            data_row = {
                'التاريخ': formatted_date,
                'الوحدة المتدخلة': result_dict['unit_name'],
                'نوع التدخل': result_dict['intervention_type'],
                'طبيعة التدخل': result_dict['intervention_nature'],
                'داخل المنزل': 1 if result_dict['location'] == 'داخل المنزل' else 0,
                'خارج المنزل': 1 if result_dict['location'] == 'خارج المنزل' else 0,
                'عدد العمليات': result_dict['operations_count'],
                'عدد التدخلات': result_dict['interventions_count'],
                # Detailed fields
                'عدد المسعفين أطفال': result_dict.get('paramedics_children_count', 0),
                'عدد المسعفين نساء': result_dict.get('paramedics_women_count', 0),
                'عدد المسعفين رجال': result_dict.get('paramedics_men_count', 0),
                'مجموع المسعفين التفصيلي': (result_dict.get('paramedics_children_count', 0) +
                                        result_dict.get('paramedics_women_count', 0) +
                                        result_dict.get('paramedics_men_count', 0)),
                'عدد الوفيات أطفال': result_dict.get('deaths_children_count', 0),
                'عدد الوفيات نساء': result_dict.get('deaths_women_count', 0),
                'عدد الوفيات رجال': result_dict.get('deaths_men_count', 0),
                'مجموع الوفيات التفصيلي': (result_dict.get('deaths_children_count', 0) +
                                       result_dict.get('deaths_women_count', 0) +
                                       result_dict.get('deaths_men_count', 0)),
            }

            # Add detailed columns for all intervention types that use detailed fields
            detailed_intervention_types = ['الاختناق', 'التسممات', 'الحروق', 'الانفجارات', 'إجلاء المرضى', 'الغرقى']
            if result_dict['intervention_type'] in detailed_intervention_types:
                data_row.update({
                    'عدد المسعفين أطفال': result_dict['paramedics_children_count'],
                    'عدد المسعفين نساء': result_dict['paramedics_women_count'],
                    'عدد المسعفين رجال': result_dict['paramedics_men_count'],
                    'مجموع المسعفين التفصيلي': result_dict['paramedics_children_count'] + result_dict['paramedics_women_count'] + result_dict['paramedics_men_count'],
                    'عدد الوفيات أطفال': result_dict['deaths_children_count'],
                    'عدد الوفيات نساء': result_dict['deaths_women_count'],
                    'عدد الوفيات رجال': result_dict['deaths_men_count'],
                    'مجموع الوفيات التفصيلي': result_dict['deaths_children_count'] + result_dict['deaths_women_count'] + result_dict['deaths_men_count']
                })

            # Add wilaya name for admin users
            if is_admin:
                data_row['الولاية'] = wilaya_name_from_code

            data.append(data_row)

        # Arabic filename will be generated by get_arabic_filename function

    elif table_type == 'coordination-forest-fires':
        # Get data from coordination center forest fires model
        # Create base queryset
        base_queryset = CoordinationCenterForestFire.objects

        # Apply user permission filter
        if not is_admin and user_wilaya:
            user_units = InterventionUnit.objects.filter(wilaya=user_wilaya).values_list('name', flat=True)
            base_queryset = base_queryset.filter(intervening_unit__in=user_units)

        # Apply filtered IDs if provided
        if filtered_ids:
            id_list = filtered_ids.split(',')
            queryset = base_queryset.filter(id__in=id_list).order_by('-date', '-telegram_number')
        else:
            queryset = base_queryset.all().order_by('-date', '-telegram_number')

        # Create DataFrame
        data = []
        for item in queryset:
            # Format date in Arabic
            from .utils import format_date_algerian_arabic
            formatted_date = format_date_algerian_arabic(item.date)

            row_data = {
                'التاريخ': formatted_date,
                'رقم البرقية': item.telegram_number,
                'ساعة التدخل': item.intervention_time,
                'الوحدة المتدخلة': item.intervening_unit,
                'البلدية': item.municipality,
                'المكان المسمى': item.location_name,
                'مدة العملية': item.operation_duration,
                'الوسائل المتدخلة': item.intervention_means,
                'طبيعة الخسائر': item.loss_nature,
                'الخسائر بالهكتار': item.losses_hectare,
                'الخسائر بالآر': item.losses_are,
                'الخسائر بالمتر مربع': item.losses_square_meter,
                'طبيعة الخسائر الأخرى': item.other_loss_nature or '',
                'عدد الخسائر': item.other_loss_count or 0,
                'وضعية التحكم في الحريق': item.fire_control_status,
                'عدد الجرحى': item.injured_count,
                'عدد الوفيات': item.deaths_count,
                'عدد العائلات المجلاة': item.evacuated_families_count,
                'عدد الأشخاص المجلين': item.evacuated_people_count,
                'أماكن الإجلاء': item.evacuation_locations or '',
                'إجراءات التكفل': item.family_care_measures or ''
            }

            # Add wilaya column for admins
            if is_admin:
                # Get wilaya from intervening unit
                try:
                    unit = InterventionUnit.objects.get(name=item.intervening_unit)
                    wilaya_name_from_unit = dict(WILAYA_CHOICES).get(unit.wilaya, 'غير محدد')
                    row_data['الولاية'] = wilaya_name_from_unit
                except InterventionUnit.DoesNotExist:
                    row_data['الولاية'] = 'غير محدد'

            data.append(row_data)

        # Reorder columns to put wilaya after date for admins
        if is_admin:
            column_order = ['التاريخ', 'الولاية', 'رقم البرقية', 'ساعة التدخل', 'الوحدة المتدخلة', 'البلدية', 'المكان المسمى', 'مدة العملية', 'الوسائل المتدخلة', 'طبيعة الخسائر', 'الخسائر بالهكتار', 'الخسائر بالآر', 'الخسائر بالمتر مربع', 'طبيعة الخسائر الأخرى', 'عدد الخسائر', 'وضعية التحكم في الحريق', 'عدد الجرحى', 'عدد الوفيات', 'عدد العائلات المجلاة', 'عدد الأشخاص المجلين', 'أماكن الإجلاء', 'إجراءات التكفل']
        else:
            column_order = ['التاريخ', 'رقم البرقية', 'ساعة التدخل', 'الوحدة المتدخلة', 'البلدية', 'المكان المسمى', 'مدة العملية', 'الوسائل المتدخلة', 'طبيعة الخسائر', 'الخسائر بالهكتار', 'الخسائر بالآر', 'الخسائر بالمتر مربع', 'طبيعة الخسائر الأخرى', 'عدد الخسائر', 'وضعية التحكم في الحريق', 'عدد الجرحى', 'عدد الوفيات', 'عدد العائلات المجلاة', 'عدد الأشخاص المجلين', 'أماكن الإجلاء', 'إجراءات التكفل']

        df = pd.DataFrame(data)
        if not df.empty:
            df = df[column_order]

        # Arabic filename will be generated by get_arabic_filename function

    elif table_type == 'coordination-crop-fires':
        # Get data from coordination center crop fires model
        # Create base queryset
        base_queryset = CoordinationCenterCropFire.objects

        # Apply user permission filter
        if not is_admin and user_wilaya:
            user_units = InterventionUnit.objects.filter(wilaya=user_wilaya).values_list('name', flat=True)
            base_queryset = base_queryset.filter(intervening_unit__in=user_units)

        # Apply filtered IDs if provided
        if filtered_ids:
            id_list = filtered_ids.split(',')
            queryset = base_queryset.filter(id__in=id_list).order_by('-date', '-telegram_number')
        else:
            queryset = base_queryset.all().order_by('-date', '-telegram_number')

        # Create DataFrame
        data = []
        for item in queryset:
            # Format date in Arabic
            from .utils import format_date_algerian_arabic
            formatted_date = format_date_algerian_arabic(item.date)

            row_data = {
                'التاريخ': formatted_date,
                'رقم البرقية': item.telegram_number,
                'ساعة التدخل': item.intervention_time,
                'الوحدة المتدخلة': item.intervening_unit,
                'البلدية': item.municipality,
                'المكان المسمى': item.location_name,
                'مدة العملية': item.operation_duration,
                'الوسائل المتدخلة': item.intervention_means,
                'طبيعة الخسائر': item.loss_nature,
                'الخسائر بالهكتار': item.losses_hectare,
                'الخسائر بالآر': item.losses_are,
                'الخسائر بالمتر مربع': item.losses_square_meter,
                'طبيعة الخسائر الأخرى': item.other_loss_nature or '',
                'عدد الخسائر': item.other_loss_count or 0,
                'وضعية التحكم في الحريق': item.fire_control_status,
                'عدد الجرحى': item.injured_count,
                'عدد الوفيات': item.deaths_count,
                'عدد العائلات المجلاة': item.evacuated_families_count,
                'عدد الأشخاص المجلين': item.evacuated_people_count,
                'أماكن الإجلاء': item.evacuation_locations or '',
                'إجراءات التكفل': item.family_care_measures or ''
            }

            # Add wilaya column for admins
            if is_admin:
                # Get wilaya from intervening unit
                try:
                    unit = InterventionUnit.objects.get(name=item.intervening_unit)
                    wilaya_name_from_unit = dict(WILAYA_CHOICES).get(unit.wilaya, 'غير محدد')
                    row_data['الولاية'] = wilaya_name_from_unit
                except InterventionUnit.DoesNotExist:
                    row_data['الولاية'] = 'غير محدد'

            data.append(row_data)

        # Reorder columns to put wilaya after date for admins
        if is_admin:
            column_order = ['التاريخ', 'الولاية', 'رقم البرقية', 'ساعة التدخل', 'الوحدة المتدخلة', 'البلدية', 'المكان المسمى', 'مدة العملية', 'الوسائل المتدخلة', 'طبيعة الخسائر', 'الخسائر بالهكتار', 'الخسائر بالآر', 'الخسائر بالمتر مربع', 'طبيعة الخسائر الأخرى', 'عدد الخسائر', 'وضعية التحكم في الحريق', 'عدد الجرحى', 'عدد الوفيات', 'عدد العائلات المجلاة', 'عدد الأشخاص المجلين', 'أماكن الإجلاء', 'إجراءات التكفل']
        else:
            column_order = ['التاريخ', 'رقم البرقية', 'ساعة التدخل', 'الوحدة المتدخلة', 'البلدية', 'المكان المسمى', 'مدة العملية', 'الوسائل المتدخلة', 'طبيعة الخسائر', 'الخسائر بالهكتار', 'الخسائر بالآر', 'الخسائر بالمتر مربع', 'طبيعة الخسائر الأخرى', 'عدد الخسائر', 'وضعية التحكم في الحريق', 'عدد الجرحى', 'عدد الوفيات', 'عدد العائلات المجلاة', 'عدد الأشخاص المجلين', 'أماكن الإجلاء', 'إجراءات التكفل']

        df = pd.DataFrame(data)
        if not df.empty:
            df = df[column_order]

        # Arabic filename will be generated by get_arabic_filename function

    else:
        # Unsupported table type
        return HttpResponse("نوع الجدول غير مدعوم", status=400)

    # Create DataFrame
    df = pd.DataFrame(data)

    # Remove any existing sum rows (rows with 'المجموع' in the first column)
    if not df.empty and len(df.columns) > 0:
        first_col = df.columns[0]
        df = df[df[first_col] != 'المجموع']

    # Add a single sum row for numeric columns at the end of the workbook
    if not df.empty:
        # Identify truly numeric columns (exclude text that might be converted to numbers)
        numeric_cols = []
        for col in df.columns:
            try:
                # Convert to numeric and check if it's actually numeric data
                numeric_series = pd.to_numeric(df[col], errors='coerce')
                # Only include if more than 50% of values are numeric and not all zeros
                if numeric_series.notna().sum() > len(df) * 0.5 and numeric_series.sum() != 0:
                    numeric_cols.append(col)
            except:
                continue

        if len(numeric_cols) > 0:
            sum_row = {}
            for col in df.columns:
                if col in numeric_cols:
                    # Calculate sum only for numeric columns
                    numeric_values = pd.to_numeric(df[col], errors='coerce').fillna(0)
                    sum_row[col] = numeric_values.sum()
                else:
                    # Leave text columns empty in sum row
                    sum_row[col] = ''

            sum_row_df = pd.DataFrame([sum_row])

            # Add a label for the sum row in the first column
            first_col = df.columns[0]
            sum_row_df[first_col] = 'المجموع'

            # Concatenate the sum row to the DataFrame
            df = pd.concat([df, sum_row_df], ignore_index=True)

    # Create Excel response
    response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

    # Set Arabic filename with proper encoding
    from urllib.parse import quote
    arabic_filename = get_arabic_filename(table_type, wilaya_name)
    encoded_filename = quote(arabic_filename)
    response['Content-Disposition'] = f'attachment; filename*=UTF-8\'\'{encoded_filename}.xlsx'

    try:
        # Create Excel writer with xlsxwriter engine to support RTL
        import io
        output = io.BytesIO()

        # Handle NaN values properly - replace with empty string for text columns, 0 for numeric
        for col in df.columns:
            if df[col].dtype == 'object':  # Text columns
                df[col] = df[col].fillna('')
                # Add line breaks for long text (more than 40 characters) - break at spaces
                df[col] = df[col].apply(lambda x:
                    '\n'.join([str(x)[i:i+40] for i in range(0, len(str(x)), 40)])
                    if len(str(x)) > 40 else str(x)
                )
            else:  # Numeric columns
                df[col] = df[col].fillna(0)

        # Create Excel writer with options to handle NaN/INF values
        writer = pd.ExcelWriter(
            output,
            engine='xlsxwriter',
            engine_kwargs={'options': {'nan_inf_to_errors': True}}
        )

        # Write DataFrame to Excel starting from row 3 to leave space for title
        df.to_excel(writer, index=False, sheet_name='البيانات', startrow=2)

        # Get the xlsxwriter workbook and worksheet objects
        workbook = writer.book
        worksheet = writer.sheets['البيانات']

        # Set RTL direction for the worksheet
        worksheet.right_to_left()

        # Add title based on table type
        title_text = ""
        if table_type == 'coordination-forest-fires':
            title_text = f"الجدول العام لحرائق الغابات - مديرية الحماية المدنية لولاية {wilaya_name}"
        elif table_type == 'coordination-crop-fires':
            title_text = f"الجدول العام لحرائق المحاصيل الزراعية - مديرية الحماية المدنية لولاية {wilaya_name}"
        else:
            # Default title for other table types
            title_text = f"{get_arabic_filename(table_type, wilaya_name).replace('_', ' ')}"

        # Add title with modern formatting
        if title_text:
            # Merge cells for title (across all columns)
            num_cols = len(df.columns)
            worksheet.merge_range(0, 0, 0, num_cols-1, title_text)

            # Format title with bold, big font (16pt)
            title_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'font_color': 'black',
                'align': 'center',
                'valign': 'vcenter',
                'text_wrap': True,
                'border': 1,
                'bg_color': '#E6E6FA'  # Light background
            })

            # Apply title format
            worksheet.write(0, 0, title_text, title_format)

        # Freeze the header row so it stays visible when scrolling (now row 3)
        worksheet.freeze_panes(3, 0)

        # Format headers with black bold fonts and proper background
        header_format = workbook.add_format({
            'bold': True,
            'font_color': 'black',
            'text_wrap': True,
            'valign': 'top',
            'align': 'center',
            'border': 1,
            'bg_color': '#E6E6FA'  # Light background for headers
        })

        # Apply header format (now on row 3, index 2)
        for col_num, value in enumerate(df.columns.values):
            worksheet.write(2, col_num, value, header_format)

        # Auto-adjust column widths
        for i, col in enumerate(df.columns):
            try:
                # Find the maximum length of the column
                # Convert all values to strings and handle NaN/None values
                col_data = df[col].fillna('').astype(str)
                max_data_len = col_data.str.len().max() if not col_data.empty else 0
                col_len = len(str(col))
                max_len = max(max_data_len, col_len) + 2

                # Set a reasonable maximum width to prevent extremely wide columns
                max_len = min(max_len, 50)

                worksheet.set_column(i, i, max_len)
            except Exception as e:
                # If there's an error, set a default width
                worksheet.set_column(i, i, 15)

        # Format the sum row with amber background and black bold fonts
        if not df.empty and len(numeric_cols) > 0:
            sum_row_format = workbook.add_format({
                'bold': True,
                'font_color': 'black',
                'bg_color': '#FFC000',  # Amber background color
                'border': 1,
                'align': 'center'
            })
            # The sum row is the last row in the DataFrame + 2 (for title and header)
            sum_row_index = len(df) + 2
            # Write the sum row with amber background and black bold formatting
            for col_num, _ in enumerate(df.columns):
                try:
                    # Get the value from the DataFrame
                    value = df.iloc[-1, col_num]  # Last row of DataFrame

                    # Handle NaN, INF, or other problematic values
                    if pd.isna(value) or pd.api.types.is_float(value) and (np.isinf(value) or np.isnan(value)):
                        value = 0

                    # Write to the worksheet with the amber background format
                    worksheet.write(sum_row_index, col_num, value, sum_row_format)
                except Exception as e:
                    # If there's an error, write a zero or empty string
                    worksheet.write(sum_row_index, col_num, 0, sum_row_format)

        # Close the writer and get the output
        writer.close()
        xlsx_data = output.getvalue()

        # Write to response
        response.write(xlsx_data)

        return response

    except PermissionError as e:
        # Handle permission errors
        error_message = f"ليس لديك إذن للوصول إلى الملف: {str(e)}"
        return HttpResponse(error_message, status=403)

    except Exception as e:
        # Handle other errors
        error_message = f"حدث خطأ أثناء إنشاء ملف Excel: {str(e)}"
        return HttpResponse(error_message, status=500)

def get_arabic_filename(table_type, wilaya_name):
    """Get Arabic filename based on table type"""
    arabic_titles = {
        'traffic_accidents': f'جدول_حوادث_المرور_{wilaya_name}',
        'general-fire': f'الجدول_العام_للحرائق_{wilaya_name}',
        'public-area-fires': f'الحرائق_في_الأماكن_المستقبلة_للجمهور_{wilaya_name}',
        'institutional-fires': f'الحرائق_في_المؤسسات_المصنفة_{wilaya_name}',
        'residential-fires': f'الحرائق_في_البنايات_المخصصة_للسكن_{wilaya_name}',
        'exceptional-operations': f'إحصاء_العمليات_الإستثنائية_{wilaya_name}',
        'misc-operations': f'إحصاء_العمليات_المختلفة_{wilaya_name}',
        'forest-agricultural-fires': f'حرائق_الغابات_والمحاصيل_الزراعية_{wilaya_name}',
        'coordination-forest-fires': f'الجدول_العام_لحرائق_الغابات_مديرية_الحماية_المدنية_لولاية_{wilaya_name}',
        'coordination-crop-fires': f'الجدول_العام_لحرائق_المحاصيل_الزراعية_مديرية_الحماية_المدنية_لولاية_{wilaya_name}',
        'interventions-without-work': f'إحصاء_التدخلات_بدون_عمل_{wilaya_name}',
        'medical_evacuation': f'الإجلاء_الصحي_{wilaya_name}'
    }

    return arabic_titles.get(table_type, f'بيانات_{table_type}_{wilaya_name}')


# ==================== العروض الجديدة للكوارث الكبرى والتدخلات ====================

@login_required(login_url='login')
def major_disasters_view(request):
    """
    صفحة الكوارث الكبرى - واجهة إدارة الكوارث الكبرى مع خريطة تفاعلية
    """
    context = {
        'page_title': 'الكوارث الكبرى',
        'user': request.user,
    }
    return render(request, 'major_disasters/index.html', context)


@login_required(login_url='login')
def field_agent_view(request):
    """
    واجهة رئيس العدد (العون الميداني)
    """
    context = {
        'page_title': 'رئيس العدد - العون الميداني',
        'user': request.user,
    }
    return render(request, 'field_agent/index.html', context)


@login_required(login_url='login')
def unit_leader_view(request):
    """
    واجهة قائد الوحدة (عند الدعم لوحدة أخرى)
    """
    context = {
        'page_title': 'قائد الوحدة - الدعم',
        'user': request.user,
    }
    return render(request, 'unit_leader/index.html', context)


# ===== نظام جاهزية الوسائل المتقدم =====

@login_required(login_url='login')
def vehicle_readiness_dashboard(request):
    """لوحة تحكم جاهزية الوسائل - محدث للنماذج الجديدة"""
    from datetime import date
    from .models import (VehicleReadiness, VehicleCrewAssignment, VehicleRequirements,
                        UnitEquipment, InterventionUnit)

    user = request.user
    is_admin = user.is_superuser or user.is_staff

    # التحقق من الصلاحيات
    if not hasattr(user, 'userprofile'):
        messages.error(request, 'لم يتم العثور على ملف تعريف المستخدم')
        return redirect('home')

    user_profile = user.userprofile

    # الحصول على الوحدات المخصصة للمستخدم
    if is_admin:
        user_units = InterventionUnit.objects.all()
    elif user_profile.role == 'wilaya_manager':
        user_units = InterventionUnit.objects.filter(wilaya=user_profile.wilaya)
    elif user_profile.role in ['unit_manager', 'unit_coordinator']:
        user_units = user_profile.intervention_units.all()
    else:
        messages.error(request, 'ليس لديك صلاحية للوصول إلى هذه الصفحة')
        return redirect('home')

    # الحصول على تاريخ اليوم
    today = date.today()

    # الحصول على جاهزية الوسائل لليوم
    from django.db.models import Q, Count, Avg

    # إحصائيات عامة - استخدام UnitEquipment بدلاً من EquipmentCount
    total_vehicles = UnitEquipment.objects.filter(
        unit__in=user_units,
        is_active=True
    ).count()

    ready_vehicles = VehicleReadiness.objects.filter(
        vehicle__unit__in=user_units,
        date=today,
        status='ready'
    ).count()

    manually_confirmed = VehicleReadiness.objects.filter(
        vehicle__unit__in=user_units,
        date=today,
        status='manually_confirmed'
    ).count()

    not_ready_vehicles = VehicleReadiness.objects.filter(
        vehicle__unit__in=user_units,
        date=today,
        status='not_ready'
    ).count()

    # حساب نسبة الجاهزية الإجمالية
    if total_vehicles > 0:
        readiness_percentage = int(((ready_vehicles + manually_confirmed) / total_vehicles) * 100)
    else:
        readiness_percentage = 0

    context = {
        'is_admin': is_admin,
        'user_profile': user_profile,
        'today': today,
        'total_vehicles': total_vehicles,
        'ready_vehicles': ready_vehicles,
        'manually_confirmed': manually_confirmed,
        'not_ready_vehicles': not_ready_vehicles,
        'readiness_percentage': readiness_percentage,
        'user_units': user_units
    }

    return render(request, 'vehicle_readiness/dashboard.html', context)


@login_required(login_url='login')
def vehicle_crew_assignment(request):
    """صفحة توزيع الأعوان على الوسائل - مكتوبة من الصفر"""
    from datetime import date, datetime
    from .models import (VehicleReadiness, VehicleCrewAssignment, VehicleRequirements,
                        UnitPersonnel, UnitEquipment, DailyPersonnelStatus, InterventionUnit)

    user = request.user
    is_admin = user.is_superuser or user.is_staff

    # التحقق من الصلاحيات
    if not hasattr(user, 'userprofile'):
        messages.error(request, 'لم يتم العثور على ملف تعريف المستخدم')
        return redirect('home')

    user_profile = user.userprofile

    # الحصول على الوحدات المخصصة للمستخدم
    if is_admin:
        user_units = InterventionUnit.objects.all()
    elif user_profile.role == 'wilaya_manager':
        user_units = InterventionUnit.objects.filter(wilaya=user_profile.wilaya)
    elif user_profile.role in ['unit_manager', 'unit_coordinator']:
        user_units = user_profile.intervention_units.all()
    else:
        messages.error(request, 'ليس لديك صلاحية للوصول إلى هذه الصفحة')
        return redirect('home')

    # الحصول على التاريخ المحدد أو تاريخ اليوم
    selected_date = request.GET.get('date', date.today().strftime('%Y-%m-%d'))
    try:
        assignment_date = datetime.strptime(selected_date, '%Y-%m-%d').date()
    except:
        assignment_date = date.today()

    # الحصول على الوحدة المحددة
    selected_unit_id = request.GET.get('unit')
    if selected_unit_id:
        try:
            selected_unit = user_units.get(id=selected_unit_id)
        except InterventionUnit.DoesNotExist:
            selected_unit = user_units.first()
    else:
        selected_unit = user_units.first()

    if not selected_unit:
        messages.error(request, 'لا توجد وحدات متاحة')
        return redirect('home')

    # الحصول على جميع أعوان الوحدة النشطين
    all_unit_personnel = UnitPersonnel.objects.filter(
        unit=selected_unit,
        is_active=True
    ).order_by('full_name')

    # الحصول على الأعوان الحاضرين لهذا التاريخ
    present_personnel_ids = []
    for personnel in all_unit_personnel:
        daily_status, created = DailyPersonnelStatus.objects.get_or_create(
            personnel=personnel,
            date=assignment_date,
            defaults={
                'status': 'present',
                'updated_by': user
            }
        )
        # إظهار الحاضرين والاحتياطيين في صفحة التوزيع
        if daily_status.status in ['present', 'standby']:
            present_personnel_ids.append(personnel.id)

    # الأعوان الحاضرين فقط
    present_personnel = all_unit_personnel.filter(id__in=present_personnel_ids)

    # فلترة الأعوان العاملين والاحتياطيين (إزالة من هم في الراحة فقط)
    available_personnel_ids = []
    for person in present_personnel:
        work_status = person.get_work_status_today(assignment_date)

        # إظهار الأعوان العاملين
        if work_status == 'working':
            available_personnel_ids.append(person.id)
        # إظهار الأعوان الاحتياطيين (غير مخصصين لفرقة أو فرقتهم في الراحة)
        elif work_status == 'unassigned':
            available_personnel_ids.append(person.id)
        # إظهار الأعوان في الراحة إذا كانوا احتياطيين
        elif work_status == 'resting':
            # التحقق من حالة الحضور اليومية
            try:
                daily_status = DailyPersonnelStatus.objects.get(
                    personnel=person,
                    date=assignment_date
                )
                # إذا كان الحضور "احتياطي" فيظهر في التوزيع
                if daily_status.status == 'standby':
                    available_personnel_ids.append(person.id)
            except DailyPersonnelStatus.DoesNotExist:
                pass

    # تطبيق الفلترة للأعوان المتاحين (العاملين + الاحتياطيين)
    present_personnel = present_personnel.filter(id__in=available_personnel_ids)

    # الحصول على وسائل الوحدة النشطة والجاهزة فقط
    from .models import DailyEquipmentStatus

    # الحصول على جميع الوسائل النشطة
    all_vehicles = UnitEquipment.objects.filter(
        unit=selected_unit,
        is_active=True
    ).order_by('equipment_type')

    # فلترة الوسائل الجاهزة فقط (operational)
    ready_vehicle_ids = []
    non_ready_vehicles = []

    for vehicle in all_vehicles:
        # الحصول على حالة الوسيلة اليومية
        daily_status, created = DailyEquipmentStatus.objects.get_or_create(
            equipment=vehicle,
            date=assignment_date,
            defaults={'status': 'operational', 'updated_by': user}
        )

        # إضافة الوسيلة فقط إذا كانت جاهزة
        if daily_status.status == 'operational':
            ready_vehicle_ids.append(vehicle.id)
        else:
            non_ready_vehicles.append({
                'vehicle': vehicle,
                'status': daily_status.status,
                'status_display': daily_status.get_status_display()
            })

    # الوسائل الجاهزة فقط
    vehicles = all_vehicles.filter(id__in=ready_vehicle_ids)

    # إضافة معلومات الفلترة للسياق للتتبع
    context_debug = {
        'total_vehicles': all_vehicles.count(),
        'ready_vehicles': len(ready_vehicle_ids),
        'non_ready_vehicles': len(non_ready_vehicles),
        'non_ready_details': non_ready_vehicles
    }

    # رسائل تحذيرية إذا لم توجد بيانات
    if not present_personnel.exists():
        messages.warning(request,
            f'لا يوجد أعوان حاضرين في الوحدة لتاريخ {assignment_date.strftime("%Y-%m-%d")}. '
            f'يرجى إضافة الأعوان أولاً من صفحة التعداد الصباحي.'
        )

    if not vehicles.exists():
        if all_vehicles.exists():
            # توجد وسائل لكنها غير جاهزة
            non_ready_count = all_vehicles.count() - len(ready_vehicle_ids)
            messages.warning(request,
                f'لا توجد وسائل جاهزة للتوزيع في الوحدة لتاريخ {assignment_date.strftime("%Y-%m-%d")}. '
                f'يوجد {non_ready_count} وسيلة غير جاهزة (معطلة أو في الصيانة). '
                f'يرجى تحديث حالة الوسائل من صفحة التعداد الصباحي الموحد.'
            )
        else:
            # لا توجد وسائل مسجلة أصلاً
            messages.warning(request,
                f'لا توجد وسائل مسجلة في الوحدة. '
                f'يرجى إضافة الوسائل أولاً من صفحة التعداد الصباحي الموحد.'
            )

    # الحصول على التعيينات الحالية
    assignments = VehicleCrewAssignment.objects.filter(
        personnel__unit=selected_unit,
        assignment_date=assignment_date,
        is_active=True
    ).select_related('vehicle', 'personnel')

    # تنظيم التعيينات حسب الوسيلة
    vehicle_assignments = {}
    for vehicle in vehicles:
        vehicle_assignments[vehicle.id] = {
            'vehicle': vehicle,
            'assignments': assignments.filter(vehicle=vehicle),
            'readiness': None
        }

        # الحصول على حالة الجاهزية
        try:
            readiness = VehicleReadiness.objects.get(
                vehicle=vehicle,
                date=assignment_date
            )
            vehicle_assignments[vehicle.id]['readiness'] = readiness
        except VehicleReadiness.DoesNotExist:
            # إنشاء سجل جاهزية جديد
            readiness = VehicleReadiness.objects.create(
                vehicle=vehicle,
                date=assignment_date
            )
            readiness.calculate_readiness_score()
            readiness.save()
            vehicle_assignments[vehicle.id]['readiness'] = readiness

    # الحصول على الأعوان غير المعينين
    assigned_personnel_ids = assignments.values_list('personnel_id', flat=True)
    unassigned_personnel = present_personnel.exclude(id__in=assigned_personnel_ids)

    context = {
        'is_admin': is_admin,
        'user_profile': user_profile,
        'user_units': user_units,
        'selected_unit': selected_unit,
        'assignment_date': assignment_date,
        'vehicles': vehicles,
        'personnel': present_personnel,
        'unassigned_personnel': unassigned_personnel,
        'vehicle_assignments': vehicle_assignments,
        'role_choices': VehicleCrewAssignment.ROLE_CHOICES,
        'debug_info': context_debug  # معلومات التتبع
    }

    return render(request, 'vehicle_readiness/crew_assignment.html', context)


@login_required(login_url='login')
def assign_personnel_to_vehicle(request):
    """API لتعيين عون على وسيلة - محدث للنماذج الجديدة"""
    from datetime import datetime
    from django.shortcuts import get_object_or_404
    from .models import VehicleCrewAssignment, UnitEquipment, UnitPersonnel, VehicleReadiness

    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': 'طريقة غير مسموحة'})

    try:
        data = json.loads(request.body)
        vehicle_id = data.get('vehicle_id')
        personnel_id = data.get('personnel_id')
        role = data.get('role')
        assignment_date_str = data.get('assignment_date')

        # التحقق من البيانات
        if not all([vehicle_id, personnel_id, role, assignment_date_str]):
            return JsonResponse({'success': False, 'message': 'بيانات ناقصة'})

        # تحويل التاريخ
        assignment_date = datetime.strptime(assignment_date_str, '%Y-%m-%d').date()

        # الحصول على الوسيلة والعون
        vehicle = get_object_or_404(UnitEquipment, id=vehicle_id)
        personnel = get_object_or_404(UnitPersonnel, id=personnel_id)

        # التحقق من الصلاحيات
        user_profile = request.user.userprofile
        if not (request.user.is_superuser or
                user_profile.role == 'wilaya_manager' or
                vehicle.unit in user_profile.intervention_units.all()):
            return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية لهذا الإجراء'})

        # التحقق من أن العون والوسيلة في نفس الوحدة
        if vehicle.unit != personnel.unit:
            return JsonResponse({'success': False, 'message': 'العون والوسيلة يجب أن يكونا في نفس الوحدة'})

        # السماح بتعيين العون في أدوار متعددة - بدون قيود
        # إزالة التحقق من التعيين المسبق للسماح بالمرونة

        # إنشاء التعيين
        assignment = VehicleCrewAssignment.objects.create(
            vehicle=vehicle,
            personnel=personnel,
            role=role,
            assignment_date=assignment_date
        )

        # تحديث حالة الجاهزية
        try:
            readiness = VehicleReadiness.objects.get(vehicle=vehicle, date=assignment_date)
        except VehicleReadiness.DoesNotExist:
            readiness = VehicleReadiness.objects.create(vehicle=vehicle, date=assignment_date)

        readiness.calculate_readiness_score()
        readiness.save()

        return JsonResponse({
            'success': True,
            'message': 'تم تعيين العون بنجاح',
            'assignment_id': assignment.id,
            'readiness_score': readiness.readiness_score,
            'readiness_status': readiness.get_status_display()
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})


@login_required(login_url='login')
def mark_vehicle_ready(request):
    """API لتأكيد جاهزية الوسيلة"""
    from datetime import datetime
    from django.shortcuts import get_object_or_404
    from .models import VehicleReadiness, UnitEquipment

    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': 'طريقة غير مسموحة'})

    try:
        data = json.loads(request.body)
        vehicle_id = data.get('vehicle_id')
        date_str = data.get('date')

        # التحقق من البيانات
        if not all([vehicle_id, date_str]):
            return JsonResponse({'success': False, 'message': 'بيانات ناقصة'})

        # تحويل التاريخ
        ready_date = datetime.strptime(date_str, '%Y-%m-%d').date()

        # الحصول على الوسيلة
        vehicle = get_object_or_404(UnitEquipment, id=vehicle_id)

        # التحقق من الصلاحيات
        user_profile = request.user.userprofile
        if not (request.user.is_superuser or
                user_profile.role == 'wilaya_manager' or
                vehicle.unit in user_profile.intervention_units.all()):
            return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية لهذا الإجراء'})

        # الحصول على أو إنشاء سجل الجاهزية
        readiness, created = VehicleReadiness.objects.get_or_create(
            vehicle=vehicle,
            date=ready_date,
            defaults={
                'status': 'manually_confirmed',
                'readiness_score': 100,
                'notes': 'تم تأكيد الجاهزية يدوياً'
            }
        )

        if not created:
            # تحديث الجاهزية الموجودة
            readiness.status = 'manually_confirmed'
            readiness.readiness_score = 100
            readiness.notes = 'تم تأكيد الجاهزية يدوياً'
            readiness.save()

        return JsonResponse({
            'success': True,
            'message': 'تم تأكيد جاهزية الوسيلة بنجاح',
            'readiness_score': readiness.readiness_score,
            'status': readiness.get_status_display()
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})


@csrf_exempt
@login_required(login_url='login')
def update_readiness_from_assignment(request):
    """تحديث الجاهزية من صفحة التوزيع مع المزامنة"""
    import json
    from datetime import datetime
    from django.shortcuts import get_object_or_404
    from .models import VehicleReadiness, UnitEquipment, VehicleCrewAssignment

    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': 'طريقة غير مسموحة'})

    try:
        data = json.loads(request.body)
        vehicle_id = data.get('vehicle_id')
        date_str = data.get('date')
        action = data.get('action', 'calculate')  # calculate, manual_confirm

        # التحقق من البيانات
        if not all([vehicle_id, date_str]):
            return JsonResponse({'success': False, 'message': 'بيانات ناقصة'})

        # تحويل التاريخ
        target_date = datetime.strptime(date_str, '%Y-%m-%d').date()

        # الحصول على الوسيلة
        vehicle = get_object_or_404(UnitEquipment, id=vehicle_id)

        # التحقق من الصلاحيات
        user_profile = request.user.userprofile
        if not (request.user.is_superuser or
                user_profile.role == 'wilaya_manager' or
                vehicle.unit in user_profile.intervention_units.all()):
            return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية لهذا الإجراء'})

        # الحصول على أو إنشاء سجل الجاهزية
        readiness, created = VehicleReadiness.objects.get_or_create(
            vehicle=vehicle,
            date=target_date
        )

        # حساب الجاهزية بناءً على الأعوان المعينين
        assignments = VehicleCrewAssignment.objects.filter(
            vehicle=vehicle,
            assignment_date=target_date,
            is_active=True
        )

        crew_count = assignments.count()

        if action == 'manual_confirm':
            # تأكيد يدوي
            readiness.status = 'manually_confirmed'
            readiness.readiness_score = 100
            readiness.is_manually_confirmed = True
            readiness.confirmed_by = request.user
            readiness.confirmation_reason = 'تم تأكيد الجاهزية من صفحة التوزيع'
        else:
            # حساب تلقائي
            if crew_count > 0:
                readiness.status = 'ready'
                readiness.readiness_score = 100
                readiness.is_automatically_ready = True
            else:
                readiness.status = 'not_ready'
                readiness.readiness_score = 0
                readiness.is_automatically_ready = False

        readiness.save()

        return JsonResponse({
            'success': True,
            'message': 'تم تحديث الجاهزية بنجاح',
            'readiness_score': readiness.readiness_score,
            'readiness_status': readiness.get_status_display(),
            'crew_count': crew_count,
            'sync_data': {
                'type': 'readiness_update',
                'vehicle_id': vehicle_id,
                'readiness_score': readiness.readiness_score,
                'readiness_status': readiness.get_status_display(),
                'crew_count': crew_count,
                'timestamp': datetime.now().isoformat()
            }
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})


@login_required(login_url='login')
def remove_personnel_from_vehicle(request):
    """API لإزالة عون من وسيلة"""
    from django.shortcuts import get_object_or_404
    from .models import VehicleCrewAssignment, VehicleReadiness

    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': 'طريقة غير مسموحة'})

    try:
        data = json.loads(request.body)
        assignment_id = data.get('assignment_id')

        if not assignment_id:
            return JsonResponse({'success': False, 'message': 'معرف التعيين مطلوب'})

        # الحصول على التعيين
        assignment = get_object_or_404(VehicleCrewAssignment, id=assignment_id)

        # التحقق من الصلاحيات
        user_profile = request.user.userprofile
        if not (request.user.is_superuser or
                user_profile.role == 'wilaya_manager' or
                assignment.vehicle.daily_count.unit in user_profile.intervention_units.all()):
            return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية لهذا الإجراء'})

        # حفظ معلومات الوسيلة والتاريخ قبل الحذف
        vehicle = assignment.vehicle
        assignment_date = assignment.assignment_date

        # حذف التعيين
        assignment.delete()

        # تحديث حالة الجاهزية
        try:
            readiness = VehicleReadiness.objects.get(vehicle=vehicle, date=assignment_date)
            readiness.calculate_readiness_score()
            readiness.save()
        except VehicleReadiness.DoesNotExist:
            pass

        return JsonResponse({
            'success': True,
            'message': 'تم إزالة العون بنجاح',
            'readiness_score': readiness.readiness_score if 'readiness' in locals() else 0,
            'readiness_status': readiness.get_status_display() if 'readiness' in locals() else 'غير جاهز'
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})


@login_required(login_url='login')
def confirm_vehicle_readiness_manually(request):
    """API للتأكيد اليدوي لجاهزية الوسيلة"""
    from django.shortcuts import get_object_or_404
    from .models import VehicleReadiness, EquipmentCount

    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': 'طريقة غير مسموحة'})

    try:
        data = json.loads(request.body)
        vehicle_id = data.get('vehicle_id')
        date_str = data.get('date')
        reason = data.get('reason', '')

        if not all([vehicle_id, date_str]):
            return JsonResponse({'success': False, 'message': 'بيانات ناقصة'})

        # تحويل التاريخ
        from datetime import datetime
        confirmation_date = datetime.strptime(date_str, '%Y-%m-%d').date()

        # الحصول على الوسيلة
        vehicle = get_object_or_404(EquipmentCount, id=vehicle_id)

        # التحقق من الصلاحيات (فقط رؤساء مراكز التنسيق)
        user_profile = request.user.userprofile
        if not (request.user.is_superuser or
                user_profile.role in ['wilaya_manager', 'unit_manager']):
            return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية للتأكيد اليدوي'})

        # الحصول على سجل الجاهزية أو إنشاؤه
        readiness, created = VehicleReadiness.objects.get_or_create(
            vehicle=vehicle,
            date=confirmation_date,
            defaults={'status': 'not_ready'}
        )

        # تأكيد الجاهزية يدوياً
        readiness.confirm_manually(request.user, reason)

        return JsonResponse({
            'success': True,
            'message': 'تم تأكيد الجاهزية يدوياً بنجاح',
            'readiness_score': readiness.readiness_score,
            'readiness_status': readiness.get_status_display()
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})


# ========================================
# Daily Interventions System APIs
# ========================================

@csrf_exempt
@require_http_methods(["POST"])
def save_initial_report(request):
    """حفظ البلاغ الأولي"""
    try:
        # التعامل مع FormData من النموذج
        if request.content_type and 'application/json' in request.content_type:
            data = json.loads(request.body)
        else:
            # التعامل مع FormData
            data = {
                'intervention_type': request.POST.get('intervention_type'),
                'departure_time': request.POST.get('departure_time'),
                'location': request.POST.get('location'),
                'contact_source': request.POST.get('contact_source'),
                'contact_type': request.POST.get('contact_type'),
                'phone_number': request.POST.get('phone_number', ''),
                'caller_name': request.POST.get('caller_name', ''),
                'initial_notes': request.POST.get('initial_notes', ''),
                'vehicle_ids': request.POST.getlist('vehicle_ids'),
            }

        from .models import DailyIntervention, InterventionVehicle, UnitEquipment, InterventionUnit
        from datetime import datetime, date

        # الحصول على الوحدة الحالية
        user_profile = getattr(request.user, 'userprofile', None)
        if user_profile and user_profile.intervention_units.exists():
            current_unit = user_profile.intervention_units.first()
        else:
            current_unit = InterventionUnit.objects.first()

        if not current_unit:
            return JsonResponse({
                'success': False,
                'message': 'لا توجد وحدة متاحة للمستخدم'
            })

        # تحويل departure_time من نص إلى كائن وقت
        departure_time_str = data.get('departure_time')
        departure_time_obj = None

        if departure_time_str:
            try:
                # تحويل من تنسيق HH:MM إلى كائن time
                departure_time_obj = datetime.strptime(departure_time_str, '%H:%M').time()
            except ValueError:
                try:
                    # محاولة تنسيق آخر HH:MM:SS
                    departure_time_obj = datetime.strptime(departure_time_str, '%H:%M:%S').time()
                except ValueError:
                    # إذا فشل التحويل، استخدم الوقت الحالي
                    departure_time_obj = datetime.now().time()
        else:
            # إذا لم يتم تحديد وقت، استخدم الوقت الحالي
            departure_time_obj = datetime.now().time()

        # إنشاء التدخل الجديد
        intervention_data = {
            'unit': current_unit,
            'intervention_type': data.get('intervention_type'),
            'departure_time': departure_time_obj,
            'location': data.get('location'),
            'contact_source': data.get('contact_source'),
            'contact_type': data.get('contact_type'),
            'phone_number': data.get('phone_number', ''),
            'caller_name': data.get('caller_name', ''),
            'initial_notes': data.get('initial_notes', ''),
            'status': 'initial_report',
            'date': date.today(),
        }

        # تحديد المستخدم المنشئ
        if request.user.is_authenticated:
            intervention_data['created_by'] = request.user
        else:
            # استخدام مستخدم افتراضي إذا لم يكن هناك مستخدم مسجل دخول
            from django.contrib.auth.models import User
            default_user = User.objects.first()
            if default_user:
                intervention_data['created_by'] = default_user
            else:
                return JsonResponse({
                    'success': False,
                    'message': 'لا يوجد مستخدم متاح في النظام'
                })

        intervention = DailyIntervention.objects.create(**intervention_data)

        # ربط الوسائل المرسلة (بسيط - نسخ البيانات من صفحة التوزيع)
        vehicle_ids = data.get('vehicle_ids', [])
        assignment_date = intervention.date

        from .models import VehicleCrewAssignment

        for vehicle_id in vehicle_ids:
            try:
                vehicle = UnitEquipment.objects.get(id=vehicle_id)

                # الحصول على الطاقم المعين من صفحة التوزيع
                crew_assignments = VehicleCrewAssignment.objects.filter(
                    vehicle=vehicle,
                    assignment_date=assignment_date,
                    is_active=True
                ).select_related('personnel')

                # إنشاء قائمة الطاقم
                crew_list = []
                for assignment in crew_assignments:
                    crew_list.append({
                        'name': assignment.personnel.full_name,
                        'rank': assignment.personnel.rank or 'غير محدد',
                        'position': assignment.personnel.position or 'غير محدد',
                        'role': assignment.get_role_display()
                    })

                intervention_vehicle = InterventionVehicle.objects.create(
                    intervention=intervention,
                    vehicle=vehicle,
                    vehicle_role='primary',  # افتراضي
                    is_primary=True,
                    crew_count=len(crew_list)
                )

                # حفظ تفاصيل الطاقم
                intervention_vehicle.set_crew_list(crew_list)
                intervention_vehicle.save()

            except UnitEquipment.DoesNotExist:
                continue

        # تحويل departure_time إلى نص بأمان
        departure_time_str = ''
        if intervention.departure_time:
            if hasattr(intervention.departure_time, 'strftime'):
                departure_time_str = intervention.departure_time.strftime('%H:%M')
            else:
                departure_time_str = str(intervention.departure_time)

        return JsonResponse({
            'success': True,
            'message': 'تم حفظ البلاغ الأولي بنجاح',
            'intervention_id': intervention.id,
            'intervention_number': intervention.intervention_number,
            'departure_time': departure_time_str,
            'location': intervention.location,
            'intervention_type': intervention.intervention_type,
            'unit_id': intervention.unit.id,
            'vehicle_count': len(vehicle_ids),
            'vehicle_ids': vehicle_ids
        })

    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"خطأ في حفظ البلاغ الأولي: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ في حفظ البلاغ: {str(e)}',
            'error_type': type(e).__name__
        })

@csrf_exempt
@require_http_methods(["GET"])
def get_intervention_details(request, intervention_id):
    """جلب تفاصيل التدخل"""
    try:
        from .models import DailyIntervention, InterventionVehicle

        # جلب التدخل
        intervention = DailyIntervention.objects.get(id=intervention_id)

        # جلب الوسائل المرتبطة
        vehicles = InterventionVehicle.objects.filter(intervention=intervention).select_related('vehicle')

        # تحويل departure_time إلى نص بأمان
        departure_time_str = ''
        if intervention.departure_time:
            if hasattr(intervention.departure_time, 'strftime'):
                departure_time_str = intervention.departure_time.strftime('%H:%M')
            else:
                departure_time_str = str(intervention.departure_time)

        # إعداد بيانات الاستجابة
        intervention_data = {
            'id': intervention.id,
            'intervention_number': intervention.intervention_number,
            'intervention_type': intervention.intervention_type,
            'departure_time': departure_time_str,
            'location': intervention.location,
            'contact_source': intervention.contact_source,
            'contact_type': intervention.contact_type,
            'phone_number': intervention.phone_number or '',
            'caller_name': intervention.caller_name or '',
            'initial_notes': intervention.initial_notes or '',
            'status': intervention.status,
            'created_at': intervention.created_at.isoformat(),
            'vehicles': [
                {
                    'equipment_type': vehicle.vehicle.equipment_type,
                    'serial_number': vehicle.vehicle.serial_number,
                    'radio_number': vehicle.vehicle.radio_number or 'غير محدد'
                }
                for vehicle in vehicles
            ]
        }

        return JsonResponse({
            'success': True,
            'intervention': intervention_data
        })

    except DailyIntervention.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': 'التدخل غير موجود'
        })
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"خطأ في جلب تفاصيل التدخل: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ في جلب التفاصيل: {str(e)}'
        })

@csrf_exempt
@require_http_methods(["GET"])
def get_all_interventions(request):
    """جلب جميع التدخلات اليومية"""
    try:
        from .models import DailyIntervention, InterventionVehicle
        from datetime import date

        # جلب تدخلات اليوم فقط
        today = date.today()
        interventions = DailyIntervention.objects.filter(date=today).order_by('-created_at')

        interventions_data = []
        for intervention in interventions:
            # تحويل departure_time إلى نص بأمان
            departure_time_str = ''
            if intervention.departure_time:
                if hasattr(intervention.departure_time, 'strftime'):
                    departure_time_str = intervention.departure_time.strftime('%H:%M')
                else:
                    departure_time_str = str(intervention.departure_time)

            # حساب عدد الوسائل
            vehicle_count = InterventionVehicle.objects.filter(intervention=intervention).count()

            interventions_data.append({
                'id': intervention.id,
                'intervention_number': intervention.intervention_number,
                'intervention_type': intervention.intervention_type,
                'departure_time': departure_time_str,
                'location': intervention.location,
                'unit_id': intervention.unit.id,
                'status': intervention.status,
                'vehicle_count': vehicle_count,
                'created_at': intervention.created_at.isoformat()
            })

        return JsonResponse({
            'success': True,
            'interventions': interventions_data
        })

    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"خطأ في جلب جميع التدخلات: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ في جلب التدخلات: {str(e)}'
        })


@csrf_exempt
@require_http_methods(["POST"])
def update_intervention_status(request):
    """تحديث حالة التدخل"""
    try:
        data = json.loads(request.body)
        intervention_id = data.get('intervention_id')
        new_status = data.get('status')

        from .models import DailyIntervention

        intervention = DailyIntervention.objects.get(id=intervention_id)
        intervention.status = new_status

        # إضافة معلومات إضافية حسب الحالة
        if new_status == 'reconnaissance' or new_status == 'intervention':
            intervention.arrival_time = data.get('arrival_time')
            intervention.location_type = data.get('location_type')
            intervention.injured_count = data.get('injured_count', 0)
            intervention.deaths_count = data.get('deaths_count', 0)
            intervention.material_damage = data.get('material_damage', '')

        elif new_status == 'completed':
            intervention.end_time = data.get('end_time')
            intervention.total_duration = data.get('total_duration')
            intervention.final_injured_count = data.get('final_injured_count', 0)
            intervention.final_deaths_count = data.get('final_deaths_count', 0)

            # تحديث حالة الوسائل إلى متاحة
            from .models import VehicleInterventionStatus
            for vehicle_assignment in intervention.vehicles.all():
                vehicle_status, created = VehicleInterventionStatus.objects.get_or_create(
                    vehicle=vehicle_assignment.vehicle,
                    date=intervention.date,
                    defaults={'status': 'available'}
                )
                if not created:
                    vehicle_status.status = 'available'
                    vehicle_status.current_intervention = None
                    vehicle_status.save()

            # إنشاء تقرير التدخل
            from .models import InterventionReport
            report, created = InterventionReport.objects.get_or_create(
                intervention=intervention
            )
            report.generate_statistics()

        intervention.save()

        return JsonResponse({
            'success': True,
            'message': f'تم تحديث حالة التدخل إلى {intervention.get_status_display()}',
            'status': intervention.status
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})


@csrf_exempt
@require_http_methods(["POST"])
def save_agricultural_fire_details(request):
    """حفظ تفاصيل حريق المحاصيل الزراعية"""
    try:
        data = json.loads(request.body)
        intervention_id = data.get('intervention_id')

        from .models import DailyIntervention, AgriculturalFireDetail

        intervention = DailyIntervention.objects.get(id=intervention_id)

        # إنشاء أو تحديث تفاصيل حريق المحاصيل
        detail, created = AgriculturalFireDetail.objects.get_or_create(
            intervention=intervention,
            defaults={}
        )

        # تحديث البيانات من النموذج
        if 'fire_type' in data:
            detail.fire_type = data['fire_type']
        if 'fire_sources_count' in data:
            detail.fire_sources_count = int(data['fire_sources_count']) if data['fire_sources_count'] else 0
        if 'wind_direction' in data:
            detail.wind_direction = data['wind_direction']
        if 'wind_speed' in data:
            detail.wind_speed = float(data['wind_speed']) if data['wind_speed'] else 0
        if 'population_threat' in data:
            detail.population_threat = bool(data['population_threat'])
        if 'evacuation_location' in data:
            detail.evacuation_location = data['evacuation_location']
        if 'intervening_agents_count' in data:
            detail.intervening_agents_count = int(data['intervening_agents_count']) if data['intervening_agents_count'] else 0
        if 'affected_families_count' in data:
            detail.affected_families_count = int(data['affected_families_count']) if data['affected_families_count'] else 0
        if 'present_authorities' in data:
            detail.present_authorities = [data['present_authorities']] if data['present_authorities'] else []
        if 'support_request' in data:
            detail.support_request = data['support_request']
        # بيانات الإنهاء
        if 'standing_wheat_area' in data:
            detail.standing_wheat_area = float(data['standing_wheat_area']) if data['standing_wheat_area'] else 0
        if 'harvest_area' in data:
            detail.harvest_area = float(data['harvest_area']) if data['harvest_area'] else 0
        if 'forest_area' in data:
            detail.forest_area = float(data['forest_area']) if data['forest_area'] else 0
        if 'barley_area' in data:
            detail.barley_area = float(data['barley_area']) if data['barley_area'] else 0
        if 'straw_bales_count' in data:
            detail.straw_bales_count = int(data['straw_bales_count']) if data['straw_bales_count'] else 0
        if 'grain_bags_count' in data:
            detail.grain_bags_count = int(data['grain_bags_count']) if data['grain_bags_count'] else 0
        if 'fruit_trees_count' in data:
            detail.fruit_trees_count = int(data['fruit_trees_count']) if data['fruit_trees_count'] else 0
        if 'beehives_count' in data:
            detail.beehives_count = int(data['beehives_count']) if data['beehives_count'] else 0
        if 'completion_notes' in data:
            detail.completion_notes = data['completion_notes']
        if 'completion_time' in data:
            detail.completion_time = data['completion_time']

        detail.save()

        return JsonResponse({
            'success': True,
            'message': 'تم حفظ تفاصيل حريق المحاصيل بنجاح'
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'خطأ في حفظ التفاصيل: {str(e)}'
        })

@csrf_exempt
@require_http_methods(["POST"])
def save_building_fire_details(request):
    """حفظ تفاصيل حريق البنايات والمؤسسات"""
    try:
        data = json.loads(request.body)
        intervention_id = data.get('intervention_id')

        from .models import DailyIntervention, BuildingFireDetail

        intervention = DailyIntervention.objects.get(id=intervention_id)

        # إنشاء أو تحديث تفاصيل حريق البنايات
        detail, created = BuildingFireDetail.objects.get_or_create(
            intervention=intervention,
            defaults={}
        )

        # تحديث البيانات من النموذج
        if 'fire_nature' in data:
            detail.fire_nature = data['fire_nature']
        if 'fire_points_count' in data:
            detail.fire_points_count = int(data['fire_points_count']) if data['fire_points_count'] else 0
        if 'wind_direction' in data:
            detail.wind_direction = data['wind_direction']
        if 'wind_speed' in data:
            detail.wind_speed = float(data['wind_speed']) if data['wind_speed'] else 0
        if 'population_threat' in data:
            detail.population_threat = bool(data['population_threat'])
        if 'evacuation_location' in data:
            detail.evacuation_location = data['evacuation_location']
        if 'intervening_agents_count' in data:
            detail.intervening_agents_count = int(data['intervening_agents_count']) if data['intervening_agents_count'] else 0
        if 'affected_families_count' in data:
            detail.affected_families_count = int(data['affected_families_count']) if data['affected_families_count'] else 0
        if 'present_entities' in data:
            detail.present_entities = data['present_entities']
        if 'support_request' in data:
            detail.support_request = data['support_request']
        # بيانات الإنهاء
        if 'damages_description' in data:
            detail.damages_description = data['damages_description']
        if 'saved_equipment' in data:
            detail.saved_properties = data['saved_equipment']  # استخدام الحقل الموجود
        if 'victims_details' in data:
            detail.final_notes = data['victims_details']  # استخدام الحقل الموجود
        if 'completion_notes' in data:
            detail.completion_notes = data['completion_notes']
        if 'completion_time' in data:
            detail.completion_time = data['completion_time']

        detail.save()

        return JsonResponse({
            'success': True,
            'message': 'تم حفظ تفاصيل حريق البنايات بنجاح'
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'خطأ في حفظ التفاصيل: {str(e)}'
        })

@csrf_exempt
@require_http_methods(["POST"])
def save_medical_evacuation_details(request):
    """حفظ تفاصيل الإجلاء الصحي"""
    try:
        data = json.loads(request.body)
        intervention_id = data.get('intervention_id')

        from .models import DailyIntervention, MedicalEvacuationDetail

        intervention = DailyIntervention.objects.get(id=intervention_id)

        # إنشاء أو تحديث تفاصيل الإجلاء الصحي
        detail, created = MedicalEvacuationDetail.objects.get_or_create(
            intervention=intervention,
            defaults={}
        )

        # تحديث البيانات من النموذج
        if 'evacuation_type' in data:
            detail.intervention_nature = data['evacuation_type']  # استخدام الحقل الموجود
        if 'patient_condition' in data:
            detail.material_damage_notes = data['patient_condition']  # استخدام الحقل الموجود مؤقتاً
        if 'destination_hospital' in data:
            detail.material_damage_notes = (detail.material_damage_notes or '') + f"\nالمستشفى المقصود: {data['destination_hospital']}"
        if 'victims_details' in data:
            detail.material_damage_notes = (detail.material_damage_notes or '') + f"\nتفاصيل الضحايا: {data['victims_details']}"

        detail.save()

        return JsonResponse({
            'success': True,
            'message': 'تم حفظ تفاصيل الإجلاء الصحي بنجاح'
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'خطأ في حفظ التفاصيل: {str(e)}'
        })

@csrf_exempt
@require_http_methods(["POST"])
def save_traffic_accident_details(request):
    """حفظ تفاصيل حوادث المرور"""
    try:
        data = json.loads(request.body)
        intervention_id = data.get('intervention_id')

        from .models import DailyIntervention, TrafficAccidentDetail

        intervention = DailyIntervention.objects.get(id=intervention_id)

        # إنشاء أو تحديث تفاصيل حوادث المرور
        detail, created = TrafficAccidentDetail.objects.get_or_create(
            intervention=intervention,
            defaults={}
        )

        # تحديث البيانات من النموذج
        if 'accident_type' in data:
            detail.accident_type = data['accident_type']
        if 'involved_vehicles' in data:
            detail.material_damage_notes = (detail.material_damage_notes or '') + f"\nالمركبات المتورطة: {data['involved_vehicles']}"
        if 'material_damage_notes' in data:
            detail.material_damage_notes = data['material_damage_notes']
        if 'victims_details' in data:
            detail.material_damage_notes = (detail.material_damage_notes or '') + f"\nتفاصيل الضحايا: {data['victims_details']}"

        detail.save()

        return JsonResponse({
            'success': True,
            'message': 'تم حفظ تفاصيل حادث المرور بنجاح'
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'خطأ في حفظ التفاصيل: {str(e)}'
        })

@csrf_exempt
@require_http_methods(["POST"])
def complete_intervention(request):
    """إنهاء المهمة وتحديث حالة الوسائل"""
    try:
        data = json.loads(request.body)
        intervention_id = data.get('intervention_id')

        from .models import DailyIntervention, VehicleInterventionStatus

        intervention = DailyIntervention.objects.get(id=intervention_id)

        # تحديث بيانات إنهاء المهمة
        intervention.status = 'completed'
        intervention.end_time = data.get('end_time')
        intervention.total_duration = data.get('total_duration')
        intervention.final_injured_count = data.get('final_injured_count', 0)
        intervention.final_deaths_count = data.get('final_deaths_count', 0)
        intervention.save()

        # تحديث حالة الوسائل إلى متاحة
        for vehicle_assignment in intervention.vehicles.all():
            vehicle_status, created = VehicleInterventionStatus.objects.get_or_create(
                vehicle=vehicle_assignment.vehicle,
                date=intervention.date,
                defaults={'status': 'available'}
            )
            if not created:
                vehicle_status.status = 'available'
                vehicle_status.current_intervention = None
                vehicle_status.save()

        # إنشاء تقرير التدخل
        from .models import InterventionReport
        report, created = InterventionReport.objects.get_or_create(
            intervention=intervention
        )
        report.generate_statistics()

        # إعداد بيانات الاستجابة مع ملخص الوسائل
        vehicles_summary = []
        for vehicle_data in report.get_vehicles_data():
            vehicles_summary.append(f"{vehicle_data['type']} ({vehicle_data['serial']}) - راديو: {vehicle_data['radio']}")

        return JsonResponse({
            'success': True,
            'message': 'تم إنهاء المهمة بنجاح',
            'intervention_number': intervention.intervention_number,
            'intervention_id': intervention.id,
            'vehicles_summary': ', '.join(vehicles_summary),
            'total_vehicles': report.total_vehicles,
            'total_personnel': report.total_personnel,
            'intervention_type': intervention.get_intervention_type_display(),
            'location': intervention.location,
            'departure_time': intervention.departure_time.strftime('%H:%M'),
            'end_time': intervention.end_time.strftime('%H:%M') if intervention.end_time else '',
            'unit_name': intervention.unit.name
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})


@csrf_exempt
@require_http_methods(["GET"])
def get_available_vehicles(request):
    """الحصول على الوسائل والأعوان المتاحة (متزامنة مع صفحة التوزيع)"""
    try:
        from datetime import date, datetime
        from .models import (UnitEquipment, VehicleInterventionStatus, VehicleReadiness,
                           DailyEquipmentStatus, VehicleCrewAssignment, UnitPersonnel)

        unit_id = request.GET.get('unit_id')
        assignment_date = request.GET.get('date', date.today().strftime('%Y-%m-%d'))

        # تحويل التاريخ
        if isinstance(assignment_date, str):
            assignment_date = datetime.strptime(assignment_date, '%Y-%m-%d').date()

        # الحصول على الوسائل المتاحة - فلترة مبسطة
        available_vehicles = []

        # فلترة بسيطة - جميع الوسائل النشطة والتشغيلية
        vehicles = UnitEquipment.objects.filter(unit_id=unit_id, is_active=True)

        for vehicle in vehicles:
            # التحقق من حالة الوسيلة اليومية (إنشاء إذا لم توجد)
            daily_status, created = DailyEquipmentStatus.objects.get_or_create(
                equipment=vehicle,
                date=assignment_date,
                defaults={
                    'status': 'operational',
                    'updated_by_id': 1  # معرف افتراضي
                }
            )

            # التحقق من حالة التدخل
            intervention_status = VehicleInterventionStatus.objects.filter(
                vehicle=vehicle,
                date=assignment_date
            ).first()

            # الحصول على نتيجة الجاهزية (إنشاء إذا لم توجد)
            readiness, created = VehicleReadiness.objects.get_or_create(
                vehicle=vehicle,
                date=assignment_date,
                defaults={
                    'status': 'ready',
                    'readiness_score': 100,
                    'is_automatically_ready': True
                }
            )

            # الحصول على الأعوان المعينين على هذه الوسيلة من صفحة التوزيع
            crew_assignments = VehicleCrewAssignment.objects.filter(
                vehicle=vehicle,
                assignment_date=assignment_date,
                is_active=True
            ).select_related('personnel')

            crew_members = []
            for assignment in crew_assignments:
                crew_members.append({
                    'id': assignment.personnel.id,
                    'name': assignment.personnel.full_name,
                    'rank': assignment.personnel.rank or 'غير محدد',
                    'position': assignment.personnel.position or 'غير محدد',
                    'role': assignment.get_role_display()
                })

            # الوسيلة متاحة إذا كانت:
            # 1. تعمل (operational)
            # 2. جاهزة (ready أو manually_confirmed)
            # 3. ليست في تدخل حالياً
            # 4. لديها طاقم معين (حسب متطلبات Materiel_inv.md)
            is_available = (
                daily_status.status == 'operational' and
                readiness.status in ['ready', 'manually_confirmed'] and
                (not intervention_status or intervention_status.status == 'available') and
                len(crew_members) > 0  # يجب أن يكون لديها طاقم معين
            )

            if is_available:
                available_vehicles.append({
                    'id': vehicle.id,
                    'serial_number': vehicle.serial_number,
                    'equipment_type': vehicle.equipment_type,
                    'radio_number': vehicle.radio_number or 'غير محدد',
                    'readiness_score': readiness.readiness_score,
                    'crew_members': crew_members,
                    'crew_count': len(crew_members)
                })

        return JsonResponse({
            'success': True,
            'vehicles': available_vehicles
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})


@csrf_exempt
@require_http_methods(["GET"])
def get_intervention_vehicles_count(request):
    """الحصول على عدد الوسائل في التدخل حالياً"""
    try:
        from datetime import date
        from .models import VehicleInterventionStatus

        count = VehicleInterventionStatus.objects.filter(
            status='in_intervention',
            date=date.today()
        ).count()

        return JsonResponse({
            'success': True,
            'count': count
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})


# ========================================
# Morning Check System Views
# ========================================

@csrf_exempt
@login_required(login_url='login')
def morning_check_system_view(request):
    """نظام التحقق الصباحي الشامل"""
    try:
        # التحقق من صلاحيات المستخدم
        user = request.user
        user_profile = user.userprofile

        # تحديد الوحدات المتاحة حسب الدور
        if user_profile.role == 'admin':
            available_units = InterventionUnit.objects.all()
        elif user_profile.role == 'wilaya_manager':
            available_units = InterventionUnit.objects.filter(wilaya=user_profile.wilaya)
        elif user_profile.role in ['unit_manager', 'unit_coordinator']:
            available_units = user_profile.intervention_units.all()
        else:
            available_units = InterventionUnit.objects.none()

        # الحصول على الوحدة المحددة
        unit_id = request.GET.get('unit_id')
        if unit_id:
            try:
                selected_unit = available_units.get(id=unit_id)
            except InterventionUnit.DoesNotExist:
                messages.error(request, 'الوحدة المحددة غير متاحة')
                return redirect('morning_check_system')
        else:
            selected_unit = available_units.first() if available_units.exists() else None

        if not selected_unit:
            messages.error(request, 'لا توجد وحدات متاحة')
            return redirect('home')

        # الحصول على التاريخ المحدد (افتراضياً اليوم)
        from datetime import date
        selected_date = request.GET.get('date')
        if selected_date:
            try:
                selected_date = datetime.strptime(selected_date, '%Y-%m-%d').date()
            except ValueError:
                selected_date = date.today()
        else:
            selected_date = date.today()

        # الحصول على أو إنشاء ملخص التحقق الصباحي
        morning_summary, created = MorningCheckSummary.objects.get_or_create(
            unit=selected_unit,
            date=selected_date,
            defaults={
                'created_by': user,
                'overall_readiness_score': 0,
                'is_fully_ready': False
            }
        )

        # الحصول على الفرق المتاحة
        available_shifts = WorkShift.objects.filter(unit=selected_unit, is_active=True)

        # الحصول على الجدولة اليومية
        daily_schedule = DailyShiftSchedule.objects.filter(
            unit=selected_unit,
            date=selected_date
        ).first()

        # الحصول على أعوان الوحدة
        unit_personnel = UnitPersonnel.objects.filter(unit=selected_unit, is_active=True)

        # الحصول على حالات الأعوان اليومية
        personnel_statuses = {}
        for personnel in unit_personnel:
            status, created = DailyPersonnelStatus.objects.get_or_create(
                personnel=personnel,
                date=selected_date,
                defaults={
                    'status': 'present',
                    'updated_by': user
                }
            )
            personnel_statuses[personnel.id] = status

        # الحصول على وسائل الوحدة
        unit_equipment = UnitEquipment.objects.filter(unit=selected_unit, is_active=True)

        # الحصول على حالات الوسائل اليومية
        equipment_statuses = {}
        for equipment in unit_equipment:
            status, created = DailyEquipmentStatus.objects.get_or_create(
                equipment=equipment,
                date=selected_date,
                defaults={
                    'status': 'operational',
                    'updated_by': user
                }
            )
            equipment_statuses[equipment.id] = status

        # الحصول على أعوان نظام 8 ساعات
        eight_hour_personnel = EightHourPersonnel.objects.filter(
            unit=selected_unit,
            date=selected_date
        )

        # الحصول على التنبيهات النشطة
        active_alerts = ReadinessAlert.objects.filter(
            unit=selected_unit,
            date=selected_date,
            status='active'
        ).order_by('-priority', '-created_at')

        # حساب الإحصائيات
        total_personnel = unit_personnel.count()
        present_personnel = sum(1 for status in personnel_statuses.values() if status.status == 'present')
        absent_personnel = sum(1 for status in personnel_statuses.values() if status.status == 'absent')
        on_mission_personnel = sum(1 for status in personnel_statuses.values() if status.status == 'on_mission')

        total_vehicles = unit_equipment.count()
        operational_vehicles = sum(1 for status in equipment_statuses.values() if status.status == 'operational')
        maintenance_vehicles = sum(1 for status in equipment_statuses.values() if status.status == 'under_maintenance')
        out_of_service_vehicles = sum(1 for status in equipment_statuses.values() if status.status == 'out_of_service')

        # تحديث ملخص التحقق الصباحي
        morning_summary.total_personnel = total_personnel
        morning_summary.present_personnel = present_personnel
        morning_summary.absent_personnel = absent_personnel
        morning_summary.on_mission_personnel = on_mission_personnel
        morning_summary.total_vehicles = total_vehicles
        morning_summary.ready_vehicles = operational_vehicles
        morning_summary.under_maintenance_vehicles = maintenance_vehicles
        morning_summary.not_ready_vehicles = maintenance_vehicles + out_of_service_vehicles
        morning_summary.active_shift = daily_schedule.active_shift if daily_schedule else None

        # حساب نسبة الجاهزية
        morning_summary.calculate_readiness_score()
        morning_summary.save()

        context = {
            'available_units': available_units,
            'selected_unit': selected_unit,
            'selected_date': selected_date,
            'morning_summary': morning_summary,
            'available_shifts': available_shifts,
            'daily_schedule': daily_schedule,
            'unit_personnel': unit_personnel,
            'personnel_statuses': personnel_statuses,
            'unit_equipment': unit_equipment,
            'equipment_statuses': equipment_statuses,
            'eight_hour_personnel': eight_hour_personnel,
            'active_alerts': active_alerts,
            'user_role': user_profile.role,
            'is_admin': user_profile.role == 'admin',
            'can_edit': user_profile.role in ['admin', 'unit_manager', 'unit_coordinator'],
        }

        return render(request, 'morning_check/index.html', context)

    except Exception as e:
        messages.error(request, f'حدث خطأ في تحميل نظام التحقق الصباحي: {str(e)}')
        return redirect('home')


@csrf_exempt
@login_required(login_url='login')
def shift_management_view(request):
    """إدارة الفرق والجدولة"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            action = data.get('action')

            if action == 'set_active_shift':
                unit_id = data.get('unit_id')
                shift_id = data.get('shift_id')
                date_str = data.get('date')

                unit = InterventionUnit.objects.get(id=unit_id)
                shift = WorkShift.objects.get(id=shift_id)
                selected_date = datetime.strptime(date_str, '%Y-%m-%d').date()

                # إنشاء أو تحديث الجدولة اليومية
                schedule, created = DailyShiftSchedule.objects.update_or_create(
                    unit=unit,
                    date=selected_date,
                    defaults={
                        'active_shift': shift,
                        'created_by': request.user
                    }
                )

                return JsonResponse({
                    'success': True,
                    'message': f'تم تعيين {shift.get_name_display()} كفرقة عاملة لتاريخ {selected_date}'
                })

            elif action == 'create_shift':
                unit_id = data.get('unit_id')
                name = data.get('name')
                shift_type = data.get('shift_type')

                unit = InterventionUnit.objects.get(id=unit_id)

                shift = WorkShift.objects.create(
                    unit=unit,
                    name=name,
                    shift_type=shift_type,
                    created_by=request.user
                )

                return JsonResponse({
                    'success': True,
                    'message': f'تم إنشاء الفرقة {shift.get_name_display()} بنجاح',
                    'shift_id': shift.id
                })

        except Exception as e:
            return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})

    return JsonResponse({'success': False, 'message': 'طريقة غير مدعومة'})


@csrf_exempt
@login_required(login_url='login')
def personnel_status_update_view(request):
    """تحديث حالة الأعوان"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            personnel_id = data.get('personnel_id')
            status = data.get('status')
            date_str = data.get('date')
            notes = data.get('notes', '')

            personnel = UnitPersonnel.objects.get(id=personnel_id)
            selected_date = datetime.strptime(date_str, '%Y-%m-%d').date()

            # تحديث حالة العون
            personnel_status, created = DailyPersonnelStatus.objects.update_or_create(
                personnel=personnel,
                date=selected_date,
                defaults={
                    'status': status,
                    'notes': notes,
                    'updated_by': request.user
                }
            )

            return JsonResponse({
                'success': True,
                'message': f'تم تحديث حالة {personnel.full_name} إلى {personnel_status.get_status_display()}'
            })

        except Exception as e:
            return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})

    return JsonResponse({'success': False, 'message': 'طريقة غير مدعومة'})


@csrf_exempt
@login_required(login_url='login')
def eight_hour_personnel_view(request):
    """إدارة أعوان نظام 8 ساعات"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            action = data.get('action')

            if action == 'add_personnel':
                unit_id = data.get('unit_id')
                personnel_id = data.get('personnel_id')
                date_str = data.get('date')
                work_period = data.get('work_period')
                task_type = data.get('task_type')
                task_description = data.get('task_description', '')
                start_time = data.get('start_time')
                end_time = data.get('end_time')

                unit = InterventionUnit.objects.get(id=unit_id)
                personnel = UnitPersonnel.objects.get(id=personnel_id)
                selected_date = datetime.strptime(date_str, '%Y-%m-%d').date()

                # إنشاء تسجيل نظام 8 ساعات
                eight_hour_record = EightHourPersonnel.objects.create(
                    unit=unit,
                    personnel=personnel,
                    date=selected_date,
                    work_period=work_period,
                    task_type=task_type,
                    task_description=task_description,
                    start_time=start_time,
                    end_time=end_time,
                    created_by=request.user
                )

                return JsonResponse({
                    'success': True,
                    'message': f'تم تسجيل {personnel.full_name} في نظام 8 ساعات',
                    'record_id': eight_hour_record.id
                })

            elif action == 'update_presence':
                record_id = data.get('record_id')
                is_present = data.get('is_present')
                notes = data.get('notes', '')

                record = EightHourPersonnel.objects.get(id=record_id)
                record.is_present = is_present
                record.notes = notes
                record.save()

                return JsonResponse({
                    'success': True,
                    'message': f'تم تحديث حضور {record.full_name}'
                })

        except Exception as e:
            return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})

    return JsonResponse({'success': False, 'message': 'طريقة غير مدعومة'})


@csrf_exempt
@login_required(login_url='login')
def readiness_alerts_view(request):
    """إدارة تنبيهات الجاهزية"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            action = data.get('action')

            if action == 'acknowledge_alert':
                alert_id = data.get('alert_id')
                alert = ReadinessAlert.objects.get(id=alert_id)

                alert.status = 'acknowledged'
                alert.acknowledged_by = request.user
                alert.acknowledged_at = timezone.now()
                alert.save()

                return JsonResponse({
                    'success': True,
                    'message': 'تم الاطلاع على التنبيه'
                })

            elif action == 'resolve_alert':
                alert_id = data.get('alert_id')
                resolution_notes = data.get('resolution_notes', '')

                alert = ReadinessAlert.objects.get(id=alert_id)
                alert.status = 'resolved'
                alert.resolved_by = request.user
                alert.resolved_at = timezone.now()
                alert.resolution_notes = resolution_notes
                alert.save()

                return JsonResponse({
                    'success': True,
                    'message': 'تم حل التنبيه'
                })

            elif action == 'create_alert':
                unit_id = data.get('unit_id')
                alert_type = data.get('alert_type')
                priority = data.get('priority')
                title = data.get('title')
                message = data.get('message')
                date_str = data.get('date')

                unit = InterventionUnit.objects.get(id=unit_id)
                selected_date = datetime.strptime(date_str, '%Y-%m-%d').date()

                alert = ReadinessAlert.objects.create(
                    unit=unit,
                    alert_type=alert_type,
                    priority=priority,
                    title=title,
                    message=message,
                    date=selected_date
                )

                return JsonResponse({
                    'success': True,
                    'message': 'تم إنشاء التنبيه بنجاح',
                    'alert_id': alert.id
                })

        except Exception as e:
            return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})

    return JsonResponse({'success': False, 'message': 'طريقة غير مدعومة'})


@login_required(login_url='login')
def morning_check_dashboard_view(request):
    """لوحة تحكم نظام التحقق الصباحي"""
    try:
        user = request.user
        user_profile = user.userprofile

        # تحديد الوحدات المتاحة حسب الدور
        if user_profile.role == 'admin':
            available_units = InterventionUnit.objects.all()
        elif user_profile.role == 'wilaya_manager':
            available_units = InterventionUnit.objects.filter(wilaya=user_profile.wilaya)
        elif user_profile.role in ['unit_manager', 'unit_coordinator']:
            available_units = user_profile.intervention_units.all()
        else:
            available_units = InterventionUnit.objects.none()

        # الحصول على التاريخ المحدد
        from datetime import date, timedelta
        selected_date = request.GET.get('date')
        if selected_date:
            try:
                selected_date = datetime.strptime(selected_date, '%Y-%m-%d').date()
            except ValueError:
                selected_date = date.today()
        else:
            selected_date = date.today()

        # الحصول على ملخصات التحقق الصباحي
        morning_summaries = MorningCheckSummary.objects.filter(
            unit__in=available_units,
            date=selected_date
        ).select_related('unit', 'active_shift')

        # حساب الإحصائيات العامة
        total_units = available_units.count()
        units_with_summaries = morning_summaries.count()
        units_fully_ready = morning_summaries.filter(is_fully_ready=True).count()

        # حساب متوسط الجاهزية
        if morning_summaries.exists():
            avg_readiness = sum(s.overall_readiness_score for s in morning_summaries) / morning_summaries.count()
        else:
            avg_readiness = 0

        # الحصول على التنبيهات النشطة
        active_alerts = ReadinessAlert.objects.filter(
            unit__in=available_units,
            date=selected_date,
            status='active'
        ).order_by('-priority', '-created_at')

        # إحصائيات التنبيهات
        critical_alerts = active_alerts.filter(priority='critical').count()
        high_alerts = active_alerts.filter(priority='high').count()
        medium_alerts = active_alerts.filter(priority='medium').count()
        low_alerts = active_alerts.filter(priority='low').count()

        # إحصائيات الأعوان والوسائل
        total_personnel = sum(s.total_personnel for s in morning_summaries)
        present_personnel = sum(s.present_personnel for s in morning_summaries)
        total_vehicles = sum(s.total_vehicles for s in morning_summaries)
        ready_vehicles = sum(s.ready_vehicles for s in morning_summaries)

        # بيانات الرسوم البيانية
        chart_data = {
            'readiness_by_unit': [
                {
                    'unit_name': s.unit.name,
                    'readiness_score': s.overall_readiness_score,
                    'is_fully_ready': s.is_fully_ready
                }
                for s in morning_summaries
            ],
            'personnel_status': {
                'present': present_personnel,
                'absent': sum(s.absent_personnel for s in morning_summaries),
                'on_mission': sum(s.on_mission_personnel for s in morning_summaries)
            },
            'vehicle_status': {
                'ready': ready_vehicles,
                'not_ready': sum(s.not_ready_vehicles for s in morning_summaries),
                'maintenance': sum(s.under_maintenance_vehicles for s in morning_summaries)
            }
        }

        context = {
            'available_units': available_units,
            'selected_date': selected_date,
            'morning_summaries': morning_summaries,
            'total_units': total_units,
            'units_with_summaries': units_with_summaries,
            'units_fully_ready': units_fully_ready,
            'avg_readiness': round(avg_readiness, 1),
            'active_alerts': active_alerts,
            'critical_alerts': critical_alerts,
            'high_alerts': high_alerts,
            'medium_alerts': medium_alerts,
            'low_alerts': low_alerts,
            'total_personnel': total_personnel,
            'present_personnel': present_personnel,
            'total_vehicles': total_vehicles,
            'ready_vehicles': ready_vehicles,
            'chart_data': json.dumps(chart_data),
            'user_role': user_profile.role,
            'is_admin': user_profile.role == 'admin',
        }

        return render(request, 'morning_check/dashboard.html', context)

    except Exception as e:
        messages.error(request, f'حدث خطأ في تحميل لوحة التحكم: {str(e)}')
        return redirect('home')


# ========================================
# وظائف CRUD للنظام المحدث
# ========================================

@csrf_exempt
@login_required(login_url='login')
def add_personnel_ajax(request):
    """إضافة عون جديد عبر AJAX"""
    if request.method == 'POST':
        try:
            import json
            data = json.loads(request.body)

            # التحقق من البيانات المطلوبة
            required_fields = ['unit_id', 'personnel_registration_number', 'full_name', 'gender', 'age']
            for field in required_fields:
                if not data.get(field):
                    return JsonResponse({'success': False, 'error': f'الحقل {field} مطلوب'})

            # التحقق من وجود الوحدة
            try:
                unit = InterventionUnit.objects.get(id=data['unit_id'])
            except InterventionUnit.DoesNotExist:
                return JsonResponse({'success': False, 'error': 'الوحدة غير موجودة'})

            # التحقق من عدم تكرار رقم التسجيل
            if UnitPersonnel.objects.filter(personnel_registration_number=data['personnel_registration_number']).exists():
                return JsonResponse({'success': False, 'error': 'رقم التسجيل موجود مسبقاً'})

            # إنشاء العون الجديد
            personnel = UnitPersonnel.objects.create(
                unit=unit,
                personnel_registration_number=data['personnel_registration_number'],
                registration_number=data.get('registration_number', ''),
                full_name=data['full_name'],
                gender=data['gender'],
                age=int(data['age']),
                phone_number=data.get('phone_number', ''),
                rank=data.get('rank', ''),
                position=data.get('position', ''),
                work_system=data.get('work_system', '24_hours'),
                assigned_shift=data.get('assigned_shift', ''),
                created_by=request.user
            )

            return JsonResponse({
                'success': True,
                'message': 'تم إضافة العون بنجاح',
                'personnel_id': personnel.id,
                'personnel_name': personnel.full_name
            })

        except Exception as e:
            return JsonResponse({'success': False, 'error': f'حدث خطأ: {str(e)}'})

    return JsonResponse({'success': False, 'error': 'طريقة غير مدعومة'})


@csrf_exempt
@login_required(login_url='login')
def delete_personnel_ajax(request):
    """حذف عون عبر AJAX"""
    if request.method == 'POST':
        try:
            import json
            data = json.loads(request.body)

            personnel_id = data.get('personnel_id')
            if not personnel_id:
                return JsonResponse({'success': False, 'error': 'معرف العون مطلوب'})

            try:
                personnel = UnitPersonnel.objects.get(id=personnel_id)
                personnel_name = personnel.full_name
                personnel.delete()

                return JsonResponse({
                    'success': True,
                    'message': f'تم حذف العون {personnel_name} بنجاح'
                })

            except UnitPersonnel.DoesNotExist:
                return JsonResponse({'success': False, 'error': 'العون غير موجود'})

        except Exception as e:
            return JsonResponse({'success': False, 'error': f'حدث خطأ: {str(e)}'})

    return JsonResponse({'success': False, 'error': 'طريقة غير مدعومة'})


@csrf_exempt
@login_required(login_url='login')
def transfer_personnel_ajax(request):
    """تحويل عون بين الفرق عبر AJAX"""
    if request.method == 'POST':
        try:
            import json
            data = json.loads(request.body)

            personnel_id = data.get('personnel_id')
            target_shift = data.get('target_shift')
            transfer_reason = data.get('transfer_reason')

            if not all([personnel_id, target_shift, transfer_reason]):
                return JsonResponse({'success': False, 'error': 'جميع الحقول مطلوبة'})

            try:
                personnel = UnitPersonnel.objects.get(id=personnel_id)
                from_shift = personnel.assigned_shift

                # إنشاء سجل التحويل
                transfer = PersonnelTransfer.objects.create(
                    unit=personnel.unit,
                    personnel=personnel,
                    from_shift=from_shift,
                    to_shift=target_shift,
                    transfer_reason=transfer_reason,
                    transfer_date=timezone.now().date(),
                    status='completed',
                    requested_by=request.user,
                    approved_by=request.user
                )

                # تحديث فرقة العون
                personnel.assigned_shift = target_shift
                personnel.save()

                return JsonResponse({
                    'success': True,
                    'message': f'تم تحويل {personnel.full_name} بنجاح',
                    'transfer_id': transfer.id
                })

            except UnitPersonnel.DoesNotExist:
                return JsonResponse({'success': False, 'error': 'العون غير موجود'})

        except Exception as e:
            return JsonResponse({'success': False, 'error': f'حدث خطأ: {str(e)}'})

    return JsonResponse({'success': False, 'error': 'طريقة غير مدعومة'})


@csrf_exempt
@login_required(login_url='login')
def add_equipment_ajax(request):
    """إضافة وسيلة جديدة عبر AJAX"""
    if request.method == 'POST':
        try:
            import json
            data = json.loads(request.body)

            # التحقق من البيانات المطلوبة
            required_fields = ['unit_id', 'serial_number', 'equipment_type']
            for field in required_fields:
                if not data.get(field):
                    return JsonResponse({'success': False, 'error': f'الحقل {field} مطلوب'})

            # التحقق من وجود الوحدة
            try:
                unit = InterventionUnit.objects.get(id=data['unit_id'])
            except InterventionUnit.DoesNotExist:
                return JsonResponse({'success': False, 'error': 'الوحدة غير موجودة'})

            # التحقق من عدم تكرار الرقم التسلسلي
            if UnitEquipment.objects.filter(unit=unit, serial_number=data['serial_number']).exists():
                return JsonResponse({'success': False, 'error': 'الرقم التسلسلي موجود مسبقاً في هذه الوحدة'})

            # إنشاء الوسيلة الجديدة
            equipment = UnitEquipment.objects.create(
                unit=unit,
                serial_number=data['serial_number'],
                equipment_type=data['equipment_type'],
                radio_number=data.get('radio_number', ''),
                created_by=request.user
            )

            return JsonResponse({
                'success': True,
                'message': 'تم إضافة الوسيلة بنجاح',
                'equipment_id': equipment.id,
                'equipment_type': equipment.equipment_type
            })

        except Exception as e:
            return JsonResponse({'success': False, 'error': f'حدث خطأ: {str(e)}'})

    return JsonResponse({'success': False, 'error': 'طريقة غير مدعومة'})


@csrf_exempt
@login_required(login_url='login')
def delete_equipment_ajax(request):
    """حذف وسيلة عبر AJAX"""
    if request.method == 'POST':
        try:
            import json
            data = json.loads(request.body)

            equipment_id = data.get('equipment_id')
            if not equipment_id:
                return JsonResponse({'success': False, 'error': 'معرف الوسيلة مطلوب'})

            try:
                equipment = UnitEquipment.objects.get(id=equipment_id)
                equipment_type = equipment.equipment_type
                equipment.delete()

                return JsonResponse({
                    'success': True,
                    'message': f'تم حذف الوسيلة {equipment_type} بنجاح'
                })

            except UnitEquipment.DoesNotExist:
                return JsonResponse({'success': False, 'error': 'الوسيلة غير موجودة'})

        except Exception as e:
            return JsonResponse({'success': False, 'error': f'حدث خطأ: {str(e)}'})

    return JsonResponse({'success': False, 'error': 'طريقة غير مدعومة'})


@csrf_exempt
@login_required(login_url='login')
def add_eight_hour_personnel_ajax(request):
    """إضافة عون نظام 8 ساعات عبر AJAX"""
    if request.method == 'POST':
        try:
            import json
            from datetime import datetime, time
            data = json.loads(request.body)

            # التحقق من البيانات المطلوبة
            required_fields = ['unit_id', 'personnel_registration_number', 'full_name', 'gender', 'age', 'work_period', 'task_type']
            for field in required_fields:
                if not data.get(field):
                    return JsonResponse({'success': False, 'error': f'الحقل {field} مطلوب'})

            # التحقق من وجود الوحدة
            try:
                unit = InterventionUnit.objects.get(id=data['unit_id'])
            except InterventionUnit.DoesNotExist:
                return JsonResponse({'success': False, 'error': 'الوحدة غير موجودة'})

            # تحديد أوقات العمل حسب الفترة
            work_times = {
                'morning': (time(8, 0), time(16, 0)),
                'evening': (time(16, 0), time(0, 0)),
                'night': (time(0, 0), time(8, 0)),
            }

            start_time, end_time = work_times.get(data['work_period'], (time(8, 0), time(16, 0)))

            # إنشاء عون نظام 8 ساعات
            eight_hour_personnel = EightHourPersonnel.objects.create(
                unit=unit,
                personnel_registration_number=data['personnel_registration_number'],
                full_name=data['full_name'],
                gender=data['gender'],
                age=int(data['age']),
                phone_number=data.get('phone_number', ''),
                date=datetime.now().date(),
                work_period=data['work_period'],
                task_type=data['task_type'],
                task_description=data.get('task_description', ''),
                start_time=start_time,
                end_time=end_time,
                notes=data.get('notes', ''),
                created_by=request.user
            )

            return JsonResponse({
                'success': True,
                'message': 'تم إضافة عون نظام 8 ساعات بنجاح',
                'personnel_id': eight_hour_personnel.id,
                'personnel_name': eight_hour_personnel.full_name
            })

        except Exception as e:
            return JsonResponse({'success': False, 'error': f'حدث خطأ: {str(e)}'})

    return JsonResponse({'success': False, 'error': 'طريقة غير مدعومة'})


@csrf_exempt
@login_required(login_url='login')
def delete_eight_hour_personnel_ajax(request):
    """حذف عون نظام 8 ساعات عبر AJAX"""
    if request.method == 'POST':
        try:
            import json
            data = json.loads(request.body)

            personnel_id = data.get('personnel_id')
            if not personnel_id:
                return JsonResponse({'success': False, 'error': 'معرف العون مطلوب'})

            try:
                personnel = EightHourPersonnel.objects.get(id=personnel_id)
                personnel_name = personnel.full_name
                personnel.delete()

                return JsonResponse({
                    'success': True,
                    'message': f'تم حذف عون نظام 8 ساعات {personnel_name} بنجاح'
                })

            except EightHourPersonnel.DoesNotExist:
                return JsonResponse({'success': False, 'error': 'العون غير موجود'})

        except Exception as e:
            return JsonResponse({'success': False, 'error': f'حدث خطأ: {str(e)}'})

    return JsonResponse({'success': False, 'error': 'طريقة غير مدعومة'})


# ===== APIs التزامن بين الصفحات =====

@csrf_exempt
@login_required(login_url='login')
def sync_personnel_status(request):
    """تزامن حالة الأعوان بين جميع الصفحات"""
    if request.method == 'POST':
        try:
            import json
            data = json.loads(request.body)

            personnel_id = data.get('personnel_id')
            new_status = data.get('status')
            date_str = data.get('date')

            if not all([personnel_id, new_status, date_str]):
                return JsonResponse({'success': False, 'error': 'البيانات المطلوبة ناقصة'})

            # التحقق من وجود العون
            try:
                personnel = UnitPersonnel.objects.get(id=personnel_id)
            except UnitPersonnel.DoesNotExist:
                return JsonResponse({'success': False, 'error': 'العون غير موجود'})

            # تحويل التاريخ
            from datetime import datetime
            date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()

            # تحديث أو إنشاء حالة العون اليومية
            daily_status, created = DailyPersonnelStatus.objects.get_or_create(
                personnel=personnel,
                date=date_obj,
                defaults={
                    'status': new_status,
                    'updated_by': request.user
                }
            )

            if not created:
                daily_status.status = new_status
                daily_status.updated_by = request.user
                daily_status.save()

            # تحديث جاهزية الوسائل المرتبطة
            update_vehicle_readiness_for_personnel(personnel, date_obj)

            return JsonResponse({
                'success': True,
                'message': f'تم تحديث حالة العون {personnel.full_name} إلى {daily_status.get_status_display()}',
                'personnel_id': personnel_id,
                'status': new_status,
                'status_display': daily_status.get_status_display()
            })

        except Exception as e:
            return JsonResponse({'success': False, 'error': f'حدث خطأ: {str(e)}'})

    return JsonResponse({'success': False, 'error': 'طريقة غير مدعومة'})


@csrf_exempt
@login_required(login_url='login')
def sync_vehicle_readiness(request):
    """تزامن جاهزية الوسائل بين جميع الصفحات"""
    if request.method == 'POST':
        try:
            import json
            data = json.loads(request.body)

            vehicle_id = data.get('vehicle_id')
            readiness_status = data.get('readiness')
            date_str = data.get('date')

            if not all([vehicle_id, readiness_status, date_str]):
                return JsonResponse({'success': False, 'error': 'البيانات المطلوبة ناقصة'})

            # التحقق من وجود الوسيلة
            try:
                vehicle = UnitEquipment.objects.get(id=vehicle_id)
            except UnitEquipment.DoesNotExist:
                return JsonResponse({'success': False, 'error': 'الوسيلة غير موجودة'})

            # تحويل التاريخ
            from datetime import datetime
            date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()

            # تحديث أو إنشاء جاهزية الوسيلة
            vehicle_readiness, created = VehicleReadiness.objects.get_or_create(
                vehicle=vehicle,
                date=date_obj,
                defaults={
                    'status': readiness_status,
                    'is_manually_confirmed': True if readiness_status == 'manually_confirmed' else False
                }
            )

            if not created:
                vehicle_readiness.status = readiness_status
                vehicle_readiness.is_manually_confirmed = True if readiness_status == 'manually_confirmed' else False
                vehicle_readiness.save()

            # إعادة حساب نسبة الجاهزية
            vehicle_readiness.calculate_readiness_score()
            vehicle_readiness.save()

            return JsonResponse({
                'success': True,
                'message': f'تم تحديث جاهزية الوسيلة {vehicle.equipment_type} إلى {vehicle_readiness.get_status_display()}',
                'vehicle_id': vehicle_id,
                'readiness': readiness_status,
                'readiness_display': vehicle_readiness.get_status_display(),
                'readiness_score': vehicle_readiness.readiness_score
            })

        except Exception as e:
            return JsonResponse({'success': False, 'error': f'حدث خطأ: {str(e)}'})

    return JsonResponse({'success': False, 'error': 'طريقة غير مدعومة'})


def update_vehicle_readiness_for_personnel(personnel, date):
    """تحديث جاهزية الوسائل عند تغيير حالة العون"""
    try:
        # الحصول على جميع الوسائل المرتبطة بهذا العون في هذا التاريخ
        assignments = VehicleCrewAssignment.objects.filter(
            personnel=personnel,
            assignment_date=date,
            is_active=True
        )

        for assignment in assignments:
            # الحصول على أو إنشاء جاهزية الوسيلة
            vehicle_readiness, created = VehicleReadiness.objects.get_or_create(
                vehicle=assignment.vehicle,
                date=date
            )

            # إعادة حساب نسبة الجاهزية
            vehicle_readiness.calculate_readiness_score()
            vehicle_readiness.save()

    except Exception as e:
        # تسجيل الخطأ ولكن لا نوقف العملية
        print(f"خطأ في تحديث جاهزية الوسائل: {str(e)}")


@csrf_exempt
@login_required(login_url='login')
def get_sync_status(request):
    """الحصول على حالة التزامن الحالية"""
    if request.method == 'GET':
        try:
            unit_id = request.GET.get('unit_id')
            date_str = request.GET.get('date')

            if not all([unit_id, date_str]):
                return JsonResponse({'success': False, 'error': 'معرف الوحدة والتاريخ مطلوبان'})

            # تحويل التاريخ
            from datetime import datetime
            date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()

            # الحصول على الوحدة
            try:
                unit = InterventionUnit.objects.get(id=unit_id)
            except InterventionUnit.DoesNotExist:
                return JsonResponse({'success': False, 'error': 'الوحدة غير موجودة'})

            # الحصول على حالة الأعوان
            personnel_status = []
            personnel = UnitPersonnel.objects.filter(unit=unit, is_active=True)

            for person in personnel:
                try:
                    daily_status = DailyPersonnelStatus.objects.get(personnel=person, date=date_obj)
                    status = daily_status.status
                except DailyPersonnelStatus.DoesNotExist:
                    status = 'present'  # الحالة الافتراضية

                personnel_status.append({
                    'id': person.id,
                    'name': person.full_name,
                    'status': status
                })

            # الحصول على حالة الوسائل
            vehicle_status = []
            vehicles = UnitEquipment.objects.filter(unit=unit, is_active=True)

            for vehicle in vehicles:
                try:
                    readiness = VehicleReadiness.objects.get(vehicle=vehicle, date=date_obj)
                    status = readiness.status
                    score = readiness.readiness_score
                except VehicleReadiness.DoesNotExist:
                    status = 'not_ready'  # الحالة الافتراضية
                    score = 0

                vehicle_status.append({
                    'id': vehicle.id,
                    'name': f"{vehicle.equipment_type} - {vehicle.serial_number}",
                    'status': status,
                    'readiness_score': score
                })

            return JsonResponse({
                'success': True,
                'personnel_status': personnel_status,
                'vehicle_status': vehicle_status,
                'sync_timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            return JsonResponse({'success': False, 'error': f'حدث خطأ: {str(e)}'})

    return JsonResponse({'success': False, 'error': 'طريقة غير مدعومة'})


@login_required(login_url='login')
def intervention_details_view(request):
    """صفحة تفاصيل التدخلات حسب النوع"""
    from .models import InterventionUnit

    # الحصول على الوحدة الحالية للمستخدم
    user_profile = getattr(request.user, 'userprofile', None)
    if user_profile and user_profile.intervention_units.exists():
        current_unit = user_profile.intervention_units.first()
    else:
        current_unit = InterventionUnit.objects.first()

    context = {
        'current_unit': current_unit,
    }

    return render(request, 'coordination_center/intervention_details.html', context)


@csrf_exempt
@require_http_methods(["GET"])
def get_interventions_by_type(request):
    """جلب التدخلات حسب النوع"""
    try:
        from .models import DailyIntervention, InterventionUnit
        from datetime import date, datetime

        intervention_type = request.GET.get('type')
        unit_id = request.GET.get('unit_id')
        date_str = request.GET.get('date')
        intervention_id = request.GET.get('intervention_id')

        if not intervention_type:
            return JsonResponse({'success': False, 'error': 'نوع التدخل مطلوب'})

        # تحديد الوحدة
        if unit_id:
            try:
                unit = InterventionUnit.objects.get(id=unit_id)
            except InterventionUnit.DoesNotExist:
                return JsonResponse({'success': False, 'error': 'الوحدة غير موجودة'})
        else:
            # الحصول على الوحدة الحالية للمستخدم
            user_profile = getattr(request.user, 'userprofile', None)
            if user_profile and user_profile.intervention_units.exists():
                unit = user_profile.intervention_units.first()
            else:
                unit = InterventionUnit.objects.first()

        # تحديد التاريخ
        if date_str:
            try:
                selected_date = datetime.strptime(date_str, '%Y-%m-%d').date()
            except ValueError:
                selected_date = date.today()
        else:
            selected_date = date.today()

        # تحديد أنماط البحث المرنة لأنواع التدخلات
        intervention_type_patterns = {
            'medical': ['إجلاء صحي', 'اجلاء صحي', 'إسعاف', 'اسعاف', 'medical'],
            'accident': ['حادث مرور', 'حوادث المرور', 'انقلاب', 'تصادم', 'حادث', 'accident'],
            'fire': ['حريق بناية', 'حريق', 'حرائق البنايات', 'حرائق المباني', 'fire', 'building-fire'],
            'crop': ['حريق محاصيل', 'حرائق المحاصيل', 'حريق زراعي', 'محاصيل', 'crop', 'agricultural-fire']
        }

        patterns = intervention_type_patterns.get(intervention_type, [])
        if not patterns:
            return JsonResponse({'success': False, 'error': 'نوع التدخل غير صحيح'})

        # البحث المرن باستخدام Q objects
        from django.db.models import Q
        query = Q()
        for pattern in patterns:
            query |= Q(intervention_type__icontains=pattern)

        # جلب التدخلات مع التفاصيل المتخصصة
        interventions_query = DailyIntervention.objects.filter(
            query,
            unit=unit,
            date=selected_date
        ).select_related(
            'agricultural_fire_detail',
            'building_fire_detail',
            'traffic_detail',
            'medical_detail'
        ).prefetch_related('casualties')

        # إذا تم تحديد معرف تدخل معين، أضفه للاستعلام
        if intervention_id:
            try:
                specific_intervention = DailyIntervention.objects.get(id=intervention_id)
                # إضافة التدخل المحدد إلى النتائج إذا لم يكن موجوداً
                if specific_intervention not in interventions_query:
                    # إنشاء استعلام جديد يشمل التدخل المحدد
                    specific_query = Q(id=intervention_id)
                    combined_query = query | specific_query
                    interventions_query = DailyIntervention.objects.filter(
                        combined_query,
                        unit=unit
                    )
            except DailyIntervention.DoesNotExist:
                pass

        interventions = interventions_query.order_by('-created_at')

        # تحويل البيانات إلى JSON
        interventions_data = []
        for intervention in interventions:
            # تحويل departure_time إلى نص بأمان
            departure_time_str = ''
            if intervention.departure_time:
                if hasattr(intervention.departure_time, 'strftime'):
                    departure_time_str = intervention.departure_time.strftime('%H:%M')
                else:
                    departure_time_str = str(intervention.departure_time)

            # جلب الوسائل المرسلة من العلاقة
            vehicles_list = []
            for vehicle_relation in intervention.vehicles.all():
                vehicle = vehicle_relation.vehicle
                vehicle_info = f"{vehicle.equipment_type} {vehicle.serial_number}"
                if vehicle.radio_number:
                    vehicle_info += f" | راديو: {vehicle.radio_number}"
                vehicles_list.append(vehicle_info)

            vehicles_sent_text = " | ".join(vehicles_list) if vehicles_list else "-"

            # تحويل arrival_time إلى نص بأمان
            arrival_time_str = ''
            if intervention.arrival_time:
                if hasattr(intervention.arrival_time, 'strftime'):
                    arrival_time_str = intervention.arrival_time.strftime('%H:%M')
                else:
                    arrival_time_str = str(intervention.arrival_time)

            # تحويل end_time إلى نص بأمان
            end_time_str = ''
            if intervention.end_time:
                if hasattr(intervention.end_time, 'strftime'):
                    end_time_str = intervention.end_time.strftime('%H:%M')
                else:
                    end_time_str = str(intervention.end_time)

            # جلب التفاصيل المتخصصة حسب نوع التدخل
            specialized_details = {}

            # جلب بيانات الضحايا (المسعفين والوفيات)
            injured_details = []
            fatality_details = []

            for casualty in intervention.casualties.all():
                casualty_data = {
                    'name': casualty.full_name,
                    'age': casualty.age,
                    'gender': casualty.get_gender_display()
                }
                if casualty.casualty_type == 'injured':
                    injured_details.append(casualty_data)
                elif casualty.casualty_type == 'fatality':
                    fatality_details.append(casualty_data)

            # تفاصيل حريق المحاصيل الزراعية
            if hasattr(intervention, 'agricultural_fire_detail') and intervention.agricultural_fire_detail:
                detail = intervention.agricultural_fire_detail
                specialized_details.update({
                    'fire_type': getattr(detail, 'fire_type', '-'),
                    'fire_points_count': getattr(detail, 'fire_sources_count', '-'),
                    'wind_direction': getattr(detail, 'wind_direction', '-'),
                    'wind_speed': getattr(detail, 'wind_speed', '-'),
                    'population_threat': 'نعم' if getattr(detail, 'population_threat', False) else 'لا',
                    'evacuation_location': getattr(detail, 'evacuation_location', '-'),
                    'intervening_agents': getattr(detail, 'intervening_agents_count', '-'),
                    'affected_families': getattr(detail, 'affected_families_count', '-'),
                    'present_entities': getattr(detail, 'present_authorities', []),
                    'support_request': getattr(detail, 'support_request', '-'),
                    'area_losses': f"{getattr(detail, 'standing_wheat_area', 0)} + {getattr(detail, 'harvest_area', 0)} + {getattr(detail, 'forest_area', 0)} + {getattr(detail, 'barley_area', 0)} هكتار",
                    'count_losses': f"{getattr(detail, 'straw_bales_count', 0)} حزمة تبن + {getattr(detail, 'grain_bags_count', 0)} كيس + {getattr(detail, 'fruit_trees_count', 0)} شجرة + {getattr(detail, 'beehives_count', 0)} خلية",
                    'saved_properties': getattr(detail, 'saved_equipment', '-'),
                    'casualties_detail': getattr(detail, 'final_notes', '-'),
                })

            # تفاصيل حريق البنايات
            elif hasattr(intervention, 'building_fire_detail') and intervention.building_fire_detail:
                detail = intervention.building_fire_detail
                specialized_details.update({
                    'fire_type': getattr(detail, 'fire_nature', '-'),
                    'fire_location': getattr(detail, 'fire_location', '-'),
                    'fire_points_count': getattr(detail, 'fire_points_count', '-'),
                    'wind_direction': getattr(detail, 'wind_direction', '-'),
                    'wind_speed': getattr(detail, 'wind_speed', '-'),
                    'population_threat': 'نعم' if getattr(detail, 'population_threat', False) else 'لا',
                    'population_evacuated': getattr(detail, 'population_evacuated', False),
                    'assistance_provided': getattr(detail, 'assistance_provided', '-'),
                    'intervening_agents': getattr(detail, 'intervening_agents_count', '-'),
                    'affected_families': getattr(detail, 'affected_families_count', '-'),
                    'present_personnel': getattr(detail, 'present_personnel', []),
                    'support_request': getattr(detail, 'support_request', '-'),
                    'damages_description': getattr(detail, 'damages_description', '-'),
                    'saved_properties': getattr(detail, 'saved_properties', '-'),
                    'casualties_detail': getattr(detail, 'final_notes', '-'),
                })

            # تفاصيل حوادث المرور
            elif hasattr(intervention, 'traffic_detail') and intervention.traffic_detail:
                detail = intervention.traffic_detail
                specialized_details.update({
                    'accident_type': getattr(detail, 'accident_type', '-'),
                    'road_type': getattr(detail, 'road_type', '-'),
                    'involved_vehicles': getattr(detail, 'involved_vehicles', []),
                    'victims_details': getattr(detail, 'victims_details', []),
                    'fatalities_details': getattr(detail, 'fatalities_details', []),
                    'material_damage_notes': getattr(detail, 'material_damage_notes', '-'),
                    'casualties_detail': getattr(detail, 'material_damage_notes', '-'),
                })

            # تفاصيل الإجلاء الصحي
            elif hasattr(intervention, 'medical_detail') and intervention.medical_detail:
                detail = intervention.medical_detail
                specialized_details.update({
                    'evacuation_type': getattr(detail, 'intervention_nature', '-'),
                    'patient_condition': getattr(detail, 'suffocation_type', '-') or getattr(detail, 'poisoning_type', '-') or getattr(detail, 'burn_type', '-') or getattr(detail, 'explosion_type', '-') or getattr(detail, 'drowning_type', '-'),
                    'destination_hospital': getattr(detail, 'material_damage_notes', '-'),
                    'support_request': getattr(detail, 'support_request', '-'),
                    'casualties_detail': getattr(detail, 'material_damage_notes', '-'),
                })

            interventions_data.append({
                'id': intervention.id,
                'time': departure_time_str,
                'intervention_type': intervention.intervention_type,
                'location': intervention.location,
                'caller_entity': intervention.get_contact_source_display() if intervention.contact_source else '-',
                'contact_type': intervention.get_contact_type_display() if intervention.contact_type else '-',
                'phone_number': intervention.phone_number or '-',
                'vehicles_sent': vehicles_sent_text,
                'status': intervention.status,
                'created_at': intervention.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                # بيانات عملية التعرف
                'arrival_time': arrival_time_str or '-',
                'location_type': getattr(intervention, 'location_type', '-'),
                'injured_count': getattr(intervention, 'injured_count', 0),
                'deaths_count': getattr(intervention, 'deaths_count', 0),
                'material_damage': getattr(intervention, 'material_damage', '-'),
                # بيانات إنهاء المهمة
                'end_time': end_time_str or '-',
                'final_injured_count': getattr(intervention, 'final_injured_count', 0),
                'final_deaths_count': getattr(intervention, 'final_deaths_count', 0),
                'final_notes': getattr(intervention, 'final_notes', '-'),
                # بيانات إضافية
                'caller_name': intervention.caller_name or '-',
                'initial_notes': intervention.initial_notes or '-',
                # بيانات الضحايا التفصيلية
                'injured_details': injured_details,
                'fatality_details': fatality_details,
                # التفاصيل المتخصصة
                **specialized_details
            })

        # تحديد اسم نوع التدخل للعرض
        intervention_type_display = {
            'medical': 'إجلاء صحي',
            'accident': 'حادث مرور',
            'fire': 'حريق بناية',
            'crop': 'حريق محاصيل'
        }.get(intervention_type, intervention_type)

        return JsonResponse({
            'success': True,
            'interventions': interventions_data,
            'count': len(interventions_data),
            'unit_name': unit.name,
            'date': selected_date.strftime('%Y-%m-%d'),
            'intervention_type': intervention_type_display
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': f'حدث خطأ: {str(e)}'})


@csrf_exempt
@require_http_methods(["GET"])
def get_interventions_counts(request):
    """جلب عدادات جميع أنواع التدخلات"""
    try:
        from .models import DailyIntervention, InterventionUnit
        from datetime import date, datetime

        unit_id = request.GET.get('unit_id')
        date_str = request.GET.get('date')

        # تحديد الوحدة
        if unit_id:
            try:
                unit = InterventionUnit.objects.get(id=unit_id)
            except InterventionUnit.DoesNotExist:
                return JsonResponse({'success': False, 'error': 'الوحدة غير موجودة'})
        else:
            # الحصول على الوحدة الحالية للمستخدم
            user_profile = getattr(request.user, 'userprofile', None)
            if user_profile and user_profile.intervention_units.exists():
                unit = user_profile.intervention_units.first()
            else:
                unit = InterventionUnit.objects.first()

        # تحديد التاريخ
        if date_str:
            try:
                selected_date = datetime.strptime(date_str, '%Y-%m-%d').date()
            except ValueError:
                selected_date = date.today()
        else:
            selected_date = date.today()

        # أنواع التدخلات
        intervention_types = {
            'medical': 'إجلاء صحي',
            'accident': 'حادث مرور',
            'fire': 'حريق بناية',
            'crop': 'حريق محاصيل'
        }

        # جلب العدادات
        counts = {}
        for type_key, type_ar in intervention_types.items():
            count = DailyIntervention.objects.filter(
                unit=unit,
                intervention_type=type_ar,
                date=selected_date
            ).count()
            counts[type_key] = count

        return JsonResponse({
            'success': True,
            'counts': counts,
            'unit_name': unit.name,
            'date': selected_date.strftime('%Y-%m-%d'),
            'total': sum(counts.values())
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': f'حدث خطأ: {str(e)}'})
