/* ========================================
   أنماط النماذج المنبثقة الموحدة
   ======================================== */

/* النماذج المنبثقة الأساسية */
.modal-dialog {
    width: var(--modal-width-xl) !important;
    max-width: var(--modal-max-width) !important;
    margin: var(--modal-margin) !important;
}

.modal-xl {
    max-width: var(--modal-max-width) !important;
    width: var(--modal-width-xl) !important;
}

.modal-lg {
    max-width: var(--modal-width-lg) !important;
    width: var(--modal-width-lg) !important;
}

.modal-md {
    max-width: var(--modal-width-md) !important;
    width: var(--modal-width-md) !important;
}

.modal-sm {
    max-width: var(--modal-width-sm) !important;
    width: var(--modal-width-sm) !important;
}

/* محتوى النموذج */
.modal-content {
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    border: none;
    overflow: hidden;
}

/* رأس النموذج */
.modal-header {
    background: var(--gradient-primary);
    color: var(--text-white);
    border-bottom: none;
    padding: var(--spacing-lg) var(--spacing-xxl);
    text-align: center;
}

.modal-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
}

.modal-title i {
    font-size: var(--font-size-xxl);
}

/* جسم النموذج */
.modal-body {
    padding: var(--spacing-xxl) !important;
    background: var(--bg-primary);
    max-height: 80vh;
    overflow-y: auto;
}

/* ذيل النموذج */
.modal-footer {
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-light);
    padding: var(--spacing-lg) var(--spacing-xxl);
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
}

/* أقسام النموذج */
.form-section {
    background: var(--bg-secondary);
    border: 2px solid var(--primary-light);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.section-title {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.section-title i {
    font-size: var(--font-size-lg);
}

/* مجموعات الحقول */
.form-group {
    margin-bottom: var(--spacing-xl);
}

.form-label {
    font-size: var(--font-size-lg);
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.form-label i {
    color: var(--primary-color);
    font-size: var(--font-size-base);
}

/* الحقول الموحدة */
.form-control,
.clean-input,
.clean-select,
.clean-textarea {
    padding: var(--input-padding);
    font-size: var(--font-size-base);
    min-height: var(--input-height);
    border: var(--input-border-width) solid var(--border-color);
    border-radius: var(--input-border-radius);
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: var(--transition-normal);
    width: 100%;
}

.form-control:focus,
.clean-input:focus,
.clean-select:focus,
.clean-textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: none;
}

.form-control:hover,
.clean-input:hover,
.clean-select:hover,
.clean-textarea:hover {
    border-color: var(--primary-light);
}

/* الأزرار الموحدة */
.btn {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-base);
    font-weight: 500;
    border-radius: var(--border-radius-md);
    border: none;
    cursor: pointer;
    transition: var(--transition-normal);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    text-decoration: none;
    min-height: 44px;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--text-white);
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-success {
    background: var(--gradient-success);
    color: var(--text-white);
}

.btn-success:hover {
    background: var(--success-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-danger {
    background: var(--gradient-danger);
    color: var(--text-white);
}

.btn-danger:hover {
    background: var(--danger-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-warning {
    background: var(--gradient-warning);
    color: var(--text-primary);
}

.btn-warning:hover {
    background: var(--warning-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--text-white);
}

.btn-secondary:hover {
    background: var(--secondary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* أزرار الإغلاق */
.btn-close {
    background: transparent;
    border: none;
    font-size: var(--font-size-xl);
    color: var(--text-white);
    opacity: 0.8;
    cursor: pointer;
    transition: var(--transition-fast);
}

.btn-close:hover {
    opacity: 1;
    transform: scale(1.1);
}

/* التصميم المتجاوب للنماذج */
@media (max-width: 768px) {
    .modal-body {
        padding: var(--spacing-lg) !important;
    }
    
    .form-section {
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-lg);
    }
    
    .modal-footer {
        padding: var(--spacing-md) var(--spacing-lg);
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
        margin-bottom: var(--spacing-sm);
    }
}

@media (max-width: 480px) {
    .modal-body {
        padding: var(--spacing-md) !important;
    }
    
    .clean-input,
    .clean-select,
    .clean-textarea {
        font-size: var(--font-size-sm);
        padding: 12px 16px;
    }
    
    .form-label {
        font-size: var(--font-size-base);
    }
    
    .section-title {
        font-size: var(--font-size-base);
    }
}
