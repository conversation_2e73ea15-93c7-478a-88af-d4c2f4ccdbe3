/* ========================================
   أنماط النماذج الموحدة
   ======================================== */

/* النماذج الأساسية */
.form {
    width: 100%;
    max-width: 100%;
}

.form-row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -var(--spacing-sm);
}

.form-col {
    flex: 1;
    padding: 0 var(--spacing-sm);
    min-width: 0;
}

/* تخطيط الأعمدة */
.col-12 { flex: 0 0 100%; max-width: 100%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }

/* حقول الإدخال المتقدمة */
.input-group {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: stretch;
    width: 100%;
}

.input-group-prepend,
.input-group-append {
    display: flex;
}

.input-group-text {
    display: flex;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    margin-bottom: 0;
    font-size: var(--font-size-base);
    font-weight: 400;
    line-height: 1.5;
    color: var(--text-secondary);
    text-align: center;
    white-space: nowrap;
    background-color: var(--bg-light);
    border: var(--input-border-width) solid var(--border-color);
    border-radius: var(--border-radius-md);
}

/* حقول الاختيار المتعدد */
.form-check {
    position: relative;
    display: block;
    padding-left: 1.25rem;
    margin-bottom: var(--spacing-sm);
}

.form-check-input {
    position: absolute;
    margin-top: 0.3rem;
    margin-left: -1.25rem;
}

.form-check-label {
    margin-bottom: 0;
    cursor: pointer;
}

/* أزرار الراديو والتحديد */
.custom-control {
    position: relative;
    display: block;
    min-height: 1.5rem;
    padding-left: 1.5rem;
    margin-bottom: var(--spacing-sm);
}

.custom-control-input {
    position: absolute;
    left: 0;
    z-index: -1;
    width: 1rem;
    height: 1.25rem;
    opacity: 0;
}

.custom-control-label {
    position: relative;
    margin-bottom: 0;
    vertical-align: top;
    cursor: pointer;
}

.custom-control-label::before {
    position: absolute;
    top: 0.25rem;
    left: -1.5rem;
    display: block;
    width: 1rem;
    height: 1rem;
    pointer-events: none;
    content: "";
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
}

.custom-control-input:checked ~ .custom-control-label::before {
    color: var(--text-white);
    border-color: var(--primary-color);
    background-color: var(--primary-color);
}

.custom-control-input:checked ~ .custom-control-label::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='m6.564.75-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/%3e%3c/svg%3e");
}

.custom-control-label::after {
    position: absolute;
    top: 0.25rem;
    left: -1.5rem;
    display: block;
    width: 1rem;
    height: 1rem;
    content: "";
    background: no-repeat 50% / 50% 50%;
}

/* حقول التاريخ والوقت */
.datetime-input {
    display: flex;
    gap: var(--spacing-sm);
}

.datetime-input .form-control {
    flex: 1;
}

/* حقول البحث */
.search-input {
    position: relative;
}

.search-input::before {
    content: "\f002";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    position: absolute;
    left: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    pointer-events: none;
}

.search-input input {
    padding-left: calc(var(--spacing-xxl) + var(--spacing-md));
}

/* حقول التحميل */
.file-input {
    position: relative;
    display: inline-block;
    cursor: pointer;
    width: 100%;
}

.file-input input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-input-label {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--input-padding);
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius-md);
    background: var(--bg-secondary);
    color: var(--text-secondary);
    transition: var(--transition-normal);
    min-height: var(--input-height);
}

.file-input:hover .file-input-label {
    border-color: var(--primary-color);
    background: var(--primary-light);
    color: var(--primary-dark);
}

/* رسائل التحقق */
.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: var(--font-size-sm);
    color: var(--danger-color);
}

.valid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: var(--font-size-sm);
    color: var(--success-color);
}

.is-invalid {
    border-color: var(--danger-color);
}

.is-valid {
    border-color: var(--success-color);
}

/* نصوص المساعدة */
.form-text {
    display: block;
    margin-top: 0.25rem;
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

/* التصميم المتجاوب للنماذج */
@media (max-width: 768px) {
    .col-6,
    .col-4,
    .col-3,
    .col-2 {
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: var(--spacing-md);
    }
    
    .form-row {
        margin: 0;
    }
    
    .form-col {
        padding: 0;
    }
    
    .datetime-input {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .input-group {
        flex-direction: column;
    }
    
    .input-group-prepend,
    .input-group-append {
        width: 100%;
        margin-bottom: var(--spacing-sm);
    }
    
    .input-group-text {
        justify-content: center;
        border-radius: var(--border-radius-md);
    }
}
