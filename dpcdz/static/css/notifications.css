/* نظام الرسائل الأنيق */

/* الخلفية المظلمة للـ modal */
.notification-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.notification-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* محتوى الرسالة */
.notification-content {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    text-align: center;
    max-width: 450px;
    min-width: 300px;
    margin: 20px;
    transform: scale(0.7) translateY(-50px);
    transition: all 0.3s ease;
    direction: rtl;
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.notification-overlay.show .notification-content {
    transform: scale(1) translateY(0);
}

/* أيقونة الرسالة */
.notification-icon {
    font-size: 48px;
    margin-bottom: 20px;
    display: block;
}

.notification-icon.success {
    color: #28a745;
}

.notification-icon.error {
    color: #dc3545;
}

.notification-icon.warning {
    color: #ffc107;
}

.notification-icon.info {
    color: #17a2b8;
}

/* عنوان الرسالة */
.notification-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #333;
}

.notification-title.success {
    color: #28a745;
}

.notification-title.error {
    color: #dc3545;
}

.notification-title.warning {
    color: #856404;
}

.notification-title.info {
    color: #0c5460;
}

/* نص الرسالة */
.notification-message {
    font-size: 16px;
    line-height: 1.6;
    color: #666;
    margin-bottom: 25px;
}

/* أزرار الإجراءات */
.notification-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.notification-btn {
    padding: 10px 25px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 100px;
}

.notification-btn.primary {
    background: #007bff;
    color: white;
}

.notification-btn.primary:hover {
    background: #0056b3;
    transform: translateY(-2px);
}

.notification-btn.success {
    background: #28a745;
    color: white;
}

.notification-btn.success:hover {
    background: #1e7e34;
    transform: translateY(-2px);
}

.notification-btn.danger {
    background: #dc3545;
    color: white;
}

.notification-btn.danger:hover {
    background: #c82333;
    transform: translateY(-2px);
}

.notification-btn.secondary {
    background: #6c757d;
    color: white;
}

.notification-btn.secondary:hover {
    background: #545b62;
    transform: translateY(-2px);
}

/* رسائل التوست (Toast) */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9998;
    max-width: 350px;
}

.toast-notification {
    background: white;
    border-radius: 10px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    margin-bottom: 15px;
    padding: 20px;
    border-left: 5px solid #007bff;
    transform: translateX(400px);
    transition: all 0.4s ease;
    direction: rtl;
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.toast-notification.show {
    transform: translateX(0);
}

.toast-notification.success {
    border-left-color: #28a745;
}

.toast-notification.error {
    border-left-color: #dc3545;
}

.toast-notification.warning {
    border-left-color: #ffc107;
}

.toast-notification.info {
    border-left-color: #17a2b8;
}

.toast-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}

.toast-title {
    font-weight: bold;
    font-size: 16px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

.toast-close {
    background: none;
    border: none;
    font-size: 18px;
    color: #999;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toast-close:hover {
    color: #666;
}

.toast-message {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
}

/* تأثيرات الحركة */
@keyframes slideInRight {
    from {
        transform: translateX(400px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(400px);
        opacity: 0;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.8);
    }
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .notification-content {
        margin: 10px;
        padding: 20px;
        max-width: 90%;
    }
    
    .notification-actions {
        flex-direction: column;
    }
    
    .notification-btn {
        width: 100%;
    }
    
    .toast-container {
        right: 10px;
        left: 10px;
        max-width: none;
    }
    
    .toast-notification {
        margin-bottom: 10px;
    }
}

/* تحسينات إضافية */
.notification-content:focus {
    outline: none;
}

.notification-btn:focus {
    outline: 2px solid rgba(0, 123, 255, 0.5);
    outline-offset: 2px;
}

/* تأثير النبض للرسائل المهمة */
.notification-content.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    }
    50% {
        box-shadow: 0 15px 35px rgba(0, 123, 255, 0.4);
    }
    100% {
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    }
}
