/* Settings Page RTL Styles */
:root {
    --primary-color: #0d47a1;
    --primary-dark: #002171;
    --primary-light: #5472d3;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --white: #ffffff;
    --border-radius: 4px;
}

/* Basic RTL styles */
body {
    direction: rtl;
    text-align: right;
    font-family: 'Cairo', 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.settings-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    direction: rtl;
}

/* Header styles */
.settings-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px 0;
    border-bottom: 2px solid var(--primary-color);
}

.settings-title {
    font-size: 28px;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.settings-subtitle {
    font-size: 16px;
    color: var(--secondary-color);
}

/* Card styles */
.settings-card {
    background-color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    overflow: hidden;
    direction: rtl;
}

.settings-card-header {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 15px 20px;
    font-weight: bold;
    font-size: 18px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    text-align: right;
}

.settings-card-header i {
    margin-left: 10px;
    margin-right: 0;
    display: inline-block;
    vertical-align: middle;
}

.settings-card-body {
    padding: 20px;
}

/* Form styles */
.form-label {
    font-weight: bold;
    margin-bottom: 8px;
    display: block;
    text-align: right;
}

.form-control, .form-select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: var(--border-radius);
    margin-bottom: 15px;
    text-align: right;
    direction: rtl;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 71, 161, 0.25);
    outline: none;
}

/* Button styles */
.settings-btn {
    display: inline-block;
    font-weight: 400;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
    border: 1px solid transparent;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: var(--border-radius);
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    cursor: pointer;
}

.settings-btn i {
    margin-left: 8px;
    margin-right: 0;
    display: inline-block;
    vertical-align: middle;
}

.settings-btn-primary {
    color: var(--white);
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.settings-btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.settings-btn-success {
    color: var(--white);
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.settings-btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

.settings-btn-danger {
    color: var(--white);
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.settings-btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
}

.settings-btn-info {
    color: var(--white);
    background-color: var(--info-color);
    border-color: var(--info-color);
}

.settings-btn-info:hover {
    background-color: #138496;
    border-color: #117a8b;
}

/* Table styles */
.settings-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    direction: rtl;
}

.settings-table th {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 12px 15px;
    text-align: right;
    font-weight: bold;
}

.settings-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #dee2e6;
    vertical-align: middle;
    text-align: right;
}

.settings-table tr:hover {
    background-color: rgba(13, 71, 161, 0.05);
}

.table-responsive {
    direction: rtl;
    overflow-x: auto;
}

.settings-badge {
    display: inline-block;
    padding: 0.25em 0.4em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
    margin: 2px;
}

.settings-badge-primary {
    color: #004085;
    background-color: #cce5ff;
}

.settings-badge-secondary {
    color: #383d41;
    background-color: #e2e3e5;
}

/* Floating buttons */
.floating-buttons {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    z-index: 1000;
}

.floating-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    color: var(--white);
    font-size: 18px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.floating-btn:hover {
    transform: scale(1.1);
}

.floating-btn-primary {
    background-color: var(--primary-color);
}

.floating-btn-info {
    background-color: var(--info-color);
}

.floating-btn-home {
    background-color: var(--secondary-color);
}

.floating-btn-top {
    background-color: #6f42c1;
}

/* Shortcuts panel */
.shortcuts-panel {
    position: fixed;
    top: 100px;
    right: 20px;
    background-color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 15px;
    z-index: 1000;
    width: 200px;
    direction: rtl;
    text-align: right;
}

.shortcuts-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
    color: var(--primary-color);
    text-align: center;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 10px;
}

.shortcuts-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.shortcuts-item {
    margin-bottom: 8px;
}

.shortcuts-link {
    display: flex;
    align-items: center;
    padding: 8px 10px;
    border-radius: var(--border-radius);
    color: var(--dark-color);
    text-decoration: none;
    transition: all 0.2s ease;
}

.shortcuts-link:hover {
    background-color: var(--primary-light);
    color: var(--primary-color);
}

.shortcuts-link i {
    margin-left: 10px;
    margin-right: 0;
    width: 20px;
    text-align: center;
    display: inline-block;
    vertical-align: middle;
}

/* Responsive styles */
@media (max-width: 992px) {
    .shortcuts-panel {
        width: 180px;
    }
}

@media (max-width: 768px) {
    .shortcuts-panel {
        display: none;
    }

    .settings-card-header {
        font-size: 16px;
    }

    .settings-title {
        font-size: 24px;
    }
}

@media (max-width: 576px) {
    .settings-container {
        padding: 10px;
    }

    .settings-card-body {
        padding: 15px;
    }

    .floating-buttons {
        bottom: 10px;
        right: 10px;
    }

    .floating-btn {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .settings-table th,
    .settings-table td {
        padding: 8px;
        font-size: 14px;
    }
}
