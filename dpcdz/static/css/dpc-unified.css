/* ========================================
   نظام CSS الموحد لنظام DPC_DZ
   الإصدار: 1.0
   التاريخ: 16 يوليو 2025
   ======================================== */

/* استيراد الملفات الأساسية */
@import url('variables.css');
@import url('modals.css');
@import url('forms.css');
@import url('buttons.css');
@import url('tables.css');

/* إعدادات عامة */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    margin: 0;
    padding: 0;
    direction: rtl;
    text-align: right;
}

/* إعدادات الحاويات */
.container {
    width: 100%;
    padding-right: var(--spacing-md);
    padding-left: var(--spacing-md);
    margin-right: auto;
    margin-left: auto;
}

.container-fluid {
    width: 100%;
    padding-right: var(--spacing-md);
    padding-left: var(--spacing-md);
    margin-right: auto;
    margin-left: auto;
}

/* نظام الشبكة */
.row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -var(--spacing-md);
    margin-left: -var(--spacing-md);
}

.col {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
    padding-right: var(--spacing-md);
    padding-left: var(--spacing-md);
}

/* العناوين */
h1, h2, h3, h4, h5, h6 {
    margin-top: 0;
    margin-bottom: var(--spacing-lg);
    font-weight: 600;
    line-height: 1.2;
    color: var(--text-primary);
}

h1 { font-size: var(--font-size-xxl) * 1.5; }
h2 { font-size: var(--font-size-xxl) * 1.25; }
h3 { font-size: var(--font-size-xxl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

/* الفقرات */
p {
    margin-top: 0;
    margin-bottom: var(--spacing-md);
}

/* الروابط */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* القوائم */
ul, ol {
    margin-top: 0;
    margin-bottom: var(--spacing-md);
    padding-right: var(--spacing-xl);
}

li {
    margin-bottom: var(--spacing-xs);
}

/* الصور */
img {
    max-width: 100%;
    height: auto;
    border-radius: var(--border-radius-sm);
}

/* البطاقات */
.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: var(--bg-primary);
    background-clip: border-box;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--spacing-lg);
}

.card-header {
    padding: var(--spacing-lg) var(--spacing-xl);
    margin-bottom: 0;
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    border-top-left-radius: var(--border-radius-md);
    border-top-right-radius: var(--border-radius-md);
}

.card-body {
    flex: 1 1 auto;
    padding: var(--spacing-xl);
}

.card-footer {
    padding: var(--spacing-lg) var(--spacing-xl);
    background-color: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    border-bottom-left-radius: var(--border-radius-md);
    border-bottom-right-radius: var(--border-radius-md);
}

.card-title {
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-lg);
    font-weight: 600;
}

.card-text {
    margin-bottom: var(--spacing-md);
}

/* التنبيهات */
.alert {
    position: relative;
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    border: 1px solid transparent;
    border-radius: var(--border-radius-md);
}

.alert-primary {
    color: var(--primary-dark);
    background-color: var(--primary-light);
    border-color: var(--primary-color);
}

.alert-success {
    color: var(--success-dark);
    background-color: var(--success-light);
    border-color: var(--success-color);
}

.alert-danger {
    color: var(--danger-dark);
    background-color: var(--danger-light);
    border-color: var(--danger-color);
}

.alert-warning {
    color: var(--warning-dark);
    background-color: var(--warning-light);
    border-color: var(--warning-color);
}

.alert-info {
    color: var(--info-dark);
    background-color: var(--info-light);
    border-color: var(--info-color);
}

/* الشارات */
.badge {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
    font-weight: 600;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: var(--border-radius-xl);
}

.badge-primary { background-color: var(--primary-color); color: var(--text-white); }
.badge-secondary { background-color: var(--secondary-color); color: var(--text-white); }
.badge-success { background-color: var(--success-color); color: var(--text-white); }
.badge-danger { background-color: var(--danger-color); color: var(--text-white); }
.badge-warning { background-color: var(--warning-color); color: var(--text-primary); }
.badge-info { background-color: var(--info-color); color: var(--text-white); }

/* أدوات مساعدة */
.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }

.d-none { display: none !important; }
.d-block { display: block !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-flex { display: flex !important; }

.clickable { cursor: pointer !important; }

/* توحيد أسماء Classes - Backward Compatibility */
/* النماذج المنبثقة */
.clean-modal {
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    border: none;
    overflow: hidden;
}

.clean-header {
    background: var(--gradient-primary);
    color: var(--text-white);
    border-bottom: none;
    padding: var(--spacing-lg) var(--spacing-xxl);
    text-align: center;
}

.clean-body {
    padding: var(--spacing-xxl) !important;
    background: var(--bg-primary);
    max-height: 80vh;
    overflow-y: auto;
}

.clean-footer {
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-light);
    padding: var(--spacing-lg) var(--spacing-xxl);
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
}

/* الحقول الموحدة - Aliases للتوافق مع الأسماء القديمة */
.clean-input,
.clean-select,
.clean-textarea {
    padding: var(--input-padding);
    font-size: var(--font-size-base);
    min-height: var(--input-height);
    border: var(--input-border-width) solid var(--border-color);
    border-radius: var(--input-border-radius);
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: var(--transition-normal);
    width: 100%;
}

.clean-input:focus,
.clean-select:focus,
.clean-textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: none;
}

.clean-input:hover,
.clean-select:hover,
.clean-textarea:hover {
    border-color: var(--primary-light);
}

/* بطاقات التدخل */
.intervention-form-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-xl);
}

.justify-content-start { justify-content: flex-start !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }

.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-center { align-items: center !important; }
.align-items-baseline { align-items: baseline !important; }
.align-items-stretch { align-items: stretch !important; }

.flex-column { flex-direction: column !important; }
.flex-row { flex-direction: row !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }

/* المسافات */
.m-0 { margin: 0 !important; }
.m-1 { margin: var(--spacing-xs) !important; }
.m-2 { margin: var(--spacing-sm) !important; }
.m-3 { margin: var(--spacing-md) !important; }
.m-4 { margin: var(--spacing-lg) !important; }
.m-5 { margin: var(--spacing-xl) !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: var(--spacing-xs) !important; }
.p-2 { padding: var(--spacing-sm) !important; }
.p-3 { padding: var(--spacing-md) !important; }
.p-4 { padding: var(--spacing-lg) !important; }
.p-5 { padding: var(--spacing-xl) !important; }

.mb-1 { margin-bottom: var(--spacing-xs) !important; }
.mb-2 { margin-bottom: var(--spacing-sm) !important; }
.mb-3 { margin-bottom: var(--spacing-md) !important; }
.mb-4 { margin-bottom: var(--spacing-lg) !important; }
.mb-5 { margin-bottom: var(--spacing-xl) !important; }

.mt-1 { margin-top: var(--spacing-xs) !important; }
.mt-2 { margin-top: var(--spacing-sm) !important; }
.mt-3 { margin-top: var(--spacing-md) !important; }
.mt-4 { margin-top: var(--spacing-lg) !important; }
.mt-5 { margin-top: var(--spacing-xl) !important; }

/* التصميم المتجاوب العام */
@media (max-width: 768px) {
    .container {
        padding-right: var(--spacing-sm);
        padding-left: var(--spacing-sm);
    }
    
    .row {
        margin-right: -var(--spacing-sm);
        margin-left: -var(--spacing-sm);
    }
    
    .col {
        padding-right: var(--spacing-sm);
        padding-left: var(--spacing-sm);
    }
    
    h1 { font-size: var(--font-size-xxl); }
    h2 { font-size: var(--font-size-xl); }
    h3 { font-size: var(--font-size-lg); }
}

@media (max-width: 480px) {
    .container {
        padding-right: var(--spacing-xs);
        padding-left: var(--spacing-xs);
    }
    
    .card-body,
    .card-header,
    .card-footer {
        padding: var(--spacing-md);
    }
    
    .alert {
        padding: var(--spacing-md);
    }
}

/* إشعارات التزامن */
.sync-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    z-index: 9999;
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 300px;
    animation: slideInRight 0.3s ease-out;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.sync-notification.sync-success {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.sync-notification.sync-error {
    background: linear-gradient(135deg, #dc3545, #e74c3c);
}

.sync-notification i {
    font-size: 18px;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* مؤشر آخر تحديث */
#lastSyncTime {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
    text-align: center;
}

/* تحسينات لحالة الجاهزية */
.readiness-score {
    display: block;
    font-size: 11px;
    color: #6c757d;
    margin-top: 2px;
}

/* مؤشر التزامن النشط */
.sync-indicator {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: #007bff;
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    z-index: 1000;
}

.sync-indicator.syncing {
    background: #ffc107;
    color: #212529;
}

.sync-indicator i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
