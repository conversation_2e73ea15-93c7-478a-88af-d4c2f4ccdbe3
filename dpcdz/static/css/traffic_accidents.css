/* Traffic Accidents CSS */
body {
    font-family: Arial, sans-serif;
}

.form-container {
    padding: 50px;
    max-width: 1200px;
    margin: 0 auto;
    background-color: #fff;
}

.form-title {
    width: 100%;
    text-align: center;
    font-weight: bold;
    font-size: 24px;
    margin: 20px 0 30px;
}

.form-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
    justify-content: space-between;
}

.form-group {
    flex: 1;
    margin: 0 10px 15px;
    min-width: 200px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    text-align: right;
}

.form-control, .form-select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.section-title {
    width: 100%;
    text-align: center;
    margin: 30px 0 15px;
    font-size: 18px;
    font-weight: bold;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.count-field {
    text-align: center;
}

.count-field input {
    text-align: center;
}

/* Button styles */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    display: block;
    margin: 5px 0;
}

.btn-blue {
    background-color: #007bff;
    color: white;
}

.btn-green {
    background-color: #4CAF50;
    color: white;
}

.btn-gray {
    background-color: #777;
    color: white;
}

.btn-purple {
    background-color: #8e44ad;
    color: white;
}

/* Main buttons container */
.main-buttons-container {
    display: flex;
    justify-content: center;
    margin-top: 30px;
    margin-bottom: 20px;
}

/* Main buttons */
.main-buttons {
    display: flex;
    gap: 20px;
    width: 100%;
    max-width: 800px;
    direction: ltr; /* Force left-to-right layout */
}

.main-btn {
    padding: 15px 30px;
    border-radius: 50px;
    font-weight: bold;
    font-size: 18px;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    transition: all 0.3s;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    flex: 1;
    min-width: 200px;
    border: none;
}

.main-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.save-btn {
    background-color: #e63946;
    color: white;
}

.save-btn:hover {
    background-color: #d62c3b;
}

.home-btn {
    background-color: #2a9d46;
    color: white;
}

.home-btn:hover {
    background-color: #218838;
}

/* Floating buttons styles */
.floating-buttons {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    z-index: 1000;
}

.floating-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    transition: all 0.3s;
    text-decoration: none;
    font-size: 18px;
    border: none;
    cursor: pointer;
}

.floating-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
}

.tables-btn {
    background-color: #0d47a1;
}

.tables-btn:hover {
    background-color: #0a3880;
}

.home-btn {
    background-color: #28a745;
}

.home-btn:hover {
    background-color: #218838;
}

.save-btn {
    background-color: #dc3545;
}

.save-btn:hover {
    background-color: #c82333;
}

.top-btn {
    background-color: #0d6efd;
}

.top-btn:hover {
    background-color: #0a58ca;
}

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: #ffffff;
    margin: 15% auto;
    padding: 30px;
    border: none;
    width: 400px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from {opacity: 0; transform: translateY(-20px);}
    to {opacity: 1; transform: translateY(0);}
}

.modal-content h2 {
    margin-top: 0;
    color: #333;
    font-size: 22px;
}

.modal-content p {
    color: #555;
    font-size: 16px;
    margin: 20px 0;
}

.close {
    color: #aaa;
    float: left;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    left: 15px;
    top: 10px;
}

.close:hover {
    color: #333;
}

.modal-buttons {
    margin-top: 25px;
    display: flex;
    justify-content: center;
}

.modal-buttons .btn {
    min-width: 100px;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
}

.modal-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 8px rgba(0,0,0,0.2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
    }

    .form-group {
        width: 100%;
    }

    .floating-buttons {
        bottom: 10px;
        right: 10px;
        gap: 8px;
    }

    .floating-btn {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .main-buttons {
        flex-direction: column; /* Stack buttons vertically */
        gap: 15px;
        width: 90%;
    }

    .main-btn {
        width: 100%;
        padding: 12px 20px;
        font-size: 16px;
    }
}
