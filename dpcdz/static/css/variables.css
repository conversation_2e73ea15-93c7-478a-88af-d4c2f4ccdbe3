/* ========================================
   متغيرات CSS الموحدة لنظام DPC_DZ
   ======================================== */

:root {
    /* الألوان الأساسية */
    --primary-color: #007bff;
    --primary-dark: #0056b3;
    --primary-light: #66b3ff;
    
    --secondary-color: #6c757d;
    --secondary-dark: #495057;
    --secondary-light: #adb5bd;
    
    --success-color: #28a745;
    --success-dark: #1e7e34;
    --success-light: #71dd8a;
    
    --danger-color: #dc3545;
    --danger-dark: #c82333;
    --danger-light: #f1b0b7;
    
    --warning-color: #ffc107;
    --warning-dark: #e0a800;
    --warning-light: #fff3cd;
    
    --info-color: #17a2b8;
    --info-dark: #138496;
    --info-light: #bee5eb;
    
    /* ألوان الخلفية */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-dark: #343a40;
    --bg-light: #e9ecef;
    
    /* ألوان النص */
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-muted: #868e96;
    --text-white: #ffffff;
    
    /* ألوان الحدود */
    --border-color: #dee2e6;
    --border-light: #e9ecef;
    --border-dark: #adb5bd;
    
    /* أحجام النماذج المنبثقة */
    --modal-width-sm: 300px;
    --modal-width-md: 500px;
    --modal-width-lg: 800px;
    --modal-width-xl: 98vw;
    --modal-max-width: 98vw;
    --modal-margin: 5px auto;
    
    /* أحجام الخطوط */
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-base: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 20px;
    --font-size-xxl: 24px;
    
    /* المسافات */
    --spacing-xs: 5px;
    --spacing-sm: 10px;
    --spacing-md: 15px;
    --spacing-lg: 20px;
    --spacing-xl: 30px;
    --spacing-xxl: 40px;
    
    /* أحجام الحقول */
    --input-height: 50px;
    --input-padding: 18px 22px;
    --input-border-radius: 8px;
    --input-border-width: 1px;
    
    /* الظلال */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 10px 30px rgba(0, 0, 0, 0.3);
    
    /* الانتقالات */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* نصف القطر للحدود */
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
    --border-radius-round: 50%;
    
    /* ألوان الحالات */
    --status-present: #28a745;
    --status-absent: #dc3545;
    --status-on-mission: #ffc107;
    --status-ready: #28a745;
    --status-not-ready: #dc3545;
    --status-partial: #ffc107;
    
    /* ألوان التدرج */
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    --gradient-success: linear-gradient(135deg, var(--success-color), var(--success-dark));
    --gradient-danger: linear-gradient(135deg, var(--danger-color), var(--danger-dark));
    --gradient-warning: linear-gradient(135deg, var(--warning-color), var(--warning-dark));
    --gradient-info: linear-gradient(135deg, var(--info-color), var(--info-dark));
    
    /* Z-index للطبقات */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* تخصيصات للشاشات المختلفة */
@media (max-width: 576px) {
    :root {
        --modal-width-xl: 95vw;
        --modal-max-width: 95vw;
        --input-padding: 12px 16px;
        --font-size-base: 14px;
        --spacing-xxl: 20px;
    }
}

@media (min-width: 1200px) {
    :root {
        --modal-width-xl: 98vw;
        --modal-max-width: 98vw;
    }
}

@media (min-width: 1600px) {
    :root {
        --modal-width-xl: 97vw;
        --modal-max-width: 97vw;
    }
}

@media (min-width: 1920px) {
    :root {
        --modal-width-xl: 98vw;
        --modal-max-width: 98vw;
    }
}
