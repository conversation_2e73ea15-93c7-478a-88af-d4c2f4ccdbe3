/* Settings Page Styles - Complete Redesign */
:root {
    --primary-color: #0d47a1;
    --primary-light: #5472d3;
    --primary-dark: #002171;
    --secondary-color: #f50057;
    --secondary-light: #ff5983;
    --secondary-dark: #bb002f;
    --success-color: #2e7d32;
    --danger-color: #c62828;
    --warning-color: #f9a825;
    --info-color: #0288d1;
    --light-color: #f5f5f5;
    --dark-color: #212121;
    --gray-color: #757575;
    --white: #ffffff;
    --border-radius: 8px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Global RTL Styles */
body {
    direction: rtl;
    text-align: right;
    font-family: 'Cairo', 'Tajawal', sans-serif;
    background-color: #f5f5f5;
    color: var(--dark-color);
    margin: 0;
    padding: 0;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    color: var(--white);
    padding: 15px 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    margin-bottom: 30px;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header h1 {
    font-size: 24px;
    font-weight: bold;
    margin: 0;
    color: var(--white);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.header img {
    height: 60px;
    transition: var(--transition);
}

.header img:hover {
    transform: scale(1.05);
}

/* Footer Styles */
.footer {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    color: var(--white);
    padding: 20px 0;
    text-align: center;
    font-size: 14px;
    margin-top: 50px;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

/* Settings Container */
.settings-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    direction: rtl;
}

/* Settings Header */
.settings-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px 0;
    border-bottom: 2px solid var(--primary-color);
    position: relative;
}

.settings-header::after {
    content: '';
    position: absolute;
    bottom: -2px;
    right: 50%;
    transform: translateX(50%);
    width: 100px;
    height: 4px;
    background-color: var(--secondary-color);
    border-radius: 2px;
}

.settings-title {
    font-size: 28px;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 10px;
    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.1);
}

.settings-subtitle {
    font-size: 16px;
    color: var(--gray-color);
}

/* Card Styles */
.settings-card {
    background-color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 30px;
    overflow: hidden;
    transition: var(--transition);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.settings-card:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.settings-card-header {
    background: linear-gradient(to left, var(--primary-color), var(--primary-dark));
    color: var(--white);
    padding: 15px 20px;
    font-weight: bold;
    font-size: 18px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
}

.settings-card-header i {
    margin-left: 10px;
    font-size: 20px;
}

.settings-card-body {
    padding: 25px;
}

/* Form Styles */
.form-label {
    font-weight: bold;
    margin-bottom: 8px;
    display: block;
    color: var(--dark-color);
}

.form-control, .form-select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #e0e0e0;
    border-radius: var(--border-radius);
    margin-bottom: 15px;
    text-align: right;
    direction: rtl;
    font-size: 14px;
    transition: var(--transition);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(13, 71, 161, 0.25);
    outline: none;
}

/* Button Styles */
.settings-btn {
    display: inline-block;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
    border: none;
    padding: 10px 20px;
    font-size: 14px;
    line-height: 1.5;
    border-radius: var(--border-radius);
    transition: var(--transition);
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.settings-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.settings-btn:active {
    transform: translateY(0);
}

.settings-btn i {
    margin-left: 8px;
}

.btn-sm {
    padding: 6px 10px;
    font-size: 12px;
}

.settings-btn-primary {
    background: linear-gradient(to left, var(--primary-color), var(--primary-dark));
    color: var(--white);
}

.settings-btn-success {
    background: linear-gradient(to left, var(--success-color), #1b5e20);
    color: var(--white);
}

.settings-btn-danger {
    background: linear-gradient(to left, var(--danger-color), #b71c1c);
    color: var(--white);
}

.settings-btn-info {
    background: linear-gradient(to left, var(--info-color), #01579b);
    color: var(--white);
}

/* Table Styles */
.table-responsive {
    overflow-x: auto;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-top: 20px;
}

.settings-table {
    width: 100%;
    border-collapse: collapse;
    direction: rtl;
    background-color: var(--white);
}

.settings-table th {
    background: linear-gradient(to left, var(--primary-color), var(--primary-dark));
    color: var(--white);
    padding: 15px;
    text-align: right;
    font-weight: bold;
    position: sticky;
    top: 0;
}

.settings-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #e0e0e0;
    vertical-align: middle;
}

.settings-table tr:last-child td {
    border-bottom: none;
}

.settings-table tr:hover {
    background-color: rgba(13, 71, 161, 0.05);
}

/* Badge Styles */
.settings-badge {
    display: inline-block;
    padding: 5px 10px;
    font-size: 12px;
    font-weight: 600;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 20px;
    margin: 2px;
}

.settings-badge-primary {
    background-color: rgba(13, 71, 161, 0.1);
    color: var(--primary-color);
    border: 1px solid rgba(13, 71, 161, 0.2);
}

.settings-badge-secondary {
    background-color: rgba(245, 0, 87, 0.1);
    color: var(--secondary-color);
    border: 1px solid rgba(245, 0, 87, 0.2);
}

/* Floating Action Buttons */
.floating-buttons {
    position: fixed;
    bottom: 30px;
    right: 30px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    z-index: 1000;
}

.floating-btn {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    color: var(--white);
    font-size: 20px;
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.floating-btn:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.floating-btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

.floating-btn-info {
    background: linear-gradient(135deg, var(--info-color), #01579b);
}

.floating-btn-home {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
}

.floating-btn-top {
    background: linear-gradient(135deg, #6a1b9a, #4a148c);
}

/* Shortcuts Panel */
.shortcuts-panel {
    position: fixed;
    top: 120px;
    right: 30px;
    background-color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
    z-index: 1000;
    width: 220px;
    direction: rtl;
    text-align: right;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.shortcuts-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
    color: var(--primary-color);
    text-align: center;
    border-bottom: 2px solid var(--primary-light);
    padding-bottom: 10px;
}

.shortcuts-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.shortcuts-item {
    margin-bottom: 10px;
}

.shortcuts-link {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    border-radius: var(--border-radius);
    color: var(--dark-color);
    text-decoration: none;
    transition: var(--transition);
    background-color: rgba(0, 0, 0, 0.02);
}

.shortcuts-link:hover {
    background-color: var(--primary-light);
    color: var(--white);
    transform: translateX(-5px);
}

.shortcuts-link i {
    margin-left: 10px;
    width: 20px;
    text-align: center;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .shortcuts-panel {
        width: 180px;
        right: 20px;
    }

    .floating-buttons {
        right: 20px;
    }
}

@media (max-width: 768px) {
    .shortcuts-panel {
        display: none;
    }

    .settings-card-header {
        font-size: 16px;
    }

    .settings-title {
        font-size: 24px;
    }

    .header h1 {
        font-size: 20px;
    }
}

@media (max-width: 576px) {
    .settings-container {
        padding: 10px;
    }

    .settings-card-body {
        padding: 15px;
    }

    .floating-buttons {
        bottom: 20px;
        right: 15px;
    }

    .floating-btn {
        width: 48px;
        height: 48px;
        font-size: 18px;
    }

    .settings-table th,
    .settings-table td {
        padding: 10px;
        font-size: 14px;
    }

    .form-control, .form-select {
        padding: 10px;
    }
}
