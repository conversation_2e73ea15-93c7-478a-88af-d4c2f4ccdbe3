/**
 * أنماط CSS للنظام الجديد للتدخلات اليومية
 * نظام مبسط وواضح لتجنب التعقيدات
 */

/* الحاوية الرئيسية */
.container-fluid {
    padding: 20px;
}

/* رأس الصفحة */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}

/* حاوية النماذج */
.forms-container {
    display: none;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin: 20px 0;
    overflow: hidden;
}

.forms-container.show {
    display: block;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* رأ<PERSON> النموذج */
#form-header-top {
    display: none;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    position: relative;
}

#form-header-top.show {
    display: block;
}

#form-header-top h3 {
    margin: 0;
    font-size: 1.5rem;
}

#form-header-top p {
    margin: 5px 0 0 0;
    opacity: 0.9;
}

.close-form {
    position: absolute;
    top: 15px;
    right: 20px;
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    transition: opacity 0.2s;
}

.close-form:hover {
    opacity: 0.7;
}

/* أقسام النماذج */
.form-section {
    display: none;
    padding: 20px;
}

.form-section.active {
    display: block;
}

/* أقسام التفاصيل */
.detail-section {
    display: none;
    margin-top: 15px;
    padding: 15px;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

.detail-section.show {
    display: block;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.detail-section h5 {
    color: #495057;
    margin-bottom: 15px;
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
}

/* حقول النماذج */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
}

.form-control {
    border-radius: 5px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* حاوية الوسائل */
#vehicles-container {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 10px;
    background-color: #f8f9fa;
}

.form-check {
    margin-bottom: 8px;
}

.form-check-label {
    font-weight: normal;
    cursor: pointer;
}

/* الأزرار */
.btn {
    border-radius: 5px;
    font-weight: 600;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-group-actions {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* شارات الحالة */
.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-initial {
    background-color: #ffc107;
    color: #000;
}

.status-reconnaissance {
    background-color: #17a2b8;
    color: #fff;
}

.status-intervention {
    background-color: #28a745;
    color: #fff;
}

.status-completed {
    background-color: #6c757d;
    color: #fff;
}

/* الجدول */
.table {
    margin-bottom: 0;
}

.table th {
    background-color: #343a40;
    color: white;
    border: none;
    font-weight: 600;
    text-align: center;
}

.table td {
    vertical-align: middle;
    text-align: center;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

/* البطاقات */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    border-radius: 10px 10px 0 0 !important;
}

.card-header h5 {
    margin: 0;
    color: #495057;
}

/* التجاوب */
@media (max-width: 768px) {
    .container-fluid {
        padding: 10px;
    }
    
    .forms-container {
        margin: 10px 0;
    }
    
    .form-section {
        padding: 15px;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        margin-bottom: 5px;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* رسائل التنبيه */
.alert {
    border-radius: 5px;
    border: none;
    font-weight: 500;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* تحسينات إضافية */
.fade-in {
    animation: fadeIn 0.3s ease-out;
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسين مظهر الـ scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
