/**
 * تنسيقات مشتركة لجميع الجداول في التطبيق
 * Common styles for all tables in the application
 */

.table-container {
    max-width: 100%;
    margin: 0 auto;
    padding: 20px;
}

.table-wrapper {
    overflow-x: auto;
    margin-top: 20px;
    max-height: 70vh;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: relative;
}

/* إضافة تلميح للمستخدم بإمكانية التمرير */
.table-wrapper::after {
    content: "← قم بالتمرير للمزيد →";
    position: absolute;
    bottom: 10px;
    right: 50%;
    transform: translateX(50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 12px;
    opacity: 0.8;
    pointer-events: none;
    animation: fadeOut 3s forwards 3s;
}

@keyframes fadeOut {
    from { opacity: 0.8; }
    to { opacity: 0; }
}

h2.page-title {
    text-align: center;
    margin-bottom: 30px;
    color: #0d47a1;
    border-bottom: 2px solid #0d47a1;
    padding-bottom: 10px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 80px;
    background-color: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
    font-size: 14px;
}

.data-table th,
.data-table td {
    padding: 8px;
    text-align: center;
    border: 1px solid #ddd;
    white-space: nowrap;
}

/* تنسيق خاص لعمود التاريخ */
.data-table th:first-child,
.data-table td:first-child {
    min-width: 150px; /* عرض أكبر لعمود التاريخ */
    font-weight: bold; /* خط أكثر سمكًا */
    font-size: 15px; /* حجم خط أكبر */
    padding: 10px; /* تباعد داخلي أكبر */
    background-color: #f8f9fa; /* خلفية مميزة */
}

.data-table th {
    background-color: #f2f2f2;
    position: sticky;
    top: 0;
    z-index: 10;
    font-weight: bold;
    color: #333;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* تثبيت عمود الإجراءات على اليسار */
.data-table th:last-child,
.data-table td:last-child {
    position: sticky;
    left: 0;
    background-color: #f8f9fa;
    z-index: 5;
    box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
    min-width: 80px;
    text-align: center;
}

/* تغيير خلفية خلية الإجراءات عند التحويم */
.data-table tr:hover td:last-child {
    background-color: #e9f5ff;
}

/* تغيير خلفية خلية الإجراءات للصفوف الزوجية */
.data-table tr:nth-child(even) td:last-child {
    background-color: #f9f9f9;
}

/* تنسيق خاص لخلية الإجراءات */
.actions-cell {
    padding: 5px !important;
    white-space: nowrap;
    width: 80px;
}

/* تنسيق صف الفلاتر */
.filter-row th {
    padding: 5px;
    background-color: #f9f9f9;
}

.filter-select {
    width: 100%;
    padding: 5px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
    font-size: 12px;
    text-align: center;
}

.date-filter {
    display: flex;
    gap: 5px;
}

.date-filter .filter-select {
    width: 50%;
}

.reset-filters-btn {
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 5px 10px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    width: 100%;
}

.reset-filters-btn:hover {
    background-color: #e9ecef;
    border-color: #ced4da;
}

.data-table tr:hover {
    background-color: #f5f5f5;
}

.data-table tr:last-child td {
    border-bottom: none;
}

/* تنسيق للأزرار العائمة */
.table-actions {
    display: flex;
    justify-content: center;
    gap: 10px;
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 100;
}

.navigation-buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
    position: fixed;
    bottom: 20px;
    left: 20px;
    z-index: 100;
}

/* تنسيق للأجهزة المحمولة */
@media (max-width: 768px) {
    .data-table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }

    .navigation-buttons {
        left: 10px;
        bottom: 70px;
    }

    .table-actions {
        right: 10px;
        bottom: 10px;
        gap: 5px;
    }
}
