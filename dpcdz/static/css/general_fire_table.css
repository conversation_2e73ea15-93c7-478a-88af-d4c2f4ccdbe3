/* General Fire Table CSS */
.fire-details {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    padding: 30px;
    margin-top: 30px;
}

.fire-icon {
    font-size: 4rem;
    color: #e63946;
    margin-bottom: 20px;
    text-align: center;
}

.fire-title {
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 20px;
    text-align: center;
    color: #1d3557;
}

.fire-content {
    font-size: 1.1rem;
    line-height: 1.6;
}

.fire-form {
    margin-top: 30px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.form-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
    justify-content: space-between;
}

.form-group {
    margin-bottom: 15px;
    flex: 1;
    min-width: 200px;
    margin-left: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #1d3557;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: 5px;
    font-size: 1rem;
}

.form-control:focus {
    border-color: #1d3557;
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(29, 53, 87, 0.25);
}

.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 5px;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.hidden {
    display: none;
}

select.form-control {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%231d3557' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    padding-right: 2rem;
}

/* Main buttons container */
.main-buttons-container {
    display: flex;
    justify-content: center;
    margin-top: 30px;
    margin-bottom: 20px;
}

/* Main buttons */
.main-buttons {
    display: flex;
    gap: 20px;
    width: 100%;
    max-width: 800px;
    direction: ltr; /* Force left-to-right layout */
}

.main-btn {
    padding: 15px 30px;
    border-radius: 50px;
    font-weight: bold;
    font-size: 18px;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    transition: all 0.3s;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    flex: 1;
    min-width: 200px;
    border: none;
}

.main-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.save-btn {
    background-color: #e63946;
    color: white;
}

.save-btn:hover {
    background-color: #d62c3b;
}

.home-btn {
    background-color: #2a9d46;
    color: white;
}

.home-btn:hover {
    background-color: #218838;
}

.back-btn {
    background-color: #1d3557;
    color: white;
}

.back-btn:hover {
    background-color: #457b9d;
}

/* Floating buttons styles */
.floating-buttons {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    z-index: 1000;
}

.floating-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    transition: all 0.3s;
    text-decoration: none;
    font-size: 18px;
    border: none;
    cursor: pointer;
}

.floating-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
}

.tables-btn {
    background-color: #0d47a1;
}

.tables-btn:hover {
    background-color: #0a3880;
}

.home-btn {
    background-color: #28a745;
}

.home-btn:hover {
    background-color: #218838;
}

.save-btn {
    background-color: #dc3545;
}

.save-btn:hover {
    background-color: #c82333;
}

.top-btn {
    background-color: #0d6efd;
}

.top-btn:hover {
    background-color: #0a58ca;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
    }

    .form-group {
        width: 100%;
    }

    .floating-buttons {
        bottom: 10px;
        right: 10px;
        gap: 8px;
    }

    .floating-btn {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .main-buttons {
        flex-direction: column; /* Stack buttons vertically */
        gap: 15px;
        width: 90%;
    }

    .main-btn {
        width: 100%;
        padding: 12px 20px;
        font-size: 16px;
    }
}
