/* 🎨 تصميم عصري متطور لنظام إدارة التدخلات */

/* متغيرات CSS للألوان والتأثيرات */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
    --shadow-heavy: 0 15px 35px rgba(31, 38, 135, 0.5);
    --border-radius: 20px;
    --transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* تصميم العنوان المتطور مع Glass Morphism */
.header-section-enhanced {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%) !important;
    color: white !important;
    padding: 60px 0 !important;
    margin-bottom: 50px !important;
    position: relative !important;
    overflow: hidden !important;
    min-height: 300px !important;
    display: flex !important;
    align-items: center !important;
}

.header-background {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%) !important;
    animation: backgroundFloat 15s ease-in-out infinite !important;
}

@keyframes backgroundFloat {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.1) rotate(5deg); }
}

.header-pattern {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 2px, transparent 2px),
        radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 2px, transparent 2px) !important;
    background-size: 60px 60px !important;
    animation: patternMove 25s linear infinite !important;
}

@keyframes patternMove {
    0% { transform: translate(0, 0) rotate(0deg); }
    100% { transform: translate(60px, 60px) rotate(360deg); }
}

.header-content {
    max-width: 1400px !important;
    margin: 0 auto !important;
    padding: 0 30px !important;
    display: flex !important;
    align-items: center !important;
    gap: 40px !important;
    position: relative !important;
    z-index: 2 !important;
}

.header-icon-wrapper {
    flex-shrink: 0 !important;
    position: relative !important;
}

.header-icon {
    width: 120px !important;
    height: 120px !important;
    background: var(--glass-bg) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    backdrop-filter: blur(20px) !important;
    border: 2px solid var(--glass-border) !important;
    box-shadow: var(--shadow-light) !important;
    position: relative !important;
    overflow: hidden !important;
    animation: iconFloat 6s ease-in-out infinite !important;
}

@keyframes iconFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(5deg); }
}

.header-icon::before {
    content: '' !important;
    position: absolute !important;
    top: -50% !important;
    left: -50% !important;
    width: 200% !important;
    height: 200% !important;
    background: conic-gradient(from 0deg, transparent, rgba(255,255,255,0.1), transparent) !important;
    animation: iconSpin 8s linear infinite !important;
}

@keyframes iconSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.header-icon i {
    font-size: 3.5rem !important;
    color: white !important;
    position: relative !important;
    z-index: 1 !important;
    text-shadow: 0 0 20px rgba(255,255,255,0.5) !important;
    animation: iconPulse 3s ease-in-out infinite !important;
}

@keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.header-text h1 {
    font-size: 3.5rem !important;
    font-weight: 900 !important;
    margin: 0 0 15px 0 !important;
    background: linear-gradient(45deg, #fff, #f0f8ff, #fff) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
    animation: textShimmer 4s ease-in-out infinite !important;
}

@keyframes textShimmer {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.header-text p {
    font-size: 1.3rem !important;
    margin: 0 0 25px 0 !important;
    opacity: 0.95 !important;
    line-height: 1.7 !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

.header-stats {
    display: flex !important;
    gap: 20px !important;
    flex-wrap: wrap !important;
}

.stat-badge {
    background: var(--glass-bg) !important;
    padding: 12px 20px !important;
    border-radius: 30px !important;
    font-size: 1rem !important;
    font-weight: 700 !important;
    backdrop-filter: blur(15px) !important;
    border: 1px solid var(--glass-border) !important;
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
    transition: var(--transition) !important;
    box-shadow: var(--shadow-light) !important;
    animation: badgeFloat 5s ease-in-out infinite !important;
}

@keyframes badgeFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

.stat-badge:hover {
    transform: translateY(-5px) scale(1.05) !important;
    box-shadow: var(--shadow-heavy) !important;
    background: rgba(255, 255, 255, 0.2) !important;
}

.stat-badge i {
    font-size: 1.1rem !important;
    animation: iconRotate 3s ease-in-out infinite !important;
}

@keyframes iconRotate {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(10deg); }
}

/* تصميم الأزرار الكبيرة المتطورة مع Glass Morphism */
.main-actions-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)) !important;
    gap: 40px !important;
    margin: 60px 0 !important;
    padding: 0 30px !important;
    perspective: 1000px !important;
}

.btn-stage {
    background: var(--glass-bg) !important;
    backdrop-filter: blur(20px) !important;
    border: 2px solid var(--glass-border) !important;
    border-radius: var(--border-radius) !important;
    padding: 40px 30px !important;
    text-decoration: none !important;
    color: inherit !important;
    display: flex !important;
    align-items: center !important;
    gap: 30px !important;
    box-shadow: var(--shadow-light) !important;
    transition: var(--transition) !important;
    position: relative !important;
    overflow: hidden !important;
    min-height: 220px !important;
    transform-style: preserve-3d !important;
}

.btn-stage:hover {
    transform: translateY(-15px) rotateX(5deg) scale(1.03) !important;
    box-shadow: var(--shadow-heavy) !important;
    text-decoration: none !important;
    color: inherit !important;
    border-color: rgba(255, 255, 255, 0.4) !important;
}

.btn-stage::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: -100% !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent) !important;
    transition: left 0.6s ease !important;
}

.btn-stage:hover::before {
    left: 100% !important;
}

.btn-stage::after {
    content: '' !important;
    position: absolute !important;
    top: -50% !important;
    right: -50% !important;
    width: 200% !important;
    height: 200% !important;
    background: conic-gradient(from 0deg, transparent, var(--stage-color-alpha), transparent) !important;
    opacity: 0.1 !important;
    animation: stageRotate 10s linear infinite !important;
}

@keyframes stageRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ألوان المراحل المتطورة */
.btn-initial-reports {
    --stage-color: #667eea !important;
    --stage-color-alpha: rgba(102, 126, 234, 0.3) !important;
    --stage-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.btn-reconnaissance {
    --stage-color: #f093fb !important;
    --stage-color-alpha: rgba(240, 147, 251, 0.3) !important;
    --stage-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
}

.btn-completed {
    --stage-color: #4facfe !important;
    --stage-color-alpha: rgba(79, 172, 254, 0.3) !important;
    --stage-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
}

.btn-icon {
    flex-shrink: 0 !important;
    width: 100px !important;
    height: 100px !important;
    background: var(--stage-gradient) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2) !important;
    position: relative !important;
    overflow: hidden !important;
    animation: iconBounce 4s ease-in-out infinite !important;
}

@keyframes iconBounce {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.1) rotate(5deg); }
}

.btn-icon::before {
    content: '' !important;
    position: absolute !important;
    top: -50% !important;
    left: -50% !important;
    width: 200% !important;
    height: 200% !important;
    background: conic-gradient(from 0deg, transparent, rgba(255,255,255,0.3), transparent) !important;
    animation: iconSpinSlow 6s linear infinite !important;
}

@keyframes iconSpinSlow {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.btn-icon i {
    font-size: 3rem !important;
    color: white !important;
    position: relative !important;
    z-index: 1 !important;
    text-shadow: 0 0 20px rgba(255,255,255,0.5) !important;
    animation: iconGlow 3s ease-in-out infinite !important;
}

@keyframes iconGlow {
    0%, 100% { text-shadow: 0 0 20px rgba(255,255,255,0.5); }
    50% { text-shadow: 0 0 30px rgba(255,255,255,0.8), 0 0 40px rgba(255,255,255,0.6); }
}

.btn-content h3 {
    font-size: 2.2rem !important;
    font-weight: 900 !important;
    margin: 0 0 12px 0 !important;
    background: var(--stage-gradient) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.btn-content p {
    font-size: 1.1rem !important;
    color: #6c757d !important;
    margin: 0 0 20px 0 !important;
    line-height: 1.6 !important;
    font-weight: 500 !important;
}

.btn-stats {
    background: var(--glass-bg) !important;
    backdrop-filter: blur(10px) !important;
    padding: 15px 20px !important;
    border-radius: 15px !important;
    border: 2px solid var(--stage-color-alpha) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
    transition: var(--transition) !important;
}

.btn-stats:hover {
    transform: scale(1.05) !important;
    border-color: var(--stage-color) !important;
}

.btn-stats .count {
    display: block !important;
    font-size: 2.5rem !important;
    font-weight: 900 !important;
    background: var(--stage-gradient) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    line-height: 1 !important;
    margin-bottom: 6px !important;
    animation: countPulse 2s ease-in-out infinite !important;
}

@keyframes countPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.btn-stats .label {
    font-size: 0.9rem !important;
    color: #495057 !important;
    font-weight: 700 !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
}

/* تصميم الإحصائيات السريعة المتطورة */
.stats-section {
    margin: 50px 0 !important;
    padding: 0 30px !important;
    position: relative !important;
}

.stats-section::before {
    content: '' !important;
    position: absolute !important;
    top: -20px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    width: 100px !important;
    height: 4px !important;
    background: var(--primary-gradient) !important;
    border-radius: 2px !important;
    animation: lineGlow 3s ease-in-out infinite !important;
}

@keyframes lineGlow {
    0%, 100% { box-shadow: 0 0 10px rgba(102, 126, 234, 0.5); }
    50% { box-shadow: 0 0 20px rgba(102, 126, 234, 0.8), 0 0 30px rgba(102, 126, 234, 0.6); }
}

.stats-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
    gap: 30px !important;
    perspective: 1000px !important;
}

.stat-card {
    background: var(--glass-bg) !important;
    backdrop-filter: blur(20px) !important;
    border-radius: var(--border-radius) !important;
    padding: 30px !important;
    display: flex !important;
    align-items: center !important;
    gap: 25px !important;
    box-shadow: var(--shadow-light) !important;
    transition: var(--transition) !important;
    border: 2px solid var(--glass-border) !important;
    position: relative !important;
    overflow: hidden !important;
    transform-style: preserve-3d !important;
}

.stat-card:hover {
    transform: translateY(-10px) rotateX(5deg) scale(1.02) !important;
    box-shadow: var(--shadow-heavy) !important;
    border-color: var(--stat-color-alpha) !important;
}

.stat-card::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: -100% !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(90deg, transparent, var(--stat-color-alpha), transparent) !important;
    transition: left 0.6s ease !important;
}

.stat-card:hover::before {
    left: 100% !important;
}

.stat-total {
    --stat-color: #667eea !important;
    --stat-color-alpha: rgba(102, 126, 234, 0.3) !important;
    --stat-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.stat-pending {
    --stat-color: #f093fb !important;
    --stat-color-alpha: rgba(240, 147, 251, 0.3) !important;
    --stat-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
}

.stat-completed {
    --stat-color: #4facfe !important;
    --stat-color-alpha: rgba(79, 172, 254, 0.3) !important;
    --stat-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
}

.stat-icon {
    width: 80px !important;
    height: 80px !important;
    background: var(--stat-gradient) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    flex-shrink: 0 !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2) !important;
    position: relative !important;
    overflow: hidden !important;
    animation: statIconFloat 5s ease-in-out infinite !important;
}

@keyframes statIconFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-8px) rotate(5deg); }
}

.stat-icon::before {
    content: '' !important;
    position: absolute !important;
    top: -50% !important;
    left: -50% !important;
    width: 200% !important;
    height: 200% !important;
    background: conic-gradient(from 0deg, transparent, rgba(255,255,255,0.3), transparent) !important;
    animation: statIconSpin 8s linear infinite !important;
}

@keyframes statIconSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.stat-icon i {
    font-size: 2.2rem !important;
    color: white !important;
    position: relative !important;
    z-index: 1 !important;
    text-shadow: 0 0 20px rgba(255,255,255,0.5) !important;
    animation: statIconPulse 3s ease-in-out infinite !important;
}

@keyframes statIconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

.stat-content h3 {
    font-size: 3rem !important;
    font-weight: 900 !important;
    margin: 0 !important;
    background: var(--stat-gradient) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    line-height: 1 !important;
    animation: statCountUp 2s ease-out !important;
}

@keyframes statCountUp {
    0% { transform: scale(0); opacity: 0; }
    100% { transform: scale(1); opacity: 1; }
}

.stat-content p {
    font-size: 1.1rem !important;
    color: #6c757d !important;
    margin: 8px 0 0 0 !important;
    font-weight: 700 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

/* الأزرار العائمة المتطورة */
.floating-buttons {
    position: fixed !important;
    bottom: 30px !important;
    right: 30px !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 15px !important;
    z-index: 1000 !important;
    animation: floatingButtonsEntrance 1s ease-out !important;
}

@keyframes floatingButtonsEntrance {
    0% { transform: translateX(100px); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
}

.floating-btn {
    width: 60px !important;
    height: 60px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: white !important;
    box-shadow: var(--shadow-light) !important;
    transition: var(--transition) !important;
    text-decoration: none !important;
    font-size: 20px !important;
    border: 2px solid rgba(255, 255, 255, 0.2) !important;
    backdrop-filter: blur(10px) !important;
    position: relative !important;
    overflow: hidden !important;
}

.floating-btn::before {
    content: '' !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    width: 0 !important;
    height: 0 !important;
    background: rgba(255, 255, 255, 0.3) !important;
    border-radius: 50% !important;
    transition: all 0.6s ease !important;
    transform: translate(-50%, -50%) !important;
}

.floating-btn:hover::before {
    width: 100% !important;
    height: 100% !important;
}

.floating-btn:hover {
    transform: scale(1.2) rotate(10deg) !important;
    box-shadow: var(--shadow-heavy) !important;
    color: white !important;
    text-decoration: none !important;
}

.floating-btn i {
    position: relative !important;
    z-index: 1 !important;
    animation: floatingIconBounce 2s ease-in-out infinite !important;
}

@keyframes floatingIconBounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-3px); }
}

.coordination-btn {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%) !important;
    animation-delay: 0.1s !important;
}
.interventions-btn {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%) !important;
    animation-delay: 0.2s !important;
}
.unified-btn {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
    animation-delay: 0.3s !important;
}
.assignment-btn {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    animation-delay: 0.4s !important;
}
.readiness-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    animation-delay: 0.5s !important;
}
.home-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    animation-delay: 0.6s !important;
}
.top-btn {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    animation-delay: 0.7s !important;
}

/* الإشعارات المتطورة مع Glass Morphism */
.notification {
    position: fixed !important;
    top: 30px !important;
    right: 30px !important;
    background: var(--glass-bg) !important;
    backdrop-filter: blur(20px) !important;
    border-radius: var(--border-radius) !important;
    box-shadow: var(--shadow-light) !important;
    padding: 20px 25px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    min-width: 350px !important;
    max-width: 450px !important;
    z-index: 1050 !important;
    animation: notificationSlideIn 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
    border: 2px solid var(--glass-border) !important;
    position: relative !important;
    overflow: hidden !important;
}

.notification::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: -100% !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent) !important;
    animation: notificationShimmer 2s ease-in-out infinite !important;
}

@keyframes notificationShimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

@keyframes notificationSlideIn {
    0% {
        transform: translateX(100%) scale(0.8);
        opacity: 0;
    }
    50% {
        transform: translateX(-10px) scale(1.05);
    }
    100% {
        transform: translateX(0) scale(1);
        opacity: 1;
    }
}

.notification-content {
    display: flex !important;
    align-items: center !important;
    gap: 15px !important;
    position: relative !important;
    z-index: 1 !important;
}

.notification-content i {
    font-size: 1.5rem !important;
    animation: notificationIconPulse 2s ease-in-out infinite !important;
}

@keyframes notificationIconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

.notification-close {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: #6c757d !important;
    cursor: pointer !important;
    padding: 8px !important;
    border-radius: 50% !important;
    transition: var(--transition) !important;
    position: relative !important;
    z-index: 1 !important;
    width: 35px !important;
    height: 35px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.notification-close:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    color: #495057 !important;
    transform: scale(1.1) rotate(90deg) !important;
}

.notification-info {
    border-right: 4px solid #4facfe !important;
    color: #0c5460 !important;
}

.notification-info i {
    color: #4facfe !important;
    text-shadow: 0 0 10px rgba(79, 172, 254, 0.5) !important;
}

.notification-success {
    border-right: 4px solid #43e97b !important;
    color: #155724 !important;
}

.notification-success i {
    color: #43e97b !important;
    text-shadow: 0 0 10px rgba(67, 233, 123, 0.5) !important;
}

.notification-warning {
    border-right: 4px solid #f093fb !important;
    color: #856404 !important;
}

.notification-warning i {
    color: #f093fb !important;
    text-shadow: 0 0 10px rgba(240, 147, 251, 0.5) !important;
}

.notification-error {
    border-right: 4px solid #fa709a !important;
    color: #721c24 !important;
}

.notification-error i {
    color: #fa709a !important;
    text-shadow: 0 0 10px rgba(250, 112, 154, 0.5) !important;
}

/* تأثيرات إضافية متقدمة */
.container-fluid {
    background:
        radial-gradient(circle at 10% 20%, rgba(102, 126, 234, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 90% 80%, rgba(240, 147, 251, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(79, 172, 254, 0.05) 0%, transparent 50%) !important;
    min-height: 100vh !important;
    animation: backgroundPulse 20s ease-in-out infinite !important;
}

@keyframes backgroundPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

/* تأثير التحميل */
.loading-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 9999 !important;
    animation: loadingFadeOut 2s ease-out forwards !important;
}

@keyframes loadingFadeOut {
    0% { opacity: 1; }
    80% { opacity: 1; }
    100% { opacity: 0; pointer-events: none; }
}

.loading-spinner {
    width: 80px !important;
    height: 80px !important;
    border: 4px solid rgba(255, 255, 255, 0.3) !important;
    border-top: 4px solid white !important;
    border-radius: 50% !important;
    animation: loadingSpin 1s linear infinite !important;
}

@keyframes loadingSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تأثيرات خاصة للتفاعل */
.btn-stage:active {
    transform: scale(0.95) !important;
}

.stat-card:active {
    transform: scale(0.98) !important;
}

.floating-btn:active {
    transform: scale(0.9) !important;
}

/* تأثير الضوء المتحرك */
@keyframes lightSweep {
    0% { transform: translateX(-100%) skewX(-15deg); }
    100% { transform: translateX(100%) skewX(-15deg); }
}

.btn-stage:hover::after {
    animation: lightSweep 1.5s ease-in-out !important;
}

/* تحسين الأداء */
* {
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
}

.btn-stage,
.stat-card,
.floating-btn {
    will-change: transform !important;
}

/* التصميم المتجاوب المحسن */
@media (max-width: 1400px) {
    .main-actions-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)) !important;
        gap: 35px !important;
    }
}

@media (max-width: 1200px) {
    .header-text h1 {
        font-size: 3rem !important;
    }

    .main-actions-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)) !important;
        gap: 30px !important;
    }

    .btn-stage {
        min-height: 200px !important;
        padding: 35px 25px !important;
    }

    .btn-icon {
        width: 90px !important;
        height: 90px !important;
    }

    .btn-icon i {
        font-size: 2.5rem !important;
    }

    .btn-content h3 {
        font-size: 2rem !important;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
    }
}

@media (max-width: 768px) {
    .header-section-enhanced {
        padding: 40px 0 !important;
        min-height: 250px !important;
    }

    .header-content {
        flex-direction: column !important;
        text-align: center !important;
        gap: 25px !important;
        padding: 0 20px !important;
    }

    .header-icon {
        width: 100px !important;
        height: 100px !important;
    }

    .header-icon i {
        font-size: 3rem !important;
    }

    .header-text h1 {
        font-size: 2.5rem !important;
    }

    .header-text p {
        font-size: 1.1rem !important;
    }

    .main-actions-grid {
        grid-template-columns: 1fr !important;
        gap: 25px !important;
        margin: 40px 0 !important;
        padding: 0 20px !important;
    }

    .btn-stage {
        flex-direction: column !important;
        text-align: center !important;
        gap: 25px !important;
        min-height: 250px !important;
        padding: 30px 25px !important;
    }

    .btn-icon {
        width: 80px !important;
        height: 80px !important;
    }

    .btn-icon i {
        font-size: 2.5rem !important;
    }

    .btn-content h3 {
        font-size: 1.8rem !important;
    }

    .stats-section {
        padding: 0 20px !important;
    }

    .stats-grid {
        grid-template-columns: 1fr !important;
        gap: 20px !important;
    }

    .stat-card {
        padding: 25px !important;
        flex-direction: column !important;
        text-align: center !important;
        gap: 20px !important;
    }

    .stat-icon {
        width: 70px !important;
        height: 70px !important;
    }

    .stat-icon i {
        font-size: 2rem !important;
    }

    .stat-content h3 {
        font-size: 2.5rem !important;
    }

    .floating-buttons {
        bottom: 20px !important;
        right: 20px !important;
        gap: 12px !important;
    }

    .floating-btn {
        width: 55px !important;
        height: 55px !important;
        font-size: 18px !important;
    }

    .notification {
        right: 20px !important;
        min-width: 300px !important;
        max-width: 350px !important;
    }
}

@media (max-width: 576px) {
    .header-section-enhanced {
        padding: 35px 0 !important;
        min-height: 220px !important;
    }

    .header-content {
        padding: 0 15px !important;
        gap: 20px !important;
    }

    .header-icon {
        width: 80px !important;
        height: 80px !important;
    }

    .header-icon i {
        font-size: 2.5rem !important;
    }

    .header-text h1 {
        font-size: 2rem !important;
    }

    .header-text p {
        font-size: 1rem !important;
    }

    .header-stats {
        justify-content: center !important;
        gap: 15px !important;
    }

    .stat-badge {
        font-size: 0.9rem !important;
        padding: 10px 15px !important;
    }

    .main-actions-grid {
        padding: 0 15px !important;
        gap: 20px !important;
    }

    .btn-stage {
        min-height: 220px !important;
        padding: 25px 20px !important;
    }

    .btn-icon {
        width: 70px !important;
        height: 70px !important;
    }

    .btn-icon i {
        font-size: 2rem !important;
    }

    .btn-content h3 {
        font-size: 1.6rem !important;
    }

    .btn-content p {
        font-size: 1rem !important;
    }

    .btn-stats .count {
        font-size: 2rem !important;
    }

    .stats-section {
        padding: 0 15px !important;
    }

    .stat-card {
        padding: 20px !important;
    }

    .stat-icon {
        width: 60px !important;
        height: 60px !important;
    }

    .stat-icon i {
        font-size: 1.8rem !important;
    }

    .stat-content h3 {
        font-size: 2rem !important;
    }

    .floating-btn {
        width: 50px !important;
        height: 50px !important;
        font-size: 16px !important;
    }

    .notification {
        right: 15px !important;
        min-width: 280px !important;
        max-width: 320px !important;
        padding: 15px 20px !important;
    }
}

/* رسوم متحركة إضافية للتفاعل */
@keyframes ripple {
    to {
        transform: scale(2);
        opacity: 0;
    }
}

@keyframes notificationSlideOut {
    to {
        transform: translateX(100%) scale(0.8);
        opacity: 0;
    }
}
