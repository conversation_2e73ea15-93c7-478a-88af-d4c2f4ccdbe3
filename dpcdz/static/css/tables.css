/* ========================================
   أنماط الجداول الموحدة
   ======================================== */

/* الجداول الأساسية */
.table {
    width: 100%;
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
    background-color: var(--bg-primary);
    border-collapse: collapse;
}

.table th,
.table td {
    padding: var(--spacing-md) var(--spacing-lg);
    vertical-align: middle;
    border-top: 1px solid var(--border-color);
    text-align: center;
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid var(--border-color);
    background: var(--gradient-primary);
    color: var(--text-white);
    font-weight: 600;
    font-size: var(--font-size-base);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table tbody + tbody {
    border-top: 2px solid var(--border-color);
}

/* جداول مخططة */
.table-striped tbody tr:nth-of-type(odd) {
    background-color: var(--bg-secondary);
}

/* جداول محاطة بحدود */
.table-bordered {
    border: 1px solid var(--border-color);
}

.table-bordered th,
.table-bordered td {
    border: 1px solid var(--border-color);
}

.table-bordered thead th,
.table-bordered thead td {
    border-bottom-width: 2px;
}

/* جداول بدون حدود */
.table-borderless th,
.table-borderless td,
.table-borderless thead th,
.table-borderless tbody + tbody {
    border: 0;
}

/* جداول مضغوطة */
.table-sm th,
.table-sm td {
    padding: var(--spacing-sm);
}

/* جداول تفاعلية */
.table-hover tbody tr:hover {
    background-color: var(--primary-light);
    color: var(--primary-dark);
    cursor: pointer;
    transition: var(--transition-fast);
}

/* ألوان الصفوف */
.table-primary {
    background-color: var(--primary-light);
    color: var(--primary-dark);
}

.table-secondary {
    background-color: var(--secondary-light);
    color: var(--secondary-dark);
}

.table-success {
    background-color: var(--success-light);
    color: var(--success-dark);
}

.table-danger {
    background-color: var(--danger-light);
    color: var(--danger-dark);
}

.table-warning {
    background-color: var(--warning-light);
    color: var(--warning-dark);
}

.table-info {
    background-color: var(--info-light);
    color: var(--info-dark);
}

/* حاوي الجدول المتجاوب */
.table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.table-responsive > .table {
    margin-bottom: 0;
}

/* أدوات التحكم في الجدول */
.table-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.table-search {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex: 1;
    max-width: 400px;
}

.table-actions {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

/* فلاتر الجدول */
.table-filters {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
    align-items: center;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.filter-label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-secondary);
}

.filter-select {
    min-width: 150px;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background: var(--bg-primary);
    font-size: var(--font-size-sm);
}

/* إحصائيات الجدول */
.table-stats {
    display: flex;
    justify-content: space-around;
    background: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.stat-item {
    text-align: center;
    flex: 1;
    min-width: 120px;
}

.stat-number {
    font-size: var(--font-size-xxl);
    font-weight: 700;
    color: var(--primary-color);
    display: block;
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
}

/* أزرار الإجراءات في الجدول */
.table-actions-cell {
    white-space: nowrap;
    width: 1%;
}

.action-buttons-group {
    display: flex;
    gap: var(--spacing-xs);
    justify-content: center;
}

.action-btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
    min-height: 32px;
    border-radius: var(--border-radius-sm);
}

/* شارات الحالة في الجدول */
.status-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-radius: var(--border-radius-xl);
    min-width: 80px;
}

.badge-present {
    background-color: var(--status-present);
    color: var(--text-white);
}

.badge-absent {
    background-color: var(--status-absent);
    color: var(--text-white);
}

.badge-on-mission {
    background-color: var(--status-on-mission);
    color: var(--text-primary);
}

.badge-ready {
    background-color: var(--status-ready);
    color: var(--text-white);
}

.badge-not-ready {
    background-color: var(--status-not-ready);
    color: var(--text-white);
}

.badge-partial {
    background-color: var(--status-partial);
    color: var(--text-primary);
}

/* ترقيم الصفحات */
.pagination {
    display: flex;
    justify-content: center;
    margin-top: var(--spacing-lg);
    gap: var(--spacing-xs);
}

.page-item {
    display: inline-block;
}

.page-link {
    display: block;
    padding: var(--spacing-sm) var(--spacing-md);
    margin-left: -1px;
    line-height: 1.25;
    color: var(--primary-color);
    text-decoration: none;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    transition: var(--transition-fast);
}

.page-link:hover {
    color: var(--primary-dark);
    background-color: var(--bg-light);
    border-color: var(--border-dark);
}

.page-item.active .page-link {
    color: var(--text-white);
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.page-item.disabled .page-link {
    color: var(--text-muted);
    background-color: var(--bg-primary);
    border-color: var(--border-color);
    cursor: not-allowed;
}

/* التصميم المتجاوب للجداول */
@media (max-width: 768px) {
    .table-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .table-search {
        max-width: 100%;
        margin-bottom: var(--spacing-sm);
    }
    
    .table-filters {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .filter-group {
        flex-direction: row;
        align-items: center;
        gap: var(--spacing-sm);
    }
    
    .filter-select {
        min-width: auto;
        flex: 1;
    }
    
    .table-stats {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .action-buttons-group {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
}

@media (max-width: 480px) {
    .table th,
    .table td {
        padding: var(--spacing-sm);
        font-size: var(--font-size-sm);
    }
    
    .table thead th {
        font-size: var(--font-size-xs);
    }
    
    .status-badge {
        font-size: 10px;
        padding: 2px var(--spacing-xs);
        min-width: 60px;
    }
    
    .action-btn-sm {
        padding: 2px var(--spacing-xs);
        font-size: 10px;
        min-height: 24px;
    }
}
