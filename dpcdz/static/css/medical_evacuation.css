/* Medical Evacuation Form Styles */
.form-container {
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.form-title {
    text-align: center;
    margin-bottom: 30px;
    color: #0056b3;
    font-size: 24px;
}

/* Custom date picker styles */
.date-picker-container {
    position: relative;
    width: 100%;
}

.date-picker-input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    font-family: inherit;
    background-color: #f8f9fa;
    cursor: pointer;
}

/* Flatpickr customization */
.flatpickr-calendar {
    direction: rtl;
    font-family: '<PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.2);
    border-radius: 8px;
}

.flatpickr-day {
    font-size: 14px;
}

.flatpickr-day.selected {
    background: #0056b3;
    border-color: #0056b3;
}

.flatpickr-day:hover {
    background: #e6f2ff;
}

.flatpickr-months .flatpickr-month {
    background-color: #0056b3;
    color: white;
}

.flatpickr-current-month {
    font-size: 16px;
    padding: 10px 0;
}

.flatpickr-weekday {
    font-weight: bold;
    color: #0056b3;
}

/* Alert messages */
.messages {
    margin-bottom: 20px;
}

.alert {
    padding: 15px;
    margin-bottom: 15px;
    border: 1px solid transparent;
    border-radius: 4px;
    position: relative;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeeba;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

.alert-dismissible {
    padding-left: 40px;
}

.btn-close {
    position: absolute;
    top: 0;
    left: 0;
    padding: 15px;
    color: inherit;
    background: transparent;
    border: 0;
    cursor: pointer;
    font-size: 20px;
    font-weight: bold;
    line-height: 1;
}

.form-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
    gap: 15px;
}

.form-group {
    flex: 1;
    min-width: 200px;
    margin-bottom: 15px;
}

.form-control, .form-select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    font-family: inherit;
}

.form-control:focus, .form-select:focus {
    border-color: #0056b3;
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 86, 179, 0.25);
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #333;
}

.radio-group {
    display: flex;
    gap: 20px;
}

.radio-option {
    display: flex;
    align-items: center;
    gap: 5px;
}

.count-field {
    flex: 1;
    min-width: 150px;
}

.count-field input {
    text-align: center;
    /* Remove spinner buttons from number inputs */
    -moz-appearance: textfield;
    /* Ensure proper input behavior across browsers */
    font-variant-numeric: tabular-nums;
    -webkit-font-feature-settings: "tnum";
    font-feature-settings: "tnum";
}

/* Remove spinner buttons from number inputs in WebKit browsers */
.count-field input::-webkit-outer-spin-button,
.count-field input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Ensure number inputs work properly in all browsers */
input[type="number"] {
    -webkit-appearance: none;
    -moz-appearance: textfield;
    appearance: none;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    font-size: 16px;
    font-family: inherit;
    width: 100%;
    box-sizing: border-box;
}

input[type="number"]:focus {
    border-color: #0056b3;
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 86, 179, 0.25);
}

/* Ensure number inputs display correctly in older browsers */
input[type="number"]:invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25);
}

/* Button styles */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.3s;
}

.btn-green {
    background-color: #28a745;
    color: white;
}

.btn-green:hover {
    background-color: #218838;
}

.btn-blue {
    background-color: #007bff;
    color: white;
}

.btn-blue:hover {
    background-color: #0069d9;
}

.btn-gray {
    background-color: #6c757d;
    color: white;
}

.btn-gray:hover {
    background-color: #5a6268;
}

/* Main buttons container */
.main-buttons-container {
    display: flex;
    justify-content: center;
    margin-top: 30px;
    margin-bottom: 20px;
}

/* Main buttons */
.main-buttons {
    display: flex;
    gap: 20px;
    width: 100%;
    max-width: 800px;
    direction: ltr; /* Force left-to-right layout */
}

.main-btn {
    padding: 15px 30px;
    border-radius: 50px;
    font-weight: bold;
    font-size: 18px;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    transition: all 0.3s;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    flex: 1;
    min-width: 200px;
    border: none;
}

.main-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.save-btn {
    background-color: #28a745;
    color: white;
}

.save-btn:hover {
    background-color: #218838;
}

.home-btn {
    background-color: #0d47a1;
    color: white;
}

.home-btn:hover {
    background-color: #0a3880;
}

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 400px;
    border-radius: 8px;
    text-align: center;
}

.close {
    color: #aaa;
    float: left;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: black;
    text-decoration: none;
}

.modal-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 20px;
}

/* Floating buttons styles */
.floating-buttons {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    z-index: 1000;
}

.floating-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    transition: all 0.3s;
    text-decoration: none;
    font-size: 18px;
    border: none;
    cursor: pointer;
}

.floating-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
}

.tables-btn {
    background-color: #0d47a1;
}

.tables-btn:hover {
    background-color: #0a3880;
}

.home-btn {
    background-color: #0d47a1;
}

.home-btn:hover {
    background-color: #0a3880;
}

.save-btn {
    background-color: #28a745;
}

.save-btn:hover {
    background-color: #218838;
}

.top-btn {
    background-color: #0d6efd;
}

.top-btn:hover {
    background-color: #0a58ca;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
    }

    .form-group {
        width: 100%;
    }

    .radio-group {
        flex-direction: column;
        gap: 10px;
    }

    .floating-buttons {
        bottom: 10px;
        right: 10px;
        gap: 8px;
    }

    .floating-btn {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .main-buttons {
        flex-direction: column; /* Stack buttons vertically */
        gap: 15px;
        width: 90%;
    }

    .main-btn {
        width: 100%;
        padding: 12px 20px;
        font-size: 16px;
    }
}
