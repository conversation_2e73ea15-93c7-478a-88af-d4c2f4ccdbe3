/* 🎨 تصميم عصري متطور لصفحة المراحل */

/* متغيرات CSS للألوان والتأثيرات */
:root {
    --stage-primary: #667eea;
    --stage-secondary: #764ba2;
    --stage-accent: #f093fb;
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
    --shadow-heavy: 0 15px 35px rgba(31, 38, 135, 0.5);
    --border-radius: 20px;
    --transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* تصميم العنوان المتطور مع Glass Morphism */
.header-section-enhanced {
    color: white !important;
    padding: 60px 0 !important;
    margin-bottom: 50px !important;
    position: relative !important;
    overflow: hidden !important;
    min-height: 300px !important;
    display: flex !important;
    align-items: center !important;
}

.header-background {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%) !important;
    animation: backgroundFloat 15s ease-in-out infinite !important;
}

@keyframes backgroundFloat {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.1) rotate(5deg); }
}

.header-pattern {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 2px, transparent 2px),
        radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 2px, transparent 2px) !important;
    background-size: 60px 60px !important;
    animation: patternMove 25s linear infinite !important;
}

@keyframes patternMove {
    0% { transform: translate(0, 0) rotate(0deg); }
    100% { transform: translate(60px, 60px) rotate(360deg); }
}

.header-content {
    max-width: 1400px !important;
    margin: 0 auto !important;
    padding: 0 30px !important;
    display: flex !important;
    align-items: center !important;
    gap: 40px !important;
    position: relative !important;
    z-index: 2 !important;
}

.header-icon-wrapper {
    flex-shrink: 0 !important;
    position: relative !important;
}

.header-icon {
    width: 120px !important;
    height: 120px !important;
    background: var(--glass-bg) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    backdrop-filter: blur(20px) !important;
    border: 2px solid var(--glass-border) !important;
    box-shadow: var(--shadow-light) !important;
    position: relative !important;
    overflow: hidden !important;
    animation: iconFloat 6s ease-in-out infinite !important;
}

@keyframes iconFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(5deg); }
}

.header-icon::before {
    content: '' !important;
    position: absolute !important;
    top: -50% !important;
    left: -50% !important;
    width: 200% !important;
    height: 200% !important;
    background: conic-gradient(from 0deg, transparent, rgba(255,255,255,0.1), transparent) !important;
    animation: iconSpin 8s linear infinite !important;
}

@keyframes iconSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.header-icon i {
    font-size: 3.5rem !important;
    color: white !important;
    position: relative !important;
    z-index: 1 !important;
    text-shadow: 0 0 20px rgba(255,255,255,0.5) !important;
    animation: iconPulse 3s ease-in-out infinite !important;
}

@keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.header-text h1 {
    font-size: 3.5rem !important;
    font-weight: 900 !important;
    margin: 0 0 15px 0 !important;
    background: linear-gradient(45deg, #fff, #f0f8ff, #fff) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
    animation: textShimmer 4s ease-in-out infinite !important;
}

@keyframes textShimmer {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.header-text p {
    font-size: 1.3rem !important;
    margin: 0 0 25px 0 !important;
    opacity: 0.95 !important;
    line-height: 1.7 !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

.header-stats {
    display: flex !important;
    gap: 20px !important;
    flex-wrap: wrap !important;
}

.stat-badge {
    background: var(--glass-bg) !important;
    padding: 12px 20px !important;
    border-radius: 30px !important;
    font-size: 1rem !important;
    font-weight: 700 !important;
    backdrop-filter: blur(15px) !important;
    border: 1px solid var(--glass-border) !important;
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
    transition: var(--transition) !important;
    box-shadow: var(--shadow-light) !important;
    animation: badgeFloat 5s ease-in-out infinite !important;
}

@keyframes badgeFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

.stat-badge:hover {
    transform: translateY(-5px) scale(1.05) !important;
    box-shadow: var(--shadow-heavy) !important;
    background: rgba(255, 255, 255, 0.2) !important;
}

.stat-badge i {
    font-size: 1.1rem !important;
    animation: iconRotate 3s ease-in-out infinite !important;
}

@keyframes iconRotate {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(10deg); }
}

/* تصميم قسم التحكم المتطور */
.controls-section {
    background: var(--glass-bg) !important;
    backdrop-filter: blur(20px) !important;
    border-radius: var(--border-radius) !important;
    padding: 40px !important;
    margin-bottom: 40px !important;
    box-shadow: var(--shadow-light) !important;
    border: 2px solid var(--glass-border) !important;
    position: relative !important;
    overflow: hidden !important;
}

.controls-section::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: -100% !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent) !important;
    animation: controlsShimmer 3s ease-in-out infinite !important;
}

@keyframes controlsShimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.controls-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 30px !important;
    padding-bottom: 20px !important;
    border-bottom: 2px solid rgba(255, 255, 255, 0.2) !important;
    position: relative !important;
    z-index: 1 !important;
}

.controls-header h3 {
    margin: 0 !important;
    color: #495057 !important;
    font-weight: 900 !important;
    font-size: 1.8rem !important;
    display: flex !important;
    align-items: center !important;
    gap: 15px !important;
    background: linear-gradient(135deg, var(--stage-primary), var(--stage-secondary)) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
}

.controls-header h3 i {
    background: linear-gradient(135deg, var(--stage-primary), var(--stage-secondary)) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    animation: headerIconSpin 4s ease-in-out infinite !important;
}

@keyframes headerIconSpin {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(10deg); }
}

.controls-actions {
    display: flex !important;
    gap: 15px !important;
    position: relative !important;
    z-index: 1 !important;
}

.controls-actions .btn {
    background: var(--glass-bg) !important;
    backdrop-filter: blur(10px) !important;
    border: 2px solid var(--glass-border) !important;
    border-radius: 15px !important;
    padding: 12px 20px !important;
    font-weight: 700 !important;
    transition: var(--transition) !important;
    position: relative !important;
    overflow: hidden !important;
}

.controls-actions .btn::before {
    content: '' !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    width: 0 !important;
    height: 0 !important;
    background: rgba(255, 255, 255, 0.3) !important;
    border-radius: 50% !important;
    transition: all 0.6s ease !important;
    transform: translate(-50%, -50%) !important;
}

.controls-actions .btn:hover::before {
    width: 100% !important;
    height: 100% !important;
}

.controls-actions .btn:hover {
    transform: translateY(-3px) scale(1.05) !important;
    box-shadow: var(--shadow-heavy) !important;
    border-color: rgba(255, 255, 255, 0.4) !important;
}

.controls-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
    gap: 25px !important;
    position: relative !important;
    z-index: 1 !important;
}

.control-item {
    position: relative !important;
}

.control-item label {
    display: block !important;
    margin-bottom: 12px !important;
    font-weight: 700 !important;
    font-size: 1.1rem !important;
    color: #495057 !important;
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.control-item label i {
    color: var(--stage-primary) !important;
    animation: labelIconPulse 2s ease-in-out infinite !important;
}

@keyframes labelIconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

.control-item .form-control {
    background: var(--glass-bg) !important;
    backdrop-filter: blur(10px) !important;
    border: 2px solid var(--glass-border) !important;
    border-radius: 15px !important;
    padding: 15px 20px !important;
    transition: var(--transition) !important;
    font-size: 1rem !important;
    font-weight: 600 !important;
    color: #495057 !important;
}

.control-item .form-control:focus {
    border-color: var(--stage-primary) !important;
    box-shadow: 0 0 0 0.3rem rgba(102, 126, 234, 0.25) !important;
    transform: scale(1.02) !important;
    background: rgba(255, 255, 255, 0.15) !important;
}

.control-item .form-control:hover {
    border-color: rgba(255, 255, 255, 0.4) !important;
    transform: translateY(-2px) !important;
}
