/* Sidebar Styles */
.sidebar {
    position: fixed;
    height: 100%;
    width: 0;
    top: 0;
    right: 0;
    background-color: #fff;
    overflow-x: hidden;
    transition: 0.5s;
    box-shadow: -2px 0 5px rgba(0,0,0,0.1);
    z-index: 1000;
    padding-top: 60px;
    direction: rtl;
}

.sidebar.active {
    width: 250px;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    text-align: center;
}

.sidebar-header h3 {
    color: #0d47a1;
    font-size: 18px;
    margin-bottom: 10px;
}

.sidebar-content {
    padding: 20px;
}

.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-menu li {
    margin-bottom: 15px;
}

.sidebar-menu a {
    display: flex;
    align-items: center;
    color: #333;
    text-decoration: none;
    padding: 10px;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.sidebar-section {
    margin-top: 20px;
}

.sidebar-section-title {
    font-size: 14px;
    font-weight: bold;
    color: #0d47a1;
    padding: 5px 10px;
    border-bottom: 1px solid #eee;
    margin-bottom: 10px;
}

.sidebar-menu a:hover {
    background-color: #f5f5f5;
}

.sidebar-menu a i {
    margin-left: 10px;
    font-size: 18px;
    color: #0d47a1;
    width: 20px;
    text-align: center;
}

.sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
}

.sidebar-toggle {
    cursor: pointer;
}

.sidebar-close {
    position: absolute;
    top: 20px;
    left: 20px;
    font-size: 20px;
    cursor: pointer;
    color: #666;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
}

.user-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: #0d47a1;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;
    margin-bottom: 10px;
}

.user-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.user-role {
    font-size: 14px;
    color: #666;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .sidebar.active {
        width: 100%;
    }
}
