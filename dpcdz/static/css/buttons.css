/* ========================================
   أنماط الأزرار الموحدة
   ======================================== */

/* الأزرار الأساسية */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-base);
    font-weight: 500;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    border: 1px solid transparent;
    border-radius: var(--border-radius-md);
    transition: var(--transition-normal);
    min-height: 44px;
    white-space: nowrap;
}

.btn:hover {
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn:focus {
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn:disabled {
    opacity: 0.65;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* أزرار ملونة */
.btn-primary {
    color: var(--text-white);
    background: var(--gradient-primary);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-secondary {
    color: var(--text-white);
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-secondary:hover {
    background-color: var(--secondary-dark);
    border-color: var(--secondary-dark);
}

.btn-success {
    color: var(--text-white);
    background: var(--gradient-success);
    border-color: var(--success-color);
}

.btn-success:hover {
    background: var(--success-dark);
    border-color: var(--success-dark);
}

.btn-danger {
    color: var(--text-white);
    background: var(--gradient-danger);
    border-color: var(--danger-color);
}

.btn-danger:hover {
    background: var(--danger-dark);
    border-color: var(--danger-dark);
}

.btn-warning {
    color: var(--text-primary);
    background: var(--gradient-warning);
    border-color: var(--warning-color);
}

.btn-warning:hover {
    background: var(--warning-dark);
    border-color: var(--warning-dark);
}

.btn-info {
    color: var(--text-white);
    background: var(--gradient-info);
    border-color: var(--info-color);
}

.btn-info:hover {
    background: var(--info-dark);
    border-color: var(--info-dark);
}

.btn-light {
    color: var(--text-primary);
    background-color: var(--bg-light);
    border-color: var(--border-color);
}

.btn-light:hover {
    background-color: var(--border-color);
    border-color: var(--border-dark);
}

.btn-dark {
    color: var(--text-white);
    background-color: var(--bg-dark);
    border-color: var(--bg-dark);
}

.btn-dark:hover {
    background-color: #23272b;
    border-color: #1d2124;
}

/* أزرار شفافة */
.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    color: var(--text-white);
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-secondary {
    color: var(--secondary-color);
    border-color: var(--secondary-color);
    background: transparent;
}

.btn-outline-secondary:hover {
    color: var(--text-white);
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-outline-success {
    color: var(--success-color);
    border-color: var(--success-color);
    background: transparent;
}

.btn-outline-success:hover {
    color: var(--text-white);
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-outline-danger {
    color: var(--danger-color);
    border-color: var(--danger-color);
    background: transparent;
}

.btn-outline-danger:hover {
    color: var(--text-white);
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

/* أحجام الأزرار */
.btn-lg {
    padding: var(--spacing-lg) var(--spacing-xxl);
    font-size: var(--font-size-lg);
    border-radius: var(--border-radius-lg);
    min-height: 56px;
}

.btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    border-radius: var(--border-radius-sm);
    min-height: 36px;
}

.btn-xs {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
    border-radius: var(--border-radius-sm);
    min-height: 28px;
}

/* أزرار دائرية */
.btn-circle {
    width: 50px;
    height: 50px;
    border-radius: var(--border-radius-round);
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-circle-lg {
    width: 60px;
    height: 60px;
}

.btn-circle-sm {
    width: 40px;
    height: 40px;
}

/* أزرار عائمة */
.floating-buttons {
    position: fixed;
    bottom: var(--spacing-lg);
    right: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    z-index: var(--z-fixed);
}

.floating-btn {
    width: 50px;
    height: 50px;
    border-radius: var(--border-radius-round);
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.floating-btn:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: var(--shadow-xl);
}

/* مجموعات الأزرار */
.btn-group {
    position: relative;
    display: inline-flex;
    vertical-align: middle;
}

.btn-group > .btn {
    position: relative;
    flex: 1 1 auto;
    margin-right: -1px;
}

.btn-group > .btn:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.btn-group > .btn:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.btn-group > .btn:hover {
    z-index: 1;
}

/* أزرار التبديل */
.btn-toggle {
    position: relative;
}

.btn-toggle.active {
    background-color: var(--primary-color);
    color: var(--text-white);
    border-color: var(--primary-color);
}

/* أزرار الإجراءات */
.action-buttons {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: center;
    flex-wrap: wrap;
}

.action-btn {
    min-width: 120px;
    text-align: center;
}

/* أزرار الحالة */
.status-btn {
    border-radius: var(--border-radius-xl);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: var(--font-size-sm);
    padding: var(--spacing-xs) var(--spacing-md);
    min-height: auto;
}

.status-present {
    background-color: var(--status-present);
    color: var(--text-white);
}

.status-absent {
    background-color: var(--status-absent);
    color: var(--text-white);
}

.status-on-mission {
    background-color: var(--status-on-mission);
    color: var(--text-primary);
}

/* التصميم المتجاوب للأزرار */
@media (max-width: 768px) {
    .btn {
        padding: var(--spacing-sm) var(--spacing-lg);
        font-size: var(--font-size-sm);
        min-height: 40px;
    }
    
    .btn-lg {
        padding: var(--spacing-md) var(--spacing-xl);
        font-size: var(--font-size-base);
        min-height: 48px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .action-btn {
        width: 100%;
        margin-bottom: var(--spacing-sm);
    }
    
    .floating-buttons {
        bottom: var(--spacing-md);
        right: var(--spacing-md);
    }
    
    .floating-btn {
        width: 45px;
        height: 45px;
    }
}

@media (max-width: 480px) {
    .btn {
        padding: var(--spacing-xs) var(--spacing-md);
        font-size: var(--font-size-xs);
        min-height: 36px;
    }
    
    .btn-group {
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group > .btn {
        margin-right: 0;
        margin-bottom: -1px;
        border-radius: var(--border-radius-md);
    }
}
