/* إعدادات عامة */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-family: 'Cairo', '<PERSON><PERSON><PERSON>', sans-serif;
}

body {
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

/* الترويسة */
header {
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 15px 0;
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.flag img, .logo img {
    height: 60px;
}

.title h1 {
    font-size: 22px;
    color: #0d47a1;
    text-align: center;
}

/* المحتوى الرئيسي */
main {
    max-width: 1200px;
    margin: 30px auto;
    padding: 0 20px;
}

.form-container {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 30px;
}

h2 {
    font-size: 22px;
    color: #0d47a1;
    margin-bottom: 25px;
    text-align: center;
    border-bottom: 2px solid #0d47a1;
    padding-bottom: 10px;
}

/* تنسيق النموذج */
.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 8px;
}

input[type="text"],
input[type="number"],
select {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    transition: border-color 0.3s;
}

input[type="text"]:focus,
input[type="number"]:focus,
select:focus {
    border-color: #0d47a1;
    outline: none;
}

.radio-group {
    display: flex;
    gap: 20px;
}

.radio-option {
    display: flex;
    align-items: center;
}

.radio-option input[type="radio"] {
    margin-left: 8px;
}

/* أزرار الإجراءات */
.action-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
}

.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.3s, transform 0.2s;
}

.btn:hover {
    transform: translateY(-2px);
}

.btn-blue {
    background-color: #1565c0;
    color: white;
}

.btn-blue:hover {
    background-color: #0d47a1;
}

.btn-green {
    background-color: #2e7d32;
    color: white;
}

.btn-green:hover {
    background-color: #1b5e20;
}

.btn-gray {
    background-color: #757575;
    color: white;
}

.btn-gray:hover {
    background-color: #616161;
}

/* تذييل الصفحة */
footer {
    background-color: #0d47a1;
    color: white;
    text-align: center;
    padding: 15px 0;
    margin-top: 40px;
}

/* النافذة المنبثقة */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: #fff;
    margin: 15% auto;
    padding: 20px;
    border-radius: 8px;
    width: 80%;
    max-width: 500px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    position: relative;
}

.close {
    position: absolute;
    left: 20px;
    top: 15px;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
}

.modal-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .header-container {
        flex-direction: column;
        gap: 15px;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 10px;
    }
    
    .btn {
        width: 100%;
        justify-content: center;
    }
    
    .radio-group {
        flex-direction: column;
        gap: 10px;
    }
}

/* أنماط للحقول الخاطئة ورسائل الخطأ */
.error {
    border: 2px solid #ff0000 !important;
    background-color: #ffeeee !important;
}

.error-message {
    color: #ff0000;
    font-size: 0.9rem;
    margin-top: 5px;
    font-weight: bold;
}