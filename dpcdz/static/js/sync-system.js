/**
 * نظام التزامن الفوري بين الصفحات
 * Real-time Synchronization System
 */

// متغيرات عامة للتزامن
let syncInterval = null;
let currentUnitId = null;
let currentDate = null;

// تهيئة نظام التزامن
function initSyncSystem(unitId, date) {
    currentUnitId = unitId;
    currentDate = date;
    
    // بدء التزامن التلقائي كل 30 ثانية
    if (syncInterval) {
        clearInterval(syncInterval);
    }
    
    syncInterval = setInterval(() => {
        syncAllData();
    }, 30000);
    
    // تزامن فوري عند التهيئة
    syncAllData();
}

// تزامن جميع البيانات
function syncAllData() {
    if (!currentUnitId || !currentDate) {
        console.warn('معرف الوحدة أو التاريخ غير محدد');
        return;
    }
    
    fetch(`/api/get-sync-status/?unit_id=${currentUnitId}&date=${currentDate}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updatePersonnelStatusUI(data.personnel_status);
                updateVehicleStatusUI(data.vehicle_status);
                updateLastSyncTime(data.sync_timestamp);
            }
        })
        .catch(error => {
            console.error('خطأ في التزامن:', error);
        });
}

// تحديث حالة العون مع التزامن
function updatePersonnelStatusSync(personnelId, newStatus) {
    // إظهار مؤشر التحديث
    showSyncIndicator('syncing');

    const data = {
        personnel_id: personnelId,
        status: newStatus,
        date: currentDate
    };

    fetch('/api/sync-personnel-status/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // تحديث الواجهة فوراً
            updatePersonnelStatusInUI(personnelId, newStatus, data.status_display);

            // إشعار بالنجاح
            showSyncNotification('success', data.message);

            // إظهار مؤشر النجاح
            showSyncIndicator('success');

            // تزامن فوري لجميع البيانات
            setTimeout(() => syncAllData(), 500);
        } else {
            showSyncNotification('error', data.error);
            showSyncIndicator('error');
        }
    })
    .catch(error => {
        console.error('خطأ في تحديث حالة العون:', error);
        showSyncNotification('error', 'حدث خطأ في التحديث');
        showSyncIndicator('error');
    });
}

// تحديث حالة الوسيلة مع التزامن
function updateVehicleReadinessSync(vehicleId, equipmentStatus) {
    // إظهار مؤشر التحديث
    showSyncIndicator('syncing');

    const data = {
        equipment_id: vehicleId,
        status: equipmentStatus,
        date: currentDate
    };

    fetch('/api/unified/update-equipment-status/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // تحديث الواجهة فوراً
            updateVehicleReadinessInUI(vehicleId, equipmentStatus, getEquipmentStatusDisplayArabic(equipmentStatus), 100);

            // إشعار بالنجاح
            showSyncNotification('success', data.message);

            // إظهار مؤشر النجاح
            showSyncIndicator('success');

            // تحديث صفحة التوزيع إذا كانت مفتوحة
            updateAssignmentPageVehicleReadiness(vehicleId, equipmentStatus, 100);

            // تزامن فوري لجميع البيانات
            setTimeout(() => syncAllData(), 500);
        } else {
            showSyncNotification('error', data.error);
            showSyncIndicator('error');
        }
    })
    .catch(error => {
        console.error('خطأ في تحديث حالة الوسيلة:', error);
        showSyncNotification('error', 'حدث خطأ في التحديث');
        showSyncIndicator('error');
    });
}

// تحديث واجهة حالة الأعوان
function updatePersonnelStatusUI(personnelStatusList) {
    personnelStatusList.forEach(person => {
        updatePersonnelStatusInUI(person.id, person.status, getStatusDisplayArabic(person.status));
    });
}

// تحديث واجهة حالة الوسائل
function updateVehicleStatusUI(vehicleStatusList) {
    vehicleStatusList.forEach(vehicle => {
        updateVehicleReadinessInUI(
            vehicle.id, 
            vehicle.status, 
            getReadinessDisplayArabic(vehicle.status), 
            vehicle.readiness_score
        );
    });
}

// تحديث حالة عون واحد في الواجهة
function updatePersonnelStatusInUI(personnelId, status, statusDisplay) {
    // تحديث في الجدول الرئيسي - البحث عن الصف باستخدام data-personnel-id
    const row = document.querySelector(`tr[data-personnel-id="${personnelId}"]`);
    if (row) {
        // تحديث القائمة المنسدلة
        const statusSelect = row.querySelector('select.status-select[data-type="personnel"]');
        if (statusSelect) {
            statusSelect.value = status;
        }

        // تحديث خاصية البيانات
        row.setAttribute('data-status', status);

        // إضافة تأثير بصري للتحديث
        row.classList.add('status-updated');
        setTimeout(() => {
            row.classList.remove('status-updated');
        }, 2000);
    }

    // البحث البديل باستخدام data-id في القائمة المنسدلة
    const statusSelect = document.querySelector(`select[data-id="${personnelId}"][data-type="personnel"]`);
    if (statusSelect) {
        statusSelect.value = status;
    }

    // تحديث في صفحة التوزيع إذا كانت مفتوحة
    updateAssignmentPagePersonnelStatus(personnelId, status);
}

// تحديث جاهزية وسيلة واحدة في الواجهة
function updateVehicleReadinessInUI(vehicleId, status, statusDisplay, readinessScore) {
    // تحديث في الجدول الرئيسي - البحث عن الصف باستخدام data-vehicle-id
    const row = document.querySelector(`tr[data-vehicle-id="${vehicleId}"]`);
    if (row) {
        // تحديث القائمة المنسدلة للحالة
        const statusSelect = row.querySelector('select.status-select[data-type="equipment"]');
        if (statusSelect) {
            statusSelect.value = status;
        }

        // تحديث عرض الجاهزية إذا كان موجوداً
        const readinessScoreElement = row.querySelector('.readiness-score');
        if (readinessScoreElement && readinessScore !== undefined) {
            readinessScoreElement.textContent = `${readinessScore}%`;
        }

        // إضافة تأثير بصري للتحديث
        row.classList.add('status-updated');
        setTimeout(() => {
            row.classList.remove('status-updated');
        }, 2000);
    }

    // البحث البديل باستخدام data-id في القائمة المنسدلة
    const statusSelect = document.querySelector(`select[data-id="${vehicleId}"][data-type="equipment"]`);
    if (statusSelect) {
        statusSelect.value = status;
    }

    // تحديث في صفحة التوزيع إذا كانت مفتوحة
    updateAssignmentPageVehicleReadiness(vehicleId, status, readinessScore);
}

// تحديث صفحة التوزيع - حالة العون
function updateAssignmentPagePersonnelStatus(personnelId, status) {
    // إذا كانت صفحة التوزيع مفتوحة في نافذة أخرى
    if (window.assignmentWindow && !window.assignmentWindow.closed) {
        try {
            window.assignmentWindow.postMessage({
                type: 'personnel_status_update',
                personnel_id: personnelId,
                status: status
            }, '*');
        } catch (e) {
            console.warn('لا يمكن التواصل مع صفحة التوزيع');
        }
    }
}

// تحديث صفحة التوزيع - جاهزية الوسيلة
function updateAssignmentPageVehicleReadiness(vehicleId, status, readinessScore) {
    // إذا كانت صفحة التوزيع مفتوحة في نافذة أخرى
    if (window.assignmentWindow && !window.assignmentWindow.closed) {
        try {
            window.assignmentWindow.postMessage({
                type: 'vehicle_readiness_update',
                vehicle_id: vehicleId,
                status: status,
                readiness_score: readinessScore
            }, '*');
        } catch (e) {
            console.warn('لا يمكن التواصل مع صفحة التوزيع');
        }
    }

    // إشعار المستخدم إذا تم تغيير حالة الوسيلة إلى غير جاهز
    if (status === 'broken' || status === 'maintenance') {
        showSyncNotification('warning',
            'تم تغيير حالة الوسيلة إلى غير جاهز. ' +
            'ستختفي من صفحة توزيع الأعوان وسيتم تحرير الأعوان المعينين عليها تلقائياً.'
        );
    }
}

// عرض إشعارات التزامن
function showSyncNotification(type, message) {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `sync-notification sync-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
        <span>${message}</span>
    `;
    
    // إضافة الإشعار للصفحة
    document.body.appendChild(notification);
    
    // إزالة الإشعار بعد 3 ثوان
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

// تحديث وقت آخر تزامن
function updateLastSyncTime(timestamp) {
    const syncTimeElement = document.getElementById('lastSyncTime');
    if (syncTimeElement) {
        const date = new Date(timestamp);
        syncTimeElement.textContent = `آخر تحديث: ${date.toLocaleTimeString('ar-DZ')}`;
    }
}

// الحصول على النص العربي لحالة العون
function getStatusDisplayArabic(status) {
    const statusMap = {
        'present': 'حاضر',
        'absent': 'غائب',
        'on_mission': 'في مهمة'
    };
    return statusMap[status] || status;
}

// الحصول على النص العربي لجاهزية الوسيلة
function getReadinessDisplayArabic(status) {
    const statusMap = {
        'ready': 'جاهز',
        'not_ready': 'غير جاهز',
        'manually_confirmed': 'مؤكد يدوياً',
        'under_maintenance': 'تحت الصيانة'
    };
    return statusMap[status] || status;
}

// الحصول على النص العربي لحالة الوسيلة
function getEquipmentStatusDisplayArabic(status) {
    const statusMap = {
        'operational': 'جاهز',
        'broken': 'معطل',
        'maintenance': 'صيانة'
    };
    return statusMap[status] || status;
}

// استقبال رسائل من النوافذ الأخرى
window.addEventListener('message', function(event) {
    if (event.data.type === 'personnel_status_update') {
        updatePersonnelStatusInUI(event.data.personnel_id, event.data.status, getStatusDisplayArabic(event.data.status));
    } else if (event.data.type === 'vehicle_readiness_update') {
        updateVehicleReadinessInUI(event.data.vehicle_id, event.data.status, getReadinessDisplayArabic(event.data.status), event.data.readiness_score);
    }
});

// إظهار مؤشر التزامن
function showSyncIndicator(status) {
    const indicator = document.getElementById('syncIndicator');
    if (indicator) {
        indicator.className = 'sync-indicator';

        if (status === 'syncing') {
            indicator.classList.add('syncing');
            indicator.innerHTML = '<i class="fas fa-sync-alt"></i><span>جاري التحديث...</span>';
        } else if (status === 'success') {
            indicator.classList.remove('syncing', 'error');
            indicator.innerHTML = '<i class="fas fa-check-circle"></i><span>متزامن</span>';
        } else if (status === 'error') {
            indicator.classList.add('error');
            indicator.innerHTML = '<i class="fas fa-exclamation-circle"></i><span>خطأ في التزامن</span>';
        }

        // إخفاء المؤشر بعد 3 ثوان للحالات غير العادية
        if (status !== 'syncing') {
            setTimeout(() => {
                if (indicator.classList.contains('error')) {
                    indicator.innerHTML = '<i class="fas fa-sync-alt"></i><span>متزامن</span>';
                    indicator.className = 'sync-indicator';
                }
            }, 3000);
        }
    }
}

// تنظيف الموارد عند إغلاق الصفحة
window.addEventListener('beforeunload', function() {
    if (syncInterval) {
        clearInterval(syncInterval);
    }
});

// وظيفة مساعدة للحصول على CSRF token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
