document.addEventListener('DOMContentLoaded', function() {
    // Button functionality is now handled by common-buttons.js

    // Auto-calculation logic
    function updateTotals() {
        // Casualties calculation
        const cMen = parseInt(document.querySelector('.casualties-men').value) || 0;
        const cWomen = parseInt(document.querySelector('.casualties-women').value) || 0;
        const cChildren = parseInt(document.querySelector('.casualties-children').value) || 0;
        document.querySelector('.total-casualties').value = cMen + cWomen + cChildren;

        // Fatalities calculation
        const fMen = parseInt(document.querySelector('.fatalities-men').value) || 0;
        const fWomen = parseInt(document.querySelector('.fatalities-women').value) || 0;
        const fChildren = parseInt(document.querySelector('.fatalities-children').value) || 0;
        document.querySelector('.total-fatalities').value = fMen + fWomen + fChildren;
    }

    document.querySelectorAll('input[type="number"]').forEach(input => {
        input.addEventListener('input', updateTotals);
    });

    // Initialize date picker with Arabic locale
    flatpickr("#accidentDate", {
        locale: "ar",
        dateFormat: "d/m/Y",
        defaultDate: "today",
        onChange: function(selectedDates) {
            const dateDisplay = document.getElementById("selectedDateDisplay");
            const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
            dateDisplay.textContent = selectedDates[0].toLocaleDateString('ar-EG', options);

            // Set the day of week field based on the selected date
            const dayOfWeek = selectedDates[0].getDay(); // 0 = Sunday, 1 = Monday, etc.
            const dayFields = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];

            // Reset all day fields to 0
            dayFields.forEach(day => {
                document.getElementById(day).value = 0;
            });

            // Set the selected day to 1
            document.getElementById(dayFields[dayOfWeek]).value = 1;

            // Update totals
            updateTotals();
        }
    });

    // Set initial date display
    const today = new Date();
    const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
    document.getElementById("selectedDateDisplay").textContent = today.toLocaleDateString('ar-EG', options);

    // Set the day of week field based on the current date
    const dayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const dayFields = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    document.getElementById(dayFields[dayOfWeek]).value = 1;

    // Handle accident type change to populate accident nature dropdown
    document.getElementById('accidentType').addEventListener('change', function() {
        const accidentType = this.value;
        const accidentNatureSelect = document.getElementById('accidentNature');

        // Clear previous options
        accidentNatureSelect.innerHTML = '';

        // Enable the select
        accidentNatureSelect.disabled = false;

        // Add default option
        const defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.disabled = true;
        defaultOption.selected = true;
        defaultOption.textContent = 'اختر طبيعة الحادث';
        accidentNatureSelect.appendChild(defaultOption);

        // Add options based on accident type
        let options = [];

        switch(accidentType) {
            case 'ضحايا مصدومة بالمركبات':
                options = ['سيارة', 'شاحنة', 'حافلة', 'دراجة نارية', 'أخرى'];
                break;
            case 'ضحايا تصادم المركبات':
                options = ['سيارة بسيارة', 'سيارة بشاحنة', 'سيارة بحافلة', 'سيارة بدراجة نارية', 'أخرى'];
                break;
            case 'ضحايا إنقلاب المركبات':
                options = ['سيارة', 'شاحنة', 'حافلة', 'دراجة نارية', 'أخرى'];
                break;
            case 'ضحايا مصدومة بالقطار':
                options = ['سيارة', 'شاحنة', 'حافلة', 'شخص', 'أخرى'];
                break;
            case 'ضحايا حوادث أخرى':
                options = ['سقوط من مركبة', 'حريق مركبة', 'إنفجار مركبة', 'أخرى'];
                break;
        }

        // Add options to select
        options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option;
            optionElement.textContent = option;
            accidentNatureSelect.appendChild(optionElement);
        });
    });

    // Handle form submission with custom modal
    document.getElementById('saveToBoth').addEventListener('click', function() {
        // Validate form
        const form = document.getElementById('trafficAccidentForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        // Show custom confirmation modal
        const modal = document.getElementById('confirmationModal');
        modal.style.display = 'block';

        // Handle close button
        const closeBtn = modal.querySelector('.close');
        closeBtn.onclick = function() {
            modal.style.display = 'none';
        };

        // Handle clicking outside the modal
        window.onclick = function(event) {
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        };

        // Handle No button
        document.getElementById('confirmNo').onclick = function() {
            modal.style.display = 'none';
        };

        // Handle Yes button
        document.getElementById('confirmYes').onclick = function() {
            modal.style.display = 'none';

            // Get form data
            const formData = new FormData(form);
            const jsonData = {};

            // Convert FormData to JSON
            for (const [key, value] of formData.entries()) {
                // Convert string "0" to actual number 0 for numeric fields
                if (!isNaN(value) && value !== "") {
                    jsonData[key] = Number(value);
                } else {
                    jsonData[key] = value;
                }
            }

            // Send data to server
            fetch(window.location.href, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': formData.get('csrfmiddlewaretoken')
                },
                body: JSON.stringify(jsonData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Create a success message modal
                    const successModal = document.createElement('div');
                    successModal.className = 'modal';
                    successModal.style.display = 'block';

                    successModal.innerHTML = `
                        <div class="modal-content" style="width: 350px;">
                            <h2 style="color: #4CAF50;"><i class="fas fa-check-circle" style="margin-left: 10px;"></i>تم بنجاح</h2>
                            <p>${data.message}</p>
                            <div class="modal-buttons">
                                <button class="btn" style="background-color: #4CAF50; color: white;">موافق</button>
                            </div>
                        </div>
                    `;

                    document.body.appendChild(successModal);

                    // Close success modal when OK button is clicked
                    successModal.querySelector('.btn').onclick = function() {
                        document.body.removeChild(successModal);

                        // Reset form
                        form.reset();
                        // Reset date picker
                        flatpickr("#accidentDate", {}).setDate(new Date());
                        // Reset day of week
                        const today = new Date();
                        const dayOfWeek = today.getDay();
                        const dayFields = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
                        dayFields.forEach(day => {
                            document.getElementById(day).value = 0;
                        });
                        document.getElementById(dayFields[dayOfWeek]).value = 1;
                        // Update totals
                        updateTotals();
                    };

                    // Close success modal when clicking outside
                    successModal.onclick = function(event) {
                        if (event.target == successModal) {
                            document.body.removeChild(successModal);
                        }
                    };
                } else {
                    // Create an error message modal
                    const errorModal = document.createElement('div');
                    errorModal.className = 'modal';
                    errorModal.style.display = 'block';

                    errorModal.innerHTML = `
                        <div class="modal-content" style="width: 350px;">
                            <h2 style="color: #f44336;"><i class="fas fa-exclamation-circle" style="margin-left: 10px;"></i>خطأ</h2>
                            <p>${data.message || 'حدث خطأ أثناء حفظ البيانات'}</p>
                            <div class="modal-buttons">
                                <button class="btn" style="background-color: #f44336; color: white;">موافق</button>
                            </div>
                        </div>
                    `;

                    document.body.appendChild(errorModal);

                    // Close error modal when OK button is clicked
                    errorModal.querySelector('.btn').onclick = function() {
                        document.body.removeChild(errorModal);
                    };

                    // Close error modal when clicking outside
                    errorModal.onclick = function(event) {
                        if (event.target == errorModal) {
                            document.body.removeChild(errorModal);
                        }
                    };
                }
            })
            .catch(error => {
                console.error('Error:', error);

                // Create an error message modal
                const errorModal = document.createElement('div');
                errorModal.className = 'modal';
                errorModal.style.display = 'block';

                errorModal.innerHTML = `
                    <div class="modal-content" style="width: 350px;">
                        <h2 style="color: #f44336;"><i class="fas fa-exclamation-circle" style="margin-left: 10px;"></i>خطأ</h2>
                        <p>حدث خطأ أثناء حفظ البيانات</p>
                        <div class="modal-buttons">
                            <button class="btn" style="background-color: #f44336; color: white;">موافق</button>
                        </div>
                    </div>
                `;

                document.body.appendChild(errorModal);

                // Close error modal when OK button is clicked
                errorModal.querySelector('.btn').onclick = function() {
                    document.body.removeChild(errorModal);
                };

                // Close error modal when clicking outside
                errorModal.onclick = function(event) {
                    if (event.target == errorModal) {
                        document.body.removeChild(errorModal);
                    }
                };
            });
        };
    });
});
