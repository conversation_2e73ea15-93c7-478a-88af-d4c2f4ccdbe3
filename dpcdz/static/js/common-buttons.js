/**
 * Common button functionality for all forms
 * This file provides consistent button behavior across all forms
 */

document.addEventListener('DOMContentLoaded', function() {
    // Back to top button functionality
    const backToTopButton = document.getElementById('back-to-top');
    if (backToTopButton) {
        backToTopButton.addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
    }

    // Floating save button functionality
    const floatingSaveButton = document.getElementById('floatingSaveBtn');
    if (floatingSaveButton) {
        floatingSaveButton.addEventListener('click', function() {
            // Find and trigger the main save button
            // Try different button IDs used across the application
            const saveButtons = [
                document.getElementById('saveToBoth'),
                document.getElementById('saveButton'),
                document.getElementById('saveData')
            ];
            
            // Click the first valid save button found
            for (const btn of saveButtons) {
                if (btn) {
                    btn.click();
                    break;
                }
            }
        });
    }
});
