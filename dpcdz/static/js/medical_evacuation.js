document.addEventListener('DOMContentLoaded', function() {
    // Button functionality is now handled by common-buttons.js

    // Ensure number inputs work properly across all browsers
    function initializeNumberInputs() {
        const numberInputs = document.querySelectorAll('input[type="number"]');

        numberInputs.forEach(function(input) {
            // Ensure the input accepts only numbers
            input.addEventListener('input', function(e) {
                // Remove any non-numeric characters except for decimal point
                let value = e.target.value.replace(/[^0-9]/g, '');

                // Ensure the value is within the min/max range if specified
                const min = parseInt(e.target.getAttribute('min')) || 0;
                const max = parseInt(e.target.getAttribute('max')) || Infinity;

                if (value !== '') {
                    const numValue = parseInt(value);
                    if (numValue < min) {
                        value = min.toString();
                    } else if (numValue > max) {
                        value = max.toString();
                    }
                }

                e.target.value = value;
            });

            // Handle paste events
            input.addEventListener('paste', function(e) {
                e.preventDefault();
                const paste = (e.clipboardData || window.clipboardData).getData('text');
                const numericValue = paste.replace(/[^0-9]/g, '');
                if (numericValue !== '') {
                    const min = parseInt(e.target.getAttribute('min')) || 0;
                    const max = parseInt(e.target.getAttribute('max')) || Infinity;
                    let value = parseInt(numericValue);

                    if (value < min) value = min;
                    if (value > max) value = max;

                    e.target.value = value;
                    e.target.dispatchEvent(new Event('input', { bubbles: true }));
                }
            });

            // Handle keydown events to prevent invalid characters
            input.addEventListener('keydown', function(e) {
                // Allow: backspace, delete, tab, escape, enter
                if ([8, 9, 27, 13, 46].indexOf(e.keyCode) !== -1 ||
                    // Allow: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
                    (e.keyCode === 65 && e.ctrlKey === true) ||
                    (e.keyCode === 67 && e.ctrlKey === true) ||
                    (e.keyCode === 86 && e.ctrlKey === true) ||
                    (e.keyCode === 88 && e.ctrlKey === true) ||
                    // Allow: home, end, left, right, down, up
                    (e.keyCode >= 35 && e.keyCode <= 40)) {
                    return;
                }
                // Ensure that it is a number and stop the keypress
                if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
                    e.preventDefault();
                }
            });
        });
    }

    // Initialize number inputs
    initializeNumberInputs();

    // Define custom Algerian Arabic month names
    const algerianArabicMonths = {
        1: "جانفي",
        2: "فيفري",
        3: "مارس",
        4: "أفريل",
        5: "ماي",
        6: "جوان",
        7: "جويلية",
        8: "أوت",
        9: "سبتمبر",
        10: "أكتوبر",
        11: "نوفمبر",
        12: "ديسمبر"
    };

    // Define Arabic day names
    const arabicDays = ["الأحد", "الإثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"];

    // Create a custom locale for Algerian Arabic
    const algerianArabicLocale = {
        weekdays: {
            shorthand: ["أحد", "إثن", "ثلا", "أرب", "خمي", "جمع", "سبت"],
            longhand: arabicDays
        },
        months: {
            shorthand: Object.values(algerianArabicMonths),
            longhand: Object.values(algerianArabicMonths)
        },
        firstDayOfWeek: 6, // Saturday is the first day of the week in Arabic calendar
        rangeSeparator: " إلى ",
        weekAbbreviation: "أسبوع",
        scrollTitle: "قم بالتمرير للزيادة",
        toggleTitle: "اضغط للتبديل",
        amPM: ["ص", "م"],
        yearAriaLabel: "سنة",
        monthAriaLabel: "شهر",
        hourAriaLabel: "ساعة",
        minuteAriaLabel: "دقيقة"
    };

    // Register the custom locale
    flatpickr.localize(algerianArabicLocale);

    // Initialize date picker with custom formatting
    flatpickr(".date-picker", {
        dateFormat: "Y-m-d",
        disableMobile: "true",
        altInput: true,
        altFormat: "D j F Y", // Format to show day name, day number, month name, year
        formatDate: function(date, format) {
            // Custom formatter to display date in Arabic format
            if (format === "D j F Y") {
                const dayName = arabicDays[date.getDay()];
                const dayNum = date.getDate();
                const monthName = algerianArabicMonths[date.getMonth() + 1];
                const year = date.getFullYear();
                return `${dayName} ${dayNum} ${monthName} ${year}`;
            }
            return flatpickr.formatDate(date, format);
        }
    });

    // Handle intervention type change
    const interventionTypeSelect = document.getElementById('interventionType');
    const interventionNatureSelect = document.getElementById('interventionNature');

    interventionTypeSelect.addEventListener('change', function() {
        // Enable the intervention nature select
        interventionNatureSelect.disabled = false;

        // Clear existing options
        interventionNatureSelect.innerHTML = '<option value="" disabled selected>اختر طبيعة التدخل</option>';

        // Add options based on selected intervention type
        const selectedType = this.value;

        if (selectedType === 'الاختناق') {
            addOption(interventionNatureSelect, 'بالغاز الطبيعي أو البوتان', 'بالغاز الطبيعي أو البوتان');
            addOption(interventionNatureSelect, 'بغاز أحادي أكسيد الكربون', 'بغاز أحادي أكسيد الكربون');
            addOption(interventionNatureSelect, 'بإنسداد المجاري التنفسية', 'بإنسداد المجاري التنفسية');
            addOption(interventionNatureSelect, 'بالأماكن المغلقة', 'بالأماكن المغلقة');
        } else if (selectedType === 'التسممات') {
            addOption(interventionNatureSelect, 'بمواد غذائية', 'بمواد غذائية');
            addOption(interventionNatureSelect, 'بالأدوية', 'بالأدوية');
            addOption(interventionNatureSelect, 'بمواد التنظيف', 'بمواد التنظيف');
            addOption(interventionNatureSelect, 'بلسعات/عضات حيوانات', 'بلسعات/عضات حيوانات');
            addOption(interventionNatureSelect, 'أخرى', 'أخرى');
        } else if (selectedType === 'الحروق') {
            addOption(interventionNatureSelect, 'ألسنة النار', 'ألسنة النار');
            addOption(interventionNatureSelect, 'مواد سائلة ساخنة', 'مواد سائلة ساخنة');
            addOption(interventionNatureSelect, 'مواد كيميائية/مشعة', 'مواد كيميائية/مشعة');
            addOption(interventionNatureSelect, 'صعقات كهربائية', 'صعقات كهربائية');
        } else if (selectedType === 'الانفجارات') {
            addOption(interventionNatureSelect, 'غاز البوتان/البروبان', 'غاز البوتان/البروبان');
            addOption(interventionNatureSelect, 'الغاز الطبيعي', 'الغاز الطبيعي');
            addOption(interventionNatureSelect, 'الأجهزة الكهرومنزلية', 'الأجهزة الكهرومنزلية');
            addOption(interventionNatureSelect, 'أجهزة التدفئة', 'أجهزة التدفئة');
            addOption(interventionNatureSelect, 'أخرى', 'أخرى');
        } else if (selectedType === 'إجلاء المرضى') {
            addOption(interventionNatureSelect, 'إجلاء الجرحى', 'إجلاء الجرحى');
            addOption(interventionNatureSelect, 'إجلاء الإختناقات', 'إجلاء الإختناقات');
            addOption(interventionNatureSelect, 'إجلاء التسممات', 'إجلاء التسممات');
            addOption(interventionNatureSelect, 'إجلاء الحروق', 'إجلاء الحروق');
            addOption(interventionNatureSelect, 'إجلاء الإنفجارات', 'إجلاء الإنفجارات');
            addOption(interventionNatureSelect, 'إجلاء السقوط', 'إجلاء السقوط');
            addOption(interventionNatureSelect, 'إجلاء الشنق', 'إجلاء الشنق');
            addOption(interventionNatureSelect, 'إجلاء المرضى', 'إجلاء المرضى');
        } else if (selectedType === 'الغرقى') {
            addOption(interventionNatureSelect, 'الغرق في المسطحات المائية', 'الغرق في المسطحات المائية');
            addOption(interventionNatureSelect, 'الغرق في السدود', 'الغرق في السدود');
            addOption(interventionNatureSelect, 'الغرق في الأودية', 'الغرق في الأودية');
            addOption(interventionNatureSelect, 'الغرق في الشواطئ', 'الغرق في الشواطئ');
            addOption(interventionNatureSelect, 'الغرق في أماكن أخرى', 'الغرق في أماكن أخرى');
        }

        // Show/hide detailed fields based on intervention type
        const detailedFields = document.getElementById('detailedFields');
        const operationsCountInput = document.getElementById('operationsCount');
        let regularCountsRow = null;

        // Find the regular counts row more reliably
        if (operationsCountInput) {
            regularCountsRow = operationsCountInput.closest('.form-row');
            // Fallback for older browsers
            if (!regularCountsRow) {
                let parent = operationsCountInput.parentElement;
                while (parent && !parent.classList.contains('form-row')) {
                    parent = parent.parentElement;
                }
                regularCountsRow = parent;
            }
        }

        // List of intervention types that should show detailed fields
        const detailedInterventionTypes = ['الاختناق', 'التسممات', 'الحروق', 'الانفجارات', 'إجلاء المرضى', 'الغرقى'];

        if (detailedInterventionTypes.includes(selectedType)) {
            if (detailedFields) detailedFields.style.display = 'block';
            if (regularCountsRow) regularCountsRow.style.display = 'none';
        } else {
            if (detailedFields) detailedFields.style.display = 'none';
            if (regularCountsRow) regularCountsRow.style.display = 'flex';
        }
    });

    // Helper function to add options to select
    function addOption(selectElement, value, text) {
        const option = document.createElement('option');
        option.value = value;
        option.textContent = text;
        selectElement.appendChild(option);
    }

    // Handle automatic calculation of totals for detailed fields
    function calculateParamedicsTotal() {
        const childrenInput = document.getElementById('paramedicsChildrenCount');
        const womenInput = document.getElementById('paramedicsWomenCount');
        const menInput = document.getElementById('paramedicsMenCount');
        const totalInput = document.getElementById('totalParamedicsCount');

        if (childrenInput && womenInput && menInput && totalInput) {
            const children = parseInt(childrenInput.value) || 0;
            const women = parseInt(womenInput.value) || 0;
            const men = parseInt(menInput.value) || 0;
            const total = children + women + men;
            totalInput.value = total;
        }
    }

    function calculateDeathsTotal() {
        const childrenInput = document.getElementById('deathsChildrenCount');
        const womenInput = document.getElementById('deathsWomenCount');
        const menInput = document.getElementById('deathsMenCount');
        const totalInput = document.getElementById('totalDeathsDetailedCount');

        if (childrenInput && womenInput && menInput && totalInput) {
            const children = parseInt(childrenInput.value) || 0;
            const women = parseInt(womenInput.value) || 0;
            const men = parseInt(menInput.value) || 0;
            const total = children + women + men;
            totalInput.value = total;
        }
    }

    // Add event listeners for paramedics count inputs (with null checks for browser compatibility)
    const paramedicsChildrenInput = document.getElementById('paramedicsChildrenCount');
    const paramedicsWomenInput = document.getElementById('paramedicsWomenCount');
    const paramedicsMenInput = document.getElementById('paramedicsMenCount');

    if (paramedicsChildrenInput) paramedicsChildrenInput.addEventListener('input', calculateParamedicsTotal);
    if (paramedicsWomenInput) paramedicsWomenInput.addEventListener('input', calculateParamedicsTotal);
    if (paramedicsMenInput) paramedicsMenInput.addEventListener('input', calculateParamedicsTotal);

    // Add event listeners for deaths count inputs (with null checks for browser compatibility)
    const deathsChildrenInput = document.getElementById('deathsChildrenCount');
    const deathsWomenInput = document.getElementById('deathsWomenCount');
    const deathsMenInput = document.getElementById('deathsMenCount');

    if (deathsChildrenInput) deathsChildrenInput.addEventListener('input', calculateDeathsTotal);
    if (deathsWomenInput) deathsWomenInput.addEventListener('input', calculateDeathsTotal);
    if (deathsMenInput) deathsMenInput.addEventListener('input', calculateDeathsTotal);

    // Handle form submission with custom modal
    document.getElementById('saveToBoth').addEventListener('click', function() {
        // Validate form
        const form = document.getElementById('medicalEvacuationForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        // Show custom confirmation modal
        const modal = document.getElementById('confirmModal');
        modal.style.display = 'block';

        // Handle close button
        const closeBtn = modal.querySelector('.close');
        closeBtn.onclick = function() {
            modal.style.display = 'none';
        };

        // Handle clicking outside the modal
        window.onclick = function(event) {
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        };

        // Handle No button
        document.getElementById('confirmNo').onclick = function() {
            modal.style.display = 'none';
        };

        // Handle Yes button
        document.getElementById('confirmYes').onclick = function() {
            modal.style.display = 'none';

            // Get form data
            const formData = new FormData(form);
            const jsonData = {};

            // Convert FormData to JSON
            for (const [key, value] of formData.entries()) {
                // Convert string "0" to actual number 0 for numeric fields
                if (!isNaN(value) && value !== "") {
                    jsonData[key] = Number(value);
                } else {
                    jsonData[key] = value;
                }
            }

            // Send data to server
            fetch(window.location.href, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': formData.get('csrfmiddlewaretoken')
                },
                body: JSON.stringify(jsonData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success' || data.status === 'partial_success') {
                    // Create success modal
                    const successModal = document.createElement('div');
                    successModal.className = 'modal';
                    successModal.style.display = 'block';
                    successModal.innerHTML = `
                        <div class="modal-content">
                            <h3>تم الحفظ بنجاح</h3>
                            <p>${data.message}</p>
                            <button class="btn btn-green">موافق</button>
                        </div>
                    `;

                    document.body.appendChild(successModal);

                    // Close success modal when OK button is clicked
                    successModal.querySelector('.btn').onclick = function() {
                        document.body.removeChild(successModal);

                        // Reset form
                        form.reset();
                        // Reset date picker
                        flatpickr("#date", {}).setDate(new Date());
                    };
                } else {
                    // Create error modal
                    const errorModal = document.createElement('div');
                    errorModal.className = 'modal';
                    errorModal.style.display = 'block';
                    errorModal.innerHTML = `
                        <div class="modal-content">
                            <h3>خطأ</h3>
                            <p>${data.message}</p>
                            <button class="btn btn-gray">موافق</button>
                        </div>
                    `;

                    document.body.appendChild(errorModal);

                    // Close error modal when OK button is clicked
                    errorModal.querySelector('.btn').onclick = function() {
                        document.body.removeChild(errorModal);
                    };
                }
            })
            .catch(error => {
                console.error('Error:', error);
                // Create error modal
                const errorModal = document.createElement('div');
                errorModal.className = 'modal';
                errorModal.style.display = 'block';
                errorModal.innerHTML = `
                    <div class="modal-content">
                        <h3>خطأ</h3>
                        <p>حدث خطأ أثناء معالجة الطلب. يرجى المحاولة مرة أخرى.</p>
                        <button class="btn btn-gray">موافق</button>
                    </div>
                `;

                document.body.appendChild(errorModal);

                // Close error modal when OK button is clicked
                errorModal.querySelector('.btn').onclick = function() {
                    document.body.removeChild(errorModal);
                };
            });
        };
    });

    // Handle alert close buttons
    document.querySelectorAll('.btn-close').forEach(function(button) {
        button.addEventListener('click', function() {
            this.parentElement.style.display = 'none';
        });
    });

    // No increment/decrement buttons for number inputs - removed as requested
});
