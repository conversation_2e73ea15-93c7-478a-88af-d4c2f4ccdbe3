/**
 * أدوات مساعدة للتعامل مع التواريخ العربية
 * Arabic Date Utilities
 */

// قاموس لتوحيد أسماء الأشهر العربية
const standardArabicMonths = {
    // القاموس الرئيسي (الأسماء المعيارية الجزائرية)
    'جانفي': 'جانفي',
    'فيفري': 'فيفري',
    'مارس': 'مارس',
    'أفريل': 'أفريل',
    'ماي': 'ماي',
    'جوان': 'جوان',
    'جويلية': 'جويلية',
    'أوت': 'أوت',
    'سبتمبر': 'سبتمبر',
    'أكتوبر': 'أكتوبر',
    'نوفمبر': 'نوفمبر',
    'ديسمبر': 'ديسمبر',

    // الأسماء البديلة (للتوحيد)
    'يناير': 'جانفي',
    'فبراير': 'فيفري',
    'أبريل': 'أفريل',
    'مايو': 'ماي',
    'يونيو': 'جوان',
    'يوليو': 'جويلية',
    'أغسطس': 'أوت'
};

// قاموس لتحويل أسماء الأشهر العربية إلى أرقام (0-11 للاستخدام مع كائن Date)
const arabicMonthToIndex = {
    'جانفي': 0,
    'فيفري': 1,
    'مارس': 2,
    'أفريل': 3,
    'ماي': 4,
    'جوان': 5,
    'جويلية': 6,
    'أوت': 7,
    'سبتمبر': 8,
    'أكتوبر': 9,
    'نوفمبر': 10,
    'ديسمبر': 11
};

// قاموس لتحويل أرقام الأشهر (1-12) إلى أسماء عربية
const monthNumberToArabic = {
    '01': 'جانفي',
    '02': 'فيفري',
    '03': 'مارس',
    '04': 'أفريل',
    '05': 'ماي',
    '06': 'جوان',
    '07': 'جويلية',
    '08': 'أوت',
    '09': 'سبتمبر',
    '10': 'أكتوبر',
    '11': 'نوفمبر',
    '12': 'ديسمبر'
};

// قاموس لتحويل أسماء الأشهر العربية إلى أرقام الشهر (01-12)
const arabicMonthToNumberStr = {
    'جانفي': '01',
    'فيفري': '02',
    'مارس': '03',
    'أفريل': '04',
    'ماي': '05',
    'جوان': '06',
    'جويلية': '07',
    'أوت': '08',
    'سبتمبر': '09',
    'أكتوبر': '10',
    'نوفمبر': '11',
    'ديسمبر': '12',
    // الأسماء البديلة
    'يناير': '01',
    'فبراير': '02',
    'أبريل': '04',
    'مايو': '05',
    'يونيو': '06',
    'يوليو': '07',
    'أغسطس': '08'
};

/**
 * توحيد اسم الشهر العربي
 * @param {string} monthName - اسم الشهر بالعربية
 * @returns {string} - اسم الشهر الموحد
 */
function standardizeArabicMonth(monthName) {
    return standardArabicMonths[monthName] || monthName;
}

/**
 * تحويل اسم الشهر العربي إلى رقم (0-11)
 * @param {string} monthName - اسم الشهر بالعربية
 * @returns {number} - رقم الشهر (0-11) أو -1 إذا لم يتم العثور عليه
 */
function arabicMonthToNumber(monthName) {
    // توحيد اسم الشهر أولاً
    const standardName = standardizeArabicMonth(monthName);
    return arabicMonthToIndex[standardName] !== undefined ? arabicMonthToIndex[standardName] : -1;
}

/**
 * تحويل رقم الشهر إلى اسم عربي
 * @param {string|number} monthNumber - رقم الشهر (1-12)
 * @returns {string} - اسم الشهر بالعربية
 */
function numberToArabicMonth(monthNumber) {
    // تحويل الرقم إلى نص مع إضافة صفر في البداية إذا كان الرقم أقل من 10
    const monthStr = String(monthNumber).padStart(2, '0');
    return monthNumberToArabic[monthStr] || '';
}

/**
 * توحيد تنسيق التاريخ العربي في النص
 * @param {string} text - النص الذي يحتوي على تاريخ عربي
 * @returns {string} - النص بعد توحيد أسماء الأشهر
 */
function standardizeArabicDateInText(text) {
    // البحث عن نمط التاريخ العربي (مثل "28 مايو، 2025")
    const arabicDateRegex = /(\d+)\s+([\u0600-\u06FF]+)،?\s+(\d{4})/g;

    return text.replace(arabicDateRegex, (match, day, month, year) => {
        const standardMonth = standardizeArabicMonth(month);
        return `${day} ${standardMonth}، ${year}`;
    });
}

/**
 * تحويل تاريخ ISO إلى تاريخ عربي
 * @param {string} isoDate - تاريخ بتنسيق ISO (YYYY-MM-DD)
 * @returns {string} - تاريخ بالتنسيق العربي
 */
function isoToArabicDate(isoDate) {
    if (!isoDate) return '';

    const parts = isoDate.split('-');
    if (parts.length !== 3) return isoDate;

    const year = parts[0];
    const month = parts[1];
    const day = parseInt(parts[2], 10); // إزالة الأصفار في بداية اليوم

    return `${day} ${monthNumberToArabic[month]}، ${year}`;
}

/**
 * تحويل تاريخ عربي إلى تاريخ ISO
 * @param {string} arabicDate - تاريخ بالتنسيق العربي (مثل "28 ماي، 2025")
 * @returns {string} - تاريخ بتنسيق ISO (YYYY-MM-DD) أو نص فارغ إذا فشل التحويل
 */
function arabicToIsoDate(arabicDate) {
    const match = arabicDate.match(/(\d+)\s+([\u0600-\u06FF]+)،?\s+(\d{4})/);
    if (!match) return '';

    const day = match[1].padStart(2, '0');
    const monthName = standardizeArabicMonth(match[2]);
    const year = match[3];

    const monthIndex = arabicMonthToIndex[monthName];
    if (monthIndex === undefined) return '';

    // تحويل رقم الشهر (0-11) إلى تنسيق MM (01-12)
    const month = String(monthIndex + 1).padStart(2, '0');

    return `${year}-${month}-${day}`;
}

/**
 * استخراج معلومات التاريخ (الشهر والسنة) من نص
 * @param {string} dateText - نص يحتوي على تاريخ
 * @returns {Object} - كائن يحتوي على الشهر والسنة المستخرجين
 */
function extractDateInfo(dateText) {
    if (!dateText) return { month: '', year: '' };

    let year = '';
    let month = '';

    try {
        // محاولة استخراج التاريخ بتنسيق العربي مثل "28 ماي، 2025"
        const arabicDateMatch = dateText.match(/(\d+)\s+([\u0600-\u06FF]+)،?\s+(\d{4})/);
        if (arabicDateMatch) {
            year = arabicDateMatch[3];
            const arabicMonth = standardizeArabicMonth(arabicDateMatch[2]);
            month = arabicMonthToNumberStr[arabicMonth] || '';

            return { month, year };
        }

        // محاولة استخراج التاريخ بتنسيق ISO (YYYY-MM-DD)
        const isoDateMatch = dateText.match(/(\d{4})-(\d{2})-(\d{2})/);
        if (isoDateMatch) {
            year = isoDateMatch[1];
            month = isoDateMatch[2];

            return { month, year };
        }

        // محاولة استخراج التاريخ بتنسيق DD/MM/YYYY
        const slashDateMatch = dateText.match(/(\d{2})\/(\d{2})\/(\d{4})/);
        if (slashDateMatch) {
            year = slashDateMatch[3];
            month = slashDateMatch[2];

            return { month, year };
        }

        // محاولة استخراج أي أرقام قد تكون سنة وشهر
        const numbers = dateText.match(/\d+/g);
        if (numbers && numbers.length >= 2) {
            // افتراض أن أول رقم من 4 أرقام هو السنة
            for (let i = 0; i < numbers.length; i++) {
                if (numbers[i].length === 4 && parseInt(numbers[i]) > 2000) {
                    year = numbers[i];
                    // افتراض أن الرقم التالي هو الشهر
                    if (i + 1 < numbers.length) {
                        month = numbers[i + 1].padStart(2, '0');
                    }
                    break;
                }
            }
        }

        return { month, year };
    } catch (e) {
        console.error("خطأ في استخراج معلومات التاريخ:", e);
        return { month: '', year: '' };
    }
}
