/**
 * نظام الرسائل الأنيق
 * يوفر رسائل modal وtoast بتصميم جميل
 */

class NotificationSystem {
    constructor() {
        this.toastContainer = null;
        this.createToastContainer();
    }

    /**
     * إنشاء حاوية التوست
     */
    createToastContainer() {
        if (!this.toastContainer) {
            this.toastContainer = document.createElement('div');
            this.toastContainer.className = 'toast-container';
            document.body.appendChild(this.toastContainer);
        }
    }

    /**
     * عرض رسالة modal
     * @param {string} message - نص الرسالة
     * @param {string} type - نوع الرسالة (success, error, warning, info)
     * @param {string} title - عنوان الرسالة
     * @param {Array} buttons - أزرار الإجراءات
     * @param {Object} options - خيارات إضافية
     */
    showModal(message, type = 'info', title = '', buttons = null, options = {}) {
        // إزالة أي modal موجود
        this.hideModal();

        // إنشاء العناصر
        const overlay = document.createElement('div');
        overlay.className = 'notification-overlay';
        overlay.id = 'notification-modal';

        const content = document.createElement('div');
        content.className = 'notification-content';
        if (options.pulse) content.classList.add('pulse');

        // الأيقونة
        const icon = document.createElement('i');
        icon.className = `notification-icon ${type}`;
        switch (type) {
            case 'success':
                icon.classList.add('fas', 'fa-check-circle');
                break;
            case 'error':
                icon.classList.add('fas', 'fa-times-circle');
                break;
            case 'warning':
                icon.classList.add('fas', 'fa-exclamation-triangle');
                break;
            case 'info':
            default:
                icon.classList.add('fas', 'fa-info-circle');
                break;
        }

        // العنوان
        const titleElement = document.createElement('div');
        titleElement.className = `notification-title ${type}`;
        titleElement.textContent = title || this.getDefaultTitle(type);

        // الرسالة
        const messageElement = document.createElement('div');
        messageElement.className = 'notification-message';
        messageElement.innerHTML = message;

        // الأزرار
        const actionsElement = document.createElement('div');
        actionsElement.className = 'notification-actions';

        if (buttons && buttons.length > 0) {
            buttons.forEach(button => {
                const btn = document.createElement('button');
                btn.className = `notification-btn ${button.class || 'primary'}`;
                btn.textContent = button.text;
                btn.onclick = () => {
                    if (button.action) button.action();
                    this.hideModal();
                };
                actionsElement.appendChild(btn);
            });
        } else {
            // زر إغلاق افتراضي
            const closeBtn = document.createElement('button');
            closeBtn.className = 'notification-btn primary';
            closeBtn.textContent = 'موافق';
            closeBtn.onclick = () => this.hideModal();
            actionsElement.appendChild(closeBtn);
        }

        // تجميع العناصر
        content.appendChild(icon);
        content.appendChild(titleElement);
        content.appendChild(messageElement);
        content.appendChild(actionsElement);
        overlay.appendChild(content);
        document.body.appendChild(overlay);

        // إظهار الـ modal
        setTimeout(() => {
            overlay.classList.add('show');
        }, 10);

        // إغلاق عند النقر على الخلفية
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                this.hideModal();
            }
        });

        // إغلاق عند الضغط على Escape
        const escapeHandler = (e) => {
            if (e.key === 'Escape') {
                this.hideModal();
                document.removeEventListener('keydown', escapeHandler);
            }
        };
        document.addEventListener('keydown', escapeHandler);

        return overlay;
    }

    /**
     * إخفاء الـ modal
     */
    hideModal() {
        const modal = document.getElementById('notification-modal');
        if (modal) {
            modal.classList.remove('show');
            setTimeout(() => {
                modal.remove();
            }, 300);
        }
    }

    /**
     * عرض رسالة toast
     * @param {string} message - نص الرسالة
     * @param {string} type - نوع الرسالة
     * @param {string} title - عنوان الرسالة
     * @param {number} duration - مدة العرض بالميلي ثانية
     */
    showToast(message, type = 'info', title = '', duration = 5000) {
        const toast = document.createElement('div');
        toast.className = `toast-notification ${type}`;

        // الرأس
        const header = document.createElement('div');
        header.className = 'toast-header';

        const titleElement = document.createElement('div');
        titleElement.className = 'toast-title';
        
        // أيقونة صغيرة
        const icon = document.createElement('i');
        switch (type) {
            case 'success':
                icon.className = 'fas fa-check-circle';
                break;
            case 'error':
                icon.className = 'fas fa-times-circle';
                break;
            case 'warning':
                icon.className = 'fas fa-exclamation-triangle';
                break;
            case 'info':
            default:
                icon.className = 'fas fa-info-circle';
                break;
        }
        
        titleElement.appendChild(icon);
        titleElement.appendChild(document.createTextNode(title || this.getDefaultTitle(type)));

        // زر الإغلاق
        const closeBtn = document.createElement('button');
        closeBtn.className = 'toast-close';
        closeBtn.innerHTML = '&times;';
        closeBtn.onclick = () => this.hideToast(toast);

        header.appendChild(titleElement);
        header.appendChild(closeBtn);

        // الرسالة
        const messageElement = document.createElement('div');
        messageElement.className = 'toast-message';
        messageElement.innerHTML = message;

        toast.appendChild(header);
        toast.appendChild(messageElement);
        this.toastContainer.appendChild(toast);

        // إظهار التوست
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);

        // إخفاء تلقائي
        if (duration > 0) {
            setTimeout(() => {
                this.hideToast(toast);
            }, duration);
        }

        return toast;
    }

    /**
     * إخفاء toast محدد
     */
    hideToast(toast) {
        toast.classList.remove('show');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 400);
    }

    /**
     * الحصول على عنوان افتراضي حسب النوع
     */
    getDefaultTitle(type) {
        switch (type) {
            case 'success':
                return 'نجح العملية';
            case 'error':
                return 'خطأ';
            case 'warning':
                return 'تحذير';
            case 'info':
            default:
                return 'معلومات';
        }
    }

    /**
     * رسالة تأكيد
     */
    confirm(message, title = 'تأكيد', onConfirm = null, onCancel = null) {
        const buttons = [
            {
                text: 'إلغاء',
                class: 'secondary',
                action: onCancel
            },
            {
                text: 'تأكيد',
                class: 'primary',
                action: onConfirm
            }
        ];

        return this.showModal(message, 'warning', title, buttons);
    }

    /**
     * رسالة نجاح سريعة
     */
    success(message, title = 'نجح العملية') {
        return this.showToast(message, 'success', title);
    }

    /**
     * رسالة خطأ سريعة
     */
    error(message, title = 'خطأ') {
        return this.showToast(message, 'error', title);
    }

    /**
     * رسالة تحذير سريعة
     */
    warning(message, title = 'تحذير') {
        return this.showToast(message, 'warning', title);
    }

    /**
     * رسالة معلومات سريعة
     */
    info(message, title = 'معلومات') {
        return this.showToast(message, 'info', title);
    }
}

// إنشاء مثيل عام
const notifications = new NotificationSystem();

// دوال مساعدة عامة لسهولة الاستخدام
function showNotification(message, type = 'info', title = '', buttons = null, options = {}) {
    return notifications.showModal(message, type, title, buttons, options);
}

function showToast(message, type = 'info', title = '', duration = 5000) {
    return notifications.showToast(message, type, title, duration);
}

function showSuccess(message, title = 'نجح العملية') {
    return notifications.success(message, title);
}

function showError(message, title = 'خطأ') {
    return notifications.error(message, title);
}

function showWarning(message, title = 'تحذير') {
    return notifications.warning(message, title);
}

function showInfo(message, title = 'معلومات') {
    return notifications.info(message, title);
}

function confirmAction(message, title = 'تأكيد', onConfirm = null, onCancel = null) {
    return notifications.confirm(message, title, onConfirm, onCancel);
}

// استبدال alert العادي
window.originalAlert = window.alert;
window.alert = function(message) {
    showNotification(message, 'info');
};

// استبدال confirm العادي
window.originalConfirm = window.confirm;
window.confirm = function(message) {
    return new Promise((resolve) => {
        confirmAction(message, 'تأكيد', 
            () => resolve(true), 
            () => resolve(false)
        );
    });
};
