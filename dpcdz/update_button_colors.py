import os
import re

# List of form templates to update
form_templates = [
    # Form templates
    'templates/traffic_accidents/index.html',
    'templates/fires/residential_fires.html',
    'templates/fires/institutional_fires.html',
    'templates/fires/public_area_fires.html',
    'templates/fires/forest_agricultural_fires.html',
    'templates/fires/general_fire_table.html',
    'templates/misc_operations/stats.html',
    'templates/misc_operations/security_device_stats.html',
    'templates/misc_operations/exceptional_operations_stats.html',
    'templates/misc_operations/interventions_without_work_stats.html',

    # Table templates
    'templates/tables/medical_evacuation_table.html',
    'templates/tables/traffic_accidents_table.html',
    'templates/tables/general_fire_table.html',
    'templates/tables/residential_fires_table.html',
    'templates/tables/institutional_fires_table.html',
    'templates/tables/public_area_fires_table.html',
    'templates/tables/forest_agricultural_fires_table.html',
    'templates/tables/misc_operations_table.html',
    'templates/tables/security_device_table.html',
    'templates/tables/exceptional_operations_table.html',
    'templates/tables/interventions_without_work_table.html',
]

# Function to add button-override.css to the head section
def add_button_override_css(content):
    # Check if button-override.css is already included
    if '<link rel="stylesheet" href="{% static \'css/button-override.css\' %}' not in content:
        # Add button-override.css after the last CSS file
        css_pattern = r'<link rel="stylesheet" href="(?:{% static \'css/[^\']+\' %}|https://[^"]+)">'
        matches = list(re.finditer(css_pattern, content))

        if matches:
            last_match = matches[-1]
            last_css_link = last_match.group(0)
            new_content = content.replace(
                last_css_link,
                f'{last_css_link}\n    <link rel="stylesheet" href="{{% static \'css/button-override.css\' %}}?v=1.0">'
            )
            return new_content

    return content

# Main function to update a template
def update_template(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()

        # Update the template
        updated_content = add_button_override_css(content)

        # Write the updated content back to the file
        if content != updated_content:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(updated_content)
            print(f"Updated {file_path}")
        else:
            print(f"No changes needed for {file_path}")

    except Exception as e:
        print(f"Error updating {file_path}: {str(e)}")

# Update all form templates
for file_path in form_templates:
    if os.path.exists(file_path):
        update_template(file_path)
    else:
        print(f"File not found: {file_path}")

print("Button color update completed!")
