/* تنسيق خاص بالصفحة الرئيسية */
.home-container {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 30px;
    text-align: center;
}

.menu-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-top: 30px;
}

.menu-item {
    background-color: #f5f5f5;
    border-radius: 8px;
    padding: 25px;
    text-decoration: none;
    color: #333;
    transition: transform 0.3s, box-shadow 0.3s;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.menu-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.menu-icon {
    font-size: 40px;
    margin-bottom: 15px;
    color: #0d47a1;
}

.menu-title {
    font-size: 18px;
    font-weight: bold;
}

/* تصميم متجاوب للقائمة */
@media (max-width: 992px) {
    .menu-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .menu-grid {
        grid-template-columns: 1fr;
    }
}