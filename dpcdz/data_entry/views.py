from django.shortcuts import render, redirect
from django.http import HttpResponse, JsonResponse
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from .models import (
    InterventionUnit, InterventionType, InterventionNature,
    Fire, MiscOperation,
    Province, User
)

def home(request):
    return render(request, 'home.html')



def get_intervention_natures(request):
    intervention_type_id = request.GET.get('intervention_type_id')
    natures = InterventionNature.objects.filter(intervention_type_id=intervention_type_id).values('id', 'name')
    return JsonResponse(list(natures), safe=False)

# Traffic accidents view has been moved to views_traffic_accidents.py

def fires(request):
    # سيتم تنفيذه لاحقًا حسب طلب المستخدم
    return render(request, 'fires.html')

def misc_operations(request):
    # سيتم تنفيذه لاحقًا حسب طلب المستخدم
    return render(request, 'misc_operations.html')

def settings(request):
    provinces = Province.objects.all()
    units = InterventionUnit.objects.all()
    intervention_types = InterventionType.objects.all()
    intervention_natures = InterventionNature.objects.all()

    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'add_province':
            name = request.POST.get('province_name')
            Province.objects.create(name=name)
            messages.success(request, 'تمت إضافة الولاية بنجاح')

        elif action == 'add_unit':
            name = request.POST.get('unit_name')
            InterventionUnit.objects.create(name=name)
            messages.success(request, 'تمت إضافة وحدة التدخل بنجاح')

        elif action == 'add_intervention_type':
            name = request.POST.get('type_name')
            InterventionType.objects.create(name=name)
            messages.success(request, 'تمت إضافة نوع التدخل بنجاح')

        elif action == 'add_intervention_nature':
            type_id = request.POST.get('type_id')
            name = request.POST.get('nature_name')
            InterventionNature.objects.create(intervention_type_id=type_id, name=name)
            messages.success(request, 'تمت إضافة طبيعة التدخل بنجاح')

        elif action == 'delete_province':
            province_id = request.POST.get('province_id')
            Province.objects.filter(id=province_id).delete()
            messages.success(request, 'تم حذف الولاية بنجاح')

        elif action == 'delete_unit':
            unit_id = request.POST.get('unit_id')
            InterventionUnit.objects.filter(id=unit_id).delete()
            messages.success(request, 'تم حذف وحدة التدخل بنجاح')

        elif action == 'delete_intervention_type':
            type_id = request.POST.get('type_id')
            InterventionType.objects.filter(id=type_id).delete()
            messages.success(request, 'تم حذف نوع التدخل بنجاح')

        elif action == 'delete_intervention_nature':
            nature_id = request.POST.get('nature_id')
            InterventionNature.objects.filter(id=nature_id).delete()
            messages.success(request, 'تم حذف طبيعة التدخل بنجاح')

        return redirect('settings')

    context = {
        'provinces': provinces,
        'units': units,
        'intervention_types': intervention_types,
        'intervention_natures': intervention_natures,
    }
    return render(request, 'settings.html', context)

@login_required
def coordination_center(request):
    fires = Fire.objects.all().order_by('-date')
    misc_operations = MiscOperation.objects.all().order_by('-date')

    context = {
        'fires': fires,
        'misc_operations': misc_operations,
    }
    return render(request, 'coordination_center.html', context)

def login_view(request):
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')

        try:
            user = User.objects.get(username=username, password=password)
            # في الإنتاج، يجب استخدام نظام مصادقة Django الآمن
            request.session['user_id'] = user.id
            return redirect('coordination_center')
        except User.DoesNotExist:
            messages.error(request, 'اسم المستخدم أو كلمة المرور غير صحيحة')

    return render(request, 'login.html')

def logout_view(request):
    if 'user_id' in request.session:
        del request.session['user_id']
    return redirect('home')

def export_excel(request, model_type):
    # يمكن إضافة المزيد من أنواع النماذج هنا

    return HttpResponse("نوع النموذج غير مدعوم", status=400)