# Generated by Django 5.2 on 2025-05-22 05:41

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('data_entry', '0004_remove_traffic_accident'),
        ('home', '0011_delete_exceptionaloperationsdata_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='TrafficAccident',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('accident_type', models.CharField(max_length=100, verbose_name='نوع الحادث')),
                ('accident_nature', models.CharField(max_length=100, verbose_name='طبيعة الحادث')),
                ('accidents_count', models.PositiveIntegerField(default=0, verbose_name='عدد الحوادث')),
                ('operations_count', models.PositiveIntegerField(default=0, verbose_name='عدد العمليات')),
                ('road_type', models.CharField(max_length=100, verbose_name='نوع الطريق')),
                ('driver_age', models.CharField(max_length=100, verbose_name='فئة السائقين')),
                ('casualties_men', models.PositiveIntegerField(default=0, verbose_name='الجرحى رجال')),
                ('casualties_women', models.PositiveIntegerField(default=0, verbose_name='الجرحى نساء')),
                ('casualties_children', models.PositiveIntegerField(default=0, verbose_name='الجرحى أطفال')),
                ('total_casualties', models.PositiveIntegerField(default=0, verbose_name='مجموع الجرحى')),
                ('fatalities_men', models.PositiveIntegerField(default=0, verbose_name='الوفيات رجال')),
                ('fatalities_women', models.PositiveIntegerField(default=0, verbose_name='الوفيات نساء')),
                ('fatalities_children', models.PositiveIntegerField(default=0, verbose_name='الوفيات أطفال')),
                ('total_fatalities', models.PositiveIntegerField(default=0, verbose_name='مجموع الوفيات')),
                ('fuel_cars', models.PositiveIntegerField(default=0, verbose_name='سيارات وقود')),
                ('lpg_cars', models.PositiveIntegerField(default=0, verbose_name='سيارات غاز مميع')),
                ('trucks', models.PositiveIntegerField(default=0, verbose_name='شاحنات')),
                ('buses', models.PositiveIntegerField(default=0, verbose_name='حافلات')),
                ('motorcycles', models.PositiveIntegerField(default=0, verbose_name='دراجات')),
                ('tractors', models.PositiveIntegerField(default=0, verbose_name='جرارات')),
                ('directed_transport', models.PositiveIntegerField(default=0, verbose_name='النقل موجه')),
                ('other_vehicles', models.PositiveIntegerField(default=0, verbose_name='أخرى')),
                ('time_06_09', models.PositiveIntegerField(default=0, verbose_name='06:00 - 09:00')),
                ('time_09_12', models.PositiveIntegerField(default=0, verbose_name='09:00 - 12:00')),
                ('time_12_14', models.PositiveIntegerField(default=0, verbose_name='12:00 - 14:00')),
                ('time_14_16', models.PositiveIntegerField(default=0, verbose_name='14:00 - 16:00')),
                ('time_16_20', models.PositiveIntegerField(default=0, verbose_name='16:00 - 20:00')),
                ('time_20_00', models.PositiveIntegerField(default=0, verbose_name='20:00 - 00:00')),
                ('time_00_06', models.PositiveIntegerField(default=0, verbose_name='00:00 - 06:00')),
                ('sunday', models.PositiveIntegerField(default=0, verbose_name='الأحد')),
                ('monday', models.PositiveIntegerField(default=0, verbose_name='الإثنين')),
                ('tuesday', models.PositiveIntegerField(default=0, verbose_name='الثلاثاء')),
                ('wednesday', models.PositiveIntegerField(default=0, verbose_name='الأربعاء')),
                ('thursday', models.PositiveIntegerField(default=0, verbose_name='الخميس')),
                ('friday', models.PositiveIntegerField(default=0, verbose_name='الجمعة')),
                ('saturday', models.PositiveIntegerField(default=0, verbose_name='السبت')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='home.interventionunit', verbose_name='الوحدة المتدخلة')),
            ],
            options={
                'verbose_name': 'حادث مرور',
                'verbose_name_plural': 'حوادث المرور',
                'ordering': ['-date'],
            },
        ),
    ]
