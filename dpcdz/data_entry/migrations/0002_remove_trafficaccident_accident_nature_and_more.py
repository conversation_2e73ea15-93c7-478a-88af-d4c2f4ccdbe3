# Generated by Django 5.2 on 2025-05-18 14:03

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('data_entry', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='trafficaccident',
            name='accident_nature',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='accident_type',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='accidents_count',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='buses',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='casualties_children',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='casualties_men',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='casualties_women',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='directed_transport',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='driver_age',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='fatalities_children',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='fatalities_men',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='fatalities_women',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='friday',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='fuel_cars',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='lpg_cars',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='monday',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='motorcycles',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='operations_count',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='other_vehicles',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='road_type',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='saturday',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='sunday',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='thursday',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='time_00_06',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='time_06_09',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='time_09_12',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='time_12_14',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='time_14_16',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='time_16_20',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='time_20_00',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='total_casualties',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='total_fatalities',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='tractors',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='trucks',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='tuesday',
        ),
        migrations.RemoveField(
            model_name='trafficaccident',
            name='wednesday',
        ),
    ]
