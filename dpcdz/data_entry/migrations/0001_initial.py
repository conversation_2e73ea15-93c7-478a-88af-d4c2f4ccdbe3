# Generated by Django 5.2 on 2025-05-17 16:12

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='InterventionType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='نوع التدخل')),
            ],
            options={
                'verbose_name': 'نوع التدخل',
                'verbose_name_plural': 'أنواع التدخل',
            },
        ),
        migrations.CreateModel(
            name='InterventionUnit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الوحدة')),
            ],
            options={
                'verbose_name': 'وحدة التدخل',
                'verbose_name_plural': 'وحدات التدخل',
            },
        ),
        migrations.CreateModel(
            name='Province',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الولاية')),
            ],
            options={
                'verbose_name': 'ولاية',
                'verbose_name_plural': 'الولايات',
            },
        ),
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('username', models.CharField(max_length=100, verbose_name='اسم المستخدم')),
                ('password', models.CharField(max_length=100, verbose_name='كلمة المرور')),
                ('is_admin', models.BooleanField(default=False, verbose_name='مسؤول')),
            ],
            options={
                'verbose_name': 'مستخدم',
                'verbose_name_plural': 'المستخدمين',
            },
        ),
        migrations.CreateModel(
            name='InterventionNature',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='طبيعة التدخل')),
                ('intervention_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='natures', to='data_entry.interventiontype', verbose_name='نوع التدخل')),
            ],
            options={
                'verbose_name': 'طبيعة التدخل',
                'verbose_name_plural': 'طبيعة التدخلات',
            },
        ),
        migrations.CreateModel(
            name='Fire',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='data_entry.interventionunit', verbose_name='الوحدة المتدخلة')),
            ],
            options={
                'verbose_name': 'حريق',
                'verbose_name_plural': 'الحرائق',
            },
        ),
        migrations.CreateModel(
            name='MedicalEvacuation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('is_indoor', models.BooleanField(verbose_name='داخل المنزل')),
                ('operations_count', models.PositiveIntegerField(verbose_name='عدد العمليات')),
                ('interventions_count', models.PositiveIntegerField(verbose_name='عدد التدخلات')),
                ('paramedics_count', models.PositiveIntegerField(verbose_name='عدد المسعفين')),
                ('deaths_count', models.PositiveIntegerField(verbose_name='عدد الوفيات')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('intervention_nature', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='data_entry.interventionnature', verbose_name='طبيعة التدخل')),
                ('intervention_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='data_entry.interventiontype', verbose_name='نوع التدخل')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='data_entry.interventionunit', verbose_name='الوحدة المتدخلة')),
            ],
            options={
                'verbose_name': 'إجلاء طبي',
                'verbose_name_plural': 'الإجلاء الطبي',
            },
        ),
        migrations.CreateModel(
            name='MiscOperation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='data_entry.interventionunit', verbose_name='الوحدة المتدخلة')),
            ],
            options={
                'verbose_name': 'عملية مختلفة',
                'verbose_name_plural': 'عمليات مختلفة',
            },
        ),
        migrations.CreateModel(
            name='TrafficAccident',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('accident_type', models.CharField(blank=True, max_length=100, null=True, verbose_name='نوع الحادث')),
                ('accident_nature', models.CharField(blank=True, max_length=100, null=True, verbose_name='طبيعة الحادث')),
                ('accidents_count', models.PositiveIntegerField(default=0, verbose_name='عدد الحوادث')),
                ('operations_count', models.PositiveIntegerField(default=0, verbose_name='عدد العمليات')),
                ('road_type', models.CharField(blank=True, max_length=100, null=True, verbose_name='نوع الطريق')),
                ('driver_age', models.CharField(blank=True, max_length=100, null=True, verbose_name='فئة السائقين')),
                ('casualties_men', models.PositiveIntegerField(default=0, verbose_name='الجرحى رجال')),
                ('casualties_women', models.PositiveIntegerField(default=0, verbose_name='الجرحى نساء')),
                ('casualties_children', models.PositiveIntegerField(default=0, verbose_name='الجرحى أطفال')),
                ('total_casualties', models.PositiveIntegerField(default=0, verbose_name='مجموع الجرحى')),
                ('fatalities_men', models.PositiveIntegerField(default=0, verbose_name='الوفيات رجال')),
                ('fatalities_women', models.PositiveIntegerField(default=0, verbose_name='الوفيات نساء')),
                ('fatalities_children', models.PositiveIntegerField(default=0, verbose_name='الوفيات أطفال')),
                ('total_fatalities', models.PositiveIntegerField(default=0, verbose_name='مجموع الوفيات')),
                ('fuel_cars', models.PositiveIntegerField(default=0, verbose_name='سيارات وقود')),
                ('lpg_cars', models.PositiveIntegerField(default=0, verbose_name='سيارات غاز مميع')),
                ('trucks', models.PositiveIntegerField(default=0, verbose_name='شاحنات')),
                ('buses', models.PositiveIntegerField(default=0, verbose_name='حافلات')),
                ('motorcycles', models.PositiveIntegerField(default=0, verbose_name='دراجات')),
                ('tractors', models.PositiveIntegerField(default=0, verbose_name='جرارات')),
                ('directed_transport', models.PositiveIntegerField(default=0, verbose_name='النقل موجه')),
                ('other_vehicles', models.PositiveIntegerField(default=0, verbose_name='أخرى')),
                ('time_06_09', models.PositiveIntegerField(default=0, verbose_name='06 سا إلى 09 سا')),
                ('time_09_12', models.PositiveIntegerField(default=0, verbose_name='09 سا إلى 12 سا')),
                ('time_12_14', models.PositiveIntegerField(default=0, verbose_name='12 سا إلى 14 سا')),
                ('time_14_16', models.PositiveIntegerField(default=0, verbose_name='14 سا إلى 16 سا')),
                ('time_16_20', models.PositiveIntegerField(default=0, verbose_name='16 سا إلى 20 سا')),
                ('time_20_00', models.PositiveIntegerField(default=0, verbose_name='20 سا إلى 00 سا')),
                ('time_00_06', models.PositiveIntegerField(default=0, verbose_name='00 سا إلى 06 سا')),
                ('sunday', models.PositiveIntegerField(default=0, verbose_name='الأحد')),
                ('monday', models.PositiveIntegerField(default=0, verbose_name='الإثنين')),
                ('tuesday', models.PositiveIntegerField(default=0, verbose_name='الثلاثاء')),
                ('wednesday', models.PositiveIntegerField(default=0, verbose_name='الإربعاء')),
                ('thursday', models.PositiveIntegerField(default=0, verbose_name='الخميس')),
                ('friday', models.PositiveIntegerField(default=0, verbose_name='الجمعة')),
                ('saturday', models.PositiveIntegerField(default=0, verbose_name='السبت')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='data_entry.interventionunit', verbose_name='الوحدة المتدخلة')),
            ],
            options={
                'verbose_name': 'حادث مرور',
                'verbose_name_plural': 'حوادث المرور',
            },
        ),
    ]
