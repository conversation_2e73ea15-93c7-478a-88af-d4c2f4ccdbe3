# Generated by Django 5.2 on 2025-05-22 04:17

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('data_entry', '0002_remove_trafficaccident_accident_nature_and_more'),
        ('home', '0011_delete_exceptionaloperationsdata_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='fire',
            name='unit',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='home.interventionunit', verbose_name='الوحدة المتدخلة'),
        ),
        migrations.AlterField(
            model_name='miscoperation',
            name='unit',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='home.interventionunit', verbose_name='الوحدة المتدخلة'),
        ),
        migrations.AlterField(
            model_name='trafficaccident',
            name='unit',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='home.interventionunit', verbose_name='الوحدة المتدخلة'),
        ),
        migrations.AlterField(
            model_name='medicalevacuation',
            name='unit',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='home.interventionunit', verbose_name='الوحدة المتدخلة'),
        ),
        migrations.AlterModelOptions(
            name='trafficaccident',
            options={'ordering': ['-date'], 'verbose_name': 'حادث مرور', 'verbose_name_plural': 'حوادث المرور'},
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='accident_nature',
            field=models.CharField(default='أخرى', max_length=100, verbose_name='طبيعة الحادث'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='accident_type',
            field=models.CharField(default='ضحايا حوادث أخرى', max_length=100, verbose_name='نوع الحادث'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='accidents_count',
            field=models.PositiveIntegerField(default=0, verbose_name='عدد الحوادث'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='buses',
            field=models.PositiveIntegerField(default=0, verbose_name='حافلات'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='casualties_children',
            field=models.PositiveIntegerField(default=0, verbose_name='الجرحى أطفال'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='casualties_men',
            field=models.PositiveIntegerField(default=0, verbose_name='الجرحى رجال'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='casualties_women',
            field=models.PositiveIntegerField(default=0, verbose_name='الجرحى نساء'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='directed_transport',
            field=models.PositiveIntegerField(default=0, verbose_name='النقل موجه'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='driver_age',
            field=models.CharField(default='من 21 سنة إلى 30 سنة', max_length=100, verbose_name='فئة السائقين'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='fatalities_children',
            field=models.PositiveIntegerField(default=0, verbose_name='الوفيات أطفال'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='fatalities_men',
            field=models.PositiveIntegerField(default=0, verbose_name='الوفيات رجال'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='fatalities_women',
            field=models.PositiveIntegerField(default=0, verbose_name='الوفيات نساء'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='friday',
            field=models.PositiveIntegerField(default=0, verbose_name='الجمعة'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='fuel_cars',
            field=models.PositiveIntegerField(default=0, verbose_name='سيارات وقود'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='lpg_cars',
            field=models.PositiveIntegerField(default=0, verbose_name='سيارات غاز مميع'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='monday',
            field=models.PositiveIntegerField(default=0, verbose_name='الإثنين'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='motorcycles',
            field=models.PositiveIntegerField(default=0, verbose_name='دراجات'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='operations_count',
            field=models.PositiveIntegerField(default=0, verbose_name='عدد العمليات'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='other_vehicles',
            field=models.PositiveIntegerField(default=0, verbose_name='أخرى'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='road_type',
            field=models.CharField(default='طرق أخرى', max_length=100, verbose_name='نوع الطريق'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='saturday',
            field=models.PositiveIntegerField(default=0, verbose_name='السبت'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='sunday',
            field=models.PositiveIntegerField(default=0, verbose_name='الأحد'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='thursday',
            field=models.PositiveIntegerField(default=0, verbose_name='الخميس'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='time_00_06',
            field=models.PositiveIntegerField(default=0, verbose_name='00 سا إلى 06 سا'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='time_06_09',
            field=models.PositiveIntegerField(default=0, verbose_name='06 سا إلى 09 سا'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='time_09_12',
            field=models.PositiveIntegerField(default=0, verbose_name='09 سا إلى 12 سا'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='time_12_14',
            field=models.PositiveIntegerField(default=0, verbose_name='12 سا إلى 14 سا'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='time_14_16',
            field=models.PositiveIntegerField(default=0, verbose_name='14 سا إلى 16 سا'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='time_16_20',
            field=models.PositiveIntegerField(default=0, verbose_name='16 سا إلى 20 سا'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='time_20_00',
            field=models.PositiveIntegerField(default=0, verbose_name='20 سا إلى 00 سا'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='total_casualties',
            field=models.PositiveIntegerField(default=0, verbose_name='مجموع الجرحى'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='total_fatalities',
            field=models.PositiveIntegerField(default=0, verbose_name='مجموع الوفيات'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='tractors',
            field=models.PositiveIntegerField(default=0, verbose_name='جرارات'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='trucks',
            field=models.PositiveIntegerField(default=0, verbose_name='شاحنات'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='tuesday',
            field=models.PositiveIntegerField(default=0, verbose_name='الثلاثاء'),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='trafficaccident',
            name='wednesday',
            field=models.PositiveIntegerField(default=0, verbose_name='الإربعاء'),
        ),
        migrations.DeleteModel(
            name='InterventionUnit',
        ),
    ]
