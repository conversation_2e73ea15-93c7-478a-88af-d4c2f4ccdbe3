from django.db import models

# Import the InterventionUnit model from home app
from home.models import InterventionUnit

class InterventionType(models.Model):
    name = models.CharField(max_length=100, verbose_name="نوع التدخل")

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "نوع التدخل"
        verbose_name_plural = "أنواع التدخل"

class InterventionNature(models.Model):
    intervention_type = models.ForeignKey(InterventionType, on_delete=models.CASCADE, related_name="natures", verbose_name="نوع التدخل")
    name = models.Char<PERSON>ield(max_length=100, verbose_name="طبيعة التدخل")

    def __str__(self):
        return f"{self.intervention_type.name} - {self.name}"

    class Meta:
        verbose_name = "طبيعة التدخل"
        verbose_name_plural = "طبيعة التدخلات"

class MedicalEvacuation(models.Model):
    date = models.DateField(verbose_name="التاريخ")
    unit = models.ForeignKey(InterventionUnit, on_delete=models.CASCADE, verbose_name="الوحدة المتدخلة")
    intervention_type = models.ForeignKey(InterventionType, on_delete=models.CASCADE, verbose_name="نوع التدخل")
    intervention_nature = models.ForeignKey(InterventionNature, on_delete=models.CASCADE, verbose_name="طبيعة التدخل")
    is_indoor = models.BooleanField(verbose_name="داخل المنزل")
    operations_count = models.PositiveIntegerField(verbose_name="عدد العمليات")
    interventions_count = models.PositiveIntegerField(verbose_name="عدد التدخلات")

    # Detailed fields for paramedics by demographics (used for all intervention types)
    paramedics_children_count = models.PositiveIntegerField(default=0, verbose_name="عدد المسعفين أطفال")
    paramedics_women_count = models.PositiveIntegerField(default=0, verbose_name="عدد المسعفين نساء")
    paramedics_men_count = models.PositiveIntegerField(default=0, verbose_name="عدد المسعفين رجال")

    # Detailed fields for deaths by demographics (used for all intervention types)
    deaths_children_count = models.PositiveIntegerField(default=0, verbose_name="عدد الوفيات أطفال")
    deaths_women_count = models.PositiveIntegerField(default=0, verbose_name="عدد الوفيات نساء")
    deaths_men_count = models.PositiveIntegerField(default=0, verbose_name="عدد الوفيات رجال")

    created_at = models.DateTimeField(auto_now_add=True)

    @property
    def total_paramedics_detailed_count(self):
        """Calculate total paramedics from detailed breakdown"""
        return self.paramedics_children_count + self.paramedics_women_count + self.paramedics_men_count

    @property
    def total_deaths_detailed_count(self):
        """Calculate total deaths from detailed breakdown"""
        return self.deaths_children_count + self.deaths_women_count + self.deaths_men_count

    def __str__(self):
        return f"إجلاء صحي - {self.date} - {self.intervention_type.name}"

    class Meta:
        verbose_name = "إجلاء صحي"
        verbose_name_plural = "الإجلاء الصحي"
        ordering = ['-date']



class Fire(models.Model):
    date = models.DateField(verbose_name="التاريخ")
    unit = models.ForeignKey(InterventionUnit, on_delete=models.CASCADE, verbose_name="الوحدة المتدخلة")
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"حريق - {self.date}"

    class Meta:
        verbose_name = "حريق"
        verbose_name_plural = "الحرائق"

class MiscOperation(models.Model):
    date = models.DateField(verbose_name="التاريخ")
    unit = models.ForeignKey(InterventionUnit, on_delete=models.CASCADE, verbose_name="الوحدة المتدخلة")
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"عملية مختلفة - {self.date}"

    class Meta:
        verbose_name = "عملية مختلفة"
        verbose_name_plural = "عمليات مختلفة"

class Province(models.Model):
    name = models.CharField(max_length=100, verbose_name="اسم الولاية")

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "ولاية"
        verbose_name_plural = "الولايات"

class User(models.Model):
    username = models.CharField(max_length=100, verbose_name="اسم المستخدم")
    password = models.CharField(max_length=100, verbose_name="كلمة المرور")
    is_admin = models.BooleanField(default=False, verbose_name="مسؤول")

    def __str__(self):
        return self.username

    class Meta:
        verbose_name = "مستخدم"
        verbose_name_plural = "المستخدمين"