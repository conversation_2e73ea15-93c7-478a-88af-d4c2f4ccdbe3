from django.urls import path
from . import views

urlpatterns = [
    path('', views.home, name='data_entry_home'),  # Renamed to avoid conflict

    path('fires/', views.fires, name='data_entry_fires'),  # Renamed to avoid conflict
    path('misc-operations/', views.misc_operations, name='data_entry_misc_operations'),  # Renamed to avoid conflict
    path('settings/', views.settings, name='data_entry_settings'),  # Renamed to avoid conflict
    path('coordination-center/', views.coordination_center, name='data_entry_coordination_center'),  # Renamed to avoid conflict
    path('login/', views.login_view, name='data_entry_login'),  # Renamed to avoid conflict
    path('logout/', views.logout_view, name='data_entry_logout'),  # Renamed to avoid conflict
    path('export-excel/<str:model_type>/', views.export_excel, name='export_excel'),
    path('get-intervention-natures/', views.get_intervention_natures, name='get_intervention_natures'),
]
