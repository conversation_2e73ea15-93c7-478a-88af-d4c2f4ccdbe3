# Memory Daily Pro - فهم نظام التدخلات اليومية المتقدم

## 🎯 الفهم العام للنظام

بعد قراءة مجلد "التدخلات اليومية المتقدم"، فهمت أن هذا نظام شامل لإدارة التدخلات اليومية للحماية المدنية في الجزائر، يتكون من:

### 📋 المكونات الرئيسية:

#### 1. **واجهة التدخلات اليومية المتقدمة**
- تمكن مراكز التنسيق (محلية، ولائية، وطنية) من تسجيل ومتابعة التدخلات
- تدعم 5 أنواع من التدخلات:
  - الإجلاء الصحي
  - حوادث المرور
  - حريق المحاصيل الزراعية
  - حرائق البنايات والمؤسسات
  - العمليات المختلفة

#### 2. **مراحل التدخل الثلاث**
- **البلاغ الأولي**: تسجيل المعلومات الأساسية (لا يظهر في الجدول)
- **عملية التعرف**: تحديث المعلومات بعد وصول الفريق
- **إنهاء المهمة**: التقرير النهائي والإحصائيات (يظهر في الجدول)

#### 3. **نظام طلب الدعم المتدرج**
- دعم داخلي من نفس الوحدة
- دعم من وحدات مجاورة (عبر مركز التنسيق الولائي)
- فرق متخصصة (غطاسين، سينوتقنية، أماكن وعرة)
- تصعيد إلى "كارثة كبرى" عند الضرورة

### 🗄️ الجداول المتخصصة:

#### جدول الإجلاء الصحي (28 حقل):
- معلومات البلاغ الأولي (10 حقول)
- تفاصيل عملية التعرف (6 حقول)
- إحصائيات إنهاء المهمة (12 حقل)

#### جدول حوادث المرور (35 حقل):
- يتضمن تفاصيل إضافية مثل نوع الطريق والطاقة المستهلكة
- تصنيف الضحايا (سائق/راكب/مشاة)

#### جدول حريق المحاصيل الزراعية (42 حقل):
- الأكثر تفصيلاً، يشمل معلومات الرياح والبؤر
- تفاصيل الملاك المتضررين والمحاصيل المنقذة

#### جدول حرائق البنايات والمؤسسات (38 حقل):
- تفاصيل نقاط الاشتعال والأضرار
- معلومات إجلاء السكان

#### جدول العمليات المختلفة (31 حقل):
- الأبسط، يغطي التدخلات المتنوعة والاستثنائية

### 🔧 النظام التقني:

#### التكامل مع إدارة الوسائل:
- تزامن كامل مع صفحة الجاهزية
- نظام checklist للوسائل المتاحة
- تحديث فوري لحالة الوسائل (متاحة ← في تدخل ← متاحة)
- جلب بيانات الطاقم من صفحة التوزيع

#### المشاكل المحلولة:
- مشكلة "لا توجد وسائل متاحة" (تم حلها بإصلاح الفلترة)
- تحسين التخطيط الأفقي للوسائل
- تبسيط واجهة الاختيار للسرعة والوضوح

### 📊 الحالة الحالية:
- النظام تم تطويره وتحسينه عبر عدة إصدارات
- آخر تحديث: 20 يوليو 2025 (الإصدار 2.3.0)
- تم حذف النظام القديم بالكامل وإعادة البناء من الصفر
- يعمل بتزامن كامل مع أنظمة إدارة الوسائل والجاهزية

### 🎯 الهدف النهائي:
تمكين مراكز التنسيق من متابعة التدخلات من لحظة البلاغ حتى الانتهاء، مع إمكانية طلب الدعم والتصعيد للكوارث الكبرى، وتوليد تقارير شاملة لكل تدخل.

## 📝 تفاصيل النماذج والعمليات:

### 🚨 البلاغ الأولي (مشترك لجميع الأنواع):
- ساعة ودقيقة الخروج
- مكان التدخل (نص حر)
- نوع التدخل (قائمة منسدلة)
- الوسائل المرسلة (checklist من صفحة إدارة الوسائل)
- الجهة المتصلة (مواطن، شرطة، درك)
- نوع الاتصال (هاتف، راديو، وحدة تطلب الدعم)
- رقم الهاتف
- اسم المتصل
- ملاحظات إضافية

**الحالة بعد البلاغ**: `قيد التعرف` (لا يظهر في الجدول)

### 🔍 عملية التعرف (5 نماذج مختلفة):

#### 1. الإجلاء الصحي:
- ساعة الوصول
- موقع الإجلاء المحدد (داخل/خارج منزل، مكان عمل، إلخ)
- نوع الإجلاء (اختناق، تسممات، حروق، انفجارات، إجلاء مرضى، غرقى)
- طبيعة الإجلاء (تفاصيل حسب النوع)
- طلب الدعم (4 خيارات)

#### 2. حوادث المرور:
- ساعة الوصول
- موقع الحادث (وسط المدينة، سوق أسبوعي، إلخ)
- نوع الحادث (ضحايا مصدومة، تصادم، انقلاب، قطار، أخرى)
- طبيعة الحادث (تفاصيل حسب النوع)
- طلب الدعم

#### 3. حريق المحاصيل الزراعية:
- ساعة الوصول
- موقع الحريق (حقل، بستان، سهل، هضبة)
- نوع المحصول المحترق (قمح، شعير، غابة، إلخ - متعدد الاختيار)
- عدد البؤر (الموقد)
- اتجاه وسرعة الرياح
- تهديد للسكان وعدد العائلات المتأثرة
- الجهات الحاضرة (حراس الغابات، أمن، درك، إلخ)
- طلب الدعم

#### 4. حرائق البنايات والمؤسسات:
- ساعة الوصول
- موقع الحريق (داخل/خارج بناية/منزل)
- نوع الحريق (سكنية، مصنفة، مستقبلة للجمهور، مركبات)
- طبيعة الحريق (تفاصيل حسب النوع)
- طابق وغرفة محددة (اختياري)
- عدد نقاط الاشتعال
- تهديد وإجلاء السكان
- طلب الدعم

#### 5. العمليات المختلفة:
- ساعة الوصول
- موقع العملية
- نوع العملية (تدخلات مختلفة، استثنائية، جهاز أمني)
- طبيعة العملية (حسب النوع)
- طلب الدعم

**الحالة بعد التعرف**: `عملية تدخل`

### ✅ إنهاء المهمة (5 نماذج مختلفة):

#### مشترك لجميع الأنواع:
- ساعة نهاية التدخل
- مدة التدخل (حساب تلقائي)
- عدد التدخلات (حساب تلقائي: وسائل أساسية + دعم)
- إحصائيات المسعفين (عدد، أسماء، أعمار، جنس)
- إحصائيات الوفيات (عدد، أسماء، أعمار، جنس)

#### خاص بحوادث المرور:
- نوع الطريق (سيار، وطني، ولائي، بلدي)
- الطاقة المستهلكة (وقود، غاز، كهرباء)
- طبيعة الخسائر (سيارة، دراجة، جرار، حافلة - متعدد)

#### خاص بحريق المحاصيل:
- تفاصيل المحاصيل المحترقة (نوع، وحدة قياس، كمية)
- تفاصيل المحاصيل المنقذة
- إحصائيات الملاك المتضررين

#### خاص بحرائق البنايات:
- عدد العائلات المتضررة
- وصف الخسائر (نص تفصيلي)
- وصف الأملاك المنقذة

**الحالة النهائية**: `منتهية` (يظهر في الجدول مع زر عرض التفاصيل)

## 🆘 نظام طلب الدعم المتدرج:

### 1. **شكراً، الوضع تحت السيطرة**
- لا يتم طلب أي دعم إضافي
- التدخل يستمر بالوسائل الحالية

### 2. **وسيلة إضافية من نفس الوحدة**
- اختيار من الوسائل المتوفرة (checklist)
- تحديث فوري لحالة الوسيلة إلى "في تدخل"
- إضافة الوسيلة للتدخل الحالي

### 3. **وحدة مجاورة** (عبر مركز التنسيق الولائي)
- إرسال إنذار صوتي تلقائي لمركز التنسيق الولائي
- الولائي يختار الوحدة المناسبة
- تسجيل تدخل جديد عند الوحدة الداعمة
- إرسال إشارة صوتية للوحدة الداعمة

### 4. **فريق متخصص**
- فرقة الغطاسين
- التدخل في الأماكن الوعرة
- فرقة السينوتقنية
- إمكانية إضافة فرق جديدة (للإدمن فقط)
- تسجيل الفريق كسطر مستقل في الجدول
- إرسال إشارة صوتية لمركز التنسيق الولائي

### 5. **تصعيد إلى كارثة كبرى**
- يتم من مركز التنسيق الولائي فقط
- إرسال إشارة صوتية لمركز التنسيق الوطني
- انتقال التدخل إلى صفحة الكوارث الكبرى
- تغيير الحالة إلى `كارثة كبرى`

## 🏗️ البنية التقنية والتكامل:

### 📊 قاعدة البيانات:
- **DailyIntervention**: النموذج الرئيسي للتدخلات
- **InterventionVehicle**: ربط الوسائل بالتدخلات
- **VehicleInterventionStatus**: حالة الوسيلة في التدخل
- **InterventionReport**: التقرير المفصل
- **تفاصيل متخصصة**: نماذج منفصلة لكل نوع تدخل

### 🔗 APIs المطورة:
- `POST /api/interventions/save-initial-report/`: حفظ البلاغ الأولي
- `POST /api/interventions/update-status/`: تحديث حالة التدخل
- `POST /api/interventions/complete/`: إنهاء المهمة
- `GET /api/interventions/get-available-vehicles/`: جلب الوسائل المتاحة
- `GET /api/interventions/get-intervention-vehicles-count/`: عدد الوسائل

### 🔄 آلية التزامن:
- تزامن فوري مع صفحة الجاهزية (`/vehicle-readiness/`)
- تزامن مع صفحة التوزيع (`/vehicle-crew-assignment/`)
- تزامن مع الصفحة الموحدة (`/coordination-center/unified-morning-check/`)
- استخدام `window.postMessage` للتزامن بين الصفحات

### 📱 الواجهات المتكاملة:
- **صفحة التدخلات اليومية**: `/coordination-center/daily-interventions/`
- **صفحة تفاصيل التدخل**: `/coordination-center/intervention-details/`
- **صفحة الجداول المتقدم**: (مخطط لها)
- **صفحة مركز التنسيق الولائي**: (مخطط لها)
- **صفحة الكوارث الكبرى**: (موجودة)

## 📋 الحالة الحالية والمهام المطلوبة:

### ✅ ما تم إنجازه:
- تطوير النظام الأساسي للتدخلات اليومية
- حل مشكلة "لا توجد وسائل متاحة"
- تحسين واجهة اختيار الوسائل (checklist مبسط)
- تكامل كامل مع أنظمة إدارة الوسائل
- نظام تقارير تلقائي

### ❌ ما لم يتم إنشاؤه بعد:
- **جداول التدخلات المتقدم**: 5 جداول متخصصة
  - جدول الإجلاء الصحي
  - جدول حوادث المرور
  - جدول حريق المحاصيل الزراعية (يحتاج إصلاح)
  - جدول حرائق البنايات والمؤسسات
  - جدول العمليات المختلفة

### 🔧 مشاكل معروفة:
- جدول حريق المحاصيل الزراعية يحتاج إصلاح:
  - حذف عمود "الحالة (سائق/راكب/مشاة)"
  - إصلاح عرض البيانات الفارغة
  - الاختبار على: `http://127.0.0.1:8000/coordination-center/intervention-details/`

### 🎯 المهام التالية:
1. إنشاء الجداول المتقدمة الخمسة
2. إصلاح جدول حريق المحاصيل الزراعية
3. تطوير صفحة مركز التنسيق الولائي
4. تطوير نظام الإنذارات الصوتية
5. تحسين نظام التقارير وإضافة تصدير PDF